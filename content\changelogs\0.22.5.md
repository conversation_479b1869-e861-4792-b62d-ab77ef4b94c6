Based on the provided commits, here is the changelog for the new screenpipe update:

### **Fixes:**
- **Fixed auto destruct for Windows CLI:** Resolved issue #926 to ensure the Windows command line interface properly auto-destructs when necessary.
- **Fixed benchmark tests:** Addressed issues within the benchmark tests for improved reliability.
- **Resolved unit test issues:** Fixed various unit tests to ensure accurate functionality and coverage.

#### **Full Changelog:** [e147b..06914](https://github.com/mediar-ai/screenpipe/compare/e147b..06914)

