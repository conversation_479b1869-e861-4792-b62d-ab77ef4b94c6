Based on your provided commits, here is the generated changelog:

### **New Features:**
- **Added notification for FFmpeg download:** Users will now see a toast notification when FFmpeg is being downloaded, ensuring they are informed of the process.
- **User guidance during FFmpeg installation:** Implemented a message to inform users that FFmpeg is being downloaded to prevent unnecessary re-installation.

### **Improvements:**
- **Enhanced UX for data table:** Made smaller improvements to the user experience on the data table.

### **Fixes:**
- **Fixed build issues:** Resolved various issues related to the application build process.

#### **Full Changelog:** [dfdc5..87803](https://github.com/mediar-ai/screenpipe/compare/dfdc5..87803)

