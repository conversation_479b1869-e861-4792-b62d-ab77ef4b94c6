{"$schema": "https://mintlify.com/docs.json", "theme": "mint", "name": "screenpipe documentation", "colors": {"primary": "#000000", "light": "#333333", "dark": "#000000"}, "favicon": {"light": "favicon.ico", "dark": "favicon.ico"}, "logo": "favicon.ico", "navigation": {"dropdowns": [{"dropdown": "Terminator", "icon": "robot", "pages": [{"group": "Getting Started", "icon": "rocket", "pages": ["terminator/introduction", "terminator/getting-started"]}, {"group": "Reference", "icon": "code", "pages": ["terminator/js-sdk-reference", "terminator/python-sdk-reference"]}]}, {"dropdown": "Screenpipe", "icon": "monitor-recorder", "pages": [{"group": "Getting Started", "icon": "rocket", "pages": ["home", "getting-started"]}, {"group": "Reference", "icon": "code", "pages": ["sdk-reference", "operator-api", "architecture", "cli-reference"]}, {"group": "Extend", "icon": "puzzle-piece", "pages": ["plugins", "mcp-server", "contributing"]}, {"group": "Help", "icon": "question-circle", "pages": ["faq"]}]}, {"dropdown": "Screenpipe API Reference", "icon": "terminal", "openapi": "openapi.yaml"}]}, "api": {"playground": {"display": "none"}}, "navbar": {"links": [{"label": "Support", "href": "https://discord.gg/dU9EBuw7Uq"}, {"label": "GitHub", "href": "https://github.com/mediar-ai/screenpipe"}], "primary": {"type": "button", "label": "Get App", "href": "https://screenpi.pe/onboarding"}}, "footer": {"socials": {"github": "https://github.com/mediar-ai/screenpipe"}}, "search": {"prompt": "Search documentation..."}, "integrations": {"posthog": {"apiKey": "phc_Bt8GoTBPgkCpDrbaIZzJIEYt0CrJjhBiuLaBck1clce", "apiHost": "https://eu.i.posthog.com"}}}