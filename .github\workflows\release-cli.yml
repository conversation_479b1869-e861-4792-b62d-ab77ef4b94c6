# # Run for macOS
# act -W .github/workflows/release-cli.yml --container-architecture linux/amd64 -j build-macos -P macos-latest=-self-hosted

# act -W .github/workflows/release-cli.yml --container-architecture linux/amd64 -j build-linux -P ubuntu-latest=catthehacker/ubuntu:act-latest --secret GITHUB_TOKEN=$(cat .env | grep GITHUB_TOKEN | tail -n 1 | cut -d '=' -f 2)

name: Release CLI

on:
  push:
    tags:
      - "v*"
  workflow_dispatch:
permissions:
  contents: write
jobs:
  build-macos:
    runs-on: macos-latest
    strategy:
      matrix:
        target: [x86_64-apple-darwin, aarch64-apple-darwin]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Rust
        uses: actions-rust-lang/setup-rust-toolchain@v1
        with:
          toolchain: stable
          override: true
          cache: true
          target: ${{ matrix.target }}
          rustflags: ""

      - name: Cache Homebrew packages
        uses: actions/cache@v4
        with:
          path: |
            ~/Library/Caches/Homebrew
            /usr/local/Cellar/ffmpeg
            /usr/local/Cellar/pkg-config
          key: ${{ runner.os }}-brew-${{ hashFiles('.github/workflows/release-cli.yml') }}
          restore-keys: |
            ${{ runner.os }}-brew-

      - name: Install dependencies
        run: |
          brew unlink pkg-config@0.29.2 || true
          brew install ffmpeg pkg-config
          brew link --overwrite pkg-config

      - uses: actions/cache@v4
        with:
          path: |
            ~/.cargo/bin/
            ~/.cargo/registry/index/
            ~/.cargo/registry/cache/
            ~/.cargo/git/db/
          key: ${{ matrix.platform }}-cargo-${{ hashFiles('**/Cargo.lock') }}

      - name: Build with Metal feature
        run: |
          export PKG_CONFIG_PATH="/usr/local/opt/ffmpeg/lib/pkgconfig:$PKG_CONFIG_PATH"
          export PKG_CONFIG_ALLOW_CROSS=1
          export RUSTFLAGS="-C link-arg=-Wl,-rpath,@executable_path/../lib -C link-arg=-Wl,-rpath,@loader_path/../lib"
          cargo build --release --features metal --target ${{ matrix.target }}

      - name: Set version
        run: |
          if [[ $GITHUB_REF == refs/tags/* ]]; then
            VERSION=${GITHUB_REF#refs/tags/v}
          else
            VERSION=$(git ls-remote --tags --refs --sort="version:refname" | tail -n1 | sed 's/.*\///' | sed 's/^v//')
          fi
          if [[ -z "$VERSION" ]]; then
            VERSION="0.0.0"
          fi
          echo "VERSION=$VERSION" >> $GITHUB_ENV
          echo "Set version to: $VERSION"

      - name: Create deployment package
        run: |
          mkdir -p screenpipe-${{ env.VERSION }}-${{ matrix.target }}/bin
          cp target/${{ matrix.target }}/release/screenpipe screenpipe-${{ env.VERSION }}-${{ matrix.target }}/bin/
          tar -czf screenpipe-${{ env.VERSION }}-${{ matrix.target }}.tar.gz -C screenpipe-${{ env.VERSION }}-${{ matrix.target }} .

      - name: Calculate SHA256
        run: |
          echo "MAC_SHA256_${{ matrix.target }}=$(shasum -a 256 screenpipe-*.tar.gz | cut -d ' ' -f 1)" >> $GITHUB_ENV

      - name: Upload Artifact
        uses: actions/upload-artifact@v4
        with:
          name: screenpipe-macos-${{ matrix.target }}
          path: screenpipe-*.tar.gz

  build-windows:
    runs-on: windows-2019
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install Rust
        run: |
          Invoke-WebRequest https://static.rust-lang.org/rustup/dist/x86_64-pc-windows-gnu/rustup-init.exe -OutFile rustup-init.exe
          .\rustup-init.exe -y

      - name: Install 7zip
        shell: powershell
        run: |
          $7zipUrl = "https://7-zip.org/a/7z2301-x64.exe"
          $7zipInstaller = "7z-installer.exe"
          Invoke-WebRequest -Uri $7zipUrl -OutFile $7zipInstaller
          Start-Process -FilePath .\$7zipInstaller -Args "/S" -Wait
          Remove-Item $7zipInstaller
          # Add 7zip to PATH and make it persistent for subsequent steps
          echo "C:\Program Files\7-Zip" | Out-File -FilePath $env:GITHUB_PATH -Encoding utf8 -Append
          # Verify installation
          & "C:\Program Files\7-Zip\7z.exe" i

      - name: Set up MSVC
        uses: ilammy/msvc-dev-cmd@v1

      - name: Install LLVM and Clang
        uses: KyleMayes/install-llvm-action@v2
        with:
          version: "10.0"

      - uses: actions/cache@v4
        with:
          path: |
            ~/.cargo/registry/index/
            ~/.cargo/registry/cache/
            ~/.cargo/git/db/
          key: windows-cargo-${{ hashFiles('**/Cargo.lock') }}

      - name: Build CLI
        env:
          CARGO_PROFILE_RELEASE_STRIP: "symbols"
          CARGO_PROFILE_RELEASE_PANIC: "abort"
          CARGO_PROFILE_RELEASE_INCREMENTAL: "false"
          RUSTFLAGS: "-C target-feature=+crt-static -C link-arg=/LTCG"
        run: |
          cargo build --release --target x86_64-pc-windows-msvc

      - name: Set version
        shell: pwsh
        run: |
          $VERSION = if ($env:GITHUB_REF -match "refs/tags/*") {
              $env:GITHUB_REF -replace "refs/tags/v", ""
          } else {
              $tags = git ls-remote --tags --refs --sort="version:refname"
              if ($tags) {
                  $latestTag = ($tags -split "`n")[-1] -replace ".*/v", ""
                  $latestTag
              } else {
                  "0.0.0"
              }
          }
          if ([string]::IsNullOrEmpty($VERSION)) {
              $VERSION = "0.0.0"
          }
          "VERSION=$VERSION" | Out-File -FilePath $env:GITHUB_ENV -Append
          "Set version to: $VERSION"

      - name: Create deployment package
        shell: pwsh
        run: |
          $packageDir = "screenpipe-${{ env.VERSION }}-x86_64-pc-windows-msvc"
          New-Item -Path "$packageDir/bin" -ItemType Directory -Force
          Copy-Item "target/x86_64-pc-windows-msvc/release/screenpipe.exe" "$packageDir/bin/"
          Copy-Item "target/x86_64-pc-windows-msvc/release/onnxruntime.dll" "$packageDir/bin/"
          7z a "$packageDir.zip" "./$packageDir/*"

      - name: Calculate SHA256
        shell: pwsh
        run: |
          $hash = Get-FileHash "screenpipe-${{ env.VERSION }}-x86_64-pc-windows-msvc.zip" -Algorithm SHA256
          "WIN_SHA256=$($hash.Hash)" | Out-File -FilePath $env:GITHUB_ENV -Append

      - name: Upload Artifact
        uses: actions/upload-artifact@v4
        with:
          name: screenpipe-windows
          path: screenpipe-*.zip

  build-linux:
    runs-on: ubuntu-22.04
    strategy:
      matrix:
        target: [x86_64-unknown-linux-gnu]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Rust
        uses: dtolnay/rust-toolchain@stable
        with:
          targets: ${{ matrix.target }}

      - name: Install dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y \
            pkg-config \
            ffmpeg \
            libavcodec-dev \
            libavformat-dev \
            libavutil-dev \
            libswscale-dev \
            libasound2-dev \
            libdbus-1-dev \
            libxcb1-dev \
            libxcb-render0-dev \
            libxcb-shape0-dev \
            libxcb-xfixes0-dev \
            libtesseract-dev \
            libssl-dev \
            cmake \
            build-essential \
            libx11-dev \
            libxi-dev \
            libxext-dev \
            libxtst-dev \
            libxrandr-dev \
            libxinerama-dev \
            libxcursor-dev \
            libxdo-dev

      - name: Install LLVM and Clang
        uses: KyleMayes/install-llvm-action@v1
        with:
          version: "14.0"

      - name: Set LIBCLANG_PATH
        run: |
          echo "LIBCLANG_PATH=$(llvm-config --prefix)/lib" >> $GITHUB_ENV

      - uses: actions/cache@v4
        with:
          path: |
            ~/.cargo/bin/
            ~/.cargo/registry/index/
            ~/.cargo/registry/cache/
            ~/.cargo/git/db/
          key: ${{ matrix.target }}-cargo-${{ hashFiles('**/Cargo.lock') }}

      - name: Build CLI
        run: |
          cargo build --release --target ${{ matrix.target }}

      - name: Set version
        run: |
          if [[ $GITHUB_REF == refs/tags/* ]]; then
            VERSION=${GITHUB_REF#refs/tags/v}
          else
            VERSION=$(git ls-remote --tags --refs --sort="version:refname" | tail -n1 | sed 's/.*\///' | sed 's/^v//')
          fi
          if [[ -z "$VERSION" ]]; then
            VERSION="0.0.0"
          fi
          echo "VERSION=$VERSION" >> $GITHUB_ENV
          echo "Set version to: $VERSION"

      - name: Create deployment package
        run: |
          mkdir -p screenpipe-${{ env.VERSION }}-${{ matrix.target }}/bin
          cp target/${{ matrix.target }}/release/screenpipe screenpipe-${{ env.VERSION }}-${{ matrix.target }}/bin/
          tar -czf screenpipe-${{ env.VERSION }}-${{ matrix.target }}.tar.gz -C screenpipe-${{ env.VERSION }}-${{ matrix.target }} .

      - name: Calculate SHA256
        run: |
          echo "LINUX_SHA256_${{ matrix.target }}=$(sha256sum screenpipe-*.tar.gz | cut -d ' ' -f 1)" >> $GITHUB_ENV

      - name: Upload Artifact
        uses: actions/upload-artifact@v4
        with:
          name: screenpipe-linux-${{ matrix.target }}
          path: screenpipe-*.tar.gz

  release:
    runs-on: ubuntu-latest
    needs: [build-macos, build-windows, build-linux]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set version
        run: |
          if [[ $GITHUB_REF == refs/tags/* ]]; then
            VERSION=${GITHUB_REF#refs/tags/v}
          else
            VERSION=$(git ls-remote --tags --refs --sort="version:refname" | tail -n1 | sed 's/.*\///' | sed 's/^v//')
          fi
          if [[ -z "$VERSION" ]]; then
            VERSION="0.0.0"
          fi
          echo "VERSION=$VERSION" >> $GITHUB_ENV
          echo "Set version to: $VERSION"

      - name: Download Artifacts
        uses: actions/download-artifact@v4
        with:
          path: artifacts

      - name: List artifacts
        run: ls -R artifacts

      - name: Create or update Release
        env:
          GH_TOKEN: ${{ secrets.PAT }}
        run: |
          gh release create v${{ env.VERSION }} --title ${{ env.VERSION }} --generate-notes || true
          for file in artifacts/screenpipe-macos-*/screenpipe-*.tar.gz; do
            if [ -f "$file" ]; then
              gh release upload v${{ env.VERSION }} "$file" --clobber
            else
              echo "Warning: $file not found"
            fi
          done
          for file in artifacts/screenpipe-windows/screenpipe-*.zip; do
            if [ -f "$file" ]; then
              gh release upload v${{ env.VERSION }} "$file" --clobber
            else
              echo "Warning: $file not found"
            fi
          done
          for file in artifacts/screenpipe-linux-*/screenpipe-*.tar.gz; do
            if [ -f "$file" ]; then
              gh release upload v${{ env.VERSION }} "$file" --clobber
            else
              echo "Warning: $file not found"
            fi
          done
