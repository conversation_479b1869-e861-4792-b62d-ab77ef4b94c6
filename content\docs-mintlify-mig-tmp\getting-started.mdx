---
title: "Getting Started"
icon: "rocket"
---

### choose your installation method

| method      | best for                          | pros                           | cons                                 |
| ----------- | --------------------------------- | ------------------------------ | ------------------------------------ |
| app         | personal users, quick setup       | easy gui, no terminal required | less customizable, automatic updates |
| cli         | developers, automation, scripting | fast, scriptable               | requires terminal familiarity        |

### install desktop app

- download the [desktop app](https://screenpi.pe) and follow the installation instructions.

### install cli

<Tabs>
  <Tab title="macos/linux">
    ```bash 
    curl -fsSL get.screenpi.pe/cli | sh
    screenpipe
    ```
  </Tab>
  <Tab title="windows">
    ```powershell 
    iwr get.screenpi.pe/cli.ps1 | iex
    screenpipe.exe
    ```
  </Tab>
</Tabs>

### verify installation

- run `screenpipe` or `screenpipe.exe` to verify successful installation.

### query data with javascript sdk

then query the data using our javaScript sdk:

```typescript 
import { pipe } from "@screenpipe/js";

async function queryScreenpipe() {
  // get content from last 5 minutes
  const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000).toISOString();

  const results = await pipe.queryScreenpipe({
    startTime: fiveMinutesAgo,
    limit: 10,
    contentType: "all", // can be "ocr", "audio", or "all"
  });

  if (!results) {
    console.log("no results found or error occurred");
    return;
  }

  console.log(`found ${results.pagination.total} items`);

  // process each result
  for (const item of results.data) {
    if (item.type === "OCR") {
      console.log(`OCR: ${JSON.stringify(item.content)}`);
    } else if (item.type === "Audio") {
      console.log(`transcript: ${JSON.stringify(item.content)}`);
    } 
  }
}

queryScreenpipe().catch(console.error);
```

now download the [desktop app](https://screenpi.pe) and use pipes (plugins) to add more features\!

### connect to AI providers

screenpipe can connect to various AI providers to process your data. here's how to set up popular local AI providers:

<Tabs>
  <Tab title="ollama">
    1. install ollama from [ollama.ai](https://ollama.ai) and run your preferred model

    ```bash 
    # start ollama with your preferred model
    ollama run phi4:14b-q4_K_M
    ```

    1. then configure screenpipe to use ollama in your settings with model phi4:14b-q4_K_M

    that's it\! screenpipe will now use ollama for AI like search, rewind, and more. you can change the model in settings.
  </Tab>
  <Tab title="LMStudio">
    to use LMStudio:

    1. download and install [LMStudio](https://lmstudio.ai)
    2. select your preferred model
    3. start the local server

    <Frame>
      <img
        src="public/lmstudio2.png"
        alt="Select model in LMStudio"
        width={800}
        height={450}
        className="rounded-lg"
      />
    </Frame>

    <Frame>
      <img
        src="public/lmstudio3.png"
        alt="Start server in LMStudio"
        width={800}
        height={450}
        className="rounded-lg"
      />
    </Frame>

    <Frame> 
      <img
        src="public/lmstudio1.png"
        alt="LMStudio setup"
        width={800}
        height={450}
        className="rounded-lg"
      />
    </Frame>
  </Tab>
</Tabs>

verify your ai provider using any pipe in the store\!

### for developers

if you're interested in building from source or contributing to screenpipe, please check our [contributing guide](https://github.com/mediar-ai/screenpipe/blob/main/CONTRIBUTING.md).

### for businesses

some of our customers use screenpipe in the following ways:

- have existing screen recording software and want enterprise screen search engine
- want to integrate team's scale meeting transcriptions
- want to extract knowledge from enterprise-scale screens
- running the CLI on their customer's computer
- running the app on their customer's computer
- embedding the library or CLI in their own software
- running the CLI in the cloud and forward the video/audio through SSH
- using our Microsoft Remote Desktop / SSH integration

[book a call to discuss your business needs](https://cal.com/louis030195/screenpipe-for-businesses)
