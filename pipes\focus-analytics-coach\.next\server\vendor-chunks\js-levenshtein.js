"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/js-levenshtein";
exports.ids = ["vendor-chunks/js-levenshtein"];
exports.modules = {

/***/ "(ssr)/./node_modules/js-levenshtein/index.js":
/*!**********************************************!*\
  !*** ./node_modules/js-levenshtein/index.js ***!
  \**********************************************/
/***/ ((module) => {

eval("\nmodule.exports = (function()\n{\n  function _min(d0, d1, d2, bx, ay)\n  {\n    return d0 < d1 || d2 < d1\n        ? d0 > d2\n            ? d2 + 1\n            : d0 + 1\n        : bx === ay\n            ? d1\n            : d1 + 1;\n  }\n\n  return function(a, b)\n  {\n    if (a === b) {\n      return 0;\n    }\n\n    if (a.length > b.length) {\n      var tmp = a;\n      a = b;\n      b = tmp;\n    }\n\n    var la = a.length;\n    var lb = b.length;\n\n    while (la > 0 && (a.charCodeAt(la - 1) === b.charCodeAt(lb - 1))) {\n      la--;\n      lb--;\n    }\n\n    var offset = 0;\n\n    while (offset < la && (a.charCodeAt(offset) === b.charCodeAt(offset))) {\n      offset++;\n    }\n\n    la -= offset;\n    lb -= offset;\n\n    if (la === 0 || lb < 3) {\n      return lb;\n    }\n\n    var x = 0;\n    var y;\n    var d0;\n    var d1;\n    var d2;\n    var d3;\n    var dd;\n    var dy;\n    var ay;\n    var bx0;\n    var bx1;\n    var bx2;\n    var bx3;\n\n    var vector = [];\n\n    for (y = 0; y < la; y++) {\n      vector.push(y + 1);\n      vector.push(a.charCodeAt(offset + y));\n    }\n\n    var len = vector.length - 1;\n\n    for (; x < lb - 3;) {\n      bx0 = b.charCodeAt(offset + (d0 = x));\n      bx1 = b.charCodeAt(offset + (d1 = x + 1));\n      bx2 = b.charCodeAt(offset + (d2 = x + 2));\n      bx3 = b.charCodeAt(offset + (d3 = x + 3));\n      dd = (x += 4);\n      for (y = 0; y < len; y += 2) {\n        dy = vector[y];\n        ay = vector[y + 1];\n        d0 = _min(dy, d0, d1, bx0, ay);\n        d1 = _min(d0, d1, d2, bx1, ay);\n        d2 = _min(d1, d2, d3, bx2, ay);\n        dd = _min(d2, d3, dd, bx3, ay);\n        vector[y] = dd;\n        d3 = d2;\n        d2 = d1;\n        d1 = d0;\n        d0 = dy;\n      }\n    }\n\n    for (; x < lb;) {\n      bx0 = b.charCodeAt(offset + (d0 = x));\n      dd = ++x;\n      for (y = 0; y < len; y += 2) {\n        dy = vector[y];\n        vector[y] = dd = _min(dy, d0, dd, bx0, vector[y + 1]);\n        d0 = dy;\n      }\n    }\n\n    return dd;\n  };\n})();\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/js-levenshtein/index.js\n");

/***/ })

};
;