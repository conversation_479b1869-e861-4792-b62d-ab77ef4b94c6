---
title: "Terminator: Getting Started"
---

## ⚡ hello world — quick start

> skip the boilerplate. this is the fastest way to feel the magic.

first, clone the terminator repository:

```bash
git clone https://github.com/mediar-ai/terminator
cd terminator 
```

### 🐍 python

```bash
pip install terminator-py
```

```python
import terminator
desktop = terminator.Desktop()
desktop.open_application('calc')
seven = desktop.locator('name:Seven')
seven.click()
```

### 🟦 typescript / node.js

```bash
bun i terminator.js # or npm, pnpm, yarn
```

```typescript
const { Desktop } = require('terminator.js');
const desktop = new Desktop();
await desktop.openApplication('notepad')
await desktop.locator('name:Edit').typeText('hello world')
```

## more examples

### calculator automation (python)

```python
import terminator

# create desktop instance
desktop = terminator.Desktop()

# open calculator
desktop.open_application('calc')

# interact with calculator buttons
seven = desktop.locator('name:Seven')
plus = desktop.locator('name:Plus')
equals = desktop.locator('name:Equals')

# perform calculation: 7 + 7 = 14
seven.click()
plus.click()
seven.click()
equals.click()
```

### notepad automation (typescript)

```typescript
const { Desktop } = require('terminator.js');

async function automateNotepad() {
  const desktop = new Desktop();
  
  // open notepad
  await desktop.openApplication('notepad');
  
  // find text editor and type
  const editor = desktop.locator('name:Edit');
  await editor.typeText('hello from terminator!\nthis is automated text input.');
  
  // get the text back
  const content = await editor.getText();
  console.log('editor content:', content);
}

automateNotepad();
```

## next steps

- explore the sdk references ([typescript](/terminator/js-sdk-reference), [python](/terminator/python-sdk-reference)) to understand all available commands.
- check out more [examples](https://github.com/mediar-ai/terminator/tree/main/examples) to use terminator.
- join our [discord](https://discord.gg/dU9EBuw7Uq) for help and discussions.
- start building your own automation scripts\!
