---
name: pull request
about: submit changes to the project
title: "[pr] "
labels: ''
assignees: ''

---

## description

brief description of the changes in this pr.

related issue: #

## how to test

add a few steps to test the pr in the most time efficient way.

1. 
2. 
3. 

if relevant add screenshots or screen captures to prove that this PR works to save us time (check [Cap](https://cap.so)).

if you are not the author of this PR and you see it and you think it can take more than 30 mins for maintainers to review, we will tip you between $20 and $200 for you to review and test it for us.
