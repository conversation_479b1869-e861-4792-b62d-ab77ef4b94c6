

very raw v0 of screenpipe operator usage example (which is experimental itself)

allow you to click, type, scrap, control your computer using a playwright API like, using accessibility API instead of pixels like everyone is doing (eg openai, anthropic, all trash, dinosaur people :D)


https://cap.so/s/4kpvfe76hw4jd6q


backend code:

https://github.com/mediar-ai/screenpipe/tree/main/screenpipe-core/src/operator
