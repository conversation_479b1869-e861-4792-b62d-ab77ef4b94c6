Based on your commits, here is the changelog for the new screenpipe update:

### **New Features:**
- **Improved store pipes UX:** Enhanced user experience for managing stored pipes.

### **Improvements:**
- **Better handling of switch with screenpipe cloud in settings:** Improved the settings configuration for switching options related to screenpipe cloud.
- **Moved auto start to general settings:** Relocated the auto start option for easier access.
- **Improved CLI command dialog:** Enhanced the command line interface dialog for better usability.

### **Fixes:**
- **Fixed layout shifting:** Resolved issues with layout shifting in the application.
- **Added skeleton card when loading:** Implemented a skeleton card to improve the loading experience.

#### **Full Changelog:** [6f3f8..7f589](https://github.com/mediar-ai/screenpipe/compare/6f3f8..7f589)

