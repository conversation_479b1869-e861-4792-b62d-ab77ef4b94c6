### **New Features:**
- **Added support for MKL acceleration:** Enhanced performance through MKL acceleration on macOS, Windows, and Linux.
- **Added endpoint for database:** Implemented an additional endpoint for smoother database integration.

### **Improvements:**
- **Updated UI search feature:** Improved search functionality to handle missing audio paths efficiently.
- **Refactored code for clarity:** Refactored codebase for better organization and maintainability.

### **Fixes:**
- **Resolved JSON parsing issue:** Fixed endpoint JSON parsing to ensure accurate data processing.
- **Fixed CSS and UI issues:** Addressed various CSS and UI bugs for a smoother user experience.
- **Fixed JOIN logic:** Corrected JOIN logic to improve data query efficiency.
