self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"00689101726a740ae1b809750642788b6cced5a9ee\": {\n      \"workers\": {\n        \"app/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cprojects%5C%5Cscreenpipe%5C%5Cscreenpipe%5C%5Cpipes%5C%5Cfocus-analytics-coach%5C%5Clib%5C%5Cactions%5C%5Cget-screenpipe-app-settings.ts%22%2C%5B%7B%22id%22%3A%2200689101726a740ae1b809750642788b6cced5a9ee%22%2C%22exportedName%22%3A%22getScreenpipeAppSettings%22%7D%2C%7B%22id%22%3A%2240d1cc6778dd0e335ea346a2af0964e84396a12af9%22%2C%22exportedName%22%3A%22updateScreenpipeAppSettings%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/page\": \"action-browser\"\n      }\n    },\n    \"40d1cc6778dd0e335ea346a2af0964e84396a12af9\": {\n      \"workers\": {\n        \"app/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cprojects%5C%5Cscreenpipe%5C%5Cscreenpipe%5C%5Cpipes%5C%5Cfocus-analytics-coach%5C%5Clib%5C%5Cactions%5C%5Cget-screenpipe-app-settings.ts%22%2C%5B%7B%22id%22%3A%2200689101726a740ae1b809750642788b6cced5a9ee%22%2C%22exportedName%22%3A%22getScreenpipeAppSettings%22%7D%2C%7B%22id%22%3A%2240d1cc6778dd0e335ea346a2af0964e84396a12af9%22%2C%22exportedName%22%3A%22updateScreenpipeAppSettings%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/page\": \"action-browser\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"process.env.NEXT_SERVER_ACTIONS_ENCRYPTION_KEY\"\n}"