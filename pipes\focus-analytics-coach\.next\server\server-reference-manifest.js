self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"00dd940e07828289ffa35be4849402c3efc5e5804e\": {\n      \"workers\": {\n        \"app/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cprojects%5C%5Cscreenpipe%5C%5Cscreenpipe%5C%5Cpipes%5C%5Cfocus-analytics-coach%5C%5Clib%5C%5Cactions%5C%5Cget-screenpipe-app-settings.ts%22%2C%5B%7B%22id%22%3A%2200dd940e07828289ffa35be4849402c3efc5e5804e%22%2C%22exportedName%22%3A%22getScreenpipeAppSettings%22%7D%2C%7B%22id%22%3A%2240369bb696b313d1bf35d518802188672f34858d64%22%2C%22exportedName%22%3A%22updateScreenpipeAppSettings%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/page\": \"action-browser\"\n      }\n    },\n    \"40369bb696b313d1bf35d518802188672f34858d64\": {\n      \"workers\": {\n        \"app/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cprojects%5C%5Cscreenpipe%5C%5Cscreenpipe%5C%5Cpipes%5C%5Cfocus-analytics-coach%5C%5Clib%5C%5Cactions%5C%5Cget-screenpipe-app-settings.ts%22%2C%5B%7B%22id%22%3A%2200dd940e07828289ffa35be4849402c3efc5e5804e%22%2C%22exportedName%22%3A%22getScreenpipeAppSettings%22%7D%2C%7B%22id%22%3A%2240369bb696b313d1bf35d518802188672f34858d64%22%2C%22exportedName%22%3A%22updateScreenpipeAppSettings%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/page\": \"action-browser\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"process.env.NEXT_SERVER_ACTIONS_ENCRYPTION_KEY\"\n}"