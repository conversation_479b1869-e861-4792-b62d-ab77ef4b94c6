# Focus Analytics & Productivity Coach

A comprehensive productivity tracking and coaching system that analyzes your screen activity to provide actionable insights and real-time guidance. Built as a Screenpipe plugin, it leverages AI to help you optimize your focus, reduce distractions, and achieve your productivity goals.

## 🚀 Features

### Deep Work Analysis
- **Focus Sessions Detection**: Automatically identifies when you're in deep work mode vs. distracted
- **Context Switching Tracking**: Measures how often you switch between applications/websites
- **Attention Span Metrics**: Tracks how long you stay focused on specific tasks
- **Distraction Heatmaps**: Visual representation of when and where distractions occur

### Real-time Coaching
- **Smart Notifications**: Gentle nudges when you've been distracted for too long
- **Focus Mode Activation**: Automatically suggests entering focus mode based on patterns
- **Break Reminders**: Intelligent break suggestions based on your work intensity
- **Goal Progress**: Real-time updates on daily/weekly productivity goals

### Intelligent Categorization
- **Automatic Task Detection**: Uses OCR to identify what you're working on (emails, coding, writing, etc.)
- **Project Time Tracking**: Automatically categorizes time spent on different projects
- **Meeting Analysis**: Detects and analyzes meeting participation and effectiveness
- **Learning vs. Doing**: Distinguishes between learning activities and productive work

### Advanced Analytics Dashboard
- **Productivity Patterns**: Weekly/monthly trends and insights
- **Energy Level Correlation**: Matches productivity with time of day
- **App/Website Efficiency Scoring**: Rates tools based on your productive output
- **Comparative Analysis**: Benchmarks against your historical performance

### AI-Powered Insights
- **Personalized Recommendations**: Suggests optimal work schedules and environments
- **Habit Formation**: Identifies and reinforces positive productivity patterns
- **Bottleneck Detection**: Finds what's slowing down your workflow
- **Predictive Scheduling**: Suggests best times for different types of work

## 🛠️ Technical Implementation

### Architecture
- **Frontend**: Next.js with React and TypeScript
- **Analytics Engine**: Custom algorithms for focus detection and pattern recognition
- **AI Analysis**: TensorFlow.js for client-side machine learning
- **Data Visualization**: Recharts for interactive charts and graphs
- **Real-time Processing**: WebSocket connections for live coaching
- **Privacy-First**: All analysis happens locally on your device

### Key Components
- `FocusAnalyticsEngine`: Core analytics processing
- `AIAnalysisEngine`: OCR-based task detection and categorization
- `RealTimeCoachingSystem`: Live monitoring and notifications
- `PatternRecognitionEngine`: ML-powered pattern detection
- `ProductivityDashboard`: Interactive analytics interface

## 📦 Installation

### Quick Start
1. **Prerequisites**: Ensure you have Screenpipe installed and running
2. **Create the plugin**:
   ```bash
   cd path/to/screenpipe/screenpipe
   bunx --bun @screenpipe/dev@latest pipe create
   # Name: focus-analytics-coach
   # Path: pipes/focus-analytics-coach
   ```
3. **Install dependencies**:
   ```bash
   cd pipes/focus-analytics-coach
   bun install
   bun add recharts date-fns fuse.js ml-matrix @tensorflow/tfjs framer-motion react-hook-form sonner lodash @types/lodash
   ```
4. **Start development server**:
   ```bash
   bun run dev
   ```
5. **Build for production**:
   ```bash
   bun run build
   ```

📋 **Detailed Installation Guide**: See [INSTALLATION.md](./INSTALLATION.md) for complete setup instructions.

## 🎯 Usage

### Getting Started
1. **Launch the Plugin**: Navigate to `http://localhost:3000` after starting the dev server
2. **Enable Monitoring**: Click "Start Monitoring" to begin real-time tracking
3. **Explore Demo Data**: The plugin shows demo data when Screenpipe isn't running
4. **Configure Settings**: Adjust thresholds and preferences in the Settings tab
5. **Set Goals**: Define productivity targets in the Goals tab
6. **Review Analytics**: Monitor your productivity patterns in the Overview tab

### Dashboard Features
- **📊 Overview**: Key metrics, hourly patterns, and productivity insights
- **⏱️ Sessions**: Detailed focus session analysis and trends
- **🧠 Coaching**: Real-time notifications and AI-powered suggestions
- **🎯 Goals**: Progress tracking with visual indicators
- **📈 Patterns**: AI-detected productivity patterns and recommendations
- **⚙️ Settings**: Customizable analytics configuration

### Current Implementation Status
✅ **Fully Implemented**:
- Core analytics engine with focus session detection
- Real-time coaching system with smart notifications
- AI-powered task categorization using OCR analysis
- Interactive dashboard with comprehensive visualizations
- Goal tracking and progress monitoring
- Pattern recognition and predictive scheduling
- Demo data system for testing without Screenpipe
- API endpoints for analytics processing and daily summaries

🔄 **Demo Mode**: When Screenpipe isn't running, the plugin displays realistic demo data to showcase all features.

📊 **Key Metrics**:
- **Focus Score**: Overall productivity rating (0-100%)
- **Deep Work Sessions**: Extended periods of concentrated work (45+ minutes)
- **Context Switches**: App/window changes indicating potential distractions
- **Attention Span**: Average duration of uninterrupted focus periods

## ⚙️ Configuration

### Analytics Settings
```typescript
{
  focusThreshold: 15,        // Minimum minutes for focus session
  distractionThreshold: 30,  // Seconds before considering distraction
  contextSwitchWindow: 5,    // Seconds to group rapid switches
  breakReminderInterval: 60, // Minutes between break reminders
  deepWorkMinimum: 45,       // Minimum minutes for deep work
  enableRealTimeCoaching: true,
  enableBreakReminders: true,
  enableGoalTracking: true
}
```

### Productivity Categories
The system automatically categorizes activities into:
- **Deep Work**: Coding, design, writing
- **Communication**: Email, chat, meetings
- **Research**: Web browsing, documentation
- **Creative**: Design tools, content creation
- **Administrative**: Spreadsheets, planning
- **Learning**: Tutorials, courses, reading
- **Entertainment**: Videos, games, social media
- **Social**: Social networks, messaging

## 🔒 Privacy & Security

- **Local Processing**: All analytics run on your device
- **No Data Upload**: Your activity data never leaves your computer
- **Secure Storage**: Encrypted local storage for sensitive information
- **Minimal Permissions**: Only accesses necessary screen/audio data
- **Transparent**: Open-source code for full transparency

## ✅ Implementation Status

### Completed Features
- **🏗️ Core Architecture**: Full analytics engine with TypeScript implementation
- **📊 Data Processing**: Focus session detection, context switching analysis, attention span measurement
- **🤖 AI Analysis**: OCR-based task detection, intelligent categorization, pattern recognition
- **🎯 Real-time Coaching**: Smart notifications, break reminders, goal tracking
- **📈 Dashboard**: Interactive analytics interface with charts and visualizations
- **⚙️ Configuration**: Customizable settings and preferences
- **🔄 Demo Mode**: Realistic demo data for testing without Screenpipe
- **🛠️ API Endpoints**: Analytics processing and daily summary generation
- **📱 Responsive UI**: Modern, clean interface that works across devices

### Testing Results
- ✅ **Build Success**: Compiles without errors using TypeScript and Next.js
- ✅ **Development Server**: Runs successfully on `http://localhost:3000`
- ✅ **API Endpoints**: Both analytics endpoints respond correctly
- ✅ **Demo Data**: Realistic productivity data displays properly
- ✅ **UI Components**: All dashboard tabs and features are functional
- ✅ **TensorFlow.js**: ML library loads and initializes correctly
- ✅ **Real-time Features**: Coaching system and notifications work as expected

### Ready for Production
The plugin is fully functional and ready for:
- Integration with live Screenpipe data
- Real-world productivity tracking
- Deployment in production environments
- Extension with additional features

## 🚀 Why This Plugin is Valuable

### Universal Need
Everyone wants to be more productive, making this plugin relevant to all Screenpipe users.

### Unique Data Access
Only Screenpipe can provide this level of detailed activity analysis with screen recordings and audio transcriptions.

### Actionable Insights
Goes beyond simple time tracking to provide AI-powered coaching and recommendations.

### Privacy-First Approach
All analysis happens locally, ensuring your sensitive work data remains private.

### Monetization Potential
High-value productivity insights justify subscription pricing for advanced features.

## 🤝 Contributing

Contributions are welcome! Please read our contributing guidelines and:

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Built on the powerful Screenpipe platform
- Inspired by productivity research and cognitive science
- Uses open-source libraries for ML and data visualization

---

**Transform your productivity with AI-powered insights. Start your journey to better focus today!**

1. view rendered components in their intended state
2. inspect the raw output from api calls
3. study the complete component code
4. examine the ai prompts and context used to generate components

## component structure

each playground card includes:
- component title and collapsible interface
- tabs for different views (rendered output, raw output, code, ai prompt)
- copy functionality for sharing prompts and context

## getting started

1. install this pipe from UI and play with it
2. follow docs to create your pipe (it will create this app) (https://docs.screenpi.pe/plugins)
3. modify code from ready-to-use-examples directory

