Based on your commits, here is the updated changelog for the new Screenpipe update:

### **New Features:**
- **Implemented new pipe system:** Launched a comprehensive new pipe system that includes automatic payouts to developers, robust statistics, and automatic pipe publishing.
- **Introduced Plugin Store:** Added a new store for plugins, enhancing user customization and extensibility.

### **Improvements:**
- **Rewind AI panel updates:** Refactored the UI to include full date information in the rewind AI panel for improved clarity.

### **Fixes:**
- **Fixed rewind timeline recursion bug:** Resolved an infinite recursion issue in the rewind timeline functionality.
- **Updated AI & settings functionality:** Addressed issues related to the search and Reddit pipe, ensuring smoother operations.

#### **Full Changelog:** [8afe7..ed435](https://github.com/mediar-ai/screenpipe/compare/8afe7..ed435)

