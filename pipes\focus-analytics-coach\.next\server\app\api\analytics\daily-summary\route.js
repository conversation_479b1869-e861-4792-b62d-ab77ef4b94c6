/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/analytics/daily-summary/route";
exports.ids = ["app/api/analytics/daily-summary/route"];
exports.modules = {

/***/ "(rsc)/./app/api/analytics/daily-summary/route.ts":
/*!**************************************************!*\
  !*** ./app/api/analytics/daily-summary/route.ts ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_analytics_engine__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/analytics-engine */ \"(rsc)/./lib/analytics-engine.ts\");\n/* harmony import */ var _lib_ai_analysis__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/ai-analysis */ \"(rsc)/./lib/ai-analysis.ts\");\n/* harmony import */ var _lib_pattern_recognition__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/pattern-recognition */ \"(rsc)/./lib/pattern-recognition.ts\");\n/* harmony import */ var _barrel_optimize_names_endOfDay_format_startOfDay_subDays_date_fns__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=endOfDay,format,startOfDay,subDays!=!date-fns */ \"(rsc)/./node_modules/date-fns/subDays.js\");\n/* harmony import */ var _barrel_optimize_names_endOfDay_format_startOfDay_subDays_date_fns__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=endOfDay,format,startOfDay,subDays!=!date-fns */ \"(rsc)/./node_modules/date-fns/startOfDay.js\");\n/* harmony import */ var _barrel_optimize_names_endOfDay_format_startOfDay_subDays_date_fns__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=endOfDay,format,startOfDay,subDays!=!date-fns */ \"(rsc)/./node_modules/date-fns/endOfDay.js\");\n/* harmony import */ var _barrel_optimize_names_endOfDay_format_startOfDay_subDays_date_fns__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=endOfDay,format,startOfDay,subDays!=!date-fns */ \"(rsc)/./node_modules/date-fns/format.js\");\n\n\n\n\n\nconst DEFAULT_CONFIG = {\n    focusThreshold: 15,\n    distractionThreshold: 30,\n    contextSwitchWindow: 5,\n    breakReminderInterval: 60,\n    deepWorkMinimum: 45,\n    productivityCategories: [],\n    enableRealTimeCoaching: true,\n    enableBreakReminders: true,\n    enableGoalTracking: true\n};\nasync function POST(request) {\n    try {\n        console.log(\"Generating daily productivity summary...\");\n        // Initialize analytics engines\n        const analyticsEngine = new _lib_analytics_engine__WEBPACK_IMPORTED_MODULE_1__.FocusAnalyticsEngine(DEFAULT_CONFIG);\n        const aiAnalysis = new _lib_ai_analysis__WEBPACK_IMPORTED_MODULE_2__.AIAnalysisEngine();\n        const patternEngine = new _lib_pattern_recognition__WEBPACK_IMPORTED_MODULE_3__.PatternRecognitionEngine();\n        // Define time range for yesterday's data\n        const yesterday = (0,_barrel_optimize_names_endOfDay_format_startOfDay_subDays_date_fns__WEBPACK_IMPORTED_MODULE_4__.subDays)(new Date(), 1);\n        const timeRange = {\n            start: (0,_barrel_optimize_names_endOfDay_format_startOfDay_subDays_date_fns__WEBPACK_IMPORTED_MODULE_5__.startOfDay)(yesterday),\n            end: (0,_barrel_optimize_names_endOfDay_format_startOfDay_subDays_date_fns__WEBPACK_IMPORTED_MODULE_6__.endOfDay)(yesterday)\n        };\n        // Analyze yesterday's activity\n        const analysisResult = await analyticsEngine.analyzeActivity(timeRange);\n        const taskAnalysis = await aiAnalysis.analyzeTaskContent(timeRange);\n        const patterns = await patternEngine.analyzeProductivityPatterns(analysisResult.focusSessions, timeRange);\n        // Generate summary insights\n        const summary = generateDailySummary(analysisResult.metrics, analysisResult.focusSessions, taskAnalysis.insights, patterns);\n        console.log(\"Daily summary generated successfully\");\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: \"Daily summary generated\",\n            date: (0,_barrel_optimize_names_endOfDay_format_startOfDay_subDays_date_fns__WEBPACK_IMPORTED_MODULE_7__.format)(yesterday, 'yyyy-MM-dd'),\n            summary\n        });\n    } catch (error) {\n        console.error(\"Error generating daily summary:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Failed to generate daily summary\",\n            details: error instanceof Error ? error.message : \"Unknown error\"\n        }, {\n            status: 500\n        });\n    }\n}\nfunction generateDailySummary(metrics, sessions, insights, patterns) {\n    const totalFocusHours = Math.round(metrics.totalFocusTime / 60 * 10) / 10;\n    const averageSessionLength = Math.round(metrics.averageSessionLength);\n    // Generate performance rating\n    let performanceRating = \"Good\";\n    if (metrics.focusScore >= 80) performanceRating = \"Excellent\";\n    else if (metrics.focusScore >= 60) performanceRating = \"Good\";\n    else if (metrics.focusScore >= 40) performanceRating = \"Fair\";\n    else performanceRating = \"Needs Improvement\";\n    // Generate key achievements\n    const achievements = [];\n    if (metrics.deepWorkSessions >= 3) {\n        achievements.push(\"🎯 Achieved multiple deep work sessions\");\n    }\n    if (totalFocusHours >= 4) {\n        achievements.push(\"⏰ Maintained 4+ hours of focused work\");\n    }\n    if (metrics.focusScore >= 75) {\n        achievements.push(\"🧠 High focus score maintained\");\n    }\n    if (metrics.contextSwitches <= 10) {\n        achievements.push(\"🎯 Minimal context switching\");\n    }\n    // Generate recommendations\n    const recommendations = [];\n    if (metrics.focusScore < 60) {\n        recommendations.push(\"Consider reducing distractions during work sessions\");\n    }\n    if (metrics.contextSwitches > 20) {\n        recommendations.push(\"Try batching similar tasks to reduce context switching\");\n    }\n    if (averageSessionLength < 30) {\n        recommendations.push(\"Aim for longer focus sessions (45+ minutes)\");\n    }\n    if (metrics.deepWorkSessions < 2) {\n        recommendations.push(\"Schedule dedicated deep work blocks\");\n    }\n    return {\n        overview: {\n            performanceRating,\n            totalFocusTime: `${totalFocusHours} hours`,\n            focusScore: `${Math.round(metrics.focusScore)}%`,\n            deepWorkSessions: metrics.deepWorkSessions,\n            averageSessionLength: `${averageSessionLength} minutes`\n        },\n        achievements,\n        recommendations,\n        insights: insights.slice(0, 3),\n        patterns: patterns.slice(0, 2).map((p)=>({\n                name: p.name,\n                description: p.description,\n                confidence: `${Math.round(p.confidence)}%`\n            })),\n        topApps: metrics.topProductiveApps.slice(0, 3).map((app)=>({\n                name: app.appName,\n                focusTime: `${Math.round(app.focusTime)} min`,\n                score: `${Math.round(app.productivityScore)}%`\n            }))\n    };\n}\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const date = searchParams.get('date') || (0,_barrel_optimize_names_endOfDay_format_startOfDay_subDays_date_fns__WEBPACK_IMPORTED_MODULE_7__.format)((0,_barrel_optimize_names_endOfDay_format_startOfDay_subDays_date_fns__WEBPACK_IMPORTED_MODULE_4__.subDays)(new Date(), 1), 'yyyy-MM-dd');\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: \"Daily summary endpoint is active\",\n            availableDate: date,\n            note: \"Use POST to generate a new summary\"\n        });\n    } catch (error) {\n        console.error(\"Error getting daily summary status:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Failed to get daily summary status\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/analytics/daily-summary/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/ai-analysis.ts":
/*!****************************!*\
  !*** ./lib/ai-analysis.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AIAnalysisEngine: () => (/* binding */ AIAnalysisEngine)\n/* harmony export */ });\n/* harmony import */ var _screenpipe_browser__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @screenpipe/browser */ \"(rsc)/./node_modules/@screenpipe/browser/dist/index.js\");\n/* harmony import */ var fuse_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fuse.js */ \"(rsc)/./node_modules/fuse.js/dist/fuse.mjs\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/uuid/dist/esm-node/v4.js\");\n\n\n\nclass AIAnalysisEngine {\n    constructor(){\n        // Initialize fuzzy search for task categorization\n        this.fuse = new fuse_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]([], {\n            keys: [\n                'text',\n                'appName',\n                'windowName'\n            ],\n            threshold: 0.3\n        });\n    }\n    /**\n   * Analyze OCR content to detect specific tasks and activities\n   */ async analyzeTaskContent(timeRange) {\n        try {\n            // Query OCR data for task detection\n            const results = await _screenpipe_browser__WEBPACK_IMPORTED_MODULE_0__.pipe.queryScreenpipe({\n                startTime: timeRange.start.toISOString(),\n                endTime: timeRange.end.toISOString(),\n                contentType: \"ocr\",\n                limit: 500,\n                includeFrames: false\n            });\n            if (!results?.data) {\n                return {\n                    detectedTasks: [],\n                    insights: []\n                };\n            }\n            const detectedTasks = [];\n            const insights = [];\n            for (const item of results.data){\n                if (item.type === \"OCR\") {\n                    const ocrText = item.content.text || \"\";\n                    const appName = item.content.appName || \"\";\n                    const windowName = item.content.windowName || \"\";\n                    // Analyze OCR content for task detection\n                    const taskAnalysis = this.analyzeOCRForTasks(ocrText, appName, windowName);\n                    if (taskAnalysis.confidence > 0.6) {\n                        detectedTasks.push({\n                            id: (0,uuid__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(),\n                            timestamp: new Date(item.content.timestamp),\n                            taskType: taskAnalysis.taskType,\n                            description: taskAnalysis.description,\n                            confidence: taskAnalysis.confidence,\n                            appName,\n                            windowName,\n                            ocrText: ocrText.substring(0, 200) // Truncate for storage\n                        });\n                    }\n                }\n            }\n            // Generate insights from detected tasks\n            const taskInsights = this.generateTaskInsights(detectedTasks);\n            insights.push(...taskInsights);\n            return {\n                detectedTasks,\n                insights\n            };\n        } catch (error) {\n            console.error(\"Error analyzing task content:\", error);\n            return {\n                detectedTasks: [],\n                insights: []\n            };\n        }\n    }\n    /**\n   * Analyze OCR text to determine task type and extract meaningful information\n   */ analyzeOCRForTasks(ocrText, appName, windowName) {\n        const text = ocrText.toLowerCase();\n        const app = appName.toLowerCase();\n        const window = windowName.toLowerCase();\n        // Define task detection patterns\n        const patterns = {\n            'deep-work': {\n                keywords: [\n                    'function',\n                    'class',\n                    'import',\n                    'export',\n                    'const',\n                    'let',\n                    'var',\n                    'def',\n                    'public',\n                    'private'\n                ],\n                apps: [\n                    'vscode',\n                    'intellij',\n                    'xcode',\n                    'sublime',\n                    'vim'\n                ],\n                confidence: 0.9\n            },\n            'communication': {\n                keywords: [\n                    'message',\n                    'chat',\n                    'email',\n                    'meeting',\n                    'call',\n                    'video',\n                    'send',\n                    'reply'\n                ],\n                apps: [\n                    'slack',\n                    'teams',\n                    'discord',\n                    'zoom',\n                    'mail'\n                ],\n                confidence: 0.8\n            },\n            'research': {\n                keywords: [\n                    'search',\n                    'google',\n                    'stackoverflow',\n                    'documentation',\n                    'tutorial',\n                    'how to'\n                ],\n                apps: [\n                    'chrome',\n                    'firefox',\n                    'safari',\n                    'edge'\n                ],\n                confidence: 0.7\n            },\n            'creative': {\n                keywords: [\n                    'design',\n                    'layer',\n                    'brush',\n                    'color',\n                    'font',\n                    'canvas',\n                    'artboard'\n                ],\n                apps: [\n                    'photoshop',\n                    'illustrator',\n                    'figma',\n                    'sketch'\n                ],\n                confidence: 0.8\n            },\n            'administrative': {\n                keywords: [\n                    'spreadsheet',\n                    'document',\n                    'presentation',\n                    'table',\n                    'chart',\n                    'formula'\n                ],\n                apps: [\n                    'excel',\n                    'word',\n                    'powerpoint',\n                    'sheets',\n                    'docs'\n                ],\n                confidence: 0.7\n            },\n            'learning': {\n                keywords: [\n                    'course',\n                    'lesson',\n                    'tutorial',\n                    'learn',\n                    'study',\n                    'education'\n                ],\n                apps: [\n                    'coursera',\n                    'udemy',\n                    'khan'\n                ],\n                confidence: 0.8\n            },\n            'entertainment': {\n                keywords: [\n                    'watch',\n                    'video',\n                    'movie',\n                    'music',\n                    'game',\n                    'play'\n                ],\n                apps: [\n                    'netflix',\n                    'youtube',\n                    'spotify',\n                    'steam'\n                ],\n                confidence: 0.9\n            },\n            'social': {\n                keywords: [\n                    'post',\n                    'like',\n                    'share',\n                    'comment',\n                    'follow',\n                    'friend'\n                ],\n                apps: [\n                    'facebook',\n                    'twitter',\n                    'instagram',\n                    'linkedin'\n                ],\n                confidence: 0.8\n            }\n        };\n        let bestMatch = {\n            taskType: 'unknown',\n            confidence: 0,\n            description: 'Unknown activity'\n        };\n        // Check each pattern\n        for (const [taskType, pattern] of Object.entries(patterns)){\n            let confidence = 0;\n            let matchedKeywords = [];\n            // Check app match\n            if (pattern.apps.some((appPattern)=>app.includes(appPattern))) {\n                confidence += 0.4;\n            }\n            // Check keyword matches\n            const keywordMatches = pattern.keywords.filter((keyword)=>text.includes(keyword));\n            if (keywordMatches.length > 0) {\n                confidence += keywordMatches.length / pattern.keywords.length * 0.6;\n                matchedKeywords = keywordMatches;\n            }\n            // Apply pattern-specific confidence modifier\n            confidence *= pattern.confidence;\n            if (confidence > bestMatch.confidence) {\n                bestMatch = {\n                    taskType: taskType,\n                    confidence,\n                    description: this.generateTaskDescription(taskType, matchedKeywords, appName)\n                };\n            }\n        }\n        return bestMatch;\n    }\n    /**\n   * Generate human-readable task description\n   */ generateTaskDescription(taskType, keywords, appName) {\n        const descriptions = {\n            'deep-work': `Coding/development work in ${appName}`,\n            'communication': `Communication activity in ${appName}`,\n            'research': `Research and information gathering in ${appName}`,\n            'creative': `Creative/design work in ${appName}`,\n            'administrative': `Administrative tasks in ${appName}`,\n            'learning': `Learning activity in ${appName}`,\n            'entertainment': `Entertainment/leisure in ${appName}`,\n            'social': `Social media activity in ${appName}`,\n            'distraction': `Potentially distracting activity in ${appName}`,\n            'unknown': `Activity in ${appName}`\n        };\n        return descriptions[taskType] || descriptions.unknown;\n    }\n    /**\n   * Generate insights from detected tasks\n   */ generateTaskInsights(tasks) {\n        const insights = [];\n        // Analyze task distribution\n        const taskCounts = tasks.reduce((acc, task)=>{\n            acc[task.taskType] = (acc[task.taskType] || 0) + 1;\n            return acc;\n        }, {});\n        const totalTasks = tasks.length;\n        if (totalTasks === 0) return insights;\n        // Find dominant task type\n        const dominantTask = Object.entries(taskCounts).sort(([, a], [, b])=>b - a)[0];\n        if (dominantTask) {\n            const [taskType, count] = dominantTask;\n            const percentage = Math.round(count / totalTasks * 100);\n            insights.push(`${percentage}% of detected activities were ${taskType.replace('-', ' ')}`);\n        }\n        // Analyze productivity patterns\n        const productiveTasks = tasks.filter((task)=>[\n                'deep-work',\n                'creative',\n                'learning',\n                'research'\n            ].includes(task.taskType));\n        const productivePercentage = Math.round(productiveTasks.length / totalTasks * 100);\n        if (productivePercentage >= 70) {\n            insights.push(\"High productivity detected - great focus on meaningful work!\");\n        } else if (productivePercentage >= 40) {\n            insights.push(\"Moderate productivity - consider reducing distractions\");\n        } else {\n            insights.push(\"Low productivity detected - focus on high-value tasks\");\n        }\n        // Analyze app switching patterns\n        const appSwitches = this.analyzeAppSwitching(tasks);\n        if (appSwitches > 10) {\n            insights.push(`High app switching detected (${appSwitches} switches) - consider batching similar tasks`);\n        }\n        return insights;\n    }\n    /**\n   * Analyze app switching patterns\n   */ analyzeAppSwitching(tasks) {\n        let switches = 0;\n        let lastApp = \"\";\n        for (const task of tasks.sort((a, b)=>a.timestamp.getTime() - b.timestamp.getTime())){\n            if (lastApp && lastApp !== task.appName) {\n                switches++;\n            }\n            lastApp = task.appName;\n        }\n        return switches;\n    }\n    /**\n   * Detect productivity patterns using AI analysis\n   */ async detectProductivityPatterns(sessions) {\n        const patterns = [];\n        // Group sessions by time patterns\n        const timePatterns = this.groupSessionsByTimePattern(sessions);\n        for (const [patternKey, patternSessions] of Object.entries(timePatterns)){\n            if (patternSessions.length < 3) continue; // Need minimum sessions for pattern\n            const averageProductivity = patternSessions.reduce((sum, session)=>sum + session.focusScore, 0) / patternSessions.length;\n            const confidence = Math.min(100, patternSessions.length / sessions.length * 100);\n            const [timeRange, daysOfWeek] = patternKey.split('|');\n            const [startHour, endHour] = timeRange.split('-').map(Number);\n            patterns.push({\n                id: (0,uuid__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(),\n                name: this.generatePatternName(startHour, endHour, daysOfWeek),\n                description: this.generatePatternDescription(averageProductivity, patternSessions.length),\n                timePattern: {\n                    startHour,\n                    endHour,\n                    daysOfWeek: daysOfWeek.split(',').map(Number)\n                },\n                averageProductivity,\n                confidence,\n                recommendations: this.generatePatternRecommendations(averageProductivity, startHour, endHour)\n            });\n        }\n        return patterns.sort((a, b)=>b.confidence - a.confidence);\n    }\n    /**\n   * Group sessions by time patterns (hour ranges and days of week)\n   */ groupSessionsByTimePattern(sessions) {\n        const patterns = {};\n        sessions.forEach((session)=>{\n            const hour = session.startTime.getHours();\n            const dayOfWeek = session.startTime.getDay();\n            // Create 2-hour time blocks\n            const timeBlock = Math.floor(hour / 2) * 2;\n            const timeRange = `${timeBlock}-${timeBlock + 2}`;\n            // Group by weekday vs weekend\n            const dayGroup = dayOfWeek === 0 || dayOfWeek === 6 ? '0,6' : '1,2,3,4,5';\n            const patternKey = `${timeRange}|${dayGroup}`;\n            if (!patterns[patternKey]) {\n                patterns[patternKey] = [];\n            }\n            patterns[patternKey].push(session);\n        });\n        return patterns;\n    }\n    /**\n   * Generate human-readable pattern name\n   */ generatePatternName(startHour, endHour, daysOfWeek) {\n        const timeStr = `${startHour}:00-${endHour}:00`;\n        const dayStr = daysOfWeek === '0,6' ? 'Weekends' : 'Weekdays';\n        return `${dayStr} ${timeStr}`;\n    }\n    /**\n   * Generate pattern description\n   */ generatePatternDescription(averageProductivity, sessionCount) {\n        const productivityLevel = averageProductivity >= 80 ? 'high' : averageProductivity >= 60 ? 'moderate' : 'low';\n        return `${productivityLevel} productivity pattern with ${sessionCount} sessions`;\n    }\n    /**\n   * Generate recommendations based on pattern analysis\n   */ generatePatternRecommendations(productivity, startHour, endHour) {\n        const recommendations = [];\n        if (productivity >= 80) {\n            recommendations.push(\"Excellent productivity window - schedule important tasks here\");\n            recommendations.push(\"Consider extending this time block if possible\");\n        } else if (productivity >= 60) {\n            recommendations.push(\"Good productivity window - optimize environment for better focus\");\n            recommendations.push(\"Minimize distractions during this time\");\n        } else {\n            recommendations.push(\"Low productivity window - consider rescheduling demanding tasks\");\n            recommendations.push(\"Use this time for lighter activities or breaks\");\n        }\n        // Time-specific recommendations\n        if (startHour >= 6 && startHour <= 10) {\n            recommendations.push(\"Morning energy - great for creative and analytical work\");\n        } else if (startHour >= 14 && startHour <= 16) {\n            recommendations.push(\"Post-lunch dip - consider light exercise or break\");\n        } else if (startHour >= 20) {\n            recommendations.push(\"Evening hours - wind down with lighter tasks\");\n        }\n        return recommendations;\n    }\n    /**\n   * Generate break recommendations based on current activity\n   */ async generateBreakRecommendation(currentSession, recentSessions) {\n        if (!currentSession) return null;\n        const sessionDuration = Date.now() - currentSession.startTime.getTime();\n        const sessionMinutes = sessionDuration / (1000 * 60);\n        // Calculate urgency based on session length and recent activity\n        let urgency = 0;\n        let breakType = 'micro';\n        let suggestedDuration = 5;\n        let reason = \"\";\n        if (sessionMinutes >= 120) {\n            urgency = 90;\n            breakType = 'long';\n            suggestedDuration = 15;\n            reason = \"Extended focus session - time for a longer break\";\n        } else if (sessionMinutes >= 60) {\n            urgency = 70;\n            breakType = 'short';\n            suggestedDuration = 10;\n            reason = \"Good focus session - take a short break to recharge\";\n        } else if (sessionMinutes >= 25) {\n            urgency = 50;\n            breakType = 'micro';\n            suggestedDuration = 5;\n            reason = \"Pomodoro break - quick refresh recommended\";\n        }\n        // Adjust based on recent break patterns\n        const recentBreaks = this.analyzeRecentBreaks(recentSessions);\n        if (recentBreaks.timeSinceLastBreak > 180) {\n            urgency = Math.min(100, urgency + 30);\n            reason = \"Long period without breaks - rest is important\";\n        }\n        if (urgency < 40) return null; // No break needed yet\n        return {\n            id: (0,uuid__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(),\n            timestamp: new Date(),\n            reason,\n            type: breakType,\n            suggestedDuration,\n            activities: this.getBreakActivities(breakType),\n            urgency\n        };\n    }\n    /**\n   * Analyze recent break patterns\n   */ analyzeRecentBreaks(sessions) {\n        // This is a simplified implementation\n        // In a real system, you'd track actual breaks\n        const now = Date.now();\n        const lastSession = sessions[sessions.length - 1];\n        const timeSinceLastBreak = lastSession ? (now - lastSession.endTime.getTime()) / (1000 * 60) : 0;\n        return {\n            timeSinceLastBreak,\n            averageBreakInterval: 60 // Default assumption\n        };\n    }\n    /**\n   * Get appropriate break activities based on break type\n   */ getBreakActivities(breakType) {\n        const activities = {\n            micro: [\n                \"Look away from screen (20-20-20 rule)\",\n                \"Deep breathing exercises\",\n                \"Stretch your neck and shoulders\",\n                \"Drink water\"\n            ],\n            short: [\n                \"Walk around the room\",\n                \"Light stretching\",\n                \"Grab a healthy snack\",\n                \"Step outside for fresh air\",\n                \"Chat with a colleague\"\n            ],\n            long: [\n                \"Take a walk outside\",\n                \"Have a proper meal\",\n                \"Do some exercise\",\n                \"Meditate or relax\",\n                \"Call a friend or family member\"\n            ]\n        };\n        return activities[breakType];\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/ai-analysis.ts\n");

/***/ }),

/***/ "(rsc)/./lib/analytics-engine.ts":
/*!*********************************!*\
  !*** ./lib/analytics-engine.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FocusAnalyticsEngine: () => (/* binding */ FocusAnalyticsEngine)\n/* harmony export */ });\n/* harmony import */ var _screenpipe_browser__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @screenpipe/browser */ \"(rsc)/./node_modules/@screenpipe/browser/dist/index.js\");\n/* harmony import */ var _barrel_optimize_names_differenceInMinutes_differenceInSeconds_date_fns__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=differenceInMinutes,differenceInSeconds!=!date-fns */ \"(rsc)/./node_modules/date-fns/differenceInSeconds.js\");\n/* harmony import */ var _barrel_optimize_names_differenceInMinutes_differenceInSeconds_date_fns__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=differenceInMinutes,differenceInSeconds!=!date-fns */ \"(rsc)/./node_modules/date-fns/differenceInMinutes.js\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/uuid/dist/esm-node/v4.js\");\n\n\n\nclass FocusAnalyticsEngine {\n    constructor(config){\n        this.config = config;\n    }\n    /**\n   * Analyze screen activity data to detect focus sessions and context switches\n   */ async analyzeActivity(timeRange) {\n        try {\n            // Query Screenpipe for activity data\n            const results = await _screenpipe_browser__WEBPACK_IMPORTED_MODULE_0__.pipe.queryScreenpipe({\n                startTime: timeRange.start.toISOString(),\n                endTime: timeRange.end.toISOString(),\n                contentType: \"all\",\n                limit: 1000,\n                includeFrames: false\n            });\n            if (!results?.data) {\n                throw new Error(\"No data received from Screenpipe\");\n            }\n            // Process the data to extract focus sessions and context switches\n            const processedData = this.processActivityData(results.data);\n            // Calculate productivity metrics\n            const metrics = this.calculateProductivityMetrics(processedData.focusSessions, processedData.contextSwitches, timeRange.start);\n            return {\n                focusSessions: processedData.focusSessions,\n                contextSwitches: processedData.contextSwitches,\n                metrics\n            };\n        } catch (error) {\n            console.error(\"Error analyzing activity:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Process raw activity data into focus sessions and context switches\n   */ processActivityData(data) {\n        const sessions = [];\n        const switches = [];\n        let currentSession = null;\n        let lastActivity = null;\n        // Sort data by timestamp\n        const sortedData = data.sort((a, b)=>new Date(a.content.timestamp).getTime() - new Date(b.content.timestamp).getTime());\n        for (const item of sortedData){\n            const timestamp = new Date(item.content.timestamp);\n            let appName = \"\";\n            let windowName = \"\";\n            // Extract app and window info based on content type\n            if (item.type === \"OCR\") {\n                appName = item.content.appName || \"\";\n                windowName = item.content.windowName || \"\";\n            } else if (item.type === \"UI\") {\n                appName = item.content.appName || \"\";\n                windowName = item.content.windowName || \"\";\n            }\n            if (!appName) continue;\n            // Detect context switches\n            if (lastActivity && (lastActivity.app !== appName || lastActivity.window !== windowName)) {\n                const switchDuration = (0,_barrel_optimize_names_differenceInMinutes_differenceInSeconds_date_fns__WEBPACK_IMPORTED_MODULE_1__.differenceInSeconds)(timestamp, lastActivity.timestamp);\n                if (switchDuration >= this.config.contextSwitchWindow) {\n                    switches.push({\n                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(),\n                        timestamp,\n                        fromApp: lastActivity.app,\n                        toApp: appName,\n                        fromWindow: lastActivity.window,\n                        toWindow: windowName,\n                        duration: switchDuration,\n                        switchType: lastActivity.app !== appName ? 'app' : 'window'\n                    });\n                    // End current session if context switch indicates distraction\n                    if (currentSession && this.isDistractingSwitch(lastActivity.app, appName)) {\n                        this.finalizeSession(currentSession, lastActivity.timestamp, sessions);\n                        currentSession = null;\n                    }\n                }\n            }\n            // Start or continue focus session\n            if (!currentSession || this.shouldStartNewSession(currentSession, appName, windowName, timestamp)) {\n                // Finalize previous session\n                if (currentSession) {\n                    this.finalizeSession(currentSession, timestamp, sessions);\n                }\n                // Start new session\n                currentSession = {\n                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(),\n                    startTime: timestamp,\n                    appName,\n                    windowName,\n                    taskCategory: this.categorizeTask(appName, windowName),\n                    distractionCount: 0,\n                    contextSwitches: 0\n                };\n            }\n            // Update current session\n            if (currentSession) {\n                currentSession.endTime = timestamp;\n                // Count context switches within session\n                if (switches.length > 0 && currentSession.startTime && switches[switches.length - 1].timestamp >= currentSession.startTime) {\n                    currentSession.contextSwitches = (currentSession.contextSwitches || 0) + 1;\n                }\n            }\n            lastActivity = {\n                app: appName,\n                window: windowName,\n                timestamp\n            };\n        }\n        // Finalize last session\n        if (currentSession && lastActivity) {\n            this.finalizeSession(currentSession, lastActivity.timestamp, sessions);\n        }\n        return {\n            focusSessions: sessions,\n            contextSwitches: switches\n        };\n    }\n    /**\n   * Finalize a focus session with calculated metrics\n   */ finalizeSession(session, endTime, sessions) {\n        if (!session.startTime || !session.appName) return;\n        const duration = (0,_barrel_optimize_names_differenceInMinutes_differenceInSeconds_date_fns__WEBPACK_IMPORTED_MODULE_3__.differenceInMinutes)(endTime, session.startTime);\n        // Only consider sessions above threshold as focus sessions\n        if (duration >= this.config.focusThreshold) {\n            const focusScore = this.calculateFocusScore(duration, session.contextSwitches || 0, session.distractionCount || 0);\n            sessions.push({\n                id: session.id,\n                startTime: session.startTime,\n                endTime,\n                duration,\n                appName: session.appName,\n                windowName: session.windowName || \"\",\n                taskCategory: session.taskCategory || 'unknown',\n                focusScore,\n                distractionCount: session.distractionCount || 0,\n                contextSwitches: session.contextSwitches || 0,\n                productivity: this.getProductivityLevel(focusScore)\n            });\n        }\n    }\n    /**\n   * Calculate focus score based on session metrics\n   */ calculateFocusScore(duration, contextSwitches, distractions) {\n        let score = 100;\n        // Penalize for context switches (more switches = lower focus)\n        score -= Math.min(contextSwitches * 5, 30);\n        // Penalize for distractions\n        score -= Math.min(distractions * 10, 40);\n        // Bonus for longer sessions\n        if (duration >= this.config.deepWorkMinimum) {\n            score += 10;\n        }\n        return Math.max(0, Math.min(100, score));\n    }\n    /**\n   * Categorize task based on app and window name\n   */ categorizeTask(appName, windowName) {\n        const text = `${appName} ${windowName}`.toLowerCase();\n        // Define categorization rules\n        const categories = {\n            'deep-work': [\n                'vscode',\n                'intellij',\n                'xcode',\n                'sublime',\n                'vim',\n                'emacs',\n                'figma',\n                'sketch'\n            ],\n            'communication': [\n                'slack',\n                'teams',\n                'discord',\n                'zoom',\n                'meet',\n                'skype',\n                'mail',\n                'outlook'\n            ],\n            'research': [\n                'chrome',\n                'firefox',\n                'safari',\n                'edge',\n                'browser',\n                'wikipedia',\n                'stackoverflow'\n            ],\n            'creative': [\n                'photoshop',\n                'illustrator',\n                'premiere',\n                'after effects',\n                'blender',\n                'canva'\n            ],\n            'administrative': [\n                'excel',\n                'sheets',\n                'word',\n                'docs',\n                'powerpoint',\n                'slides',\n                'notion'\n            ],\n            'learning': [\n                'coursera',\n                'udemy',\n                'youtube',\n                'khan academy',\n                'duolingo',\n                'anki'\n            ],\n            'entertainment': [\n                'netflix',\n                'youtube',\n                'spotify',\n                'twitch',\n                'gaming',\n                'steam'\n            ],\n            'social': [\n                'facebook',\n                'twitter',\n                'instagram',\n                'linkedin',\n                'reddit',\n                'tiktok'\n            ],\n            'distraction': [\n                'news',\n                'shopping',\n                'amazon',\n                'ebay',\n                'social media'\n            ],\n            'unknown': []\n        };\n        for (const [category, keywords] of Object.entries(categories)){\n            if (keywords.some((keyword)=>text.includes(keyword))) {\n                return category;\n            }\n        }\n        return 'unknown';\n    }\n    /**\n   * Determine if a context switch indicates distraction\n   */ isDistractingSwitch(fromApp, toApp) {\n        const distractingApps = [\n            'chrome',\n            'firefox',\n            'safari',\n            'social',\n            'entertainment'\n        ];\n        const productiveApps = [\n            'vscode',\n            'intellij',\n            'figma',\n            'notion',\n            'excel'\n        ];\n        return productiveApps.some((app)=>fromApp.toLowerCase().includes(app)) && distractingApps.some((app)=>toApp.toLowerCase().includes(app));\n    }\n    /**\n   * Determine if a new session should be started\n   */ shouldStartNewSession(currentSession, appName, _windowName, timestamp) {\n        if (!currentSession.startTime) return true;\n        // Start new session if app changed significantly\n        if (currentSession.appName !== appName) {\n            const timeSinceStart = (0,_barrel_optimize_names_differenceInMinutes_differenceInSeconds_date_fns__WEBPACK_IMPORTED_MODULE_3__.differenceInMinutes)(timestamp, currentSession.startTime);\n            return timeSinceStart >= this.config.focusThreshold;\n        }\n        return false;\n    }\n    /**\n   * Convert focus score to productivity level\n   */ getProductivityLevel(focusScore) {\n        if (focusScore >= 90) return 'very-high';\n        if (focusScore >= 75) return 'high';\n        if (focusScore >= 50) return 'medium';\n        if (focusScore >= 25) return 'low';\n        return 'very-low';\n    }\n    /**\n   * Calculate comprehensive productivity metrics\n   */ calculateProductivityMetrics(sessions, switches, date) {\n        const totalFocusTime = sessions.reduce((sum, session)=>sum + session.duration, 0);\n        const averageFocusScore = sessions.length > 0 ? sessions.reduce((sum, session)=>sum + session.focusScore, 0) / sessions.length : 0;\n        const deepWorkSessions = sessions.filter((s)=>s.duration >= this.config.deepWorkMinimum).length;\n        const averageSessionLength = sessions.length > 0 ? totalFocusTime / sessions.length : 0;\n        // Calculate hourly productivity\n        const hourlyProductivity = this.calculateHourlyProductivity(sessions);\n        const mostProductiveHour = this.getMostProductiveHour(hourlyProductivity);\n        const leastProductiveHour = this.getLeastProductiveHour(hourlyProductivity);\n        // Calculate app usage statistics\n        const appUsage = this.calculateAppUsage(sessions);\n        const topProductiveApps = appUsage.filter((app)=>app.productivityScore >= 70).sort((a, b)=>b.focusTime - a.focusTime).slice(0, 5);\n        const topDistractingApps = appUsage.filter((app)=>app.productivityScore < 50).sort((a, b)=>b.totalTime - a.totalTime).slice(0, 5);\n        return {\n            date,\n            totalFocusTime,\n            totalDistractionTime: 0,\n            focusScore: averageFocusScore,\n            contextSwitches: switches.length,\n            deepWorkSessions,\n            averageSessionLength,\n            mostProductiveHour,\n            leastProductiveHour,\n            topProductiveApps,\n            topDistractingApps\n        };\n    }\n    calculateHourlyProductivity(sessions) {\n        const hourlyData = {};\n        for(let hour = 0; hour < 24; hour++){\n            hourlyData[hour] = {\n                totalTime: 0,\n                totalScore: 0,\n                count: 0\n            };\n        }\n        sessions.forEach((session)=>{\n            const hour = session.startTime.getHours();\n            hourlyData[hour].totalTime += session.duration;\n            hourlyData[hour].totalScore += session.focusScore;\n            hourlyData[hour].count += 1;\n        });\n        const productivity = {};\n        for(let hour = 0; hour < 24; hour++){\n            const data = hourlyData[hour];\n            productivity[hour] = data.count > 0 ? data.totalScore / data.count : 0;\n        }\n        return productivity;\n    }\n    getMostProductiveHour(hourlyProductivity) {\n        return Object.entries(hourlyProductivity).reduce((max, [hour, productivity])=>productivity > hourlyProductivity[max] ? parseInt(hour) : max, 0);\n    }\n    getLeastProductiveHour(hourlyProductivity) {\n        return Object.entries(hourlyProductivity).reduce((min, [hour, productivity])=>productivity < hourlyProductivity[min] ? parseInt(hour) : min, 0);\n    }\n    calculateAppUsage(sessions) {\n        const appData = {};\n        sessions.forEach((session)=>{\n            if (!appData[session.appName]) {\n                appData[session.appName] = {\n                    totalTime: 0,\n                    focusTime: 0,\n                    sessionCount: 0,\n                    totalScore: 0\n                };\n            }\n            const data = appData[session.appName];\n            data.totalTime += session.duration;\n            data.sessionCount += 1;\n            data.totalScore += session.focusScore;\n            if (session.focusScore >= 70) {\n                data.focusTime += session.duration;\n            }\n        });\n        return Object.entries(appData).map(([appName, data])=>({\n                appName,\n                totalTime: data.totalTime,\n                focusTime: data.focusTime,\n                distractionTime: data.totalTime - data.focusTime,\n                sessionCount: data.sessionCount,\n                averageSessionLength: data.totalTime / data.sessionCount,\n                productivityScore: data.totalScore / data.sessionCount\n            }));\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/analytics-engine.ts\n");

/***/ }),

/***/ "(rsc)/./lib/pattern-recognition.ts":
/*!************************************!*\
  !*** ./lib/pattern-recognition.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PatternRecognitionEngine: () => (/* binding */ PatternRecognitionEngine)\n/* harmony export */ });\n/* harmony import */ var _barrel_optimize_names_getDay_getHours_date_fns__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=getDay,getHours!=!date-fns */ \"(rsc)/./node_modules/date-fns/getHours.js\");\n/* harmony import */ var _barrel_optimize_names_getDay_getHours_date_fns__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=getDay,getHours!=!date-fns */ \"(rsc)/./node_modules/date-fns/getDay.js\");\n/* harmony import */ var _tensorflow_tfjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tensorflow/tfjs */ \"(rsc)/./node_modules/@tensorflow/tfjs/dist/index.js\");\n\n\nclass PatternRecognitionEngine {\n    constructor(){\n        this.patterns = [];\n        this.energyLevels = [];\n        this.model = null;\n        this.initializeModel();\n    }\n    /**\n   * Initialize TensorFlow model for pattern prediction\n   */ async initializeModel() {\n        try {\n            // Create a simple neural network for productivity prediction\n            this.model = _tensorflow_tfjs__WEBPACK_IMPORTED_MODULE_0__.sequential({\n                layers: [\n                    _tensorflow_tfjs__WEBPACK_IMPORTED_MODULE_0__.layers.dense({\n                        inputShape: [\n                            7\n                        ],\n                        units: 16,\n                        activation: 'relu'\n                    }),\n                    _tensorflow_tfjs__WEBPACK_IMPORTED_MODULE_0__.layers.dropout({\n                        rate: 0.2\n                    }),\n                    _tensorflow_tfjs__WEBPACK_IMPORTED_MODULE_0__.layers.dense({\n                        units: 8,\n                        activation: 'relu'\n                    }),\n                    _tensorflow_tfjs__WEBPACK_IMPORTED_MODULE_0__.layers.dense({\n                        units: 1,\n                        activation: 'sigmoid'\n                    })\n                ]\n            });\n            this.model.compile({\n                optimizer: 'adam',\n                loss: 'meanSquaredError',\n                metrics: [\n                    'mae'\n                ]\n            });\n            console.log(\"Pattern recognition model initialized\");\n        } catch (error) {\n            console.error(\"Error initializing ML model:\", error);\n        }\n    }\n    /**\n   * Analyze productivity patterns from historical data\n   */ async analyzeProductivityPatterns(sessions, timeRange) {\n        if (sessions.length < 10) {\n            console.warn(\"Insufficient data for pattern analysis\");\n            return [];\n        }\n        // Group sessions by time patterns\n        const timePatterns = this.groupSessionsByTimePatterns(sessions);\n        const patterns = [];\n        for (const [patternKey, patternSessions] of Object.entries(timePatterns)){\n            if (patternSessions.length < 3) continue;\n            const pattern = await this.analyzeTimePattern(patternKey, patternSessions);\n            if (pattern) {\n                patterns.push(pattern);\n            }\n        }\n        // Analyze weekly patterns\n        const weeklyPatterns = this.analyzeWeeklyPatterns(sessions);\n        patterns.push(...weeklyPatterns);\n        // Analyze task-specific patterns\n        const taskPatterns = this.analyzeTaskPatterns(sessions);\n        patterns.push(...taskPatterns);\n        // Sort by confidence and relevance\n        return patterns.sort((a, b)=>b.confidence - a.confidence);\n    }\n    /**\n   * Group sessions by time patterns (hour blocks and days)\n   */ groupSessionsByTimePatterns(sessions) {\n        const patterns = {};\n        sessions.forEach((session)=>{\n            const hour = (0,_barrel_optimize_names_getDay_getHours_date_fns__WEBPACK_IMPORTED_MODULE_1__.getHours)(session.startTime);\n            const dayOfWeek = (0,_barrel_optimize_names_getDay_getHours_date_fns__WEBPACK_IMPORTED_MODULE_2__.getDay)(session.startTime);\n            // Create 2-hour time blocks\n            const timeBlock = Math.floor(hour / 2) * 2;\n            const endBlock = timeBlock + 2;\n            // Categorize days: weekday vs weekend\n            const dayCategory = dayOfWeek === 0 || dayOfWeek === 6 ? 'weekend' : 'weekday';\n            const patternKey = `${timeBlock}-${endBlock}_${dayCategory}`;\n            if (!patterns[patternKey]) {\n                patterns[patternKey] = [];\n            }\n            patterns[patternKey].push(session);\n        });\n        return patterns;\n    }\n    /**\n   * Analyze a specific time pattern\n   */ async analyzeTimePattern(patternKey, sessions) {\n        const [timeRange, dayCategory] = patternKey.split('_');\n        const [startHour, endHour] = timeRange.split('-').map(Number);\n        // Calculate pattern metrics\n        const averageProductivity = sessions.reduce((sum, s)=>sum + s.focusScore, 0) / sessions.length;\n        const averageDuration = sessions.reduce((sum, s)=>sum + s.duration, 0) / sessions.length;\n        const totalSessions = sessions.length;\n        // Calculate confidence based on consistency and sample size\n        const productivityVariance = this.calculateVariance(sessions.map((s)=>s.focusScore));\n        const consistency = Math.max(0, 100 - productivityVariance);\n        const sampleSizeScore = Math.min(100, totalSessions / 10 * 100);\n        const confidence = (consistency + sampleSizeScore) / 2;\n        if (confidence < 30) return null; // Too low confidence\n        // Generate insights and recommendations\n        const insights = this.generatePatternInsights(sessions, averageProductivity, averageDuration);\n        const recommendations = this.generatePatternRecommendations(averageProductivity, startHour, endHour, dayCategory);\n        return {\n            id: `pattern_${patternKey}`,\n            name: this.generatePatternName(startHour, endHour, dayCategory),\n            description: `${dayCategory} productivity pattern (${totalSessions} sessions)`,\n            timePattern: {\n                startHour,\n                endHour,\n                daysOfWeek: dayCategory === 'weekend' ? [\n                    0,\n                    6\n                ] : [\n                    1,\n                    2,\n                    3,\n                    4,\n                    5\n                ]\n            },\n            averageProductivity,\n            confidence,\n            recommendations: [\n                ...insights,\n                ...recommendations\n            ]\n        };\n    }\n    /**\n   * Analyze weekly productivity patterns\n   */ analyzeWeeklyPatterns(sessions) {\n        const weeklyData = {};\n        // Group by day of week\n        sessions.forEach((session)=>{\n            const dayOfWeek = (0,_barrel_optimize_names_getDay_getHours_date_fns__WEBPACK_IMPORTED_MODULE_2__.getDay)(session.startTime);\n            if (!weeklyData[dayOfWeek]) {\n                weeklyData[dayOfWeek] = [];\n            }\n            weeklyData[dayOfWeek].push(session);\n        });\n        const patterns = [];\n        const dayNames = [\n            'Sunday',\n            'Monday',\n            'Tuesday',\n            'Wednesday',\n            'Thursday',\n            'Friday',\n            'Saturday'\n        ];\n        Object.entries(weeklyData).forEach(([day, daySessions])=>{\n            if (daySessions.length < 3) return;\n            const dayNum = parseInt(day);\n            const averageProductivity = daySessions.reduce((sum, s)=>sum + s.focusScore, 0) / daySessions.length;\n            const confidence = Math.min(100, daySessions.length / sessions.length * 100 * 7);\n            if (confidence >= 20) {\n                patterns.push({\n                    id: `weekly_${day}`,\n                    name: `${dayNames[dayNum]} Pattern`,\n                    description: `Productivity pattern for ${dayNames[dayNum]}s`,\n                    timePattern: {\n                        startHour: 0,\n                        endHour: 24,\n                        daysOfWeek: [\n                            dayNum\n                        ]\n                    },\n                    averageProductivity,\n                    confidence,\n                    recommendations: this.generateDaySpecificRecommendations(dayNum, averageProductivity)\n                });\n            }\n        });\n        return patterns;\n    }\n    /**\n   * Analyze task-specific productivity patterns\n   */ analyzeTaskPatterns(sessions) {\n        const taskData = {};\n        sessions.forEach((session)=>{\n            if (!taskData[session.taskCategory]) {\n                taskData[session.taskCategory] = [];\n            }\n            taskData[session.taskCategory].push(session);\n        });\n        const patterns = [];\n        Object.entries(taskData).forEach(([taskType, taskSessions])=>{\n            if (taskSessions.length < 5) return;\n            const averageProductivity = taskSessions.reduce((sum, s)=>sum + s.focusScore, 0) / taskSessions.length;\n            const confidence = Math.min(100, taskSessions.length / sessions.length * 100 * 5);\n            // Find optimal time patterns for this task type\n            const hourlyPerformance = this.analyzeHourlyPerformance(taskSessions);\n            const bestHours = this.findBestPerformanceHours(hourlyPerformance);\n            if (confidence >= 30 && bestHours.length > 0) {\n                patterns.push({\n                    id: `task_${taskType}`,\n                    name: `${taskType.replace('-', ' ')} Optimization`,\n                    description: `Best times for ${taskType.replace('-', ' ')} tasks`,\n                    timePattern: {\n                        startHour: Math.min(...bestHours),\n                        endHour: Math.max(...bestHours) + 1,\n                        daysOfWeek: [\n                            1,\n                            2,\n                            3,\n                            4,\n                            5\n                        ] // Weekdays\n                    },\n                    averageProductivity,\n                    confidence,\n                    recommendations: this.generateTaskSpecificRecommendations(taskType, bestHours)\n                });\n            }\n        });\n        return patterns;\n    }\n    /**\n   * Predict optimal schedule based on patterns\n   */ async predictOptimalSchedule(date, taskTypes) {\n        const schedule = [];\n        if (!this.model) {\n            console.warn(\"ML model not available for predictions\");\n            return this.generateRuleBasedSchedule(date, taskTypes);\n        }\n        try {\n            // Prepare features for prediction\n            const dayOfWeek = (0,_barrel_optimize_names_getDay_getHours_date_fns__WEBPACK_IMPORTED_MODULE_2__.getDay)(date);\n            const isWeekend = dayOfWeek === 0 || dayOfWeek === 6 ? 1 : 0;\n            for(let hour = 8; hour < 18; hour++){\n                for (const taskType of taskTypes){\n                    // Create feature vector: [hour, dayOfWeek, isWeekend, taskTypeEncoded, ...]\n                    const features = [\n                        hour / 24,\n                        dayOfWeek / 7,\n                        isWeekend,\n                        this.encodeTaskType(taskType),\n                        this.getHistoricalPerformance(hour, dayOfWeek, taskType),\n                        this.getEnergyLevel(hour),\n                        this.getContextualFactors(date, hour)\n                    ];\n                    const prediction = this.model.predict(_tensorflow_tfjs__WEBPACK_IMPORTED_MODULE_0__.tensor2d([\n                        features\n                    ]));\n                    const predictedProductivity = (await prediction.data())[0] * 100;\n                    prediction.dispose();\n                    const confidence = this.calculatePredictionConfidence(hour, dayOfWeek, taskType);\n                    schedule.push({\n                        hour,\n                        taskType,\n                        predictedProductivity,\n                        confidence,\n                        reasoning: this.generatePredictionReasoning(hour, taskType, predictedProductivity)\n                    });\n                }\n            }\n            // Sort by predicted productivity and return top recommendations\n            return schedule.sort((a, b)=>b.predictedProductivity - a.predictedProductivity).slice(0, 8); // Top 8 time slots\n        } catch (error) {\n            console.error(\"Error in ML prediction:\", error);\n            return this.generateRuleBasedSchedule(date, taskTypes);\n        }\n    }\n    /**\n   * Generate rule-based schedule as fallback\n   */ generateRuleBasedSchedule(date, taskTypes) {\n        const schedule = [];\n        const dayOfWeek = (0,_barrel_optimize_names_getDay_getHours_date_fns__WEBPACK_IMPORTED_MODULE_2__.getDay)(date);\n        const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;\n        // Rule-based optimal times\n        const optimalTimes = {\n            'deep-work': isWeekend ? [\n                10,\n                11,\n                14,\n                15\n            ] : [\n                9,\n                10,\n                14,\n                15\n            ],\n            'creative': [\n                10,\n                11,\n                16,\n                17\n            ],\n            'communication': [\n                11,\n                12,\n                14,\n                16\n            ],\n            'administrative': [\n                13,\n                14,\n                15,\n                16\n            ],\n            'learning': [\n                9,\n                10,\n                11,\n                15\n            ],\n            'research': [\n                9,\n                10,\n                14,\n                15\n            ],\n            'entertainment': [\n                18,\n                19,\n                20,\n                21\n            ],\n            'social': [\n                12,\n                13,\n                17,\n                18\n            ],\n            'distraction': [\n                16,\n                17,\n                18,\n                19\n            ],\n            'unknown': [\n                10,\n                11,\n                14,\n                15\n            ]\n        };\n        taskTypes.forEach((taskType)=>{\n            const optimalHours = optimalTimes[taskType] || [\n                10,\n                11,\n                14,\n                15\n            ];\n            optimalHours.forEach((hour)=>{\n                schedule.push({\n                    hour,\n                    taskType,\n                    predictedProductivity: this.getRuleBasedProductivity(hour, taskType, isWeekend),\n                    confidence: 70,\n                    reasoning: `Based on general productivity patterns for ${taskType.replace('-', ' ')}`\n                });\n            });\n        });\n        return schedule.sort((a, b)=>b.predictedProductivity - a.predictedProductivity);\n    }\n    // Helper methods\n    calculateVariance(values) {\n        const mean = values.reduce((sum, val)=>sum + val, 0) / values.length;\n        const squaredDiffs = values.map((val)=>Math.pow(val - mean, 2));\n        return squaredDiffs.reduce((sum, diff)=>sum + diff, 0) / values.length;\n    }\n    generatePatternName(startHour, endHour, dayCategory) {\n        const timeStr = `${startHour}:00-${endHour}:00`;\n        const dayStr = dayCategory === 'weekend' ? 'Weekend' : 'Weekday';\n        return `${dayStr} ${timeStr}`;\n    }\n    generatePatternInsights(sessions, avgProductivity, avgDuration) {\n        const insights = [];\n        if (avgProductivity >= 80) {\n            insights.push(\"High-performance time window - excellent for important tasks\");\n        } else if (avgProductivity >= 60) {\n            insights.push(\"Good productivity window - suitable for focused work\");\n        } else {\n            insights.push(\"Lower productivity period - consider lighter tasks\");\n        }\n        if (avgDuration >= 60) {\n            insights.push(\"Long focus sessions typical - good for deep work\");\n        } else if (avgDuration >= 30) {\n            insights.push(\"Moderate session length - good for regular tasks\");\n        } else {\n            insights.push(\"Short sessions typical - better for quick tasks\");\n        }\n        return insights;\n    }\n    generatePatternRecommendations(productivity, startHour, endHour, dayCategory) {\n        const recommendations = [];\n        if (productivity >= 80) {\n            recommendations.push(\"Schedule your most important work during this time\");\n            recommendations.push(\"Minimize interruptions and distractions\");\n        } else if (productivity >= 60) {\n            recommendations.push(\"Good time for regular focused work\");\n            recommendations.push(\"Consider optimizing environment for better focus\");\n        } else {\n            recommendations.push(\"Use for lighter tasks or administrative work\");\n            recommendations.push(\"Consider taking breaks or doing physical activity\");\n        }\n        // Time-specific recommendations\n        if (startHour <= 10) {\n            recommendations.push(\"Morning energy - great for analytical tasks\");\n        } else if (startHour >= 14 && startHour <= 16) {\n            recommendations.push(\"Post-lunch period - may need energy boost\");\n        }\n        return recommendations;\n    }\n    generateDaySpecificRecommendations(day, productivity) {\n        const dayNames = [\n            'Sunday',\n            'Monday',\n            'Tuesday',\n            'Wednesday',\n            'Thursday',\n            'Friday',\n            'Saturday'\n        ];\n        const dayName = dayNames[day];\n        const recommendations = [\n            `Optimize ${dayName} productivity`\n        ];\n        if (day === 1) {\n            recommendations.push(\"Start week with planning and goal setting\");\n        } else if (day === 5) {\n            recommendations.push(\"Wrap up weekly tasks and plan for next week\");\n        } else if (day === 0 || day === 6) {\n            recommendations.push(\"Focus on personal projects and learning\");\n        }\n        return recommendations;\n    }\n    generateTaskSpecificRecommendations(taskType, bestHours) {\n        const recommendations = [];\n        const timeStr = bestHours.map((h)=>`${h}:00`).join(', ');\n        recommendations.push(`Best performance for ${taskType.replace('-', ' ')} at ${timeStr}`);\n        switch(taskType){\n            case 'deep-work':\n                recommendations.push(\"Minimize distractions and notifications\");\n                recommendations.push(\"Ensure comfortable environment and good lighting\");\n                break;\n            case 'creative':\n                recommendations.push(\"Allow for inspiration and experimentation\");\n                recommendations.push(\"Consider background music or change of scenery\");\n                break;\n            case 'communication':\n                recommendations.push(\"Batch similar communication tasks together\");\n                recommendations.push(\"Set specific times for checking messages\");\n                break;\n        }\n        return recommendations;\n    }\n    analyzeHourlyPerformance(sessions) {\n        const hourlyData = {};\n        sessions.forEach((session)=>{\n            const hour = (0,_barrel_optimize_names_getDay_getHours_date_fns__WEBPACK_IMPORTED_MODULE_1__.getHours)(session.startTime);\n            if (!hourlyData[hour]) {\n                hourlyData[hour] = {\n                    total: 0,\n                    count: 0\n                };\n            }\n            hourlyData[hour].total += session.focusScore;\n            hourlyData[hour].count += 1;\n        });\n        const performance = {};\n        Object.entries(hourlyData).forEach(([hour, data])=>{\n            performance[parseInt(hour)] = data.total / data.count;\n        });\n        return performance;\n    }\n    findBestPerformanceHours(hourlyPerformance) {\n        const avgPerformance = Object.values(hourlyPerformance).reduce((sum, val)=>sum + val, 0) / Object.values(hourlyPerformance).length;\n        return Object.entries(hourlyPerformance).filter(([, performance])=>performance > avgPerformance * 1.1) // 10% above average\n        .map(([hour])=>parseInt(hour));\n    }\n    encodeTaskType(taskType) {\n        const encoding = {\n            'deep-work': 0.9,\n            'creative': 0.8,\n            'research': 0.7,\n            'learning': 0.6,\n            'administrative': 0.5,\n            'communication': 0.4,\n            'social': 0.3,\n            'entertainment': 0.2,\n            'distraction': 0.1,\n            'unknown': 0.0\n        };\n        return encoding[taskType] || 0.0;\n    }\n    getHistoricalPerformance(hour, dayOfWeek, taskType) {\n        // Simplified - would use actual historical data\n        return 0.7; // Default performance score\n    }\n    getEnergyLevel(hour) {\n        // Simplified energy curve based on circadian rhythms\n        if (hour >= 9 && hour <= 11) return 0.9; // Morning peak\n        if (hour >= 14 && hour <= 16) return 0.8; // Afternoon peak\n        if (hour >= 20 || hour <= 6) return 0.3; // Low energy\n        return 0.6; // Moderate energy\n    }\n    getContextualFactors(date, hour) {\n        // Consider factors like meetings, deadlines, etc.\n        // Simplified implementation\n        return 0.5;\n    }\n    calculatePredictionConfidence(hour, dayOfWeek, taskType) {\n        // Calculate confidence based on historical data availability\n        // Simplified implementation\n        return 75;\n    }\n    generatePredictionReasoning(hour, taskType, productivity) {\n        if (productivity >= 80) {\n            return `Optimal time for ${taskType.replace('-', ' ')} based on historical performance`;\n        } else if (productivity >= 60) {\n            return `Good time for ${taskType.replace('-', ' ')} with moderate productivity expected`;\n        } else {\n            return `Lower productivity expected for ${taskType.replace('-', ' ')} at this time`;\n        }\n    }\n    getRuleBasedProductivity(hour, taskType, isWeekend) {\n        // Simple rule-based productivity scoring\n        let score = 70; // Base score\n        // Time of day adjustments\n        if (hour >= 9 && hour <= 11) score += 15; // Morning peak\n        if (hour >= 14 && hour <= 16) score += 10; // Afternoon peak\n        if (hour <= 8 || hour >= 18) score -= 20; // Early/late penalties\n        // Task type adjustments\n        if (taskType === 'deep-work' && hour >= 9 && hour <= 11) score += 10;\n        if (taskType === 'creative' && hour >= 10 && hour <= 12) score += 10;\n        if (taskType === 'communication' && hour >= 11 && hour <= 16) score += 5;\n        // Weekend adjustments\n        if (isWeekend) score -= 10;\n        return Math.max(0, Math.min(100, score));\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/pattern-recognition.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fanalytics%2Fdaily-summary%2Froute&page=%2Fapi%2Fanalytics%2Fdaily-summary%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalytics%2Fdaily-summary%2Froute.ts&appDir=G%3A%5Cprojects%5Cscreenpipe%5Cscreenpipe%5Cpipes%5Cfocus-analytics-coach%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5Cprojects%5Cscreenpipe%5Cscreenpipe%5Cpipes%5Cfocus-analytics-coach&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fanalytics%2Fdaily-summary%2Froute&page=%2Fapi%2Fanalytics%2Fdaily-summary%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalytics%2Fdaily-summary%2Froute.ts&appDir=G%3A%5Cprojects%5Cscreenpipe%5Cscreenpipe%5Cpipes%5Cfocus-analytics-coach%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5Cprojects%5Cscreenpipe%5Cscreenpipe%5Cpipes%5Cfocus-analytics-coach&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var G_projects_screenpipe_screenpipe_pipes_focus_analytics_coach_app_api_analytics_daily_summary_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/analytics/daily-summary/route.ts */ \"(rsc)/./app/api/analytics/daily-summary/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/analytics/daily-summary/route\",\n        pathname: \"/api/analytics/daily-summary\",\n        filename: \"route\",\n        bundlePath: \"app/api/analytics/daily-summary/route\"\n    },\n    resolvedPagePath: \"G:\\\\projects\\\\screenpipe\\\\screenpipe\\\\pipes\\\\focus-analytics-coach\\\\app\\\\api\\\\analytics\\\\daily-summary\\\\route.ts\",\n    nextConfigOutput,\n    userland: G_projects_screenpipe_screenpipe_pipes_focus_analytics_coach_app_api_analytics_daily_summary_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fanalytics%2Fdaily-summary%2Froute&page=%2Fapi%2Fanalytics%2Fdaily-summary%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalytics%2Fdaily-summary%2Froute.ts&appDir=G%3A%5Cprojects%5Cscreenpipe%5Cscreenpipe%5Cpipes%5Cfocus-analytics-coach%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5Cprojects%5Cscreenpipe%5Cscreenpipe%5Cpipes%5Cfocus-analytics-coach&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("string_decoder");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/@tensorflow","vendor-chunks/posthog-js","vendor-chunks/tr46","vendor-chunks/date-fns","vendor-chunks/fuse.js","vendor-chunks/whatwg-url","vendor-chunks/long","vendor-chunks/@screenpipe","vendor-chunks/seedrandom","vendor-chunks/webidl-conversions","vendor-chunks/uuid"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fanalytics%2Fdaily-summary%2Froute&page=%2Fapi%2Fanalytics%2Fdaily-summary%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalytics%2Fdaily-summary%2Froute.ts&appDir=G%3A%5Cprojects%5Cscreenpipe%5Cscreenpipe%5Cpipes%5Cfocus-analytics-coach%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5Cprojects%5Cscreenpipe%5Cscreenpipe%5Cpipes%5Cfocus-analytics-coach&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();