Based on your commits, here is the changelog for the new screenpipe update:

### **New Features:**
- **Added hypr-v0 pipe to the store:** Users can now access the new hypr-v0 pipe directly from the store.
- **Analytics in SDK:** Implemented analytics functionality in the SDK for enhanced tracking and insights.

### **Improvements:**
- **Refactored screenpipe pipe download to screenpipe pipe install:** Improved naming for clarity and ease of understanding in the pipe management.
- **Updated documentation:** Enhanced and cleaned up the README files for better guidance and support.

### **Fixes:**
- **Fixed small issue in streaming transcription API:** Corrected minor bugs related to the streaming transcription functionality.
- **Addressed linting issues:** Fixed various linting errors for cleaner and more maintainable code.
- **Fixed documentation issues:** Resolved inaccuracies in the documentation to provide clearer information.

#### **Full Changelog:** [67f09..674f4](https://github.com/mediar-ai/screenpipe/compare/67f09..674f4)

