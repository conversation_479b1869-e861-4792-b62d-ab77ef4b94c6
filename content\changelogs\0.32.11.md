### **New Features:**
- **Introduced pipe update version endpoint:** Added a new endpoint to prevent loss of configuration during updates and facilitate smoother updates.

### **Improvements:**
- **Documented LMStudio and Ollama usage:** Added documentation to assist users in utilizing LMStudio and Ollama effectively.
- **Reduced auto destruct polling:** Decreased the polling duration for auto destruct to enhance performance during Windows updates.

### **Fixes:**
- **Fixed search with custom AI provider:** Resolved an issue where search functionality was not compatible with custom AI providers lacking an API key.
- **Fixed hydration errors:** Addressed hydration errors to improve stability and performance.

#### **Full Changelog:** [f025f..6336b](https://github.com/mediar-ai/screenpipe/compare/f025f..6336b)

