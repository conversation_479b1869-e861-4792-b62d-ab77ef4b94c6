Based on the provided commits, here is the changelog for the new screenpipe update:

### **Improvements:**
- **Removed emojis and ASCII Art from installation prompts:** Cleaned up the install.ps to present a more professional and straightforward installation experience.

### **Fixes:**
- **Attempted fix for sideload issue:** Addressed issues related to sideloading the application to enhance overall functionality. 

(Note: The specific result of the attempt fix for sideload is not clear; only the action taken is included under fixes.)

#### **Full Changelog:** [20c3c..c3905](https://github.com/mediar-ai/screenpipe/compare/20c3c..c3905)

