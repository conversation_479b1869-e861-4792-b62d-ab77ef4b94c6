import type { Config } from "tailwindcss";

export default {
    darkMode: ["class"],
    content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
  	extend: {
  		colors: {
  			background: 'hsl(var(--background))',
  			foreground: 'hsl(var(--foreground))',
  			card: {
  				DEFAULT: 'hsl(var(--card))',
  				foreground: 'hsl(var(--card-foreground))'
  			},
  			popover: {
  				DEFAULT: 'hsl(var(--popover))',
  				foreground: 'hsl(var(--popover-foreground))'
  			},
  			primary: {
  				DEFAULT: 'hsl(var(--primary))',
  				foreground: 'hsl(var(--primary-foreground))'
  			},
  			secondary: {
  				DEFAULT: 'hsl(var(--secondary))',
  				foreground: 'hsl(var(--secondary-foreground))'
  			},
  			muted: {
  				DEFAULT: 'hsl(var(--muted))',
  				foreground: 'hsl(var(--muted-foreground))'
  			},
  			accent: {
  				DEFAULT: 'hsl(var(--accent))',
  				foreground: 'hsl(var(--accent-foreground))'
  			},
  			destructive: {
  				DEFAULT: 'hsl(var(--destructive))',
  				foreground: 'hsl(var(--destructive-foreground))'
  			},
  			border: 'hsl(var(--border))',
  			input: 'hsl(var(--input))',
  			ring: 'hsl(var(--ring))',
  			chart: {
  				'1': 'hsl(var(--chart-1))',
  				'2': 'hsl(var(--chart-2))',
  				'3': 'hsl(var(--chart-3))',
  				'4': 'hsl(var(--chart-4))',
  				'5': 'hsl(var(--chart-5))'
  			}
  		},
  		borderRadius: {
  			lg: 'var(--radius)',
  			md: 'calc(var(--radius) - 2px)',
  			sm: 'calc(var(--radius) - 4px)'
  		},
  		keyframes: {
  			'accordion-down': {
  				from: {
  					height: '0'
  				},
  				to: {
  					height: 'var(--radix-accordion-content-height)'
  				}
  			},
  			'accordion-up': {
  				from: {
  					height: 'var(--radix-accordion-content-height)'
  				},
  				to: {
  					height: '0'
  				}
  			},
  			shimmer: {
  				'0%, 100%': { 
  					backgroundPosition: '200% 0',
  					opacity: '0.9'
  				},
  				'50%': { 
  					backgroundPosition: '-200% 0',
  					opacity: '0.4'
  				}
  			},
  			glow: {
  				'0%': { 
  					boxShadow: '0 0 0 rgba(0,0,0,0)',
  					transform: 'scale(1)',
  					opacity: '0.7'
  				},
  				'25%': {
  					boxShadow: '0 0 30px rgba(0,0,0,0.3)',
  					transform: 'scale(1.01)',
  					opacity: '1'
  				},
  				'50%': { 
  					boxShadow: '0 0 15px rgba(0,0,0,0.2)',
  					transform: 'scale(0.99)',
  					opacity: '0.8'
  				},
  				'75%': {
  					boxShadow: '0 0 20px rgba(0,0,0,0.25)',
  					transform: 'scale(1.005)',
  					opacity: '0.9'
  				},
  				'100%': { 
  					boxShadow: '0 0 0 rgba(0,0,0,0)',
  					transform: 'scale(1)',
  					opacity: '1'
  				}
  			}
  		},
  		animation: {
  			'accordion-down': 'accordion-down 0.2s ease-out',
  			'accordion-up': 'accordion-up 0.2s ease-out',
  			'shimmer': 'shimmer 1.5s ease-in-out infinite',
  			'glow': 'glow 0.7s ease-in-out'
  		}
  	}
  },
  plugins: [require("tailwindcss-animate")],
} satisfies Config;
