# screenpipe playground

a flexible playground for displaying, testing, and exploring components with their associated code, documentation, and ai prompts.

<!-- <img width="1312" alt="screenshot of component playground" src="https://github.com/user-attachments/assets/3e5abd07-0a3c-4c3b-8351-5107beb4fb10"> -->

## components listed

- health status check
- latest UI record
- latest OCR record
- latest audio transcription
- screen streaming
- audio streaming

## features

- **interactive component display**: view rendered components in action
- **code inspection**: examine the full source code of each component
- **raw output**: see the raw api responses and data
- **ai prompt visibility**: view the prompts and context used to generate components
- **collapsible interface**: toggle component visibility for a cleaner workspace

## usage

the playground allows you to:

1. view rendered components in their intended state
2. inspect the raw output from api calls
3. study the complete component code
4. examine the ai prompts and context used to generate components

## component structure

each playground card includes:
- component title and collapsible interface
- tabs for different views (rendered output, raw output, code, ai prompt)
- copy functionality for sharing prompts and context

## getting started

1. install this pipe from UI and play with it
2. follow docs to create your pipe (it will create this app) (https://docs.screenpi.pe/plugins)
3. modify code from ready-to-use-examples directory

