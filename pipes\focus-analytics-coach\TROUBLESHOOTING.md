# Focus Analytics & Productivity Coach - Troubleshooting Guide

## Common Issues and Solutions

### 1. "Settings Stream Timeout" Error

**Problem**: <PERSON>sole shows "Error: settings stream timeout" or similar connection errors.

**Cause**: The plugin tries to connect to Screenpipe's settings API but Screenpipe isn't running or is on a different port.

**Solution**: This is expected behavior when Screenpipe isn't running. The plugin includes:
- ✅ **Automatic fallback** to default settings
- ✅ **Error suppression** to prevent console spam
- ✅ **Demo mode** that works without Screenpipe

**No action needed** - the plugin will work normally in demo mode.

### 2. Build Errors

**Problem**: TypeScript compilation errors or build failures.

**Solution**:
```bash
# Clean and reinstall dependencies
rm -rf node_modules .next
bun install
bun run build
```

### 3. Missing Dependencies

**Problem**: Module not found errors for analytics dependencies.

**Solution**:
```bash
# Install all required dependencies
bun add recharts date-fns fuse.js ml-matrix @tensorflow/tfjs framer-motion react-hook-form sonner lodash @types/lodash
```

### 4. Port Conflicts

**Problem**: "Port 3000 is already in use" error.

**Solution**:
```bash
# Use a different port
bun run dev -- --port 3001
```

### 5. TensorFlow.js Loading Issues

**Problem**: ML features not working or TensorFlow errors.

**Symptoms**:
- Charts not displaying
- Pattern recognition not working
- Console errors about TensorFlow kernels

**Solution**:
1. **Check browser compatibility** - TensorFlow.js requires modern browsers
2. **Clear browser cache** - Old cached versions can cause conflicts
3. **Disable ML features** if needed:
   ```typescript
   // In analytics config
   const config = {
     enableMLPredictions: false
   }
   ```

### 6. Demo Data Not Showing

**Problem**: Dashboard appears empty or shows loading indefinitely.

**Cause**: Demo data generation might be failing.

**Solution**:
1. **Check browser console** for JavaScript errors
2. **Refresh the page** - demo data is generated on load
3. **Verify API endpoints**:
   ```bash
   curl http://localhost:3000/api/analytics/process
   curl http://localhost:3000/api/analytics/daily-summary
   ```

### 7. Real Screenpipe Data Not Loading

**Problem**: Plugin shows demo data even when Screenpipe is running.

**Solution**:
1. **Verify Screenpipe is running** on the correct port (default: 3030)
2. **Check Screenpipe API**:
   ```bash
   curl http://localhost:3030/health
   ```
3. **Update API endpoints** in the plugin if Screenpipe uses different ports
4. **Check CORS settings** - browser might block cross-origin requests

### 8. Performance Issues

**Problem**: Plugin is slow or causes browser lag.

**Solutions**:
1. **Reduce data processing frequency**:
   - Increase cron job intervals
   - Limit historical data range
   
2. **Optimize TensorFlow.js**:
   - Use WebGL backend for better performance
   - Reduce model complexity
   
3. **Limit chart data points**:
   - Show only recent data by default
   - Implement data pagination

### 9. Styling Issues

**Problem**: UI components not displaying correctly.

**Solutions**:
1. **Check Tailwind CSS** is properly configured
2. **Verify component imports** from shadcn/ui
3. **Clear browser cache** and hard refresh
4. **Check for CSS conflicts** with other styles

### 10. API Endpoint Errors

**Problem**: 500 errors from `/api/analytics/*` endpoints.

**Solutions**:
1. **Check server logs** in the terminal running `bun run dev`
2. **Verify API route files** exist and are properly configured
3. **Test endpoints individually**:
   ```bash
   curl -X POST http://localhost:3000/api/analytics/process
   curl -X GET http://localhost:3000/api/analytics/daily-summary
   ```

## Development Tips

### Hot Reload Issues
If changes aren't reflecting:
```bash
# Restart development server
Ctrl+C
bun run dev
```

### TypeScript Errors
For strict type checking:
```bash
# Run type check separately
bun run type-check
```

### Debugging Analytics
Enable verbose logging:
```typescript
// In analytics engine
const DEBUG = true;
if (DEBUG) console.log('Analytics data:', data);
```

### Testing Without Screenpipe
The plugin is designed to work standalone:
- Demo data is automatically generated
- All features work in demo mode
- No external dependencies required

## Getting Help

### Check Logs
1. **Browser Console** - JavaScript errors and warnings
2. **Network Tab** - API request failures
3. **Terminal Output** - Server-side errors

### Verify Installation
```bash
# Check all dependencies are installed
bun install --check

# Verify build works
bun run build

# Test development server
bun run dev
```

### Reset to Clean State
```bash
# Complete reset
rm -rf node_modules .next
bun install
bun run build
bun run dev
```

## Known Limitations

1. **Screenpipe Connection**: Requires Screenpipe running for real data
2. **Browser Compatibility**: Modern browsers required for TensorFlow.js
3. **Performance**: Large datasets may impact performance
4. **Privacy**: All processing is local - no cloud features

## Support

For additional help:
1. Check the main README.md for setup instructions
2. Review the FEATURES.md for functionality details
3. Ensure all prerequisites are met per INSTALLATION.md
