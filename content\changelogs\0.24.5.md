Based on the provided commits, here is the changelog for the new Screenpipe update:

### **Improvements:**
- **Improved Obsidian pipe:** Enhanced functionality and performance of the Obsidian integration.
- **Refined Obsidian cron jobs:** Improved the scheduling and reliability of Obsidian-related cron tasks.

### **Fixes:**
- **Fixed issue with killing pipes:** Resolved a problem where pipes were not being terminated correctly, improving overall system stability. 

Please let me know if you need further assistance!

#### **Full Changelog:** [4a969..24e64](https://github.com/mediar-ai/screenpipe/compare/4a969..24e64)

