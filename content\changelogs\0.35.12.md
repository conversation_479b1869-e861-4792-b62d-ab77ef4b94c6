### **New Features:**
- **Added warning when using reasoning model for logs:** Implemented a warning system to alert users when the reasoning model is employed for logs, enhancing user awareness.

### **Improvements:**
- **Improved Obsidian prompts:** Enhanced the clarity and functionality of prompts within the Obsidian integration.
- **Organized files into subfolders:** Improved file organization by moving relevant files into subfolders for easier navigation.
- **Removed log noise:** Cleaned up unnecessary log outputs to streamline the logging process.

### **Fixes:**
- **Fixed Tauri arguments for Linux CI:** Addressed issues with Tauri command-line arguments specifically for Linux continuous integration to ensure proper functionality.

#### **Full Changelog:** [333c8..06e5c](https://github.com/mediar-ai/screenpipe/compare/333c8..06e5c)

