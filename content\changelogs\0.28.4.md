Based on your commits, here is the changelog for the new Screenpipe update:

### **New Features:**
- **Introduced broken icon in tray menu:** The tray menu now shows a broken icon when Screenpipe is down, enhancing user awareness of the application's status.
- **New logging worker implemented:** Added a new worker for improved logging functionality.

### **Improvements:**
- **Updated getting started documentation:** Enhanced the documentation to provide clearer guidance for new users.

### **Fixes:**
- **Fixed CLI CI issues:** Resolved issues with the command line interface continuous integration.
- **Resolved Obsidian pipe and pipes logging issues:** Fixed bugs related to logging in the Obsidian pipe functionality.
- **Corrected video rendering bug:** Fixed a bug that affected video rendering performance.

#### **Full Changelog:** [22ced..5c6d8](https://github.com/mediar-ai/screenpipe/compare/22ced..5c6d8)

