# # Run for macOS
# act -W .github/workflows/release-app.yml --container-architecture linux/amd64 -j publish-tauri -P macos-latest=-self-hosted

name: Release App

on:
  workflow_dispatch:
    inputs:
      commit_hash:
        description: "Commit hash to build from (optional)"
        required: false
      version:
        description: "Version to set in Cargo.toml (required if commit_hash is provided)"
        required: false
      needs_testing:
        description: "Whether this release needs community testing"
        type: boolean
        default: false
  push:
    branches: [main]

jobs:
  check_commit:
    runs-on: ubuntu-latest
    outputs:
      should_release: ${{ steps.check.outputs.should_release }}
      should_publish: ${{ steps.check.outputs.should_publish }}
      should_test: ${{ steps.check.outputs.should_test }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 2
      - id: check
        run: |
          COMMIT_MSG=$(git log -1 --pretty=%B)
          if echo "$COMMIT_MSG" | grep -q "release-app"; then
            echo "should_release=true" >> $GITHUB_OUTPUT
          else
            echo "should_release=false" >> $GITHUB_OUTPUT
          fi
          if echo "$COMMIT_MSG" | grep -q "release-app-publish"; then
            echo "should_publish=true" >> $GITHUB_OUTPUT
          else
            echo "should_publish=false" >> $GITHUB_OUTPUT
          fi
          
          # Check for explicit testing or skip flags first
          if echo "$COMMIT_MSG" | grep -q "release-app"; then
            echo "should_test=true" >> $GITHUB_OUTPUT
          elif echo "$COMMIT_MSG" | grep -q "release-app-skip-test"; then
            echo "should_test=false" >> $GITHUB_OUTPUT
          # For regular release-app (not publish), default to testing=true
          elif echo "$COMMIT_MSG" | grep -q "release-app" && ! echo "$COMMIT_MSG" | grep -q "release-app-publish"; then
            echo "should_test=true" >> $GITHUB_OUTPUT
          # For manual workflow runs, use the input parameter
          else
            echo "should_test=${{ github.event.inputs.needs_testing }}" >> $GITHUB_OUTPUT
          fi

  generate_changelog:
    needs: check_commit
    if: github.event_name == 'workflow_dispatch' || needs.check_commit.outputs.should_release == 'true'
    runs-on: ubuntu-latest

    permissions:
      contents: write

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Get version from Cargo.toml if not provided
        id: get_version
        run: |
          if [ -z "${{ github.event.inputs.version }}" ]; then
            VERSION=$(grep '^version = ' screenpipe-app-tauri/src-tauri/Cargo.toml | sed 's/version = "\(.*\)"/\1/')
            echo "VERSION=$VERSION" >> $GITHUB_ENV
          else
            echo "VERSION=${{ github.event.inputs.version }}" >> $GITHUB_ENV
          fi

      - name: Generate Changelog
        env:
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
          CN_API_KEY: ${{ secrets.CN_API_KEY }}
        run: .github/scripts/generate_changelog_md.sh ${{ env.VERSION }} ${{ github.event.inputs.commit_hash }}

      - name: Commit and push changelog files
        if: env.CHANGELOG_GENERATED == 1
        run: |
          git config user.name "GitHub Actions Bot"
          git config user.email "github-actions[bot]@users.noreply.github.com"
          git remote set-url origin https://x-access-token:${GH_TOKEN}@github.com/${GITHUB_REPOSITORY}.git
          git add .
          git commit -m "docs: add changelog for ${{ env.VERSION }}"
          git pull origin main
          git push origin main

  draft:
    needs: [check_commit, generate_changelog]
    if: github.event_name == 'workflow_dispatch' || needs.check_commit.outputs.should_release == 'true'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.commit_hash || github.ref }}

      - name: Update version in Cargo.toml
        if: github.event.inputs.commit_hash && github.event.inputs.version
        run: |
          if [[ "$OSTYPE" == "darwin"* ]]; then
            sed -i '' 's/^version = ".*"/version = "${{ github.event.inputs.version }}"/' screenpipe-app-tauri/src-tauri/Cargo.toml
          else
            sed -i 's/^version = ".*"/version = "${{ github.event.inputs.version }}"/' screenpipe-app-tauri/src-tauri/Cargo.toml
          fi

      - name: create draft release
        uses: crabnebula-dev/cloud-release@v0.2.6
        with:
          command: release draft ${{ secrets.CN_APP_SLUG }} --framework tauri
          api-key: ${{ secrets.CN_API_KEY }}

  publish-tauri:
    needs: [check_commit, draft]
    if: github.event_name == 'workflow_dispatch' || needs.check_commit.outputs.should_release == 'true'
    permissions:
      contents: write
    strategy:
      fail-fast: false
      matrix:
        include:
          - platform: "macos-latest"
            args: "--target aarch64-apple-darwin --features metal"
            target: aarch64-apple-darwin
            tauri-args: "--target aarch64-apple-darwin"
            os_type: "macos"
          - platform: "macos-latest"
            args: "--target x86_64-apple-darwin --features metal"
            target: x86_64-apple-darwin
            tauri-args: "--target x86_64-apple-darwin"
            os_type: "macos"
          - platform: "windows-2019"
            args: "--target x86_64-pc-windows-msvc --features mkl"
            pre-build-args: ""
            tauri-args: "--target x86_64-pc-windows-msvc"
            os_type: "windows"
          - platform: "ubuntu-22.04"
            args: "--target x86_64-unknown-linux-gnu"
            target: x86_64-unknown-linux-gnu
            tauri-args: ""
            os_type: "linux"

    runs-on: ${{ matrix.platform }}
    steps:
      - name: Enable Git long paths on Windows
        if: matrix.os_type == 'windows'
        shell: powershell
        run: |
          Write-Host "Enabling git core.longpaths..."
          git config --system core.longpaths true
          # Verify the setting
          $longpaths = git config --system --get core.longpaths
          Write-Host "core.longpaths is set to: $longpaths"
          if ($longpaths -ne "true") {
            throw "Failed to enable git long paths"
          }

      - uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.commit_hash || github.ref }}

      - name: Update version in Cargo.toml
        if: github.event.inputs.commit_hash && github.event.inputs.version && matrix.platform != 'macos-latest'
        shell: pwsh
        run: |
          $content = Get-Content screenpipe-app-tauri/src-tauri/Cargo.toml
          $content = $content -replace '^version = ".*"', ('version = "' + '${{ github.event.inputs.version }}' + '"')
          $content | Set-Content screenpipe-app-tauri/src-tauri/Cargo.toml -Force

      - name: Update version in Cargo.toml
        if: github.event.inputs.commit_hash && github.event.inputs.version && matrix.platform == 'macos-latest'
        shell: bash
        run: |
          sed -i '' 's/^version = ".*"/version = "${{ github.event.inputs.version }}"/' screenpipe-app-tauri/src-tauri/Cargo.toml

      - name: Setup Bun
        uses: oven-sh/setup-bun@v2
        with:
          bun-version: 1.1.43

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Set up Rust
        if: matrix.os_type != 'windows'
        uses: dtolnay/rust-toolchain@stable
        with:
          toolchain: stable
          targets: ${{ matrix.target }}

      - name: Install Rust
        if: matrix.os_type == 'windows'
        run: |
          Invoke-WebRequest https://static.rust-lang.org/rustup/dist/x86_64-pc-windows-gnu/rustup-init.exe -OutFile rustup-init.exe
          .\rustup-init.exe -y

      - name: setup rust path
        if: matrix.os_type == 'windows'
        shell: powershell
        run: |
          $env:PATH = "$env:USERPROFILE\.cargo\bin;$env:PATH"
          echo "$env:USERPROFILE\.cargo\bin" | Out-File -FilePath $env:GITHUB_PATH -Encoding utf8 -Append
          rustc --version # verify rust is available

      - name: Rust Cache
        uses: Swatinem/rust-cache@v2
        with:
          # Specify custom cache key
          key: ${{ runner.os }}-rust-${{ matrix.target }}-${{ hashFiles('**/Cargo.lock') }}
          # Cache these paths
          cache-directories: |
            ~/.cargo/registry/index/
            ~/.cargo/registry/cache/
            ~/.cargo/git/db/
            target/${{ matrix.target }}
            screenpipe-app-tauri/src-tauri/target
          # Shared cache across branches
          shared-key: "rust-cache-${{ matrix.target }}"
          # Save cache even on failed builds
          save-if: true

      - name: Cache Build Artifacts
        uses: actions/cache@v4
        with:
          path: |
            ~/.cargo/bin/
            ~/.cargo/.crates.toml
            ~/.cargo/.crates2.json
            target/${{ matrix.target }}/release
            screenpipe-app-tauri/src-tauri/target/release
          key: ${{ runner.os }}-artifacts-${{ matrix.target }}-${{ hashFiles('**/Cargo.lock') }}
          restore-keys: |
            ${{ runner.os }}-artifacts-${{ matrix.target }}-

      - name: Cache Homebrew packages
        if: matrix.platform == 'macos-latest'
        uses: actions/cache@v4
        with:
          path: |
            ~/Library/Caches/Homebrew
            /usr/local/Cellar/ffmpeg
            /usr/local/Cellar/pkg-config
          key: ${{ runner.os }}-brew-${{ hashFiles('.github/workflows/release-cli.yml') }}
          restore-keys: |
            ${{ runner.os }}-brew-

      - name: Cache Pre Build
        id: cache-pre-build
        uses: actions/cache@v4
        with:
          path: |
            screenpipe-app-tauri/src-tauri/ffmpeg
            screenpipe-app-tauri/src-tauri/tesseract-*
            screenpipe-app-tauri/node_modules
            screenpipe-app-tauri/src-tauri/target
            screenpipe-app-tauri/.tauri
            screenpipe-app-tauri/src-tauri/WixTools
            screenpipe-app-tauri/src-tauri/mkl
            # Ollama binaries and libs
            screenpipe-app-tauri/src-tauri/ollama-*
            screenpipe-app-tauri/src-tauri/lib/ollama
            # Swift UI monitoring binaries
            screenpipe-app-tauri/src-tauri/ui_monitor-*
            # FFmpeg downloads
            screenpipe-app-tauri/src-tauri/ffmpeg-*
            # Platform specific bins
            screenpipe-app-tauri/src-tauri/bun-*
            screenpipe-app-tauri/src-tauri/screenpipe-*
          key: ${{ matrix.platform }}-${{ matrix.target }}-pre-build-${{ hashFiles('**/Cargo.lock', '**/bun.lockb') }}
          restore-keys: |
            ${{ matrix.platform }}-${{ matrix.target }}-pre-build-

      - name: Install dependencies
        if: matrix.os_type == 'macos'
        shell: bash
        run: |
          brew install ffmpeg

      - name: Install dependencies
        if: matrix.os_type == 'linux'
        shell: bash
        run: |
          sudo apt-get update
          sudo apt-get install -y \
            g++ \
            ffmpeg \
            tesseract-ocr \
            cmake \
            clang \
            libavformat-dev \
            libavfilter-dev \
            libavdevice-dev \
            libssl-dev \
            libtesseract-dev \
            libxdo-dev \
            libsdl2-dev \
            libclang-dev \
            libxtst-dev \
            libx11-dev \
            libxext-dev \
            libxrandr-dev \
            libxinerama-dev \
            libxcursor-dev \
            libxi-dev \
            libgl1-mesa-dev \
            libasound2-dev \
            libpulse-dev \
            curl \
            pkg-config \
            libsqlite3-dev \
            libbz2-dev \
            zlib1g-dev \
            libonig-dev \
            libayatana-appindicator3-dev \
            libsamplerate-dev \
            libwebrtc-audio-processing-dev \
            libgtk-3-dev \
            librsvg2-dev \
            patchelf
          # download old versions because of EGL issues, see https://github.com/h3poteto/fedistar/issues/1717
          mkdir -p /tmp/ubuntu-packages
          cd /tmp/ubuntu-packages
          wget https://launchpadlibrarian.net/723972773/libwebkit2gtk-4.1-0_2.44.0-0ubuntu0.22.04.1_amd64.deb
          wget https://launchpadlibrarian.net/723972761/libwebkit2gtk-4.1-dev_2.44.0-0ubuntu0.22.04.1_amd64.deb
          wget https://launchpadlibrarian.net/723972770/libjavascriptcoregtk-4.1-0_2.44.0-0ubuntu0.22.04.1_amd64.deb
          wget https://launchpadlibrarian.net/723972746/libjavascriptcoregtk-4.1-dev_2.44.0-0ubuntu0.22.04.1_amd64.deb
          wget https://launchpadlibrarian.net/723972735/gir1.2-javascriptcoregtk-4.1_2.44.0-0ubuntu0.22.04.1_amd64.deb
          wget https://launchpadlibrarian.net/723972739/gir1.2-webkit2-4.1_2.44.0-0ubuntu0.22.04.1_amd64.deb
          wget https://launchpadlibrarian.net/606433947/libicu70_70.1-2ubuntu1_amd64.deb
          wget https://launchpadlibrarian.net/606433941/libicu-dev_70.1-2ubuntu1_amd64.deb
          wget https://launchpadlibrarian.net/606433945/icu-devtools_70.1-2ubuntu1_amd64.deb
          wget https://launchpadlibrarian.net/595623693/libjpeg8_8c-2ubuntu10_amd64.deb
          wget https://launchpadlibrarian.net/587202140/libjpeg-turbo8_2.1.2-0ubuntu1_amd64.deb
          wget https://launchpadlibrarian.net/592959859/xdg-desktop-portal-gtk_1.14.0-1build1_amd64.deb
          sudo apt-get install -y /tmp/ubuntu-packages/*.deb

      - name: Install frontend dependencies
        working-directory: ./screenpipe-app-tauri
        run: bun install

      - name: Install vcpkg
        if: matrix.os_type == 'windows'
        uses: lukka/run-vcpkg@v11
        with:
          vcpkgGitCommitId: "7adc2e4d49e8d0efc07a369079faa6bc3dbb90f3"

      - name: Set up MSVC
        if: matrix.os_type == 'windows'
        uses: ilammy/msvc-dev-cmd@v1

      - name: Install 7zip
        if: matrix.os_type == 'windows'
        shell: powershell
        run: |
          $7zipUrl = "https://7-zip.org/a/7z2301-x64.exe"
          $7zipInstaller = "7z-installer.exe"
          Invoke-WebRequest -Uri $7zipUrl -OutFile $7zipInstaller
          Start-Process -FilePath .\$7zipInstaller -Args "/S" -Wait
          Remove-Item $7zipInstaller
          echo "C:\Program Files\7-Zip" | Out-File -FilePath $env:GITHUB_PATH -Encoding utf8 -Append
          & "C:\Program Files\7-Zip\7z.exe" i

      - name: Cache LLVM
        if: matrix.os_type == 'windows'
        id: cache-llvm
        uses: actions/cache@v4
        with:
          path: |
            C:\Program Files\LLVM
            ${{ runner.temp }}\llvm
          key: llvm-10.0-windows-${{ hashFiles('.github/workflows/release-app.yml') }}

      - name: Install LLVM and Clang
        if: matrix.os_type == 'windows' && steps.cache-llvm.outputs.cache-hit != 'true'
        shell: powershell
        run: |
          # Create LLVM directory with proper permissions
          $llvmPath = "C:\Program Files\LLVM"
          if (!(Test-Path $llvmPath)) {
            New-Item -ItemType Directory -Force -Path $llvmPath | Out-Null
            $acl = Get-Acl $llvmPath
            $rule = New-Object System.Security.AccessControl.FileSystemAccessRule("Users","FullControl","Allow")
            $acl.SetAccessRule($rule)
            Set-Acl $llvmPath $acl
          }

          # Download and install LLVM
          $installerUrl = "https://github.com/llvm/llvm-project/releases/download/llvmorg-10.0.0/LLVM-10.0.0-win64.exe"
          $installerPath = Join-Path $env:TEMP "LLVM-10.0.0-win64.exe"

          Write-Host "Downloading LLVM installer..."
          Invoke-WebRequest -Uri $installerUrl -OutFile $installerPath

          Write-Host "Installing LLVM..."
          Start-Process -FilePath $installerPath -ArgumentList "/S","/D=$llvmPath" -Wait -NoNewWindow

          Remove-Item $installerPath -Force

          # Verify installation
          if (!(Test-Path "$llvmPath\bin\clang.exe")) {
            throw "LLVM installation failed - clang.exe not found"
          }
          Write-Host "LLVM installation completed successfully"

      - name: Install wget on Windows
        if: matrix.os_type == 'windows'
        shell: powershell
        env:
          SKIP_SCREENPIPE_SETUP: true
        run: |
          # Remove wget alias if it exists
          if (Test-Path Alias:wget) {
            Remove-Item Alias:wget -Force
          }

          # Create wget directory
          $wgetDir = "C:\wget"
          New-Item -ItemType Directory -Force -Path $wgetDir

          # Download wget executable
          $wgetUrl = "https://eternallybored.org/misc/wget/releases/wget-1.21.4-win64.zip"
          $wgetZip = "$wgetDir\wget.zip"

          # Use Invoke-WebRequest to download
          Invoke-WebRequest -Uri $wgetUrl -OutFile $wgetZip

          # Extract wget
          Expand-Archive -Path $wgetZip -DestinationPath $wgetDir -Force
          Remove-Item $wgetZip

          # Add to PATH for current session only
          $env:Path = "$wgetDir;$env:Path"

          # Verify installation
          & "$wgetDir\wget.exe" --version

      - name: Run pre_build.js
        env:
          SKIP_SCREENPIPE_SETUP: true
        run: bun ./scripts/pre_build.js ${{ matrix.pre-build-args }}
        working-directory: ./screenpipe-app-tauri

      - name: Build CLI
        if: matrix.os_type != 'windows'
        shell: bash
        run: |
          if [[ "${{ matrix.os_type }}" == "macos" ]]; then
            export PKG_CONFIG_PATH="/usr/local/opt/ffmpeg/lib/pkgconfig:$PKG_CONFIG_PATH"
            export PKG_CONFIG_ALLOW_CROSS=1
          fi
          cargo build --release -p screenpipe-server ${{ matrix.args }}

      - name: Build CLI on Windows
        if: matrix.os_type == 'windows'
        shell: powershell
        env:
          CARGO_PROFILE_RELEASE_STRIP: symbols
          CARGO_PROFILE_RELEASE_PANIC: abort
          CARGO_PROFILE_RELEASE_INCREMENTAL: false
        run: cargo build --release -p screenpipe-server ${{ matrix.args }}

        # Run pre build again to copy the CLI into app
      - name: Run pre_build.js
        run: |
          bun ./scripts/pre_build.js ${{ matrix.pre-build-args }}
        working-directory: ./screenpipe-app-tauri

      - uses: actions/setup-python@v5
        if: matrix.os_type == 'windows'
        with:
          python-version: '3.13'

      - name: Copy library for MKL
        if: matrix.os_type == 'windows'
        shell: powershell
        run: |
          # Create target directory
          $mkl_dir = "screenpipe-app-tauri/src-tauri/mkl"
          New-Item -ItemType Directory -Force -Path $mkl_dir | Out-Null

          # Install and copy Intel OpenMP
          python -m pip install --upgrade pip
          $temp_dir = "temp_omp"
          New-Item -ItemType Directory -Force -Path $temp_dir | Out-Null

          Write-Host "Installing Intel OpenMP..."
          python -m pip install intel-openmp --target $temp_dir

          Write-Host "Copying DLL files..."
          Get-ChildItem -Path $temp_dir -Recurse -Filter "*.dll" | ForEach-Object {
            Write-Host "Copying $_"
            Copy-Item $_.FullName -Destination $mkl_dir -Force
          }

          # Verify DLLs were copied
          $dll_count = (Get-ChildItem -Path $mkl_dir -Filter "*.dll").Count
          Write-Host "Found $dll_count DLLs in target directory"
          if ($dll_count -eq 0) {
            throw "No DLLs found in target directory!"
          }

          Remove-Item -Path $temp_dir -Recurse -Force

      - name: Copy library for vcredist
        if: matrix.os_type == 'windows'
        shell: powershell
        run: |
          # Create target directory
          $vcredist_dir = "screenpipe-app-tauri/src-tauri/vcredist"
          New-Item -ItemType Directory -Force -Path $vcredist_dir | Out-Null

          # Install and copy VcRedist
          Set-ExecutionPolicy Bypass -Scope Process -Force
          [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
          iex ((New-Object System.Net.WebClient).DownloadString('https://vcredist.com/install.ps1'))

          Write-Host "Copying DLL files..."
          Copy-Item C:\Windows\System32\vcruntime140.dll -Destination $vcredist_dir -Force

          # Verify DLLs were copied
          $dll_count = (Get-ChildItem -Path $vcredist_dir -Filter "*.dll").Count
          Write-Host "Found $dll_count DLLs in target directory"
          if ($dll_count -eq 0) {
            throw "No DLLs found in target directory!"
          }

      - name: Build
        uses: tauri-apps/tauri-action@v0.5.17
        env:
          # for updater
          TAURI_SIGNING_PRIVATE_KEY: ${{ secrets.TAURI_PRIVATE_KEY }}
          TAURI_SIGNING_PRIVATE_KEY_PASSWORD: ${{ secrets.TAURI_KEY_PASSWORD }}
          # for release
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          # for macos signing
          APPLE_CERTIFICATE: ${{ secrets.APPLE_CERTIFICATE }}
          APPLE_CERTIFICATE_PASSWORD: ${{ secrets.APPLE_CERTIFICATE_PASSWORD }}
          APPLE_SIGNING_IDENTITY: ${{ secrets.APPLE_SIGNING_IDENTITY }}
          APPLE_ID: ${{ secrets.APPLE_ID }}
          APPLE_PASSWORD: ${{ secrets.APPLE_PASSWORD }}
          APPLE_TEAM_ID: ${{ secrets.APPLE_TEAM_ID }}
          # https://tauri.app/v1/guides/distribution/sign-macos
          CI: true
          # Optimize for build speed on Windows
          CARGO_PROFILE_RELEASE_LTO: ${{ matrix.os_type == 'windows' && 'thin' || 'false' }}
          CARGO_PROFILE_RELEASE_OPT_LEVEL: ${{ matrix.os_type == 'windows' && '2' || '3' }}
          CARGO_PROFILE_RELEASE_CODEGEN_UNITS: ${{ matrix.os_type == 'windows' && '16' || '16' }}
          # Enable incremental compilation for Windows
          CARGO_INCREMENTAL: ${{ matrix.os_type == 'windows' && 'true' || 'false' }}
          # Windows specific optimizations
          CARGO_PROFILE_RELEASE_STRIP: ${{ matrix.os_type == 'windows' && 'symbols' || 'none' }}
          CARGO_PROFILE_RELEASE_PANIC: ${{ matrix.os_type == 'windows' && 'abort' || 'unwind' }}
          CARGO_PROFILE_RELEASE_INCREMENTAL: ${{ matrix.os_type == 'windows' && 'false' || 'true' }}
          RUSTFLAGS: ${{ matrix.os_type == 'windows' && '-C target-feature=+crt-static -C link-arg=/LTCG' || '' }}
          VCPKG_STATIC_LINKAGE: "true"
          KNF_STATIC_CRT: ${{ matrix.os_type == 'windows' && '1' || '' }}
        with:
          args: ${{ matrix.tauri-args }}
          projectPath: "./screenpipe-app-tauri"
          tauriScript: bunx tauri -vvv
          retryAttempts: 3

      - name: Download and Run CLI Manually (Windows)
        if: matrix.os_type == 'windows'
        shell: powershell
        env:
          CN_API_KEY: ${{ secrets.CN_API_KEY }}
          CN_VERBOSE: "2"
          USER_AGENT: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
          MAX_RETRIES: 5
          RETRY_DELAY: 30
        run: |
          # create temp directory for cli
          $cliDir = Join-Path $Env:GITHUB_WORKSPACE "cn-cli-temp"
          New-Item -ItemType Directory -Force -Path $cliDir

          # download cli
          $cliUrl = "https://cdn.crabnebula.app/download/crabnebula/cn-cli/latest/platform/windows-binary-x86_64"
          $cliExe = Join-Path $cliDir "cn.exe"
          Write-Host "downloading crabnebula cli..."
          Invoke-WebRequest -Uri $cliUrl -UserAgent $env:USER_AGENT -OutFile $cliExe

          # move to the directory we want to upload from
          cd screenpipe-app-tauri/src-tauri

          # upload assets with retry logic
          $cliFullPath = Join-Path $cliDir "cn.exe"
          Write-Host "uploading assets from $(pwd) using $cliFullPath..."

          $attempt = 1
          $success = $false

          while (-not $success -and $attempt -le $env:MAX_RETRIES) {
            Write-Host "Attempt $attempt of $env:MAX_RETRIES"
            try {
              & $cliFullPath release upload ${{ secrets.CN_APP_SLUG }} --framework tauri -vv
              if ($LASTEXITCODE -eq 0) {
                $success = $true
                Write-Host "Upload successful!"
              } else {
                throw "CLI returned exit code $LASTEXITCODE"
              }
            } catch {
              Write-Host "Error during attempt $attempt`: $_"
              if ($attempt -lt $env:MAX_RETRIES) {
                Write-Host "Waiting $env:RETRY_DELAY seconds before retry..."
                Start-Sleep -Seconds $env:RETRY_DELAY
              }
            }
            $attempt++
          }

          if (-not $success) {
            Write-Host "Failed to upload after $env:MAX_RETRIES attempts"
            exit 1
          }

      - name: Upload Assets to CrabNebula Cloud (Non-Windows)
        if: matrix.os_type != 'windows'
        uses: crabnebula-dev/cloud-release@v0.2.6
        env:
          CN_VERBOSE: "2"
        with:
          command: release upload ${{ secrets.CN_APP_SLUG }} --framework tauri -vv
          api-key: ${{ secrets.CN_API_KEY }}
          path: ./screenpipe-app-tauri/src-tauri

  publish-release:
    needs: [check_commit, publish-tauri]
    if: needs.check_commit.outputs.should_publish == 'true' && (needs.publish-tauri.result == 'success' || needs.retry-win.result == 'success')
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.commit_hash || github.ref }}

      - name: Get version from Cargo.toml
        id: get_version
        run: |
          VERSION=$(grep '^version = ' screenpipe-app-tauri/src-tauri/Cargo.toml | sed 's/version = "\(.*\)"/\1/')
          echo "VERSION=$VERSION" >> $GITHUB_ENV

      - name: Publish Release on CrabNebula Cloud
        uses: crabnebula-dev/cloud-release@v0.2.6
        with:
          command: release publish ${{ secrets.CN_APP_SLUG }} ${{ env.VERSION }}
          api-key: ${{ secrets.CN_API_KEY }}
          
      - name: Create testing notification
        if: needs.check_commit.outputs.should_test == 'true'
        uses: actions/github-script@v6
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            // Get CrabNebula release URL from environment
            const releaseUrl = "https://web.crabnebula.cloud/mediar/screenpipe/releases";
            
            // Create release discussion issue
            const message = `
            ## 🧪 testing needed for v${{ env.VERSION }}
            
            this release needs community testing. a testing bounty issue will be created shortly.
            
            - download the release: [crabnebula cloud](${ releaseUrl })
            - testing guidelines: [testing.md](https://github.com/mediar-ai/screenpipe/blob/main/TESTING.md)
            
            each tester who provides quality feedback will receive a $20 bounty through algora.
            `;
            
            // Create a new issue for testing discussion
            const issue = await github.rest.issues.create({
              owner: context.repo.owner,
              repo: context.repo.repo,
              title: `🧪 Testing Discussion: v${{ env.VERSION }}`,
              body: message,
              labels: ['testing', 'discussion']
            });
            
            console.log(`Created testing discussion issue: ${issue.data.html_url}`);
