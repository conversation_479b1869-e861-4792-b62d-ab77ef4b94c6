Based on the commits provided, here is the changelog for the new Screenpipe update:

### **New Features:**
- **Added browser URL to search pipe for Windows:** Users can now search using browser URLs on Windows platforms.
- **Added LLM links:** Introduced new links for improved access to language model resources.
- **Updated main page:** Enhanced main page with new content for better user engagement.
- **Improved stream capabilities:** Enhanced streaming features for better performance and reliability.
- **Added OCR component:** Introduced an optical character recognition component for enhanced functionality.
- **Added health monitoring component:** Implemented a health monitoring component to track system performance.

### **Improvements:**
- **Updated UI monitoring component:** Improved the UI monitoring component to provide better insights and usability.
- **Refactored components for clarity:** Refactored various components for improved clarity and maintainability.
- **Updated card display:** Enhanced the card display for a more user-friendly experience.

### **Fixes:**
- **Fixed date and time input issue:** Resolved an issue with the date and time input not working correctly in the application.
- **Fixed various build issues:** Addressed multiple build issues to ensure smoother installations and updates.

This changelog highlights the significant changes that bring clear value to customers based on the provided commits.

#### **Full Changelog:** [64c24..a1167](https://github.com/mediar-ai/screenpipe/compare/64c24..a1167)

