"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@screenpipe";
exports.ids = ["vendor-chunks/@screenpipe"];
exports.modules = {

/***/ "(action-browser)/./node_modules/@screenpipe/js/dist/index.js":
/*!***************************************************!*\
  !*** ./node_modules/@screenpipe/js/dist/index.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getDefaultSettings: () => (/* binding */ k),\n/* harmony export */   pipe: () => (/* binding */ ae)\n/* harmony export */ });\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs/promises */ \"fs/promises\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var os__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! os */ \"os\");\n/* harmony import */ var child_process__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! child_process */ \"child_process\");\n/* harmony import */ var net__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! net */ \"net\");\n/* harmony import */ var posthog_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! posthog-js */ \"(action-browser)/./node_modules/posthog-js/dist/module.js\");\nvar S=(n,e=\"\")=>Object.keys(n).reduce((t,r)=>{let s=e.length?e+\".\":\"\";return typeof n[r]==\"object\"&&n[r]!==null&&!Array.isArray(n[r])?Object.assign(t,S(n[r],s+r)):t[s+r]=n[r],t},{}),P=n=>{let e={};for(let t in n){let r=t.split(\".\"),s=e;for(let a=0;a<r.length;a++){let o=r[a];a===r.length-1?s[o]=n[t]:(s[o]=s[o]||{},s=s[o]);}}return e};function C(n){return n.replace(/([-_][a-z])/g,e=>e.toUpperCase().replace(\"-\",\"\").replace(\"_\",\"\"))}function m(n){return n.replace(/[A-Z]/g,e=>`_${e.toLowerCase()}`)}function h(n){return Object.keys(n).reduce((e,t)=>{let r=m(t);return e[r]=h(n[t]),e},{})}function c(n){return Array.isArray(n)?n.map(c):n!==null&&typeof n==\"object\"?Object.keys(n).reduce((e,t)=>{let r=C(t);return e[r]=c(n[t]),e},{}):n}function k(){return {openaiApiKey:\"\",deepgramApiKey:\"\",aiModel:\"gpt-4o\",aiUrl:\"https://api.openai.com/v1\",customPrompt:\"Rules:\\n    - You can analyze/view/show/access videos to the user by putting .mp4 files in a code block (we'll render it) like this: `/users/video.mp4`, use the exact, absolute, file path from file_path property\\n    - Do not try to embed video in links (e.g. [](.mp4) or https://.mp4) instead put the file_path in a code block using backticks\\n    - Do not put video in multiline code block it will not render the video (e.g. ```bash\\n.mp4``` IS WRONG) instead using inline code block with single backtick\\n    - Always answer my question/intent, do not make up things\\n    \",port:3030,dataDir:\"default\",disableAudio:false,ignoredWindows:[],includedWindows:[],aiProviderType:\"openai\",embeddedLLM:{enabled:false,model:\"llama3.2:1b-instruct-q4_K_M\",port:11434},enableFrameCache:true,enableUiMonitoring:false,aiMaxContextChars:512e3,analyticsEnabled:true,user:{token:\"\"},customSettings:{},monitorIds:[\"default\"],audioDevices:[\"default\"],audioTranscriptionEngine:\"whisper-large-v3-turbo\",enableRealtimeAudioTranscription:false,realtimeAudioTranscriptionEngine:\"deepgram\",disableVision:false,aiPresets:[]}}var y=k(),f=class{settings;storePath;initialized=false;constructor(){this.settings=y,this.storePath=\"\";}async getStorePath(){let e=process.platform,t=os__WEBPACK_IMPORTED_MODULE_2__.homedir(),r;switch(e){case\"darwin\":r=path__WEBPACK_IMPORTED_MODULE_1__.join(t,\"Library\",\"Application Support\",\"screenpipe\");break;case\"linux\":let o=process.env.XDG_DATA_HOME||path__WEBPACK_IMPORTED_MODULE_1__.join(t,\".local\",\"share\");r=path__WEBPACK_IMPORTED_MODULE_1__.join(o,\"screenpipe\");break;case\"win32\":r=path__WEBPACK_IMPORTED_MODULE_1__.join(process.env.LOCALAPPDATA||path__WEBPACK_IMPORTED_MODULE_1__.join(t,\"AppData\",\"Local\"),\"screenpipe\");break;default:throw new Error(`unsupported platform: ${e}`)}let s=path__WEBPACK_IMPORTED_MODULE_1__.join(r,\"profiles.bin\"),a=\"default\";try{let o=await fs_promises__WEBPACK_IMPORTED_MODULE_0__.readFile(s),u=JSON.parse(o.toString());u.activeProfile&&(a=u.activeProfile);}catch{}return a===\"default\"?path__WEBPACK_IMPORTED_MODULE_1__.join(r,\"store.bin\"):path__WEBPACK_IMPORTED_MODULE_1__.join(r,`store-${a}.bin`)}async init(){if(!fs_promises__WEBPACK_IMPORTED_MODULE_0__||!path__WEBPACK_IMPORTED_MODULE_1__)throw new Error(\"failed to load required modules\");this.storePath=await this.getStorePath();try{await fs_promises__WEBPACK_IMPORTED_MODULE_0__.mkdir(path__WEBPACK_IMPORTED_MODULE_1__.dirname(this.storePath),{recursive:!0});let e=await fs_promises__WEBPACK_IMPORTED_MODULE_0__.readFile(this.storePath),t=JSON.parse(e.toString());this.settings={...y,...P(t)},this.initialized=!0;}catch(e){if(e.code===\"ENOENT\")await this.save(),this.initialized=true;else throw e}}async save(){await fs_promises__WEBPACK_IMPORTED_MODULE_0__.mkdir(path__WEBPACK_IMPORTED_MODULE_1__.dirname(this.storePath),{recursive:true});let e=S(this.settings);await fs_promises__WEBPACK_IMPORTED_MODULE_0__.writeFile(this.storePath,JSON.stringify(e,null,2));}async get(e){return this.initialized||await this.init(),this.settings[e]}async set(e,t){this.initialized||await this.init(),this.settings[e]=t,await this.save();}async getAll(){return await this.init(),{...this.settings}}async update(e){this.initialized||await this.init(),this.settings={...this.settings,...e},await this.save();}async reset(){this.settings={...y},await this.save();}async resetKey(e){this.initialized||await this.init(),this.settings[e]=y[e],await this.save();}async getCustomSetting(e,t){return this.initialized||await this.init(),this.settings.customSettings?.[e]?.[t]}async setCustomSetting(e,t,r){this.initialized||await this.init(),this.settings.customSettings=this.settings.customSettings||{},this.settings.customSettings[e]=this.settings.customSettings[e]||{},this.settings.customSettings[e][t]=r,await this.save();}async getNamespaceSettings(e){return this.initialized||await this.init(),this.settings.customSettings?.[e]}async updateNamespaceSettings(e,t){this.initialized||await this.init(),this.settings.customSettings=this.settings.customSettings||{},this.settings.customSettings[e]=t,await this.save();}};async function J(){return new Promise((n,e)=>{let t=(0,net__WEBPACK_IMPORTED_MODULE_4__.createServer)();t.unref(),t.on(\"error\",e),t.listen(0,()=>{let r=t.address().port;t.close(()=>n(r));});})}var g=class{actionServerPort;actionServerProcess;async send(e){this.actionServerPort||(this.actionServerPort=await J(),this.actionServerProcess=(0,child_process__WEBPACK_IMPORTED_MODULE_3__.fork)(\"./inbox-server.js\",[this.actionServerPort.toString()])),e.actions&&(e.actions=e.actions.map(t=>{let r=crypto.randomUUID();return {label:t.label,action:r,port:this.actionServerPort,callback:t.callback}}));try{return (await fetch(\"http://localhost:11435/inbox\",{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({...e,type:\"inbox\",actionServerPort:this.actionServerPort})})).ok}catch(t){return console.error(\"failed to send inbox message:\",t),false}}};var w=class{async list(){try{let t=await fetch(\"http://localhost:3030/pipes/list\",{method:\"GET\",headers:{\"Content-Type\":\"application/json\"}});if(!t.ok)throw new Error(`http error! status: ${t.status}`);return {success:!0,data:(await t.json()).data}}catch(e){return console.error(\"failed to list pipes:\",e),{success:false,error:e}}}async download(e){try{let r=await fetch(\"http://localhost:3030/pipes/download\",{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({url:e})});if(!r.ok)throw new Error(`http error! status: ${r.status}`);return {success:!0,data:(await r.json()).data}}catch(t){return console.error(\"failed to download pipe:\",t),{success:false,error:t}}}async enable(e){try{return (await fetch(\"http://localhost:3030/pipes/enable\",{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({pipe_id:e})})).ok}catch(t){return console.error(\"failed to enable pipe:\",t),false}}async disable(e){try{return (await fetch(\"http://localhost:3030/pipes/disable\",{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({pipe_id:e})})).ok}catch(t){return console.error(\"failed to disable pipe:\",t),false}}async update(e,t){try{return (await fetch(\"http://localhost:3030/pipes/update\",{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({pipe_id:e,config:t})})).ok}catch(r){return console.error(\"failed to update pipe:\",r),false}}async info(e){try{let r=await fetch(`http://localhost:3030/pipes/info/${e}`,{method:\"GET\",headers:{\"Content-Type\":\"application/json\"}});if(!r.ok)throw new Error(`http error! status: ${r.status}`);return {success:!0,data:(await r.json()).data}}catch(t){return console.error(\"failed to get pipe info:\",t),{success:false,error:t}}}async downloadPrivate(e,t,r){try{let a=await fetch(\"http://localhost:3030/pipes/download-private\",{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({url:e,pipe_name:t,pipe_id:r})});if(!a.ok)throw new Error(`http error! status: ${a.status}`);return {success:!0,data:(await a.json()).data}}catch(s){return console.error(\"failed to download private pipe:\",s),{success:false,error:s}}}async delete(e){try{return (await fetch(\"http://localhost:3030/pipes/delete\",{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({pipe_id:e})})).ok}catch(t){return console.error(\"failed to delete pipe:\",t),false}}};var v=false,l=null,I=\"phc_Bt8GoTBPgkCpDrbaIZzJIEYt0CrJjhBiuLaBck1clce\",U=\"https://eu.i.posthog.com\";function O(n){l=n;}function $(n,e){!v&&l&&(l.init(I,{api_host:U,distinct_id:n,email:e}),l.identify(n,{email:e}),v=true);}async function b(n,e){if(!l)return;$(e?.distinct_id,e?.email);let{distinct_id:t,...r}=e||{};l.capture(n,r);}async function A(n,e){if(!l)return;$(e?.distinct_id,e?.email);let{distinct_id:t,...r}=e||{};l.capture(n,{feature:\"main\",...r});}var x=class{baseUrl;pixel;constructor(e=\"http://localhost:3030\"){this.baseUrl=e,this.pixel={type:t=>this.sendInputControl({type:\"WriteText\",data:t}),press:t=>this.sendInputControl({type:\"KeyPress\",data:t}),moveMouse:(t,r)=>this.sendInputControl({type:\"MouseMove\",data:{x:t,y:r}}),click:t=>this.sendInputControl({type:\"MouseClick\",data:t})};}async sendInputControl(e){try{let t=await fetch(`${this.baseUrl}/experimental/operator/pixel`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({action:e})});if(!t.ok)throw new Error(`http error! status: ${t.status}`);return (await t.json()).success}catch(t){return console.error(\"failed to control input:\",t),false}}locator(e){if(e.role&&e.id)throw new Error(\"only one of 'role' or 'id' can be specified. need both? dm us!\");let t={app_name:e.app,window_name:e.window,locator:e.role||(e.id?`#${e.id}`:\"\"),use_background_apps:e.useBackgroundApps,activate_app:e.activateApp};return new T(this.baseUrl,t)}async click(e){let t={app_name:e.app,window_name:e.window,locator:`#${e.id}`,use_background_apps:e.useBackgroundApps,activate_app:e.activateApp!==false},r=await fetch(`${this.baseUrl}/experimental/operator/click`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({selector:t})});if(!r.ok){let a=await r.text();console.log(\"error response:\",a);try{let o=JSON.parse(a);throw new Error(`failed to click element: ${o.error||r.statusText}`)}catch{throw new Error(`failed to click element (status ${r.status}): ${a||r.statusText}`)}}let s=await r.json();if(console.log(\"debug: click response data:\",JSON.stringify(s,null,2)),!s.success)throw new Error(`click operation failed: ${s.error||\"unknown error\"}`);return s.result?c(s.result):s.method?{method:s.method,coordinates:s.coordinates,details:s.details||\"Click operation succeeded\"}:(console.log(\"warning: click response missing expected structure, creating fallback object\"),{method:\"MouseSimulation\",coordinates:undefined,details:\"Click operation succeeded but returned unexpected data structure\"})}async fill(e){let t={app_name:e.app,locator:`#${e.id}`,use_background_apps:e.useBackgroundApps,activate_app:e.activateApp!==false};console.log(\"selector\",t);let r=await fetch(`${this.baseUrl}/experimental/operator/type`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({selector:t,text:e.value})});if(!r.ok){let a=await r.text();console.log(\"error response:\",a);try{let o=JSON.parse(a);throw new Error(`failed to type text: ${o.error||r.statusText}`)}catch{throw new Error(`failed to type text (status ${r.status}): ${a||r.statusText}`)}}return (await r.json()).success}getByRole(e,t){return this.locator({app:t?.app||\"\",window:t?.window,role:e,useBackgroundApps:t?.useBackgroundApps,activateApp:t?.activateApp})}getById(e,t){return this.locator({app:t?.app||\"\",window:t?.window,id:e,useBackgroundApps:t?.useBackgroundApps,activateApp:t?.activateApp})}async getText(e){let t={appName:e.app,windowName:e.window,maxDepth:e.maxDepth,useBackgroundApps:e.useBackgroundApps,activateApp:e.activateApp!==false},r=await fetch(`${this.baseUrl}/experimental/operator/get_text`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify(h(t))});if(!r.ok){let a=await r.text();console.log(\"error response:\",a);try{let o=JSON.parse(a);throw new Error(`failed to get text: ${o.error||r.statusText}`)}catch{throw new Error(`failed to get text (status ${r.status}): ${a||r.statusText}`)}}let s=await r.json();if(console.log(\"debug: text response data:\",JSON.stringify(s,null,2)),!s.success)throw new Error(`get_text operation failed: ${s.error||\"unknown error\"}`);return c(s)}async getInteractableElements(e){let t={appName:e.app,windowName:e.window,withTextOnly:e.withTextOnly,interactableOnly:e.interactableOnly,includeSometimesInteractable:e.includeSometimesInteractable,maxElements:e.maxElements,useBackgroundApps:e.useBackgroundApps,activateApp:e.activateApp},r=await fetch(`${this.baseUrl}/experimental/operator/list-interactable-elements`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify(h(t))});if(!r.ok){let a=await r.text();console.log(\"error response:\",a);try{let o=JSON.parse(a);throw new Error(`failed to get interactable elements: ${o.error||r.statusText}`)}catch{throw new Error(`failed to get interactable elements (status ${r.status}): ${a||r.statusText}`)}}let s=await r.json();return console.log(\"debug: text response data:\",JSON.stringify(s,null,2)),c(s)}async clickByIndex(e){let t=await fetch(`${this.baseUrl}/experimental/operator/click-by-index`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({element_index:e})});if(!t.ok){let s=await t.text();console.log(\"error response:\",s);try{let a=JSON.parse(s);throw new Error(`failed to click element by index: ${a.error||t.statusText}`)}catch{throw new Error(`failed to click element by index (status ${t.status}): ${s||t.statusText}`)}}let r=await t.json();if(!r.success)throw new Error(`click operation failed: ${r.message||\"unknown error\"}`);return r.success}async typeByIndex(e,t){let r=await fetch(`${this.baseUrl}/experimental/operator/type-by-index`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({element_index:e,text:t})});if(!r.ok){let a=await r.text();console.log(\"error response:\",a);try{let o=JSON.parse(a);throw new Error(`failed to type text into element by index: ${o.error||r.statusText}`)}catch{throw new Error(`failed to type text into element by index (status ${r.status}): ${a||r.statusText}`)}}let s=await r.json();if(!s.success)throw new Error(`type operation failed: ${s.message||\"unknown error\"}`);return s.success}async pressKey(e){let t={app_name:e.app,window_name:e.window,locator:`#${e.id}`,use_background_apps:e.useBackgroundApps,activate_app:e.activateApp!==false},r=await fetch(`${this.baseUrl}/experimental/operator/press-key`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify(h({selector:t,keyCombo:e.key}))});if(!r.ok){let a=await r.text();console.log(\"error response:\",a);try{let o=JSON.parse(a);throw new Error(`failed to press key: ${o.error||r.statusText}`)}catch{throw new Error(`failed to press key (status ${r.status}): ${a||r.statusText}`)}}return (await r.json()).success}async pressKeyByIndex(e,t){let r=await fetch(`${this.baseUrl}/experimental/operator/press-key-by-index`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({element_index:e,key_combo:t})});if(!r.ok){let a=await r.text();console.log(\"error response:\",a);try{let o=JSON.parse(a);throw new Error(`failed to press key on element by index: ${o.error||r.statusText}`)}catch{throw new Error(`failed to press key on element by index (status ${r.status}): ${a||r.statusText}`)}}let s=await r.json();if(!s.success)throw new Error(`press key operation failed: ${s.message||\"unknown error\"}`);return s.success}async openApplication(e){let t=await fetch(`${this.baseUrl}/experimental/operator/open-application`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({app_name:e})});if(!t.ok){let s=await t.text();console.log(\"error response:\",s);try{let a=JSON.parse(s);throw new Error(`failed to open application: ${a.error||t.statusText}`)}catch{throw new Error(`failed to open application (status ${t.status}): ${s||t.statusText}`)}}let r=await t.json();if(!r.success)throw new Error(`open application operation failed: ${r.message||\"unknown error\"}`);return r.success}async openUrl(e,t){let r=await fetch(`${this.baseUrl}/experimental/operator/open-url`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({url:e,browser:t})});if(!r.ok){let a=await r.text();console.log(\"error response:\",a);try{let o=JSON.parse(a);throw new Error(`failed to open url: ${o.error||r.statusText}`)}catch{throw new Error(`failed to open url (status ${r.status}): ${a||r.statusText}`)}}let s=await r.json();if(!s.success)throw new Error(`open url operation failed: ${s.message||\"unknown error\"}`);return s.success}async scroll(e){let t={app_name:e.app,window_name:e.window,locator:e.id?`#${e.id}`:\"\",use_background_apps:e.useBackgroundApps,activate_app:e.activateApp!==false},r=await fetch(`${this.baseUrl}/experimental/operator/scroll`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({selector:t,direction:e.direction,amount:e.amount})});if(!r.ok){let a=await r.text();console.log(\"error response:\",a);try{let o=JSON.parse(a);throw new Error(`failed to scroll element: ${o.error||r.statusText}`)}catch{throw new Error(`failed to scroll element (status ${r.status}): ${a||r.statusText}`)}}return (await r.json()).success}},T=class{baseUrl;selector;constructor(e,t){this.baseUrl=e,this.selector=t;}async first(e){let t=await this.all(1,e);return t.length>0?t[0]:null}async all(e,t){let r=await fetch(`${this.baseUrl}/experimental/operator`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({selector:this.selector,max_results:e,max_depth:t})});if(!r.ok){let a=await r.text();console.log(\"error response:\",a);try{let o=JSON.parse(a);throw new Error(`failed to find elements: ${o.error||r.statusText}`)}catch{throw new Error(`failed to find elements (status ${r.status}): ${a||r.statusText}`)}}return (await r.json()).data}async click(){let e=await fetch(`${this.baseUrl}/experimental/operator/click`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({selector:{...this.selector,activate_app:this.selector.activate_app!==false}})});if(!e.ok){let r=await e.text();console.log(\"error response:\",r);try{let s=JSON.parse(r);throw new Error(`failed to click element: ${s.error||e.statusText}`)}catch{throw new Error(`failed to click element (status ${e.status}): ${r||e.statusText}`)}}let t=await e.json();if(console.log(\"debug: click response data:\",JSON.stringify(t,null,2)),!t.success)throw new Error(`click operation failed: ${t.error||\"unknown error\"}`);return t.result?t.result:t.method?{method:t.method,coordinates:t.coordinates,details:t.details||\"Click operation succeeded\"}:(console.log(\"warning: click response missing expected structure, creating fallback object\"),{method:\"MouseSimulation\",coordinates:undefined,details:\"Click operation succeeded but returned unexpected data structure\"})}async fill(e){let t=await fetch(`${this.baseUrl}/experimental/operator/type`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({selector:{...this.selector,activate_app:this.selector.activate_app!==false},text:e})});if(!t.ok){let s=await t.text();console.log(\"error response:\",s);try{let a=JSON.parse(s);throw new Error(`failed to type text: ${a.error||t.statusText}`)}catch{throw new Error(`failed to type text (status ${t.status}): ${s||t.statusText}`)}}return (await t.json()).success}async exists(){try{return !!await this.first()}catch{return false}}async waitFor(e={}){let t=Date.now(),r=e.timeout||3e4;for(;Date.now()-t<r;){try{let s=await this.first();if(s)return s}catch{}await new Promise(s=>setTimeout(s,100));}return null}async pressKey(e){let t=await fetch(`${this.baseUrl}/experimental/operator/press-key`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({selector:{...this.selector,activate_app:this.selector.activate_app!==false},key_combo:e})});if(!t.ok){let s=await t.text();console.log(\"error response:\",s);try{let a=JSON.parse(s);throw new Error(`failed to press key: ${a.error||t.statusText}`)}catch{throw new Error(`failed to press key (status ${t.status}): ${s||t.statusText}`)}}return (await t.json()).success}async scroll(e,t){let r=await fetch(`${this.baseUrl}/experimental/operator/scroll`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({selector:{...this.selector,activate_app:this.selector.activate_app!==false},direction:e,amount:t})});if(!r.ok){let a=await r.text();console.log(\"error response:\",a);try{let o=JSON.parse(a);throw new Error(`failed to scroll element: ${o.error||r.statusText}`)}catch{throw new Error(`failed to scroll element (status ${r.status}): ${a||r.statusText}`)}}return (await r.json()).success}};O({init:posthog_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"].init.bind(posthog_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),identify:posthog_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"].identify.bind(posthog_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),capture:posthog_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"].capture.bind(posthog_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])});var E=class{analyticsInitialized=false;analyticsEnabled=true;settings=new f;inbox=new g;pipes=new w;operator=new x;async sendDesktopNotification(e){await this.initAnalyticsIfNeeded();let t=\"http://localhost:11435\";try{return await fetch(`${t}/notify`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify(e)}),await b(\"notification_sent\",{success:!0}),!0}catch(r){return console.error(\"failed to send notification:\",r),false}}async queryScreenpipe(e){await this.initAnalyticsIfNeeded();let t=new URLSearchParams;Object.entries(e).forEach(([s,a])=>{if(a!==undefined&&a!==\"\")if(s===\"speakerIds\"&&Array.isArray(a))a.length>0&&t.append(m(s),a.join(\",\"));else {let o=m(s);t.append(o,a.toString());}});let r=`http://localhost:3030/search?${t}`;try{let s=await fetch(r);if(!s.ok){let o=await s.text(),u;try{u=JSON.parse(o),console.error(\"screenpipe api error:\",{status:s.status,error:u});}catch{console.error(\"screenpipe api error:\",{status:s.status,error:o});}throw new Error(`http error! status: ${s.status}`)}let a=await s.json();return await b(\"search_performed\",{content_type:e.contentType,result_count:a.pagination.total}),c(a)}catch(s){throw console.error(\"error querying screenpipe:\",s),s}}async initAnalyticsIfNeeded(){if(this.analyticsInitialized)return;let e=await this.settings.getAll();this.analyticsEnabled=e.analyticsEnabled,e.analyticsEnabled&&(this.analyticsInitialized=true);}async captureEvent(e,t){if(!this.analyticsEnabled)return;await this.initAnalyticsIfNeeded();let r=await this.settings.getAll();return b(e,{distinct_id:r.user.id,email:r.user.email,...t})}async captureMainFeatureEvent(e,t){if(this.analyticsEnabled)return await this.initAnalyticsIfNeeded(),A(e,t)}},ae=new E;\n//# sourceMappingURL=index.js.map\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ac2NyZWVucGlwZS9qcy9kaXN0L2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQTJKLDhDQUE4Qyx3QkFBd0IsMkdBQTJHLEdBQUcsUUFBUSxTQUFTLGdCQUFnQix1QkFBdUIsWUFBWSxXQUFXLEtBQUssV0FBVyx1Q0FBdUMsV0FBVyxVQUFVLGNBQWMsb0ZBQW9GLGNBQWMsaUNBQWlDLGdCQUFnQixHQUFHLGNBQWMscUNBQXFDLFdBQVcsc0JBQXNCLEdBQUcsRUFBRSxjQUFjLDRGQUE0RixXQUFXLHNCQUFzQixHQUFHLElBQUksYUFBYSxRQUFRLDZ4QkFBNnhCLDZEQUE2RCxvR0FBb0csU0FBUyxrQkFBa0Isd05BQXdOLGtCQUFrQixTQUFTLFVBQVUsa0JBQWtCLGNBQWMsbUNBQW1DLHFCQUFxQix5QkFBeUIsdUNBQVMsS0FBSyxVQUFVLGVBQWUsc0NBQU0saURBQWlELE1BQU0sNkNBQTZDLHNDQUFNLHFCQUFxQixFQUFFLHNDQUFNLGlCQUFpQixNQUFNLGNBQWMsc0NBQU0sMkJBQTJCLHNDQUFNLG9DQUFvQyxNQUFNLGlEQUFpRCxFQUFFLEdBQUcsTUFBTSxzQ0FBTSwrQkFBK0IsSUFBSSxZQUFZLGlEQUFVLCtCQUErQixzQ0FBc0MsT0FBTyxxQkFBcUIsc0NBQU0sZ0JBQWdCLHNDQUFNLFlBQVksRUFBRSxPQUFPLGFBQWEsSUFBSSx3Q0FBQyxHQUFHLGlDQUFDLG9EQUFvRCx5Q0FBeUMsSUFBSSxNQUFNLDhDQUFPLENBQUMseUNBQVMsa0JBQWtCLGFBQWEsRUFBRSxZQUFZLGlEQUFVLDRDQUE0QyxlQUFlLGFBQWEsc0JBQXNCLFNBQVMsNkRBQTZELGNBQWMsYUFBYSxNQUFNLDhDQUFPLENBQUMseUNBQVMsa0JBQWtCLGVBQWUsRUFBRSx1QkFBdUIsTUFBTSxrREFBVywyQ0FBMkMsYUFBYSw0REFBNEQsZUFBZSwwRUFBMEUsZUFBZSwwQkFBMEIsa0JBQWtCLGdCQUFnQixtREFBbUQsc0JBQXNCLG9CQUFvQixjQUFjLGVBQWUsS0FBSyxvQkFBb0Isa0JBQWtCLDZFQUE2RSw0QkFBNEIsa0ZBQWtGLDhCQUE4QixpR0FBaUcsb0VBQW9FLHlEQUF5RCw4QkFBOEIsNkVBQTZFLG1DQUFtQyxpR0FBaUcsd0RBQXdELG1CQUFtQiwyQkFBMkIsTUFBTSxpREFBWSxHQUFHLDBDQUEwQyx1QkFBdUIsbUJBQW1CLEdBQUcsRUFBRSxZQUFZLGlCQUFpQixvQkFBb0IsY0FBYyxpRkFBaUYsbURBQUksa0dBQWtHLDBCQUEwQixRQUFRLHVFQUF1RSxHQUFHLElBQUksb0RBQW9ELHVCQUF1QixrQ0FBa0Msc0JBQXNCLHlEQUF5RCxFQUFFLE1BQU0sU0FBUyxpRUFBaUUsWUFBWSxhQUFhLElBQUksc0RBQXNELHNCQUFzQixtQ0FBbUMsRUFBRSxnREFBZ0QsU0FBUyxHQUFHLFFBQVEsdUNBQXVDLFNBQVMsaURBQWlELHdCQUF3QixrQkFBa0IsSUFBSSwwREFBMEQsdUJBQXVCLGtDQUFrQyxzQkFBc0IsTUFBTSxFQUFFLEVBQUUsZ0RBQWdELFNBQVMsR0FBRyxRQUFRLHVDQUF1QyxTQUFTLG9EQUFvRCx3QkFBd0IsZ0JBQWdCLElBQUksMERBQTBELHVCQUF1QixrQ0FBa0Msc0JBQXNCLFVBQVUsRUFBRSxNQUFNLFNBQVMsd0RBQXdELGlCQUFpQixJQUFJLDJEQUEyRCx1QkFBdUIsa0NBQWtDLHNCQUFzQixVQUFVLEVBQUUsTUFBTSxTQUFTLHlEQUF5RCxrQkFBa0IsSUFBSSwwREFBMEQsdUJBQXVCLGtDQUFrQyxzQkFBc0IsbUJBQW1CLEVBQUUsTUFBTSxTQUFTLHdEQUF3RCxjQUFjLElBQUksc0RBQXNELEVBQUUsR0FBRyxzQkFBc0IsbUNBQW1DLEVBQUUsZ0RBQWdELFNBQVMsR0FBRyxRQUFRLHVDQUF1QyxTQUFTLG9EQUFvRCx3QkFBd0IsNkJBQTZCLElBQUksa0VBQWtFLHVCQUF1QixrQ0FBa0Msc0JBQXNCLDRCQUE0QixFQUFFLEVBQUUsZ0RBQWdELFNBQVMsR0FBRyxRQUFRLHVDQUF1QyxTQUFTLDREQUE0RCx3QkFBd0IsZ0JBQWdCLElBQUksMERBQTBELHVCQUF1QixrQ0FBa0Msc0JBQXNCLFVBQVUsRUFBRSxNQUFNLFNBQVMsMERBQTBELG9HQUFvRyxjQUFjLEtBQUssZ0JBQWdCLGtCQUFrQixpQ0FBaUMsZ0JBQWdCLFFBQVEsV0FBVyxzQkFBc0IsYUFBYSwyQkFBMkIsSUFBSSxtQkFBbUIsT0FBTyxnQkFBZ0Isc0JBQXNCLGFBQWEsMkJBQTJCLElBQUksbUJBQW1CLE9BQU8sYUFBYSxvQkFBb0IsR0FBRyxZQUFZLFFBQVEsTUFBTSx1Q0FBdUMsMkJBQTJCLCtCQUErQix3QkFBd0Isa0NBQWtDLHVCQUF1QiwwQ0FBMEMsdUJBQXVCLFNBQVMsa0NBQWtDLHlCQUF5QixJQUFJLDBCQUEwQixJQUFJLHFCQUFxQixhQUFhLCtCQUErQix1QkFBdUIsa0NBQWtDLHNCQUFzQixTQUFTLEVBQUUsRUFBRSxnREFBZ0QsU0FBUyxHQUFHLGdDQUFnQyxTQUFTLDBEQUEwRCxXQUFXLGtHQUFrRyxPQUFPLDhEQUE4RCxLQUFLLDBFQUEwRSw2QkFBNkIsZUFBZSxPQUFPLGdEQUFnRCxLQUFLLDZFQUE2RSxrQkFBa0IsYUFBYSwrQkFBK0IsdUJBQXVCLGtDQUFrQyxzQkFBc0IsV0FBVyxFQUFFLEVBQUUsVUFBVSxxQkFBcUIsaUNBQWlDLElBQUksb0JBQW9CLDRDQUE0QyxzQkFBc0IsR0FBRyxNQUFNLG1EQUFtRCxTQUFTLEtBQUssZ0JBQWdCLElBQUkscUJBQXFCLDZIQUE2SCx5QkFBeUIsR0FBRyxzQ0FBc0MseUZBQXlGLCtGQUErRiwwSEFBMEgsRUFBRSxjQUFjLE9BQU8sMkJBQTJCLEtBQUssOEVBQThFLDBCQUEwQixxQkFBcUIsYUFBYSw4QkFBOEIsdUJBQXVCLGtDQUFrQyxzQkFBc0Isd0JBQXdCLEVBQUUsRUFBRSxVQUFVLHFCQUFxQixpQ0FBaUMsSUFBSSxvQkFBb0Isd0NBQXdDLHNCQUFzQixHQUFHLE1BQU0sK0NBQStDLFNBQVMsS0FBSyxnQkFBZ0IsSUFBSSxnQ0FBZ0MsZUFBZSxxQkFBcUIseUdBQXlHLEVBQUUsYUFBYSxxQkFBcUIsdUdBQXVHLEVBQUUsaUJBQWlCLE9BQU8sOEhBQThILGtCQUFrQixhQUFhLGtDQUFrQyx1QkFBdUIsa0NBQWtDLDJCQUEyQixFQUFFLFVBQVUscUJBQXFCLGlDQUFpQyxJQUFJLG9CQUFvQix1Q0FBdUMsc0JBQXNCLEdBQUcsTUFBTSw4Q0FBOEMsU0FBUyxLQUFLLGdCQUFnQixJQUFJLHFCQUFxQiwrSEFBK0gseUJBQXlCLEdBQUcsWUFBWSxpQ0FBaUMsT0FBTyx3UEFBd1Asa0JBQWtCLGFBQWEsb0RBQW9ELHVCQUF1QixrQ0FBa0MsMkJBQTJCLEVBQUUsVUFBVSxxQkFBcUIsaUNBQWlDLElBQUksb0JBQW9CLHdEQUF3RCxzQkFBc0IsR0FBRyxNQUFNLCtEQUErRCxTQUFTLEtBQUssZ0JBQWdCLElBQUkscUJBQXFCLCtFQUErRSxzQkFBc0IscUJBQXFCLGFBQWEsd0NBQXdDLHVCQUF1QixrQ0FBa0Msc0JBQXNCLGdCQUFnQixFQUFFLEVBQUUsVUFBVSxxQkFBcUIsaUNBQWlDLElBQUksb0JBQW9CLHFEQUFxRCxzQkFBc0IsR0FBRyxNQUFNLDREQUE0RCxTQUFTLEtBQUssZ0JBQWdCLElBQUkscUJBQXFCLHlEQUF5RCwyQkFBMkIsR0FBRyxpQkFBaUIsdUJBQXVCLHFCQUFxQixhQUFhLHVDQUF1Qyx1QkFBdUIsa0NBQWtDLHNCQUFzQix1QkFBdUIsRUFBRSxFQUFFLFVBQVUscUJBQXFCLGlDQUFpQyxJQUFJLG9CQUFvQiw4REFBOEQsc0JBQXNCLEdBQUcsTUFBTSxxRUFBcUUsU0FBUyxLQUFLLGdCQUFnQixJQUFJLHFCQUFxQix3REFBd0QsMkJBQTJCLEdBQUcsaUJBQWlCLGtCQUFrQixPQUFPLGdEQUFnRCxLQUFLLDZFQUE2RSxrQkFBa0IsYUFBYSxtQ0FBbUMsdUJBQXVCLGtDQUFrQyx3QkFBd0IsMEJBQTBCLEdBQUcsRUFBRSxVQUFVLHFCQUFxQixpQ0FBaUMsSUFBSSxvQkFBb0Isd0NBQXdDLHNCQUFzQixHQUFHLE1BQU0sK0NBQStDLFNBQVMsS0FBSyxnQkFBZ0IsSUFBSSxnQ0FBZ0MsMkJBQTJCLHFCQUFxQixhQUFhLDRDQUE0Qyx1QkFBdUIsa0NBQWtDLHNCQUFzQiw0QkFBNEIsRUFBRSxFQUFFLFVBQVUscUJBQXFCLGlDQUFpQyxJQUFJLG9CQUFvQiw0REFBNEQsc0JBQXNCLEdBQUcsTUFBTSxtRUFBbUUsU0FBUyxLQUFLLGdCQUFnQixJQUFJLHFCQUFxQiw2REFBNkQsMkJBQTJCLEdBQUcsaUJBQWlCLHlCQUF5QixxQkFBcUIsYUFBYSwwQ0FBMEMsdUJBQXVCLGtDQUFrQyxzQkFBc0IsV0FBVyxFQUFFLEVBQUUsVUFBVSxxQkFBcUIsaUNBQWlDLElBQUksb0JBQW9CLCtDQUErQyxzQkFBc0IsR0FBRyxNQUFNLHNEQUFzRCxTQUFTLEtBQUssZ0JBQWdCLElBQUkscUJBQXFCLG9FQUFvRSwyQkFBMkIsR0FBRyxpQkFBaUIsbUJBQW1CLHFCQUFxQixhQUFhLGtDQUFrQyx1QkFBdUIsa0NBQWtDLHNCQUFzQixnQkFBZ0IsRUFBRSxFQUFFLFVBQVUscUJBQXFCLGlDQUFpQyxJQUFJLG9CQUFvQix1Q0FBdUMsc0JBQXNCLEdBQUcsTUFBTSw4Q0FBOEMsU0FBUyxLQUFLLGdCQUFnQixJQUFJLHFCQUFxQiw0REFBNEQsMkJBQTJCLEdBQUcsaUJBQWlCLGdCQUFnQixPQUFPLHFEQUFxRCxLQUFLLGdGQUFnRixrQkFBa0IsYUFBYSxnQ0FBZ0MsdUJBQXVCLGtDQUFrQyxzQkFBc0IsaURBQWlELEVBQUUsRUFBRSxVQUFVLHFCQUFxQixpQ0FBaUMsSUFBSSxvQkFBb0IsNkNBQTZDLHNCQUFzQixHQUFHLE1BQU0sb0RBQW9ELFNBQVMsS0FBSyxnQkFBZ0IsSUFBSSxpQ0FBaUMsU0FBUyxRQUFRLFNBQVMsaUJBQWlCLGdDQUFnQyxlQUFlLDBCQUEwQiw0QkFBNEIsZUFBZSxxQkFBcUIsYUFBYSx5QkFBeUIsdUJBQXVCLGtDQUFrQyxzQkFBc0IsaURBQWlELEVBQUUsRUFBRSxVQUFVLHFCQUFxQixpQ0FBaUMsSUFBSSxvQkFBb0IsNENBQTRDLHNCQUFzQixHQUFHLE1BQU0sbURBQW1ELFNBQVMsS0FBSyxnQkFBZ0IsSUFBSSw2QkFBNkIsY0FBYyxxQkFBcUIsYUFBYSwrQkFBK0IsdUJBQXVCLGtDQUFrQyxzQkFBc0IsVUFBVSxrRUFBa0UsRUFBRSxFQUFFLFVBQVUscUJBQXFCLGlDQUFpQyxJQUFJLG9CQUFvQiw0Q0FBNEMsc0JBQXNCLEdBQUcsTUFBTSxtREFBbUQsU0FBUyxLQUFLLGdCQUFnQixJQUFJLHFCQUFxQiw2SEFBNkgseUJBQXlCLEdBQUcsbUNBQW1DLHlGQUF5RiwrRkFBK0YsMEhBQTBILEVBQUUsY0FBYyxxQkFBcUIsYUFBYSw4QkFBOEIsdUJBQXVCLGtDQUFrQyxzQkFBc0IsVUFBVSxpRUFBaUUsUUFBUSxFQUFFLEVBQUUsVUFBVSxxQkFBcUIsaUNBQWlDLElBQUksb0JBQW9CLHdDQUF3QyxzQkFBc0IsR0FBRyxNQUFNLCtDQUErQyxTQUFTLEtBQUssZ0JBQWdCLElBQUksZ0NBQWdDLGVBQWUsSUFBSSw0QkFBNEIsTUFBTSxjQUFjLGtCQUFrQixFQUFFLGtDQUFrQyxLQUFLLGVBQWUsRUFBRSxJQUFJLHlCQUF5QixjQUFjLE9BQU8seUNBQXlDLFlBQVksa0JBQWtCLHFCQUFxQixhQUFhLG1DQUFtQyx1QkFBdUIsa0NBQWtDLHNCQUFzQixVQUFVLGlFQUFpRSxhQUFhLEVBQUUsRUFBRSxVQUFVLHFCQUFxQixpQ0FBaUMsSUFBSSxvQkFBb0Isd0NBQXdDLHNCQUFzQixHQUFHLE1BQU0sK0NBQStDLFNBQVMsS0FBSyxnQkFBZ0IsSUFBSSxnQ0FBZ0Msa0JBQWtCLHFCQUFxQixhQUFhLGdDQUFnQyx1QkFBdUIsa0NBQWtDLHNCQUFzQixVQUFVLGlFQUFpRSxzQkFBc0IsRUFBRSxFQUFFLFVBQVUscUJBQXFCLGlDQUFpQyxJQUFJLG9CQUFvQiw2Q0FBNkMsc0JBQXNCLEdBQUcsTUFBTSxvREFBb0QsU0FBUyxLQUFLLGdCQUFnQixJQUFJLGtDQUFrQyxHQUFHLEtBQUssa0RBQUMsV0FBVyxrREFBQyxXQUFXLGtEQUFDLGVBQWUsa0RBQUMsVUFBVSxrREFBQyxjQUFjLGtEQUFDLEVBQUUsRUFBRSxZQUFZLDJCQUEyQixzQkFBc0IsZUFBZSxZQUFZLFlBQVksZUFBZSxpQ0FBaUMsbUNBQW1DLCtCQUErQixJQUFJLHNCQUFzQixFQUFFLFVBQVUsdUJBQXVCLGtDQUFrQyx3QkFBd0IsK0JBQStCLFdBQVcsS0FBSyxTQUFTLDhEQUE4RCx5QkFBeUIsbUNBQW1DLDBCQUEwQixvQ0FBb0Msc0dBQXNHLE1BQU0sV0FBVywyQkFBMkIsRUFBRSxzQ0FBc0MsRUFBRSxFQUFFLElBQUkscUJBQXFCLFVBQVUsdUJBQXVCLElBQUksdURBQXVELHdCQUF3QixHQUFHLE1BQU0sdUNBQXVDLHdCQUF3QixHQUFHLHVDQUF1QyxTQUFTLEdBQUcscUJBQXFCLG1DQUFtQywyREFBMkQsT0FBTyxTQUFTLHVEQUF1RCw4QkFBOEIsb0NBQW9DLG1DQUFtQywrRkFBK0Ysd0JBQXdCLGlDQUFpQyxtQ0FBbUMsbUNBQW1DLFlBQVksOENBQThDLEVBQUUsbUNBQW1DLDJFQUEyRTtBQUM1aHJCO0FBQzNDIiwic291cmNlcyI6WyJHOlxccHJvamVjdHNcXHNjcmVlbnBpcGVcXHNjcmVlbnBpcGVcXHBpcGVzXFxmb2N1cy1hbmFseXRpY3MtY29hY2hcXG5vZGVfbW9kdWxlc1xcQHNjcmVlbnBpcGVcXGpzXFxkaXN0XFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgcCBmcm9tJ2ZzL3Byb21pc2VzJztpbXBvcnQgaSBmcm9tJ3BhdGgnO2ltcG9ydCBfIGZyb20nb3MnO2ltcG9ydCB7Zm9ya31mcm9tJ2NoaWxkX3Byb2Nlc3MnO2ltcG9ydCB7Y3JlYXRlU2VydmVyfWZyb20nbmV0JztpbXBvcnQgZCBmcm9tJ3Bvc3Rob2ctanMnO3ZhciBTPShuLGU9XCJcIik9Pk9iamVjdC5rZXlzKG4pLnJlZHVjZSgodCxyKT0+e2xldCBzPWUubGVuZ3RoP2UrXCIuXCI6XCJcIjtyZXR1cm4gdHlwZW9mIG5bcl09PVwib2JqZWN0XCImJm5bcl0hPT1udWxsJiYhQXJyYXkuaXNBcnJheShuW3JdKT9PYmplY3QuYXNzaWduKHQsUyhuW3JdLHMrcikpOnRbcytyXT1uW3JdLHR9LHt9KSxQPW49PntsZXQgZT17fTtmb3IobGV0IHQgaW4gbil7bGV0IHI9dC5zcGxpdChcIi5cIikscz1lO2ZvcihsZXQgYT0wO2E8ci5sZW5ndGg7YSsrKXtsZXQgbz1yW2FdO2E9PT1yLmxlbmd0aC0xP3Nbb109blt0XTooc1tvXT1zW29dfHx7fSxzPXNbb10pO319cmV0dXJuIGV9O2Z1bmN0aW9uIEMobil7cmV0dXJuIG4ucmVwbGFjZSgvKFstX11bYS16XSkvZyxlPT5lLnRvVXBwZXJDYXNlKCkucmVwbGFjZShcIi1cIixcIlwiKS5yZXBsYWNlKFwiX1wiLFwiXCIpKX1mdW5jdGlvbiBtKG4pe3JldHVybiBuLnJlcGxhY2UoL1tBLVpdL2csZT0+YF8ke2UudG9Mb3dlckNhc2UoKX1gKX1mdW5jdGlvbiBoKG4pe3JldHVybiBPYmplY3Qua2V5cyhuKS5yZWR1Y2UoKGUsdCk9PntsZXQgcj1tKHQpO3JldHVybiBlW3JdPWgoblt0XSksZX0se30pfWZ1bmN0aW9uIGMobil7cmV0dXJuIEFycmF5LmlzQXJyYXkobik/bi5tYXAoYyk6biE9PW51bGwmJnR5cGVvZiBuPT1cIm9iamVjdFwiP09iamVjdC5rZXlzKG4pLnJlZHVjZSgoZSx0KT0+e2xldCByPUModCk7cmV0dXJuIGVbcl09YyhuW3RdKSxlfSx7fSk6bn1mdW5jdGlvbiBrKCl7cmV0dXJuIHtvcGVuYWlBcGlLZXk6XCJcIixkZWVwZ3JhbUFwaUtleTpcIlwiLGFpTW9kZWw6XCJncHQtNG9cIixhaVVybDpcImh0dHBzOi8vYXBpLm9wZW5haS5jb20vdjFcIixjdXN0b21Qcm9tcHQ6XCJSdWxlczpcXG4gICAgLSBZb3UgY2FuIGFuYWx5emUvdmlldy9zaG93L2FjY2VzcyB2aWRlb3MgdG8gdGhlIHVzZXIgYnkgcHV0dGluZyAubXA0IGZpbGVzIGluIGEgY29kZSBibG9jayAod2UnbGwgcmVuZGVyIGl0KSBsaWtlIHRoaXM6IGAvdXNlcnMvdmlkZW8ubXA0YCwgdXNlIHRoZSBleGFjdCwgYWJzb2x1dGUsIGZpbGUgcGF0aCBmcm9tIGZpbGVfcGF0aCBwcm9wZXJ0eVxcbiAgICAtIERvIG5vdCB0cnkgdG8gZW1iZWQgdmlkZW8gaW4gbGlua3MgKGUuZy4gW10oLm1wNCkgb3IgaHR0cHM6Ly8ubXA0KSBpbnN0ZWFkIHB1dCB0aGUgZmlsZV9wYXRoIGluIGEgY29kZSBibG9jayB1c2luZyBiYWNrdGlja3NcXG4gICAgLSBEbyBub3QgcHV0IHZpZGVvIGluIG11bHRpbGluZSBjb2RlIGJsb2NrIGl0IHdpbGwgbm90IHJlbmRlciB0aGUgdmlkZW8gKGUuZy4gYGBgYmFzaFxcbi5tcDRgYGAgSVMgV1JPTkcpIGluc3RlYWQgdXNpbmcgaW5saW5lIGNvZGUgYmxvY2sgd2l0aCBzaW5nbGUgYmFja3RpY2tcXG4gICAgLSBBbHdheXMgYW5zd2VyIG15IHF1ZXN0aW9uL2ludGVudCwgZG8gbm90IG1ha2UgdXAgdGhpbmdzXFxuICAgIFwiLHBvcnQ6MzAzMCxkYXRhRGlyOlwiZGVmYXVsdFwiLGRpc2FibGVBdWRpbzpmYWxzZSxpZ25vcmVkV2luZG93czpbXSxpbmNsdWRlZFdpbmRvd3M6W10sYWlQcm92aWRlclR5cGU6XCJvcGVuYWlcIixlbWJlZGRlZExMTTp7ZW5hYmxlZDpmYWxzZSxtb2RlbDpcImxsYW1hMy4yOjFiLWluc3RydWN0LXE0X0tfTVwiLHBvcnQ6MTE0MzR9LGVuYWJsZUZyYW1lQ2FjaGU6dHJ1ZSxlbmFibGVVaU1vbml0b3Jpbmc6ZmFsc2UsYWlNYXhDb250ZXh0Q2hhcnM6NTEyZTMsYW5hbHl0aWNzRW5hYmxlZDp0cnVlLHVzZXI6e3Rva2VuOlwiXCJ9LGN1c3RvbVNldHRpbmdzOnt9LG1vbml0b3JJZHM6W1wiZGVmYXVsdFwiXSxhdWRpb0RldmljZXM6W1wiZGVmYXVsdFwiXSxhdWRpb1RyYW5zY3JpcHRpb25FbmdpbmU6XCJ3aGlzcGVyLWxhcmdlLXYzLXR1cmJvXCIsZW5hYmxlUmVhbHRpbWVBdWRpb1RyYW5zY3JpcHRpb246ZmFsc2UscmVhbHRpbWVBdWRpb1RyYW5zY3JpcHRpb25FbmdpbmU6XCJkZWVwZ3JhbVwiLGRpc2FibGVWaXNpb246ZmFsc2UsYWlQcmVzZXRzOltdfX12YXIgeT1rKCksZj1jbGFzc3tzZXR0aW5ncztzdG9yZVBhdGg7aW5pdGlhbGl6ZWQ9ZmFsc2U7Y29uc3RydWN0b3IoKXt0aGlzLnNldHRpbmdzPXksdGhpcy5zdG9yZVBhdGg9XCJcIjt9YXN5bmMgZ2V0U3RvcmVQYXRoKCl7bGV0IGU9cHJvY2Vzcy5wbGF0Zm9ybSx0PV8uaG9tZWRpcigpLHI7c3dpdGNoKGUpe2Nhc2VcImRhcndpblwiOnI9aS5qb2luKHQsXCJMaWJyYXJ5XCIsXCJBcHBsaWNhdGlvbiBTdXBwb3J0XCIsXCJzY3JlZW5waXBlXCIpO2JyZWFrO2Nhc2VcImxpbnV4XCI6bGV0IG89cHJvY2Vzcy5lbnYuWERHX0RBVEFfSE9NRXx8aS5qb2luKHQsXCIubG9jYWxcIixcInNoYXJlXCIpO3I9aS5qb2luKG8sXCJzY3JlZW5waXBlXCIpO2JyZWFrO2Nhc2VcIndpbjMyXCI6cj1pLmpvaW4ocHJvY2Vzcy5lbnYuTE9DQUxBUFBEQVRBfHxpLmpvaW4odCxcIkFwcERhdGFcIixcIkxvY2FsXCIpLFwic2NyZWVucGlwZVwiKTticmVhaztkZWZhdWx0OnRocm93IG5ldyBFcnJvcihgdW5zdXBwb3J0ZWQgcGxhdGZvcm06ICR7ZX1gKX1sZXQgcz1pLmpvaW4ocixcInByb2ZpbGVzLmJpblwiKSxhPVwiZGVmYXVsdFwiO3RyeXtsZXQgbz1hd2FpdCBwLnJlYWRGaWxlKHMpLHU9SlNPTi5wYXJzZShvLnRvU3RyaW5nKCkpO3UuYWN0aXZlUHJvZmlsZSYmKGE9dS5hY3RpdmVQcm9maWxlKTt9Y2F0Y2h7fXJldHVybiBhPT09XCJkZWZhdWx0XCI/aS5qb2luKHIsXCJzdG9yZS5iaW5cIik6aS5qb2luKHIsYHN0b3JlLSR7YX0uYmluYCl9YXN5bmMgaW5pdCgpe2lmKCFwfHwhaSl0aHJvdyBuZXcgRXJyb3IoXCJmYWlsZWQgdG8gbG9hZCByZXF1aXJlZCBtb2R1bGVzXCIpO3RoaXMuc3RvcmVQYXRoPWF3YWl0IHRoaXMuZ2V0U3RvcmVQYXRoKCk7dHJ5e2F3YWl0IHAubWtkaXIoaS5kaXJuYW1lKHRoaXMuc3RvcmVQYXRoKSx7cmVjdXJzaXZlOiEwfSk7bGV0IGU9YXdhaXQgcC5yZWFkRmlsZSh0aGlzLnN0b3JlUGF0aCksdD1KU09OLnBhcnNlKGUudG9TdHJpbmcoKSk7dGhpcy5zZXR0aW5ncz17Li4ueSwuLi5QKHQpfSx0aGlzLmluaXRpYWxpemVkPSEwO31jYXRjaChlKXtpZihlLmNvZGU9PT1cIkVOT0VOVFwiKWF3YWl0IHRoaXMuc2F2ZSgpLHRoaXMuaW5pdGlhbGl6ZWQ9dHJ1ZTtlbHNlIHRocm93IGV9fWFzeW5jIHNhdmUoKXthd2FpdCBwLm1rZGlyKGkuZGlybmFtZSh0aGlzLnN0b3JlUGF0aCkse3JlY3Vyc2l2ZTp0cnVlfSk7bGV0IGU9Uyh0aGlzLnNldHRpbmdzKTthd2FpdCBwLndyaXRlRmlsZSh0aGlzLnN0b3JlUGF0aCxKU09OLnN0cmluZ2lmeShlLG51bGwsMikpO31hc3luYyBnZXQoZSl7cmV0dXJuIHRoaXMuaW5pdGlhbGl6ZWR8fGF3YWl0IHRoaXMuaW5pdCgpLHRoaXMuc2V0dGluZ3NbZV19YXN5bmMgc2V0KGUsdCl7dGhpcy5pbml0aWFsaXplZHx8YXdhaXQgdGhpcy5pbml0KCksdGhpcy5zZXR0aW5nc1tlXT10LGF3YWl0IHRoaXMuc2F2ZSgpO31hc3luYyBnZXRBbGwoKXtyZXR1cm4gYXdhaXQgdGhpcy5pbml0KCksey4uLnRoaXMuc2V0dGluZ3N9fWFzeW5jIHVwZGF0ZShlKXt0aGlzLmluaXRpYWxpemVkfHxhd2FpdCB0aGlzLmluaXQoKSx0aGlzLnNldHRpbmdzPXsuLi50aGlzLnNldHRpbmdzLC4uLmV9LGF3YWl0IHRoaXMuc2F2ZSgpO31hc3luYyByZXNldCgpe3RoaXMuc2V0dGluZ3M9ey4uLnl9LGF3YWl0IHRoaXMuc2F2ZSgpO31hc3luYyByZXNldEtleShlKXt0aGlzLmluaXRpYWxpemVkfHxhd2FpdCB0aGlzLmluaXQoKSx0aGlzLnNldHRpbmdzW2VdPXlbZV0sYXdhaXQgdGhpcy5zYXZlKCk7fWFzeW5jIGdldEN1c3RvbVNldHRpbmcoZSx0KXtyZXR1cm4gdGhpcy5pbml0aWFsaXplZHx8YXdhaXQgdGhpcy5pbml0KCksdGhpcy5zZXR0aW5ncy5jdXN0b21TZXR0aW5ncz8uW2VdPy5bdF19YXN5bmMgc2V0Q3VzdG9tU2V0dGluZyhlLHQscil7dGhpcy5pbml0aWFsaXplZHx8YXdhaXQgdGhpcy5pbml0KCksdGhpcy5zZXR0aW5ncy5jdXN0b21TZXR0aW5ncz10aGlzLnNldHRpbmdzLmN1c3RvbVNldHRpbmdzfHx7fSx0aGlzLnNldHRpbmdzLmN1c3RvbVNldHRpbmdzW2VdPXRoaXMuc2V0dGluZ3MuY3VzdG9tU2V0dGluZ3NbZV18fHt9LHRoaXMuc2V0dGluZ3MuY3VzdG9tU2V0dGluZ3NbZV1bdF09cixhd2FpdCB0aGlzLnNhdmUoKTt9YXN5bmMgZ2V0TmFtZXNwYWNlU2V0dGluZ3MoZSl7cmV0dXJuIHRoaXMuaW5pdGlhbGl6ZWR8fGF3YWl0IHRoaXMuaW5pdCgpLHRoaXMuc2V0dGluZ3MuY3VzdG9tU2V0dGluZ3M/LltlXX1hc3luYyB1cGRhdGVOYW1lc3BhY2VTZXR0aW5ncyhlLHQpe3RoaXMuaW5pdGlhbGl6ZWR8fGF3YWl0IHRoaXMuaW5pdCgpLHRoaXMuc2V0dGluZ3MuY3VzdG9tU2V0dGluZ3M9dGhpcy5zZXR0aW5ncy5jdXN0b21TZXR0aW5nc3x8e30sdGhpcy5zZXR0aW5ncy5jdXN0b21TZXR0aW5nc1tlXT10LGF3YWl0IHRoaXMuc2F2ZSgpO319O2FzeW5jIGZ1bmN0aW9uIEooKXtyZXR1cm4gbmV3IFByb21pc2UoKG4sZSk9PntsZXQgdD1jcmVhdGVTZXJ2ZXIoKTt0LnVucmVmKCksdC5vbihcImVycm9yXCIsZSksdC5saXN0ZW4oMCwoKT0+e2xldCByPXQuYWRkcmVzcygpLnBvcnQ7dC5jbG9zZSgoKT0+bihyKSk7fSk7fSl9dmFyIGc9Y2xhc3N7YWN0aW9uU2VydmVyUG9ydDthY3Rpb25TZXJ2ZXJQcm9jZXNzO2FzeW5jIHNlbmQoZSl7dGhpcy5hY3Rpb25TZXJ2ZXJQb3J0fHwodGhpcy5hY3Rpb25TZXJ2ZXJQb3J0PWF3YWl0IEooKSx0aGlzLmFjdGlvblNlcnZlclByb2Nlc3M9Zm9yayhcIi4vaW5ib3gtc2VydmVyLmpzXCIsW3RoaXMuYWN0aW9uU2VydmVyUG9ydC50b1N0cmluZygpXSkpLGUuYWN0aW9ucyYmKGUuYWN0aW9ucz1lLmFjdGlvbnMubWFwKHQ9PntsZXQgcj1jcnlwdG8ucmFuZG9tVVVJRCgpO3JldHVybiB7bGFiZWw6dC5sYWJlbCxhY3Rpb246cixwb3J0OnRoaXMuYWN0aW9uU2VydmVyUG9ydCxjYWxsYmFjazp0LmNhbGxiYWNrfX0pKTt0cnl7cmV0dXJuIChhd2FpdCBmZXRjaChcImh0dHA6Ly9sb2NhbGhvc3Q6MTE0MzUvaW5ib3hcIix7bWV0aG9kOlwiUE9TVFwiLGhlYWRlcnM6e1wiQ29udGVudC1UeXBlXCI6XCJhcHBsaWNhdGlvbi9qc29uXCJ9LGJvZHk6SlNPTi5zdHJpbmdpZnkoey4uLmUsdHlwZTpcImluYm94XCIsYWN0aW9uU2VydmVyUG9ydDp0aGlzLmFjdGlvblNlcnZlclBvcnR9KX0pKS5va31jYXRjaCh0KXtyZXR1cm4gY29uc29sZS5lcnJvcihcImZhaWxlZCB0byBzZW5kIGluYm94IG1lc3NhZ2U6XCIsdCksZmFsc2V9fX07dmFyIHc9Y2xhc3N7YXN5bmMgbGlzdCgpe3RyeXtsZXQgdD1hd2FpdCBmZXRjaChcImh0dHA6Ly9sb2NhbGhvc3Q6MzAzMC9waXBlcy9saXN0XCIse21ldGhvZDpcIkdFVFwiLGhlYWRlcnM6e1wiQ29udGVudC1UeXBlXCI6XCJhcHBsaWNhdGlvbi9qc29uXCJ9fSk7aWYoIXQub2spdGhyb3cgbmV3IEVycm9yKGBodHRwIGVycm9yISBzdGF0dXM6ICR7dC5zdGF0dXN9YCk7cmV0dXJuIHtzdWNjZXNzOiEwLGRhdGE6KGF3YWl0IHQuanNvbigpKS5kYXRhfX1jYXRjaChlKXtyZXR1cm4gY29uc29sZS5lcnJvcihcImZhaWxlZCB0byBsaXN0IHBpcGVzOlwiLGUpLHtzdWNjZXNzOmZhbHNlLGVycm9yOmV9fX1hc3luYyBkb3dubG9hZChlKXt0cnl7bGV0IHI9YXdhaXQgZmV0Y2goXCJodHRwOi8vbG9jYWxob3N0OjMwMzAvcGlwZXMvZG93bmxvYWRcIix7bWV0aG9kOlwiUE9TVFwiLGhlYWRlcnM6e1wiQ29udGVudC1UeXBlXCI6XCJhcHBsaWNhdGlvbi9qc29uXCJ9LGJvZHk6SlNPTi5zdHJpbmdpZnkoe3VybDplfSl9KTtpZighci5vayl0aHJvdyBuZXcgRXJyb3IoYGh0dHAgZXJyb3IhIHN0YXR1czogJHtyLnN0YXR1c31gKTtyZXR1cm4ge3N1Y2Nlc3M6ITAsZGF0YTooYXdhaXQgci5qc29uKCkpLmRhdGF9fWNhdGNoKHQpe3JldHVybiBjb25zb2xlLmVycm9yKFwiZmFpbGVkIHRvIGRvd25sb2FkIHBpcGU6XCIsdCkse3N1Y2Nlc3M6ZmFsc2UsZXJyb3I6dH19fWFzeW5jIGVuYWJsZShlKXt0cnl7cmV0dXJuIChhd2FpdCBmZXRjaChcImh0dHA6Ly9sb2NhbGhvc3Q6MzAzMC9waXBlcy9lbmFibGVcIix7bWV0aG9kOlwiUE9TVFwiLGhlYWRlcnM6e1wiQ29udGVudC1UeXBlXCI6XCJhcHBsaWNhdGlvbi9qc29uXCJ9LGJvZHk6SlNPTi5zdHJpbmdpZnkoe3BpcGVfaWQ6ZX0pfSkpLm9rfWNhdGNoKHQpe3JldHVybiBjb25zb2xlLmVycm9yKFwiZmFpbGVkIHRvIGVuYWJsZSBwaXBlOlwiLHQpLGZhbHNlfX1hc3luYyBkaXNhYmxlKGUpe3RyeXtyZXR1cm4gKGF3YWl0IGZldGNoKFwiaHR0cDovL2xvY2FsaG9zdDozMDMwL3BpcGVzL2Rpc2FibGVcIix7bWV0aG9kOlwiUE9TVFwiLGhlYWRlcnM6e1wiQ29udGVudC1UeXBlXCI6XCJhcHBsaWNhdGlvbi9qc29uXCJ9LGJvZHk6SlNPTi5zdHJpbmdpZnkoe3BpcGVfaWQ6ZX0pfSkpLm9rfWNhdGNoKHQpe3JldHVybiBjb25zb2xlLmVycm9yKFwiZmFpbGVkIHRvIGRpc2FibGUgcGlwZTpcIix0KSxmYWxzZX19YXN5bmMgdXBkYXRlKGUsdCl7dHJ5e3JldHVybiAoYXdhaXQgZmV0Y2goXCJodHRwOi8vbG9jYWxob3N0OjMwMzAvcGlwZXMvdXBkYXRlXCIse21ldGhvZDpcIlBPU1RcIixoZWFkZXJzOntcIkNvbnRlbnQtVHlwZVwiOlwiYXBwbGljYXRpb24vanNvblwifSxib2R5OkpTT04uc3RyaW5naWZ5KHtwaXBlX2lkOmUsY29uZmlnOnR9KX0pKS5va31jYXRjaChyKXtyZXR1cm4gY29uc29sZS5lcnJvcihcImZhaWxlZCB0byB1cGRhdGUgcGlwZTpcIixyKSxmYWxzZX19YXN5bmMgaW5mbyhlKXt0cnl7bGV0IHI9YXdhaXQgZmV0Y2goYGh0dHA6Ly9sb2NhbGhvc3Q6MzAzMC9waXBlcy9pbmZvLyR7ZX1gLHttZXRob2Q6XCJHRVRcIixoZWFkZXJzOntcIkNvbnRlbnQtVHlwZVwiOlwiYXBwbGljYXRpb24vanNvblwifX0pO2lmKCFyLm9rKXRocm93IG5ldyBFcnJvcihgaHR0cCBlcnJvciEgc3RhdHVzOiAke3Iuc3RhdHVzfWApO3JldHVybiB7c3VjY2VzczohMCxkYXRhOihhd2FpdCByLmpzb24oKSkuZGF0YX19Y2F0Y2godCl7cmV0dXJuIGNvbnNvbGUuZXJyb3IoXCJmYWlsZWQgdG8gZ2V0IHBpcGUgaW5mbzpcIix0KSx7c3VjY2VzczpmYWxzZSxlcnJvcjp0fX19YXN5bmMgZG93bmxvYWRQcml2YXRlKGUsdCxyKXt0cnl7bGV0IGE9YXdhaXQgZmV0Y2goXCJodHRwOi8vbG9jYWxob3N0OjMwMzAvcGlwZXMvZG93bmxvYWQtcHJpdmF0ZVwiLHttZXRob2Q6XCJQT1NUXCIsaGVhZGVyczp7XCJDb250ZW50LVR5cGVcIjpcImFwcGxpY2F0aW9uL2pzb25cIn0sYm9keTpKU09OLnN0cmluZ2lmeSh7dXJsOmUscGlwZV9uYW1lOnQscGlwZV9pZDpyfSl9KTtpZighYS5vayl0aHJvdyBuZXcgRXJyb3IoYGh0dHAgZXJyb3IhIHN0YXR1czogJHthLnN0YXR1c31gKTtyZXR1cm4ge3N1Y2Nlc3M6ITAsZGF0YTooYXdhaXQgYS5qc29uKCkpLmRhdGF9fWNhdGNoKHMpe3JldHVybiBjb25zb2xlLmVycm9yKFwiZmFpbGVkIHRvIGRvd25sb2FkIHByaXZhdGUgcGlwZTpcIixzKSx7c3VjY2VzczpmYWxzZSxlcnJvcjpzfX19YXN5bmMgZGVsZXRlKGUpe3RyeXtyZXR1cm4gKGF3YWl0IGZldGNoKFwiaHR0cDovL2xvY2FsaG9zdDozMDMwL3BpcGVzL2RlbGV0ZVwiLHttZXRob2Q6XCJQT1NUXCIsaGVhZGVyczp7XCJDb250ZW50LVR5cGVcIjpcImFwcGxpY2F0aW9uL2pzb25cIn0sYm9keTpKU09OLnN0cmluZ2lmeSh7cGlwZV9pZDplfSl9KSkub2t9Y2F0Y2godCl7cmV0dXJuIGNvbnNvbGUuZXJyb3IoXCJmYWlsZWQgdG8gZGVsZXRlIHBpcGU6XCIsdCksZmFsc2V9fX07dmFyIHY9ZmFsc2UsbD1udWxsLEk9XCJwaGNfQnQ4R29UQlBna0NwRHJiYUlaekpJRVl0MENySmpoQml1TGFCY2sxY2xjZVwiLFU9XCJodHRwczovL2V1LmkucG9zdGhvZy5jb21cIjtmdW5jdGlvbiBPKG4pe2w9bjt9ZnVuY3Rpb24gJChuLGUpeyF2JiZsJiYobC5pbml0KEkse2FwaV9ob3N0OlUsZGlzdGluY3RfaWQ6bixlbWFpbDplfSksbC5pZGVudGlmeShuLHtlbWFpbDplfSksdj10cnVlKTt9YXN5bmMgZnVuY3Rpb24gYihuLGUpe2lmKCFsKXJldHVybjskKGU/LmRpc3RpbmN0X2lkLGU/LmVtYWlsKTtsZXR7ZGlzdGluY3RfaWQ6dCwuLi5yfT1lfHx7fTtsLmNhcHR1cmUobixyKTt9YXN5bmMgZnVuY3Rpb24gQShuLGUpe2lmKCFsKXJldHVybjskKGU/LmRpc3RpbmN0X2lkLGU/LmVtYWlsKTtsZXR7ZGlzdGluY3RfaWQ6dCwuLi5yfT1lfHx7fTtsLmNhcHR1cmUobix7ZmVhdHVyZTpcIm1haW5cIiwuLi5yfSk7fXZhciB4PWNsYXNze2Jhc2VVcmw7cGl4ZWw7Y29uc3RydWN0b3IoZT1cImh0dHA6Ly9sb2NhbGhvc3Q6MzAzMFwiKXt0aGlzLmJhc2VVcmw9ZSx0aGlzLnBpeGVsPXt0eXBlOnQ9PnRoaXMuc2VuZElucHV0Q29udHJvbCh7dHlwZTpcIldyaXRlVGV4dFwiLGRhdGE6dH0pLHByZXNzOnQ9PnRoaXMuc2VuZElucHV0Q29udHJvbCh7dHlwZTpcIktleVByZXNzXCIsZGF0YTp0fSksbW92ZU1vdXNlOih0LHIpPT50aGlzLnNlbmRJbnB1dENvbnRyb2woe3R5cGU6XCJNb3VzZU1vdmVcIixkYXRhOnt4OnQseTpyfX0pLGNsaWNrOnQ9PnRoaXMuc2VuZElucHV0Q29udHJvbCh7dHlwZTpcIk1vdXNlQ2xpY2tcIixkYXRhOnR9KX07fWFzeW5jIHNlbmRJbnB1dENvbnRyb2woZSl7dHJ5e2xldCB0PWF3YWl0IGZldGNoKGAke3RoaXMuYmFzZVVybH0vZXhwZXJpbWVudGFsL29wZXJhdG9yL3BpeGVsYCx7bWV0aG9kOlwiUE9TVFwiLGhlYWRlcnM6e1wiQ29udGVudC1UeXBlXCI6XCJhcHBsaWNhdGlvbi9qc29uXCJ9LGJvZHk6SlNPTi5zdHJpbmdpZnkoe2FjdGlvbjplfSl9KTtpZighdC5vayl0aHJvdyBuZXcgRXJyb3IoYGh0dHAgZXJyb3IhIHN0YXR1czogJHt0LnN0YXR1c31gKTtyZXR1cm4gKGF3YWl0IHQuanNvbigpKS5zdWNjZXNzfWNhdGNoKHQpe3JldHVybiBjb25zb2xlLmVycm9yKFwiZmFpbGVkIHRvIGNvbnRyb2wgaW5wdXQ6XCIsdCksZmFsc2V9fWxvY2F0b3IoZSl7aWYoZS5yb2xlJiZlLmlkKXRocm93IG5ldyBFcnJvcihcIm9ubHkgb25lIG9mICdyb2xlJyBvciAnaWQnIGNhbiBiZSBzcGVjaWZpZWQuIG5lZWQgYm90aD8gZG0gdXMhXCIpO2xldCB0PXthcHBfbmFtZTplLmFwcCx3aW5kb3dfbmFtZTplLndpbmRvdyxsb2NhdG9yOmUucm9sZXx8KGUuaWQ/YCMke2UuaWR9YDpcIlwiKSx1c2VfYmFja2dyb3VuZF9hcHBzOmUudXNlQmFja2dyb3VuZEFwcHMsYWN0aXZhdGVfYXBwOmUuYWN0aXZhdGVBcHB9O3JldHVybiBuZXcgVCh0aGlzLmJhc2VVcmwsdCl9YXN5bmMgY2xpY2soZSl7bGV0IHQ9e2FwcF9uYW1lOmUuYXBwLHdpbmRvd19uYW1lOmUud2luZG93LGxvY2F0b3I6YCMke2UuaWR9YCx1c2VfYmFja2dyb3VuZF9hcHBzOmUudXNlQmFja2dyb3VuZEFwcHMsYWN0aXZhdGVfYXBwOmUuYWN0aXZhdGVBcHAhPT1mYWxzZX0scj1hd2FpdCBmZXRjaChgJHt0aGlzLmJhc2VVcmx9L2V4cGVyaW1lbnRhbC9vcGVyYXRvci9jbGlja2Ase21ldGhvZDpcIlBPU1RcIixoZWFkZXJzOntcIkNvbnRlbnQtVHlwZVwiOlwiYXBwbGljYXRpb24vanNvblwifSxib2R5OkpTT04uc3RyaW5naWZ5KHtzZWxlY3Rvcjp0fSl9KTtpZighci5vayl7bGV0IGE9YXdhaXQgci50ZXh0KCk7Y29uc29sZS5sb2coXCJlcnJvciByZXNwb25zZTpcIixhKTt0cnl7bGV0IG89SlNPTi5wYXJzZShhKTt0aHJvdyBuZXcgRXJyb3IoYGZhaWxlZCB0byBjbGljayBlbGVtZW50OiAke28uZXJyb3J8fHIuc3RhdHVzVGV4dH1gKX1jYXRjaHt0aHJvdyBuZXcgRXJyb3IoYGZhaWxlZCB0byBjbGljayBlbGVtZW50IChzdGF0dXMgJHtyLnN0YXR1c30pOiAke2F8fHIuc3RhdHVzVGV4dH1gKX19bGV0IHM9YXdhaXQgci5qc29uKCk7aWYoY29uc29sZS5sb2coXCJkZWJ1ZzogY2xpY2sgcmVzcG9uc2UgZGF0YTpcIixKU09OLnN0cmluZ2lmeShzLG51bGwsMikpLCFzLnN1Y2Nlc3MpdGhyb3cgbmV3IEVycm9yKGBjbGljayBvcGVyYXRpb24gZmFpbGVkOiAke3MuZXJyb3J8fFwidW5rbm93biBlcnJvclwifWApO3JldHVybiBzLnJlc3VsdD9jKHMucmVzdWx0KTpzLm1ldGhvZD97bWV0aG9kOnMubWV0aG9kLGNvb3JkaW5hdGVzOnMuY29vcmRpbmF0ZXMsZGV0YWlsczpzLmRldGFpbHN8fFwiQ2xpY2sgb3BlcmF0aW9uIHN1Y2NlZWRlZFwifTooY29uc29sZS5sb2coXCJ3YXJuaW5nOiBjbGljayByZXNwb25zZSBtaXNzaW5nIGV4cGVjdGVkIHN0cnVjdHVyZSwgY3JlYXRpbmcgZmFsbGJhY2sgb2JqZWN0XCIpLHttZXRob2Q6XCJNb3VzZVNpbXVsYXRpb25cIixjb29yZGluYXRlczp1bmRlZmluZWQsZGV0YWlsczpcIkNsaWNrIG9wZXJhdGlvbiBzdWNjZWVkZWQgYnV0IHJldHVybmVkIHVuZXhwZWN0ZWQgZGF0YSBzdHJ1Y3R1cmVcIn0pfWFzeW5jIGZpbGwoZSl7bGV0IHQ9e2FwcF9uYW1lOmUuYXBwLGxvY2F0b3I6YCMke2UuaWR9YCx1c2VfYmFja2dyb3VuZF9hcHBzOmUudXNlQmFja2dyb3VuZEFwcHMsYWN0aXZhdGVfYXBwOmUuYWN0aXZhdGVBcHAhPT1mYWxzZX07Y29uc29sZS5sb2coXCJzZWxlY3RvclwiLHQpO2xldCByPWF3YWl0IGZldGNoKGAke3RoaXMuYmFzZVVybH0vZXhwZXJpbWVudGFsL29wZXJhdG9yL3R5cGVgLHttZXRob2Q6XCJQT1NUXCIsaGVhZGVyczp7XCJDb250ZW50LVR5cGVcIjpcImFwcGxpY2F0aW9uL2pzb25cIn0sYm9keTpKU09OLnN0cmluZ2lmeSh7c2VsZWN0b3I6dCx0ZXh0OmUudmFsdWV9KX0pO2lmKCFyLm9rKXtsZXQgYT1hd2FpdCByLnRleHQoKTtjb25zb2xlLmxvZyhcImVycm9yIHJlc3BvbnNlOlwiLGEpO3RyeXtsZXQgbz1KU09OLnBhcnNlKGEpO3Rocm93IG5ldyBFcnJvcihgZmFpbGVkIHRvIHR5cGUgdGV4dDogJHtvLmVycm9yfHxyLnN0YXR1c1RleHR9YCl9Y2F0Y2h7dGhyb3cgbmV3IEVycm9yKGBmYWlsZWQgdG8gdHlwZSB0ZXh0IChzdGF0dXMgJHtyLnN0YXR1c30pOiAke2F8fHIuc3RhdHVzVGV4dH1gKX19cmV0dXJuIChhd2FpdCByLmpzb24oKSkuc3VjY2Vzc31nZXRCeVJvbGUoZSx0KXtyZXR1cm4gdGhpcy5sb2NhdG9yKHthcHA6dD8uYXBwfHxcIlwiLHdpbmRvdzp0Py53aW5kb3cscm9sZTplLHVzZUJhY2tncm91bmRBcHBzOnQ/LnVzZUJhY2tncm91bmRBcHBzLGFjdGl2YXRlQXBwOnQ/LmFjdGl2YXRlQXBwfSl9Z2V0QnlJZChlLHQpe3JldHVybiB0aGlzLmxvY2F0b3Ioe2FwcDp0Py5hcHB8fFwiXCIsd2luZG93OnQ/LndpbmRvdyxpZDplLHVzZUJhY2tncm91bmRBcHBzOnQ/LnVzZUJhY2tncm91bmRBcHBzLGFjdGl2YXRlQXBwOnQ/LmFjdGl2YXRlQXBwfSl9YXN5bmMgZ2V0VGV4dChlKXtsZXQgdD17YXBwTmFtZTplLmFwcCx3aW5kb3dOYW1lOmUud2luZG93LG1heERlcHRoOmUubWF4RGVwdGgsdXNlQmFja2dyb3VuZEFwcHM6ZS51c2VCYWNrZ3JvdW5kQXBwcyxhY3RpdmF0ZUFwcDplLmFjdGl2YXRlQXBwIT09ZmFsc2V9LHI9YXdhaXQgZmV0Y2goYCR7dGhpcy5iYXNlVXJsfS9leHBlcmltZW50YWwvb3BlcmF0b3IvZ2V0X3RleHRgLHttZXRob2Q6XCJQT1NUXCIsaGVhZGVyczp7XCJDb250ZW50LVR5cGVcIjpcImFwcGxpY2F0aW9uL2pzb25cIn0sYm9keTpKU09OLnN0cmluZ2lmeShoKHQpKX0pO2lmKCFyLm9rKXtsZXQgYT1hd2FpdCByLnRleHQoKTtjb25zb2xlLmxvZyhcImVycm9yIHJlc3BvbnNlOlwiLGEpO3RyeXtsZXQgbz1KU09OLnBhcnNlKGEpO3Rocm93IG5ldyBFcnJvcihgZmFpbGVkIHRvIGdldCB0ZXh0OiAke28uZXJyb3J8fHIuc3RhdHVzVGV4dH1gKX1jYXRjaHt0aHJvdyBuZXcgRXJyb3IoYGZhaWxlZCB0byBnZXQgdGV4dCAoc3RhdHVzICR7ci5zdGF0dXN9KTogJHthfHxyLnN0YXR1c1RleHR9YCl9fWxldCBzPWF3YWl0IHIuanNvbigpO2lmKGNvbnNvbGUubG9nKFwiZGVidWc6IHRleHQgcmVzcG9uc2UgZGF0YTpcIixKU09OLnN0cmluZ2lmeShzLG51bGwsMikpLCFzLnN1Y2Nlc3MpdGhyb3cgbmV3IEVycm9yKGBnZXRfdGV4dCBvcGVyYXRpb24gZmFpbGVkOiAke3MuZXJyb3J8fFwidW5rbm93biBlcnJvclwifWApO3JldHVybiBjKHMpfWFzeW5jIGdldEludGVyYWN0YWJsZUVsZW1lbnRzKGUpe2xldCB0PXthcHBOYW1lOmUuYXBwLHdpbmRvd05hbWU6ZS53aW5kb3csd2l0aFRleHRPbmx5OmUud2l0aFRleHRPbmx5LGludGVyYWN0YWJsZU9ubHk6ZS5pbnRlcmFjdGFibGVPbmx5LGluY2x1ZGVTb21ldGltZXNJbnRlcmFjdGFibGU6ZS5pbmNsdWRlU29tZXRpbWVzSW50ZXJhY3RhYmxlLG1heEVsZW1lbnRzOmUubWF4RWxlbWVudHMsdXNlQmFja2dyb3VuZEFwcHM6ZS51c2VCYWNrZ3JvdW5kQXBwcyxhY3RpdmF0ZUFwcDplLmFjdGl2YXRlQXBwfSxyPWF3YWl0IGZldGNoKGAke3RoaXMuYmFzZVVybH0vZXhwZXJpbWVudGFsL29wZXJhdG9yL2xpc3QtaW50ZXJhY3RhYmxlLWVsZW1lbnRzYCx7bWV0aG9kOlwiUE9TVFwiLGhlYWRlcnM6e1wiQ29udGVudC1UeXBlXCI6XCJhcHBsaWNhdGlvbi9qc29uXCJ9LGJvZHk6SlNPTi5zdHJpbmdpZnkoaCh0KSl9KTtpZighci5vayl7bGV0IGE9YXdhaXQgci50ZXh0KCk7Y29uc29sZS5sb2coXCJlcnJvciByZXNwb25zZTpcIixhKTt0cnl7bGV0IG89SlNPTi5wYXJzZShhKTt0aHJvdyBuZXcgRXJyb3IoYGZhaWxlZCB0byBnZXQgaW50ZXJhY3RhYmxlIGVsZW1lbnRzOiAke28uZXJyb3J8fHIuc3RhdHVzVGV4dH1gKX1jYXRjaHt0aHJvdyBuZXcgRXJyb3IoYGZhaWxlZCB0byBnZXQgaW50ZXJhY3RhYmxlIGVsZW1lbnRzIChzdGF0dXMgJHtyLnN0YXR1c30pOiAke2F8fHIuc3RhdHVzVGV4dH1gKX19bGV0IHM9YXdhaXQgci5qc29uKCk7cmV0dXJuIGNvbnNvbGUubG9nKFwiZGVidWc6IHRleHQgcmVzcG9uc2UgZGF0YTpcIixKU09OLnN0cmluZ2lmeShzLG51bGwsMikpLGMocyl9YXN5bmMgY2xpY2tCeUluZGV4KGUpe2xldCB0PWF3YWl0IGZldGNoKGAke3RoaXMuYmFzZVVybH0vZXhwZXJpbWVudGFsL29wZXJhdG9yL2NsaWNrLWJ5LWluZGV4YCx7bWV0aG9kOlwiUE9TVFwiLGhlYWRlcnM6e1wiQ29udGVudC1UeXBlXCI6XCJhcHBsaWNhdGlvbi9qc29uXCJ9LGJvZHk6SlNPTi5zdHJpbmdpZnkoe2VsZW1lbnRfaW5kZXg6ZX0pfSk7aWYoIXQub2spe2xldCBzPWF3YWl0IHQudGV4dCgpO2NvbnNvbGUubG9nKFwiZXJyb3IgcmVzcG9uc2U6XCIscyk7dHJ5e2xldCBhPUpTT04ucGFyc2Uocyk7dGhyb3cgbmV3IEVycm9yKGBmYWlsZWQgdG8gY2xpY2sgZWxlbWVudCBieSBpbmRleDogJHthLmVycm9yfHx0LnN0YXR1c1RleHR9YCl9Y2F0Y2h7dGhyb3cgbmV3IEVycm9yKGBmYWlsZWQgdG8gY2xpY2sgZWxlbWVudCBieSBpbmRleCAoc3RhdHVzICR7dC5zdGF0dXN9KTogJHtzfHx0LnN0YXR1c1RleHR9YCl9fWxldCByPWF3YWl0IHQuanNvbigpO2lmKCFyLnN1Y2Nlc3MpdGhyb3cgbmV3IEVycm9yKGBjbGljayBvcGVyYXRpb24gZmFpbGVkOiAke3IubWVzc2FnZXx8XCJ1bmtub3duIGVycm9yXCJ9YCk7cmV0dXJuIHIuc3VjY2Vzc31hc3luYyB0eXBlQnlJbmRleChlLHQpe2xldCByPWF3YWl0IGZldGNoKGAke3RoaXMuYmFzZVVybH0vZXhwZXJpbWVudGFsL29wZXJhdG9yL3R5cGUtYnktaW5kZXhgLHttZXRob2Q6XCJQT1NUXCIsaGVhZGVyczp7XCJDb250ZW50LVR5cGVcIjpcImFwcGxpY2F0aW9uL2pzb25cIn0sYm9keTpKU09OLnN0cmluZ2lmeSh7ZWxlbWVudF9pbmRleDplLHRleHQ6dH0pfSk7aWYoIXIub2spe2xldCBhPWF3YWl0IHIudGV4dCgpO2NvbnNvbGUubG9nKFwiZXJyb3IgcmVzcG9uc2U6XCIsYSk7dHJ5e2xldCBvPUpTT04ucGFyc2UoYSk7dGhyb3cgbmV3IEVycm9yKGBmYWlsZWQgdG8gdHlwZSB0ZXh0IGludG8gZWxlbWVudCBieSBpbmRleDogJHtvLmVycm9yfHxyLnN0YXR1c1RleHR9YCl9Y2F0Y2h7dGhyb3cgbmV3IEVycm9yKGBmYWlsZWQgdG8gdHlwZSB0ZXh0IGludG8gZWxlbWVudCBieSBpbmRleCAoc3RhdHVzICR7ci5zdGF0dXN9KTogJHthfHxyLnN0YXR1c1RleHR9YCl9fWxldCBzPWF3YWl0IHIuanNvbigpO2lmKCFzLnN1Y2Nlc3MpdGhyb3cgbmV3IEVycm9yKGB0eXBlIG9wZXJhdGlvbiBmYWlsZWQ6ICR7cy5tZXNzYWdlfHxcInVua25vd24gZXJyb3JcIn1gKTtyZXR1cm4gcy5zdWNjZXNzfWFzeW5jIHByZXNzS2V5KGUpe2xldCB0PXthcHBfbmFtZTplLmFwcCx3aW5kb3dfbmFtZTplLndpbmRvdyxsb2NhdG9yOmAjJHtlLmlkfWAsdXNlX2JhY2tncm91bmRfYXBwczplLnVzZUJhY2tncm91bmRBcHBzLGFjdGl2YXRlX2FwcDplLmFjdGl2YXRlQXBwIT09ZmFsc2V9LHI9YXdhaXQgZmV0Y2goYCR7dGhpcy5iYXNlVXJsfS9leHBlcmltZW50YWwvb3BlcmF0b3IvcHJlc3Mta2V5YCx7bWV0aG9kOlwiUE9TVFwiLGhlYWRlcnM6e1wiQ29udGVudC1UeXBlXCI6XCJhcHBsaWNhdGlvbi9qc29uXCJ9LGJvZHk6SlNPTi5zdHJpbmdpZnkoaCh7c2VsZWN0b3I6dCxrZXlDb21ibzplLmtleX0pKX0pO2lmKCFyLm9rKXtsZXQgYT1hd2FpdCByLnRleHQoKTtjb25zb2xlLmxvZyhcImVycm9yIHJlc3BvbnNlOlwiLGEpO3RyeXtsZXQgbz1KU09OLnBhcnNlKGEpO3Rocm93IG5ldyBFcnJvcihgZmFpbGVkIHRvIHByZXNzIGtleTogJHtvLmVycm9yfHxyLnN0YXR1c1RleHR9YCl9Y2F0Y2h7dGhyb3cgbmV3IEVycm9yKGBmYWlsZWQgdG8gcHJlc3Mga2V5IChzdGF0dXMgJHtyLnN0YXR1c30pOiAke2F8fHIuc3RhdHVzVGV4dH1gKX19cmV0dXJuIChhd2FpdCByLmpzb24oKSkuc3VjY2Vzc31hc3luYyBwcmVzc0tleUJ5SW5kZXgoZSx0KXtsZXQgcj1hd2FpdCBmZXRjaChgJHt0aGlzLmJhc2VVcmx9L2V4cGVyaW1lbnRhbC9vcGVyYXRvci9wcmVzcy1rZXktYnktaW5kZXhgLHttZXRob2Q6XCJQT1NUXCIsaGVhZGVyczp7XCJDb250ZW50LVR5cGVcIjpcImFwcGxpY2F0aW9uL2pzb25cIn0sYm9keTpKU09OLnN0cmluZ2lmeSh7ZWxlbWVudF9pbmRleDplLGtleV9jb21ibzp0fSl9KTtpZighci5vayl7bGV0IGE9YXdhaXQgci50ZXh0KCk7Y29uc29sZS5sb2coXCJlcnJvciByZXNwb25zZTpcIixhKTt0cnl7bGV0IG89SlNPTi5wYXJzZShhKTt0aHJvdyBuZXcgRXJyb3IoYGZhaWxlZCB0byBwcmVzcyBrZXkgb24gZWxlbWVudCBieSBpbmRleDogJHtvLmVycm9yfHxyLnN0YXR1c1RleHR9YCl9Y2F0Y2h7dGhyb3cgbmV3IEVycm9yKGBmYWlsZWQgdG8gcHJlc3Mga2V5IG9uIGVsZW1lbnQgYnkgaW5kZXggKHN0YXR1cyAke3Iuc3RhdHVzfSk6ICR7YXx8ci5zdGF0dXNUZXh0fWApfX1sZXQgcz1hd2FpdCByLmpzb24oKTtpZighcy5zdWNjZXNzKXRocm93IG5ldyBFcnJvcihgcHJlc3Mga2V5IG9wZXJhdGlvbiBmYWlsZWQ6ICR7cy5tZXNzYWdlfHxcInVua25vd24gZXJyb3JcIn1gKTtyZXR1cm4gcy5zdWNjZXNzfWFzeW5jIG9wZW5BcHBsaWNhdGlvbihlKXtsZXQgdD1hd2FpdCBmZXRjaChgJHt0aGlzLmJhc2VVcmx9L2V4cGVyaW1lbnRhbC9vcGVyYXRvci9vcGVuLWFwcGxpY2F0aW9uYCx7bWV0aG9kOlwiUE9TVFwiLGhlYWRlcnM6e1wiQ29udGVudC1UeXBlXCI6XCJhcHBsaWNhdGlvbi9qc29uXCJ9LGJvZHk6SlNPTi5zdHJpbmdpZnkoe2FwcF9uYW1lOmV9KX0pO2lmKCF0Lm9rKXtsZXQgcz1hd2FpdCB0LnRleHQoKTtjb25zb2xlLmxvZyhcImVycm9yIHJlc3BvbnNlOlwiLHMpO3RyeXtsZXQgYT1KU09OLnBhcnNlKHMpO3Rocm93IG5ldyBFcnJvcihgZmFpbGVkIHRvIG9wZW4gYXBwbGljYXRpb246ICR7YS5lcnJvcnx8dC5zdGF0dXNUZXh0fWApfWNhdGNoe3Rocm93IG5ldyBFcnJvcihgZmFpbGVkIHRvIG9wZW4gYXBwbGljYXRpb24gKHN0YXR1cyAke3Quc3RhdHVzfSk6ICR7c3x8dC5zdGF0dXNUZXh0fWApfX1sZXQgcj1hd2FpdCB0Lmpzb24oKTtpZighci5zdWNjZXNzKXRocm93IG5ldyBFcnJvcihgb3BlbiBhcHBsaWNhdGlvbiBvcGVyYXRpb24gZmFpbGVkOiAke3IubWVzc2FnZXx8XCJ1bmtub3duIGVycm9yXCJ9YCk7cmV0dXJuIHIuc3VjY2Vzc31hc3luYyBvcGVuVXJsKGUsdCl7bGV0IHI9YXdhaXQgZmV0Y2goYCR7dGhpcy5iYXNlVXJsfS9leHBlcmltZW50YWwvb3BlcmF0b3Ivb3Blbi11cmxgLHttZXRob2Q6XCJQT1NUXCIsaGVhZGVyczp7XCJDb250ZW50LVR5cGVcIjpcImFwcGxpY2F0aW9uL2pzb25cIn0sYm9keTpKU09OLnN0cmluZ2lmeSh7dXJsOmUsYnJvd3Nlcjp0fSl9KTtpZighci5vayl7bGV0IGE9YXdhaXQgci50ZXh0KCk7Y29uc29sZS5sb2coXCJlcnJvciByZXNwb25zZTpcIixhKTt0cnl7bGV0IG89SlNPTi5wYXJzZShhKTt0aHJvdyBuZXcgRXJyb3IoYGZhaWxlZCB0byBvcGVuIHVybDogJHtvLmVycm9yfHxyLnN0YXR1c1RleHR9YCl9Y2F0Y2h7dGhyb3cgbmV3IEVycm9yKGBmYWlsZWQgdG8gb3BlbiB1cmwgKHN0YXR1cyAke3Iuc3RhdHVzfSk6ICR7YXx8ci5zdGF0dXNUZXh0fWApfX1sZXQgcz1hd2FpdCByLmpzb24oKTtpZighcy5zdWNjZXNzKXRocm93IG5ldyBFcnJvcihgb3BlbiB1cmwgb3BlcmF0aW9uIGZhaWxlZDogJHtzLm1lc3NhZ2V8fFwidW5rbm93biBlcnJvclwifWApO3JldHVybiBzLnN1Y2Nlc3N9YXN5bmMgc2Nyb2xsKGUpe2xldCB0PXthcHBfbmFtZTplLmFwcCx3aW5kb3dfbmFtZTplLndpbmRvdyxsb2NhdG9yOmUuaWQ/YCMke2UuaWR9YDpcIlwiLHVzZV9iYWNrZ3JvdW5kX2FwcHM6ZS51c2VCYWNrZ3JvdW5kQXBwcyxhY3RpdmF0ZV9hcHA6ZS5hY3RpdmF0ZUFwcCE9PWZhbHNlfSxyPWF3YWl0IGZldGNoKGAke3RoaXMuYmFzZVVybH0vZXhwZXJpbWVudGFsL29wZXJhdG9yL3Njcm9sbGAse21ldGhvZDpcIlBPU1RcIixoZWFkZXJzOntcIkNvbnRlbnQtVHlwZVwiOlwiYXBwbGljYXRpb24vanNvblwifSxib2R5OkpTT04uc3RyaW5naWZ5KHtzZWxlY3Rvcjp0LGRpcmVjdGlvbjplLmRpcmVjdGlvbixhbW91bnQ6ZS5hbW91bnR9KX0pO2lmKCFyLm9rKXtsZXQgYT1hd2FpdCByLnRleHQoKTtjb25zb2xlLmxvZyhcImVycm9yIHJlc3BvbnNlOlwiLGEpO3RyeXtsZXQgbz1KU09OLnBhcnNlKGEpO3Rocm93IG5ldyBFcnJvcihgZmFpbGVkIHRvIHNjcm9sbCBlbGVtZW50OiAke28uZXJyb3J8fHIuc3RhdHVzVGV4dH1gKX1jYXRjaHt0aHJvdyBuZXcgRXJyb3IoYGZhaWxlZCB0byBzY3JvbGwgZWxlbWVudCAoc3RhdHVzICR7ci5zdGF0dXN9KTogJHthfHxyLnN0YXR1c1RleHR9YCl9fXJldHVybiAoYXdhaXQgci5qc29uKCkpLnN1Y2Nlc3N9fSxUPWNsYXNze2Jhc2VVcmw7c2VsZWN0b3I7Y29uc3RydWN0b3IoZSx0KXt0aGlzLmJhc2VVcmw9ZSx0aGlzLnNlbGVjdG9yPXQ7fWFzeW5jIGZpcnN0KGUpe2xldCB0PWF3YWl0IHRoaXMuYWxsKDEsZSk7cmV0dXJuIHQubGVuZ3RoPjA/dFswXTpudWxsfWFzeW5jIGFsbChlLHQpe2xldCByPWF3YWl0IGZldGNoKGAke3RoaXMuYmFzZVVybH0vZXhwZXJpbWVudGFsL29wZXJhdG9yYCx7bWV0aG9kOlwiUE9TVFwiLGhlYWRlcnM6e1wiQ29udGVudC1UeXBlXCI6XCJhcHBsaWNhdGlvbi9qc29uXCJ9LGJvZHk6SlNPTi5zdHJpbmdpZnkoe3NlbGVjdG9yOnRoaXMuc2VsZWN0b3IsbWF4X3Jlc3VsdHM6ZSxtYXhfZGVwdGg6dH0pfSk7aWYoIXIub2spe2xldCBhPWF3YWl0IHIudGV4dCgpO2NvbnNvbGUubG9nKFwiZXJyb3IgcmVzcG9uc2U6XCIsYSk7dHJ5e2xldCBvPUpTT04ucGFyc2UoYSk7dGhyb3cgbmV3IEVycm9yKGBmYWlsZWQgdG8gZmluZCBlbGVtZW50czogJHtvLmVycm9yfHxyLnN0YXR1c1RleHR9YCl9Y2F0Y2h7dGhyb3cgbmV3IEVycm9yKGBmYWlsZWQgdG8gZmluZCBlbGVtZW50cyAoc3RhdHVzICR7ci5zdGF0dXN9KTogJHthfHxyLnN0YXR1c1RleHR9YCl9fXJldHVybiAoYXdhaXQgci5qc29uKCkpLmRhdGF9YXN5bmMgY2xpY2soKXtsZXQgZT1hd2FpdCBmZXRjaChgJHt0aGlzLmJhc2VVcmx9L2V4cGVyaW1lbnRhbC9vcGVyYXRvci9jbGlja2Ase21ldGhvZDpcIlBPU1RcIixoZWFkZXJzOntcIkNvbnRlbnQtVHlwZVwiOlwiYXBwbGljYXRpb24vanNvblwifSxib2R5OkpTT04uc3RyaW5naWZ5KHtzZWxlY3Rvcjp7Li4udGhpcy5zZWxlY3RvcixhY3RpdmF0ZV9hcHA6dGhpcy5zZWxlY3Rvci5hY3RpdmF0ZV9hcHAhPT1mYWxzZX19KX0pO2lmKCFlLm9rKXtsZXQgcj1hd2FpdCBlLnRleHQoKTtjb25zb2xlLmxvZyhcImVycm9yIHJlc3BvbnNlOlwiLHIpO3RyeXtsZXQgcz1KU09OLnBhcnNlKHIpO3Rocm93IG5ldyBFcnJvcihgZmFpbGVkIHRvIGNsaWNrIGVsZW1lbnQ6ICR7cy5lcnJvcnx8ZS5zdGF0dXNUZXh0fWApfWNhdGNoe3Rocm93IG5ldyBFcnJvcihgZmFpbGVkIHRvIGNsaWNrIGVsZW1lbnQgKHN0YXR1cyAke2Uuc3RhdHVzfSk6ICR7cnx8ZS5zdGF0dXNUZXh0fWApfX1sZXQgdD1hd2FpdCBlLmpzb24oKTtpZihjb25zb2xlLmxvZyhcImRlYnVnOiBjbGljayByZXNwb25zZSBkYXRhOlwiLEpTT04uc3RyaW5naWZ5KHQsbnVsbCwyKSksIXQuc3VjY2Vzcyl0aHJvdyBuZXcgRXJyb3IoYGNsaWNrIG9wZXJhdGlvbiBmYWlsZWQ6ICR7dC5lcnJvcnx8XCJ1bmtub3duIGVycm9yXCJ9YCk7cmV0dXJuIHQucmVzdWx0P3QucmVzdWx0OnQubWV0aG9kP3ttZXRob2Q6dC5tZXRob2QsY29vcmRpbmF0ZXM6dC5jb29yZGluYXRlcyxkZXRhaWxzOnQuZGV0YWlsc3x8XCJDbGljayBvcGVyYXRpb24gc3VjY2VlZGVkXCJ9Oihjb25zb2xlLmxvZyhcIndhcm5pbmc6IGNsaWNrIHJlc3BvbnNlIG1pc3NpbmcgZXhwZWN0ZWQgc3RydWN0dXJlLCBjcmVhdGluZyBmYWxsYmFjayBvYmplY3RcIikse21ldGhvZDpcIk1vdXNlU2ltdWxhdGlvblwiLGNvb3JkaW5hdGVzOnVuZGVmaW5lZCxkZXRhaWxzOlwiQ2xpY2sgb3BlcmF0aW9uIHN1Y2NlZWRlZCBidXQgcmV0dXJuZWQgdW5leHBlY3RlZCBkYXRhIHN0cnVjdHVyZVwifSl9YXN5bmMgZmlsbChlKXtsZXQgdD1hd2FpdCBmZXRjaChgJHt0aGlzLmJhc2VVcmx9L2V4cGVyaW1lbnRhbC9vcGVyYXRvci90eXBlYCx7bWV0aG9kOlwiUE9TVFwiLGhlYWRlcnM6e1wiQ29udGVudC1UeXBlXCI6XCJhcHBsaWNhdGlvbi9qc29uXCJ9LGJvZHk6SlNPTi5zdHJpbmdpZnkoe3NlbGVjdG9yOnsuLi50aGlzLnNlbGVjdG9yLGFjdGl2YXRlX2FwcDp0aGlzLnNlbGVjdG9yLmFjdGl2YXRlX2FwcCE9PWZhbHNlfSx0ZXh0OmV9KX0pO2lmKCF0Lm9rKXtsZXQgcz1hd2FpdCB0LnRleHQoKTtjb25zb2xlLmxvZyhcImVycm9yIHJlc3BvbnNlOlwiLHMpO3RyeXtsZXQgYT1KU09OLnBhcnNlKHMpO3Rocm93IG5ldyBFcnJvcihgZmFpbGVkIHRvIHR5cGUgdGV4dDogJHthLmVycm9yfHx0LnN0YXR1c1RleHR9YCl9Y2F0Y2h7dGhyb3cgbmV3IEVycm9yKGBmYWlsZWQgdG8gdHlwZSB0ZXh0IChzdGF0dXMgJHt0LnN0YXR1c30pOiAke3N8fHQuc3RhdHVzVGV4dH1gKX19cmV0dXJuIChhd2FpdCB0Lmpzb24oKSkuc3VjY2Vzc31hc3luYyBleGlzdHMoKXt0cnl7cmV0dXJuICEhYXdhaXQgdGhpcy5maXJzdCgpfWNhdGNoe3JldHVybiBmYWxzZX19YXN5bmMgd2FpdEZvcihlPXt9KXtsZXQgdD1EYXRlLm5vdygpLHI9ZS50aW1lb3V0fHwzZTQ7Zm9yKDtEYXRlLm5vdygpLXQ8cjspe3RyeXtsZXQgcz1hd2FpdCB0aGlzLmZpcnN0KCk7aWYocylyZXR1cm4gc31jYXRjaHt9YXdhaXQgbmV3IFByb21pc2Uocz0+c2V0VGltZW91dChzLDEwMCkpO31yZXR1cm4gbnVsbH1hc3luYyBwcmVzc0tleShlKXtsZXQgdD1hd2FpdCBmZXRjaChgJHt0aGlzLmJhc2VVcmx9L2V4cGVyaW1lbnRhbC9vcGVyYXRvci9wcmVzcy1rZXlgLHttZXRob2Q6XCJQT1NUXCIsaGVhZGVyczp7XCJDb250ZW50LVR5cGVcIjpcImFwcGxpY2F0aW9uL2pzb25cIn0sYm9keTpKU09OLnN0cmluZ2lmeSh7c2VsZWN0b3I6ey4uLnRoaXMuc2VsZWN0b3IsYWN0aXZhdGVfYXBwOnRoaXMuc2VsZWN0b3IuYWN0aXZhdGVfYXBwIT09ZmFsc2V9LGtleV9jb21ibzplfSl9KTtpZighdC5vayl7bGV0IHM9YXdhaXQgdC50ZXh0KCk7Y29uc29sZS5sb2coXCJlcnJvciByZXNwb25zZTpcIixzKTt0cnl7bGV0IGE9SlNPTi5wYXJzZShzKTt0aHJvdyBuZXcgRXJyb3IoYGZhaWxlZCB0byBwcmVzcyBrZXk6ICR7YS5lcnJvcnx8dC5zdGF0dXNUZXh0fWApfWNhdGNoe3Rocm93IG5ldyBFcnJvcihgZmFpbGVkIHRvIHByZXNzIGtleSAoc3RhdHVzICR7dC5zdGF0dXN9KTogJHtzfHx0LnN0YXR1c1RleHR9YCl9fXJldHVybiAoYXdhaXQgdC5qc29uKCkpLnN1Y2Nlc3N9YXN5bmMgc2Nyb2xsKGUsdCl7bGV0IHI9YXdhaXQgZmV0Y2goYCR7dGhpcy5iYXNlVXJsfS9leHBlcmltZW50YWwvb3BlcmF0b3Ivc2Nyb2xsYCx7bWV0aG9kOlwiUE9TVFwiLGhlYWRlcnM6e1wiQ29udGVudC1UeXBlXCI6XCJhcHBsaWNhdGlvbi9qc29uXCJ9LGJvZHk6SlNPTi5zdHJpbmdpZnkoe3NlbGVjdG9yOnsuLi50aGlzLnNlbGVjdG9yLGFjdGl2YXRlX2FwcDp0aGlzLnNlbGVjdG9yLmFjdGl2YXRlX2FwcCE9PWZhbHNlfSxkaXJlY3Rpb246ZSxhbW91bnQ6dH0pfSk7aWYoIXIub2spe2xldCBhPWF3YWl0IHIudGV4dCgpO2NvbnNvbGUubG9nKFwiZXJyb3IgcmVzcG9uc2U6XCIsYSk7dHJ5e2xldCBvPUpTT04ucGFyc2UoYSk7dGhyb3cgbmV3IEVycm9yKGBmYWlsZWQgdG8gc2Nyb2xsIGVsZW1lbnQ6ICR7by5lcnJvcnx8ci5zdGF0dXNUZXh0fWApfWNhdGNoe3Rocm93IG5ldyBFcnJvcihgZmFpbGVkIHRvIHNjcm9sbCBlbGVtZW50IChzdGF0dXMgJHtyLnN0YXR1c30pOiAke2F8fHIuc3RhdHVzVGV4dH1gKX19cmV0dXJuIChhd2FpdCByLmpzb24oKSkuc3VjY2Vzc319O08oe2luaXQ6ZC5pbml0LmJpbmQoZCksaWRlbnRpZnk6ZC5pZGVudGlmeS5iaW5kKGQpLGNhcHR1cmU6ZC5jYXB0dXJlLmJpbmQoZCl9KTt2YXIgRT1jbGFzc3thbmFseXRpY3NJbml0aWFsaXplZD1mYWxzZTthbmFseXRpY3NFbmFibGVkPXRydWU7c2V0dGluZ3M9bmV3IGY7aW5ib3g9bmV3IGc7cGlwZXM9bmV3IHc7b3BlcmF0b3I9bmV3IHg7YXN5bmMgc2VuZERlc2t0b3BOb3RpZmljYXRpb24oZSl7YXdhaXQgdGhpcy5pbml0QW5hbHl0aWNzSWZOZWVkZWQoKTtsZXQgdD1cImh0dHA6Ly9sb2NhbGhvc3Q6MTE0MzVcIjt0cnl7cmV0dXJuIGF3YWl0IGZldGNoKGAke3R9L25vdGlmeWAse21ldGhvZDpcIlBPU1RcIixoZWFkZXJzOntcIkNvbnRlbnQtVHlwZVwiOlwiYXBwbGljYXRpb24vanNvblwifSxib2R5OkpTT04uc3RyaW5naWZ5KGUpfSksYXdhaXQgYihcIm5vdGlmaWNhdGlvbl9zZW50XCIse3N1Y2Nlc3M6ITB9KSwhMH1jYXRjaChyKXtyZXR1cm4gY29uc29sZS5lcnJvcihcImZhaWxlZCB0byBzZW5kIG5vdGlmaWNhdGlvbjpcIixyKSxmYWxzZX19YXN5bmMgcXVlcnlTY3JlZW5waXBlKGUpe2F3YWl0IHRoaXMuaW5pdEFuYWx5dGljc0lmTmVlZGVkKCk7bGV0IHQ9bmV3IFVSTFNlYXJjaFBhcmFtcztPYmplY3QuZW50cmllcyhlKS5mb3JFYWNoKChbcyxhXSk9PntpZihhIT09dW5kZWZpbmVkJiZhIT09XCJcIilpZihzPT09XCJzcGVha2VySWRzXCImJkFycmF5LmlzQXJyYXkoYSkpYS5sZW5ndGg+MCYmdC5hcHBlbmQobShzKSxhLmpvaW4oXCIsXCIpKTtlbHNlIHtsZXQgbz1tKHMpO3QuYXBwZW5kKG8sYS50b1N0cmluZygpKTt9fSk7bGV0IHI9YGh0dHA6Ly9sb2NhbGhvc3Q6MzAzMC9zZWFyY2g/JHt0fWA7dHJ5e2xldCBzPWF3YWl0IGZldGNoKHIpO2lmKCFzLm9rKXtsZXQgbz1hd2FpdCBzLnRleHQoKSx1O3RyeXt1PUpTT04ucGFyc2UobyksY29uc29sZS5lcnJvcihcInNjcmVlbnBpcGUgYXBpIGVycm9yOlwiLHtzdGF0dXM6cy5zdGF0dXMsZXJyb3I6dX0pO31jYXRjaHtjb25zb2xlLmVycm9yKFwic2NyZWVucGlwZSBhcGkgZXJyb3I6XCIse3N0YXR1czpzLnN0YXR1cyxlcnJvcjpvfSk7fXRocm93IG5ldyBFcnJvcihgaHR0cCBlcnJvciEgc3RhdHVzOiAke3Muc3RhdHVzfWApfWxldCBhPWF3YWl0IHMuanNvbigpO3JldHVybiBhd2FpdCBiKFwic2VhcmNoX3BlcmZvcm1lZFwiLHtjb250ZW50X3R5cGU6ZS5jb250ZW50VHlwZSxyZXN1bHRfY291bnQ6YS5wYWdpbmF0aW9uLnRvdGFsfSksYyhhKX1jYXRjaChzKXt0aHJvdyBjb25zb2xlLmVycm9yKFwiZXJyb3IgcXVlcnlpbmcgc2NyZWVucGlwZTpcIixzKSxzfX1hc3luYyBpbml0QW5hbHl0aWNzSWZOZWVkZWQoKXtpZih0aGlzLmFuYWx5dGljc0luaXRpYWxpemVkKXJldHVybjtsZXQgZT1hd2FpdCB0aGlzLnNldHRpbmdzLmdldEFsbCgpO3RoaXMuYW5hbHl0aWNzRW5hYmxlZD1lLmFuYWx5dGljc0VuYWJsZWQsZS5hbmFseXRpY3NFbmFibGVkJiYodGhpcy5hbmFseXRpY3NJbml0aWFsaXplZD10cnVlKTt9YXN5bmMgY2FwdHVyZUV2ZW50KGUsdCl7aWYoIXRoaXMuYW5hbHl0aWNzRW5hYmxlZClyZXR1cm47YXdhaXQgdGhpcy5pbml0QW5hbHl0aWNzSWZOZWVkZWQoKTtsZXQgcj1hd2FpdCB0aGlzLnNldHRpbmdzLmdldEFsbCgpO3JldHVybiBiKGUse2Rpc3RpbmN0X2lkOnIudXNlci5pZCxlbWFpbDpyLnVzZXIuZW1haWwsLi4udH0pfWFzeW5jIGNhcHR1cmVNYWluRmVhdHVyZUV2ZW50KGUsdCl7aWYodGhpcy5hbmFseXRpY3NFbmFibGVkKXJldHVybiBhd2FpdCB0aGlzLmluaXRBbmFseXRpY3NJZk5lZWRlZCgpLEEoZSx0KX19LGFlPW5ldyBFO1xuZXhwb3J0e2sgYXMgZ2V0RGVmYXVsdFNldHRpbmdzLGFlIGFzIHBpcGV9Oy8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcFxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/@screenpipe/js/dist/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@screenpipe/browser/dist/index.js":
/*!********************************************************!*\
  !*** ./node_modules/@screenpipe/browser/dist/index.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getDefaultSettings: () => (/* binding */ A),\n/* harmony export */   pipe: () => (/* binding */ H)\n/* harmony export */ });\n/* harmony import */ var posthog_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! posthog-js */ \"(rsc)/./node_modules/posthog-js/dist/module.js\");\nfunction C(a){return a.replace(/([-_][a-z])/g,e=>e.toUpperCase().replace(\"-\",\"\").replace(\"_\",\"\"))}function w(a){return a.replace(/[A-Z]/g,e=>`_${e.toLowerCase()}`)}function h(a){return Object.keys(a).reduce((e,t)=>{let r=w(t);return e[r]=h(a[t]),e},{})}function u(a){return Array.isArray(a)?a.map(u):a!==null&&typeof a==\"object\"?Object.keys(a).reduce((e,t)=>{let r=C(t);return e[r]=u(a[t]),e},{}):a}function A(){return {openaiApiKey:\"\",deepgramApiKey:\"\",aiModel:\"gpt-4o\",aiUrl:\"https://api.openai.com/v1\",customPrompt:\"Rules:\\n    - You can analyze/view/show/access videos to the user by putting .mp4 files in a code block (we'll render it) like this: `/users/video.mp4`, use the exact, absolute, file path from file_path property\\n    - Do not try to embed video in links (e.g. [](.mp4) or https://.mp4) instead put the file_path in a code block using backticks\\n    - Do not put video in multiline code block it will not render the video (e.g. ```bash\\n.mp4``` IS WRONG) instead using inline code block with single backtick\\n    - Always answer my question/intent, do not make up things\\n    \",port:3030,dataDir:\"default\",disableAudio:false,ignoredWindows:[],includedWindows:[],aiProviderType:\"openai\",embeddedLLM:{enabled:false,model:\"llama3.2:1b-instruct-q4_K_M\",port:11434},enableFrameCache:true,enableUiMonitoring:false,aiMaxContextChars:512e3,analyticsEnabled:true,user:{token:\"\"},customSettings:{},monitorIds:[\"default\"],audioDevices:[\"default\"],audioTranscriptionEngine:\"whisper-large-v3-turbo\",enableRealtimeAudioTranscription:false,realtimeAudioTranscriptionEngine:\"deepgram\",disableVision:false,aiPresets:[]}}var v=false,m=null,N=\"phc_Bt8GoTBPgkCpDrbaIZzJIEYt0CrJjhBiuLaBck1clce\",I=\"https://eu.i.posthog.com\";function O(a){m=a;}function $(a,e){!v&&m&&(m.init(N,{api_host:I,distinct_id:a,email:e}),m.identify(a,{email:e}),v=true);}async function T(a,e){if(!m)return;$(e?.distinct_id,e?.email);let{distinct_id:t,...r}=e||{};m.capture(a,r);}async function R(a,e){if(!m)return;$(e?.distinct_id,e?.email);let{distinct_id:t,...r}=e||{};m.capture(a,{feature:\"main\",...r});}var b=class{baseUrl;pixel;constructor(e=\"http://localhost:3030\"){this.baseUrl=e,this.pixel={type:t=>this.sendInputControl({type:\"WriteText\",data:t}),press:t=>this.sendInputControl({type:\"KeyPress\",data:t}),moveMouse:(t,r)=>this.sendInputControl({type:\"MouseMove\",data:{x:t,y:r}}),click:t=>this.sendInputControl({type:\"MouseClick\",data:t})};}async sendInputControl(e){try{let t=await fetch(`${this.baseUrl}/experimental/operator/pixel`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({action:e})});if(!t.ok)throw new Error(`http error! status: ${t.status}`);return (await t.json()).success}catch(t){return console.error(\"failed to control input:\",t),false}}locator(e){if(e.role&&e.id)throw new Error(\"only one of 'role' or 'id' can be specified. need both? dm us!\");let t={app_name:e.app,window_name:e.window,locator:e.role||(e.id?`#${e.id}`:\"\"),use_background_apps:e.useBackgroundApps,activate_app:e.activateApp};return new E(this.baseUrl,t)}async click(e){let t={app_name:e.app,window_name:e.window,locator:`#${e.id}`,use_background_apps:e.useBackgroundApps,activate_app:e.activateApp!==false},r=await fetch(`${this.baseUrl}/experimental/operator/click`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({selector:t})});if(!r.ok){let s=await r.text();console.log(\"error response:\",s);try{let o=JSON.parse(s);throw new Error(`failed to click element: ${o.error||r.statusText}`)}catch{throw new Error(`failed to click element (status ${r.status}): ${s||r.statusText}`)}}let n=await r.json();if(console.log(\"debug: click response data:\",JSON.stringify(n,null,2)),!n.success)throw new Error(`click operation failed: ${n.error||\"unknown error\"}`);return n.result?u(n.result):n.method?{method:n.method,coordinates:n.coordinates,details:n.details||\"Click operation succeeded\"}:(console.log(\"warning: click response missing expected structure, creating fallback object\"),{method:\"MouseSimulation\",coordinates:undefined,details:\"Click operation succeeded but returned unexpected data structure\"})}async fill(e){let t={app_name:e.app,locator:`#${e.id}`,use_background_apps:e.useBackgroundApps,activate_app:e.activateApp!==false};console.log(\"selector\",t);let r=await fetch(`${this.baseUrl}/experimental/operator/type`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({selector:t,text:e.value})});if(!r.ok){let s=await r.text();console.log(\"error response:\",s);try{let o=JSON.parse(s);throw new Error(`failed to type text: ${o.error||r.statusText}`)}catch{throw new Error(`failed to type text (status ${r.status}): ${s||r.statusText}`)}}return (await r.json()).success}getByRole(e,t){return this.locator({app:t?.app||\"\",window:t?.window,role:e,useBackgroundApps:t?.useBackgroundApps,activateApp:t?.activateApp})}getById(e,t){return this.locator({app:t?.app||\"\",window:t?.window,id:e,useBackgroundApps:t?.useBackgroundApps,activateApp:t?.activateApp})}async getText(e){let t={appName:e.app,windowName:e.window,maxDepth:e.maxDepth,useBackgroundApps:e.useBackgroundApps,activateApp:e.activateApp!==false},r=await fetch(`${this.baseUrl}/experimental/operator/get_text`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify(h(t))});if(!r.ok){let s=await r.text();console.log(\"error response:\",s);try{let o=JSON.parse(s);throw new Error(`failed to get text: ${o.error||r.statusText}`)}catch{throw new Error(`failed to get text (status ${r.status}): ${s||r.statusText}`)}}let n=await r.json();if(console.log(\"debug: text response data:\",JSON.stringify(n,null,2)),!n.success)throw new Error(`get_text operation failed: ${n.error||\"unknown error\"}`);return u(n)}async getInteractableElements(e){let t={appName:e.app,windowName:e.window,withTextOnly:e.withTextOnly,interactableOnly:e.interactableOnly,includeSometimesInteractable:e.includeSometimesInteractable,maxElements:e.maxElements,useBackgroundApps:e.useBackgroundApps,activateApp:e.activateApp},r=await fetch(`${this.baseUrl}/experimental/operator/list-interactable-elements`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify(h(t))});if(!r.ok){let s=await r.text();console.log(\"error response:\",s);try{let o=JSON.parse(s);throw new Error(`failed to get interactable elements: ${o.error||r.statusText}`)}catch{throw new Error(`failed to get interactable elements (status ${r.status}): ${s||r.statusText}`)}}let n=await r.json();return console.log(\"debug: text response data:\",JSON.stringify(n,null,2)),u(n)}async clickByIndex(e){let t=await fetch(`${this.baseUrl}/experimental/operator/click-by-index`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({element_index:e})});if(!t.ok){let n=await t.text();console.log(\"error response:\",n);try{let s=JSON.parse(n);throw new Error(`failed to click element by index: ${s.error||t.statusText}`)}catch{throw new Error(`failed to click element by index (status ${t.status}): ${n||t.statusText}`)}}let r=await t.json();if(!r.success)throw new Error(`click operation failed: ${r.message||\"unknown error\"}`);return r.success}async typeByIndex(e,t){let r=await fetch(`${this.baseUrl}/experimental/operator/type-by-index`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({element_index:e,text:t})});if(!r.ok){let s=await r.text();console.log(\"error response:\",s);try{let o=JSON.parse(s);throw new Error(`failed to type text into element by index: ${o.error||r.statusText}`)}catch{throw new Error(`failed to type text into element by index (status ${r.status}): ${s||r.statusText}`)}}let n=await r.json();if(!n.success)throw new Error(`type operation failed: ${n.message||\"unknown error\"}`);return n.success}async pressKey(e){let t={app_name:e.app,window_name:e.window,locator:`#${e.id}`,use_background_apps:e.useBackgroundApps,activate_app:e.activateApp!==false},r=await fetch(`${this.baseUrl}/experimental/operator/press-key`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify(h({selector:t,keyCombo:e.key}))});if(!r.ok){let s=await r.text();console.log(\"error response:\",s);try{let o=JSON.parse(s);throw new Error(`failed to press key: ${o.error||r.statusText}`)}catch{throw new Error(`failed to press key (status ${r.status}): ${s||r.statusText}`)}}return (await r.json()).success}async pressKeyByIndex(e,t){let r=await fetch(`${this.baseUrl}/experimental/operator/press-key-by-index`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({element_index:e,key_combo:t})});if(!r.ok){let s=await r.text();console.log(\"error response:\",s);try{let o=JSON.parse(s);throw new Error(`failed to press key on element by index: ${o.error||r.statusText}`)}catch{throw new Error(`failed to press key on element by index (status ${r.status}): ${s||r.statusText}`)}}let n=await r.json();if(!n.success)throw new Error(`press key operation failed: ${n.message||\"unknown error\"}`);return n.success}async openApplication(e){let t=await fetch(`${this.baseUrl}/experimental/operator/open-application`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({app_name:e})});if(!t.ok){let n=await t.text();console.log(\"error response:\",n);try{let s=JSON.parse(n);throw new Error(`failed to open application: ${s.error||t.statusText}`)}catch{throw new Error(`failed to open application (status ${t.status}): ${n||t.statusText}`)}}let r=await t.json();if(!r.success)throw new Error(`open application operation failed: ${r.message||\"unknown error\"}`);return r.success}async openUrl(e,t){let r=await fetch(`${this.baseUrl}/experimental/operator/open-url`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({url:e,browser:t})});if(!r.ok){let s=await r.text();console.log(\"error response:\",s);try{let o=JSON.parse(s);throw new Error(`failed to open url: ${o.error||r.statusText}`)}catch{throw new Error(`failed to open url (status ${r.status}): ${s||r.statusText}`)}}let n=await r.json();if(!n.success)throw new Error(`open url operation failed: ${n.message||\"unknown error\"}`);return n.success}async scroll(e){let t={app_name:e.app,window_name:e.window,locator:e.id?`#${e.id}`:\"\",use_background_apps:e.useBackgroundApps,activate_app:e.activateApp!==false},r=await fetch(`${this.baseUrl}/experimental/operator/scroll`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({selector:t,direction:e.direction,amount:e.amount})});if(!r.ok){let s=await r.text();console.log(\"error response:\",s);try{let o=JSON.parse(s);throw new Error(`failed to scroll element: ${o.error||r.statusText}`)}catch{throw new Error(`failed to scroll element (status ${r.status}): ${s||r.statusText}`)}}return (await r.json()).success}},E=class{baseUrl;selector;constructor(e,t){this.baseUrl=e,this.selector=t;}async first(e){let t=await this.all(1,e);return t.length>0?t[0]:null}async all(e,t){let r=await fetch(`${this.baseUrl}/experimental/operator`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({selector:this.selector,max_results:e,max_depth:t})});if(!r.ok){let s=await r.text();console.log(\"error response:\",s);try{let o=JSON.parse(s);throw new Error(`failed to find elements: ${o.error||r.statusText}`)}catch{throw new Error(`failed to find elements (status ${r.status}): ${s||r.statusText}`)}}return (await r.json()).data}async click(){let e=await fetch(`${this.baseUrl}/experimental/operator/click`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({selector:{...this.selector,activate_app:this.selector.activate_app!==false}})});if(!e.ok){let r=await e.text();console.log(\"error response:\",r);try{let n=JSON.parse(r);throw new Error(`failed to click element: ${n.error||e.statusText}`)}catch{throw new Error(`failed to click element (status ${e.status}): ${r||e.statusText}`)}}let t=await e.json();if(console.log(\"debug: click response data:\",JSON.stringify(t,null,2)),!t.success)throw new Error(`click operation failed: ${t.error||\"unknown error\"}`);return t.result?t.result:t.method?{method:t.method,coordinates:t.coordinates,details:t.details||\"Click operation succeeded\"}:(console.log(\"warning: click response missing expected structure, creating fallback object\"),{method:\"MouseSimulation\",coordinates:undefined,details:\"Click operation succeeded but returned unexpected data structure\"})}async fill(e){let t=await fetch(`${this.baseUrl}/experimental/operator/type`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({selector:{...this.selector,activate_app:this.selector.activate_app!==false},text:e})});if(!t.ok){let n=await t.text();console.log(\"error response:\",n);try{let s=JSON.parse(n);throw new Error(`failed to type text: ${s.error||t.statusText}`)}catch{throw new Error(`failed to type text (status ${t.status}): ${n||t.statusText}`)}}return (await t.json()).success}async exists(){try{return !!await this.first()}catch{return  false}}async waitFor(e={}){let t=Date.now(),r=e.timeout||3e4;for(;Date.now()-t<r;){try{let n=await this.first();if(n)return n}catch{}await new Promise(n=>setTimeout(n,100));}return null}async pressKey(e){let t=await fetch(`${this.baseUrl}/experimental/operator/press-key`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({selector:{...this.selector,activate_app:this.selector.activate_app!==false},key_combo:e})});if(!t.ok){let n=await t.text();console.log(\"error response:\",n);try{let s=JSON.parse(n);throw new Error(`failed to press key: ${s.error||t.statusText}`)}catch{throw new Error(`failed to press key (status ${t.status}): ${n||t.statusText}`)}}return (await t.json()).success}async scroll(e,t){let r=await fetch(`${this.baseUrl}/experimental/operator/scroll`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({selector:{...this.selector,activate_app:this.selector.activate_app!==false},direction:e,amount:t})});if(!r.ok){let s=await r.text();console.log(\"error response:\",s);try{let o=JSON.parse(s);throw new Error(`failed to scroll element: ${o.error||r.statusText}`)}catch{throw new Error(`failed to scroll element (status ${r.status}): ${s||r.statusText}`)}}return (await r.json()).success}};var x=class{async list(){try{let t=await fetch(\"http://localhost:3030/pipes/list\",{method:\"GET\",headers:{\"Content-Type\":\"application/json\"}});if(!t.ok)throw new Error(`http error! status: ${t.status}`);return {success:!0,data:(await t.json()).data}}catch(e){return console.error(\"failed to list pipes:\",e),{success:false,error:e}}}async download(e){try{let r=await fetch(\"http://localhost:3030/pipes/download\",{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({url:e})});if(!r.ok)throw new Error(`http error! status: ${r.status}`);return {success:!0,data:(await r.json()).data}}catch(t){return console.error(\"failed to download pipe:\",t),{success:false,error:t}}}async enable(e){try{return (await fetch(\"http://localhost:3030/pipes/enable\",{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({pipe_id:e})})).ok}catch(t){return console.error(\"failed to enable pipe:\",t),false}}async disable(e){try{return (await fetch(\"http://localhost:3030/pipes/disable\",{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({pipe_id:e})})).ok}catch(t){return console.error(\"failed to disable pipe:\",t),false}}async update(e,t){try{return (await fetch(\"http://localhost:3030/pipes/update\",{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({pipe_id:e,config:t})})).ok}catch(r){return console.error(\"failed to update pipe:\",r),false}}async info(e){try{let r=await fetch(`http://localhost:3030/pipes/info/${e}`,{method:\"GET\",headers:{\"Content-Type\":\"application/json\"}});if(!r.ok)throw new Error(`http error! status: ${r.status}`);return {success:!0,data:(await r.json()).data}}catch(t){return console.error(\"failed to get pipe info:\",t),{success:false,error:t}}}async downloadPrivate(e,t,r){try{let s=await fetch(\"http://localhost:3030/pipes/download-private\",{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({url:e,pipe_name:t,pipe_id:r})});if(!s.ok)throw new Error(`http error! status: ${s.status}`);return {success:!0,data:(await s.json()).data}}catch(n){return console.error(\"failed to download private pipe:\",n),{success:false,error:n}}}async delete(e){try{return (await fetch(\"http://localhost:3030/pipes/delete\",{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({pipe_id:e})})).ok}catch(t){return console.error(\"failed to delete pipe:\",t),false}}};O({init:posthog_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].init.bind(posthog_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),identify:posthog_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].identify.bind(posthog_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),capture:posthog_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].capture.bind(posthog_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])});var j=\"ws://localhost:3030/ws/events\",g=null,f=null;async function*S(a=false){let e=a?g:f;(!e||e.readyState===WebSocket.CLOSED)&&(console.log(\"creating new websocket connection, includeImages:\",a),e=new WebSocket(`${j}?images=${a}`),a?g=e:f=e,await new Promise((s,o)=>{let c=()=>{console.log(\"websocket connected\"),s(undefined);},p=i=>{console.error(\"websocket connection error:\",i),o(i);};e.addEventListener(\"open\",c,{once:true}),e.addEventListener(\"error\",p,{once:true});}));let t=[],r=null,n=s=>{r?(r(s),r=null):t.push(s);};e.addEventListener(\"message\",n);try{for(;;){let s=await new Promise(o=>{t.length>0?o(t.shift()):r=o;});yield JSON.parse(s.data);}}finally{e.removeEventListener(\"message\",n);}}var P=class{operator=new b;async initAnalyticsIfNeeded(){try{let e=new EventSource(\"http://localhost:11435/sse/settings\"),t=await new Promise((r,n)=>{let s=setTimeout(()=>{e.close(),n(new Error(\"settings stream timeout\"));},5e3);e.onmessage=o=>{clearTimeout(s),e.close();let c=JSON.parse(o.data),p=c.find(([l])=>l===\"analyticsEnabled\")?.[1]??!1,i=c.find(([l])=>l===\"user.clerk_id\")?.[1]??void 0,d=c.find(([l])=>l===\"user.email\")?.[1]??void 0;r({analyticsEnabled:p,userId:i,email:d});},e.onerror=o=>{clearTimeout(s),e.close(),n(o);};});return {analyticsEnabled:t.analyticsEnabled,userId:t.userId,email:t.email}}catch(e){return console.error(\"failed to fetch settings, defaulting to analytics enabled:\",e),{analyticsEnabled:false,userId:undefined}}}async sendDesktopNotification(e){let{userId:t,email:r}=await this.initAnalyticsIfNeeded(),n=\"http://localhost:11435\";try{return await fetch(`${n}/notify`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify(e)}),await this.captureEvent(\"notification_sent\",{distinct_id:t,email:r,success:!0}),!0}catch{return  false}}async queryScreenpipe(e){console.log(\"queryScreenpipe:\",e);let{userId:t,email:r}=await this.initAnalyticsIfNeeded(),n=new URLSearchParams;Object.entries(e).forEach(([o,c])=>{if(c!==undefined&&c!==\"\")if(o===\"speakerIds\"&&Array.isArray(c))c.length>0&&n.append(w(o),c.join(\",\"));else {let p=w(o);n.append(p,c.toString());}});let s=`http://localhost:3030/search?${n}`;try{let o=await fetch(s);if(!o.ok){let p=await o.text(),i;try{i=JSON.parse(p),console.error(\"screenpipe api error:\",{status:o.status,error:i});}catch{console.error(\"screenpipe api error:\",{status:o.status,error:p});}throw new Error(`http error! status: ${o.status}`)}let c=await o.json();return await T(\"search_performed\",{distinct_id:t,content_type:e.contentType,result_count:c.pagination.total,email:r}),u(c)}catch(o){throw console.error(\"error querying screenpipe:\",o),o}}pipes={list:async()=>{try{let e=await fetch(\"http://localhost:3030/pipes/list\",{method:\"GET\",headers:{\"Content-Type\":\"application/json\"}});if(!e.ok)throw new Error(`http error! status: ${e.status}`);return {success:!0,data:(await e.json()).data}}catch(e){return console.error(\"failed to list pipes:\",e),{success:false,error:e}}},download:async e=>{try{let t=await fetch(\"http://localhost:3030/pipes/download\",{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({url:e})});if(!t.ok)throw new Error(`http error! status: ${t.status}`);return {success:!0,data:(await t.json()).data}}catch(t){return console.error(\"failed to download pipe:\",t),{success:false,error:t}}},enable:async e=>{try{return (await fetch(\"http://localhost:3030/pipes/enable\",{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({pipe_id:e})})).ok}catch(t){return console.error(\"failed to enable pipe:\",t),false}},disable:async e=>{try{return (await fetch(\"http://localhost:3030/pipes/disable\",{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({pipe_id:e})})).ok}catch(t){return console.error(\"failed to disable pipe:\",t),false}},update:async(e,t)=>{try{return (await fetch(\"http://localhost:3030/pipes/update\",{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({pipe_id:e,config:t})})).ok}catch(r){return console.error(\"failed to update pipe:\",r),false}},info:async e=>{try{let t=await fetch(`http://localhost:3030/pipes/info/${e}`,{method:\"GET\",headers:{\"Content-Type\":\"application/json\"}});if(!t.ok)throw new Error(`http error! status: ${t.status}`);return {success:!0,data:(await t.json()).data}}catch(t){return console.error(\"failed to get pipe info:\",t),{success:false,error:t}}},downloadPrivate:async(e,t,r)=>{try{let n=process.env.SCREENPIPE_SERVER_URL||\"http://localhost:3030\",s=await fetch(`${n}/pipes/download-private`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({url:e,pipe_name:t,pipe_id:r})});if(!s.ok)throw new Error(`http error! status: ${s.status}`);return {success:!0,data:(await s.json()).data}}catch(n){return console.error(\"failed to download private pipe:\",n),{success:false,error:n}}},delete:async e=>{try{let t=process.env.SCREENPIPE_SERVER_URL||\"http://localhost:3030\";return (await fetch(`${t}/pipes/delete`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({pipe_id:e})})).ok}catch(t){return console.error(\"failed to delete pipe:\",t),false}}};async*streamTranscriptions(){try{for(;;)for await(let e of S())if(e.name===\"transcription\"){let t=e.data;yield {id:crypto.randomUUID(),object:\"text_completion_chunk\",created:Date.now(),model:\"screenpipe-realtime\",choices:[{text:t.transcription,index:0,finish_reason:t.is_final?\"stop\":null}],metadata:{timestamp:t.timestamp,device:t.device,isInput:t.is_input,speaker:t.speaker}};}}catch(e){console.error(\"error streaming transcriptions:\",e);}}async*streamVision(e=false){try{for await(let t of S(e))if(t.name===\"ocr_result\"||t.name===\"ui_frame\"){let r=t.data;yield {type:t.name,data:r};}}catch(t){console.error(\"error streaming vision:\",t);}}async captureEvent(e,t){let{analyticsEnabled:r}=await this.initAnalyticsIfNeeded();if(r)return T(e,t)}async captureMainFeatureEvent(e,t){let{analyticsEnabled:r}=await this.initAnalyticsIfNeeded();if(r)return R(e,t)}async*streamEvents(e=false){for await(let t of S(e))yield t;}disconnect(){g&&(g.close(),g=null),f&&(f.close(),f=null);}async deduplicateText(e){if(e.length===0)return {groups:[],error:undefined};try{let r=[];for(let i=0;i<e.length;i+=50)r.push(e.slice(i,i+50));let s=(await Promise.all(r.map(async i=>{let d=await fetch(\"http://localhost:3030/v1/embeddings\",{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({model:\"all-MiniLM-L6-v2\",input:i.length===1?i[0]:i,encoding_format:\"float\"})});if(!d.ok)throw new Error(`http error! status: ${d.status}`);let l=await d.json();return l.data.map(k=>({text:i[l.data.indexOf(k)],embedding:k.embedding}))}))).flat(),o=.9,c=[],p=new Set;for(let i=0;i<s.length;i++){if(p.has(i))continue;let d={text:s[i].text,similar:[]};p.add(i);for(let l=i+1;l<s.length;l++){if(p.has(l))continue;J(s[i].embedding,s[l].embedding)>o&&(d.similar.push(s[l].text),p.add(l));}d.similar.length>0&&c.push(d);}return {groups:c}}catch(t){return console.error(\"failed to deduplicate texts:\",t),{groups:[],error:t?.toString()}}}};function J(a,e){let t=a.reduce((s,o,c)=>s+o*e[c],0),r=Math.sqrt(a.reduce((s,o)=>s+o*o,0)),n=Math.sqrt(e.reduce((s,o)=>s+o*o,0));return t/(r*n)}var _=new P,U=new x,H=_;_.pipes=U;\n//# sourceMappingURL=index.js.map\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHNjcmVlbnBpcGUvYnJvd3Nlci9kaXN0L2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUEwQixjQUFjLG9GQUFvRixjQUFjLGlDQUFpQyxnQkFBZ0IsR0FBRyxjQUFjLHFDQUFxQyxXQUFXLHNCQUFzQixHQUFHLEVBQUUsY0FBYyw0RkFBNEYsV0FBVyxzQkFBc0IsR0FBRyxJQUFJLGFBQWEsUUFBUSw2eEJBQTZ4Qiw2REFBNkQsb0dBQW9HLFNBQVMsa0JBQWtCLHdOQUF3TixvR0FBb0csY0FBYyxLQUFLLGdCQUFnQixrQkFBa0IsaUNBQWlDLGdCQUFnQixRQUFRLFdBQVcsc0JBQXNCLGFBQWEsMkJBQTJCLElBQUksbUJBQW1CLE9BQU8sZ0JBQWdCLHNCQUFzQixhQUFhLDJCQUEyQixJQUFJLG1CQUFtQixPQUFPLGFBQWEsb0JBQW9CLEdBQUcsWUFBWSxRQUFRLE1BQU0sdUNBQXVDLDJCQUEyQiwrQkFBK0Isd0JBQXdCLGtDQUFrQyx1QkFBdUIsMENBQTBDLHVCQUF1QixTQUFTLGtDQUFrQyx5QkFBeUIsSUFBSSwwQkFBMEIsSUFBSSxxQkFBcUIsYUFBYSwrQkFBK0IsdUJBQXVCLGtDQUFrQyxzQkFBc0IsU0FBUyxFQUFFLEVBQUUsZ0RBQWdELFNBQVMsR0FBRyxnQ0FBZ0MsU0FBUywwREFBMEQsV0FBVyxrR0FBa0csT0FBTyw4REFBOEQsS0FBSywwRUFBMEUsNkJBQTZCLGVBQWUsT0FBTyxnREFBZ0QsS0FBSyw2RUFBNkUsa0JBQWtCLGFBQWEsK0JBQStCLHVCQUF1QixrQ0FBa0Msc0JBQXNCLFdBQVcsRUFBRSxFQUFFLFVBQVUscUJBQXFCLGlDQUFpQyxJQUFJLG9CQUFvQiw0Q0FBNEMsc0JBQXNCLEdBQUcsTUFBTSxtREFBbUQsU0FBUyxLQUFLLGdCQUFnQixJQUFJLHFCQUFxQiw2SEFBNkgseUJBQXlCLEdBQUcsc0NBQXNDLHlGQUF5RiwrRkFBK0YsMEhBQTBILEVBQUUsY0FBYyxPQUFPLDJCQUEyQixLQUFLLDhFQUE4RSwwQkFBMEIscUJBQXFCLGFBQWEsOEJBQThCLHVCQUF1QixrQ0FBa0Msc0JBQXNCLHdCQUF3QixFQUFFLEVBQUUsVUFBVSxxQkFBcUIsaUNBQWlDLElBQUksb0JBQW9CLHdDQUF3QyxzQkFBc0IsR0FBRyxNQUFNLCtDQUErQyxTQUFTLEtBQUssZ0JBQWdCLElBQUksZ0NBQWdDLGVBQWUscUJBQXFCLHlHQUF5RyxFQUFFLGFBQWEscUJBQXFCLHVHQUF1RyxFQUFFLGlCQUFpQixPQUFPLDhIQUE4SCxrQkFBa0IsYUFBYSxrQ0FBa0MsdUJBQXVCLGtDQUFrQywyQkFBMkIsRUFBRSxVQUFVLHFCQUFxQixpQ0FBaUMsSUFBSSxvQkFBb0IsdUNBQXVDLHNCQUFzQixHQUFHLE1BQU0sOENBQThDLFNBQVMsS0FBSyxnQkFBZ0IsSUFBSSxxQkFBcUIsK0hBQStILHlCQUF5QixHQUFHLFlBQVksaUNBQWlDLE9BQU8sd1BBQXdQLGtCQUFrQixhQUFhLG9EQUFvRCx1QkFBdUIsa0NBQWtDLDJCQUEyQixFQUFFLFVBQVUscUJBQXFCLGlDQUFpQyxJQUFJLG9CQUFvQix3REFBd0Qsc0JBQXNCLEdBQUcsTUFBTSwrREFBK0QsU0FBUyxLQUFLLGdCQUFnQixJQUFJLHFCQUFxQiwrRUFBK0Usc0JBQXNCLHFCQUFxQixhQUFhLHdDQUF3Qyx1QkFBdUIsa0NBQWtDLHNCQUFzQixnQkFBZ0IsRUFBRSxFQUFFLFVBQVUscUJBQXFCLGlDQUFpQyxJQUFJLG9CQUFvQixxREFBcUQsc0JBQXNCLEdBQUcsTUFBTSw0REFBNEQsU0FBUyxLQUFLLGdCQUFnQixJQUFJLHFCQUFxQix5REFBeUQsMkJBQTJCLEdBQUcsaUJBQWlCLHVCQUF1QixxQkFBcUIsYUFBYSx1Q0FBdUMsdUJBQXVCLGtDQUFrQyxzQkFBc0IsdUJBQXVCLEVBQUUsRUFBRSxVQUFVLHFCQUFxQixpQ0FBaUMsSUFBSSxvQkFBb0IsOERBQThELHNCQUFzQixHQUFHLE1BQU0scUVBQXFFLFNBQVMsS0FBSyxnQkFBZ0IsSUFBSSxxQkFBcUIsd0RBQXdELDJCQUEyQixHQUFHLGlCQUFpQixrQkFBa0IsT0FBTyxnREFBZ0QsS0FBSyw2RUFBNkUsa0JBQWtCLGFBQWEsbUNBQW1DLHVCQUF1QixrQ0FBa0Msd0JBQXdCLDBCQUEwQixHQUFHLEVBQUUsVUFBVSxxQkFBcUIsaUNBQWlDLElBQUksb0JBQW9CLHdDQUF3QyxzQkFBc0IsR0FBRyxNQUFNLCtDQUErQyxTQUFTLEtBQUssZ0JBQWdCLElBQUksZ0NBQWdDLDJCQUEyQixxQkFBcUIsYUFBYSw0Q0FBNEMsdUJBQXVCLGtDQUFrQyxzQkFBc0IsNEJBQTRCLEVBQUUsRUFBRSxVQUFVLHFCQUFxQixpQ0FBaUMsSUFBSSxvQkFBb0IsNERBQTRELHNCQUFzQixHQUFHLE1BQU0sbUVBQW1FLFNBQVMsS0FBSyxnQkFBZ0IsSUFBSSxxQkFBcUIsNkRBQTZELDJCQUEyQixHQUFHLGlCQUFpQix5QkFBeUIscUJBQXFCLGFBQWEsMENBQTBDLHVCQUF1QixrQ0FBa0Msc0JBQXNCLFdBQVcsRUFBRSxFQUFFLFVBQVUscUJBQXFCLGlDQUFpQyxJQUFJLG9CQUFvQiwrQ0FBK0Msc0JBQXNCLEdBQUcsTUFBTSxzREFBc0QsU0FBUyxLQUFLLGdCQUFnQixJQUFJLHFCQUFxQixvRUFBb0UsMkJBQTJCLEdBQUcsaUJBQWlCLG1CQUFtQixxQkFBcUIsYUFBYSxrQ0FBa0MsdUJBQXVCLGtDQUFrQyxzQkFBc0IsZ0JBQWdCLEVBQUUsRUFBRSxVQUFVLHFCQUFxQixpQ0FBaUMsSUFBSSxvQkFBb0IsdUNBQXVDLHNCQUFzQixHQUFHLE1BQU0sOENBQThDLFNBQVMsS0FBSyxnQkFBZ0IsSUFBSSxxQkFBcUIsNERBQTRELDJCQUEyQixHQUFHLGlCQUFpQixnQkFBZ0IsT0FBTyxxREFBcUQsS0FBSyxnRkFBZ0Ysa0JBQWtCLGFBQWEsZ0NBQWdDLHVCQUF1QixrQ0FBa0Msc0JBQXNCLGlEQUFpRCxFQUFFLEVBQUUsVUFBVSxxQkFBcUIsaUNBQWlDLElBQUksb0JBQW9CLDZDQUE2QyxzQkFBc0IsR0FBRyxNQUFNLG9EQUFvRCxTQUFTLEtBQUssZ0JBQWdCLElBQUksaUNBQWlDLFNBQVMsUUFBUSxTQUFTLGlCQUFpQixnQ0FBZ0MsZUFBZSwwQkFBMEIsNEJBQTRCLGVBQWUscUJBQXFCLGFBQWEseUJBQXlCLHVCQUF1QixrQ0FBa0Msc0JBQXNCLGlEQUFpRCxFQUFFLEVBQUUsVUFBVSxxQkFBcUIsaUNBQWlDLElBQUksb0JBQW9CLDRDQUE0QyxzQkFBc0IsR0FBRyxNQUFNLG1EQUFtRCxTQUFTLEtBQUssZ0JBQWdCLElBQUksNkJBQTZCLGNBQWMscUJBQXFCLGFBQWEsK0JBQStCLHVCQUF1QixrQ0FBa0Msc0JBQXNCLFVBQVUsa0VBQWtFLEVBQUUsRUFBRSxVQUFVLHFCQUFxQixpQ0FBaUMsSUFBSSxvQkFBb0IsNENBQTRDLHNCQUFzQixHQUFHLE1BQU0sbURBQW1ELFNBQVMsS0FBSyxnQkFBZ0IsSUFBSSxxQkFBcUIsNkhBQTZILHlCQUF5QixHQUFHLG1DQUFtQyx5RkFBeUYsK0ZBQStGLDBIQUEwSCxFQUFFLGNBQWMscUJBQXFCLGFBQWEsOEJBQThCLHVCQUF1QixrQ0FBa0Msc0JBQXNCLFVBQVUsaUVBQWlFLFFBQVEsRUFBRSxFQUFFLFVBQVUscUJBQXFCLGlDQUFpQyxJQUFJLG9CQUFvQix3Q0FBd0Msc0JBQXNCLEdBQUcsTUFBTSwrQ0FBK0MsU0FBUyxLQUFLLGdCQUFnQixJQUFJLGdDQUFnQyxlQUFlLElBQUksNEJBQTRCLE1BQU0sZUFBZSxrQkFBa0IsRUFBRSxrQ0FBa0MsS0FBSyxlQUFlLEVBQUUsSUFBSSx5QkFBeUIsY0FBYyxPQUFPLHlDQUF5QyxZQUFZLGtCQUFrQixxQkFBcUIsYUFBYSxtQ0FBbUMsdUJBQXVCLGtDQUFrQyxzQkFBc0IsVUFBVSxpRUFBaUUsYUFBYSxFQUFFLEVBQUUsVUFBVSxxQkFBcUIsaUNBQWlDLElBQUksb0JBQW9CLHdDQUF3QyxzQkFBc0IsR0FBRyxNQUFNLCtDQUErQyxTQUFTLEtBQUssZ0JBQWdCLElBQUksZ0NBQWdDLGtCQUFrQixxQkFBcUIsYUFBYSxnQ0FBZ0MsdUJBQXVCLGtDQUFrQyxzQkFBc0IsVUFBVSxpRUFBaUUsc0JBQXNCLEVBQUUsRUFBRSxVQUFVLHFCQUFxQixpQ0FBaUMsSUFBSSxvQkFBb0IsNkNBQTZDLHNCQUFzQixHQUFHLE1BQU0sb0RBQW9ELFNBQVMsS0FBSyxnQkFBZ0IsSUFBSSxrQ0FBa0MsWUFBWSxhQUFhLElBQUksc0RBQXNELHNCQUFzQixtQ0FBbUMsRUFBRSxnREFBZ0QsU0FBUyxHQUFHLFFBQVEsdUNBQXVDLFNBQVMsaURBQWlELHdCQUF3QixrQkFBa0IsSUFBSSwwREFBMEQsdUJBQXVCLGtDQUFrQyxzQkFBc0IsTUFBTSxFQUFFLEVBQUUsZ0RBQWdELFNBQVMsR0FBRyxRQUFRLHVDQUF1QyxTQUFTLG9EQUFvRCx3QkFBd0IsZ0JBQWdCLElBQUksMERBQTBELHVCQUF1QixrQ0FBa0Msc0JBQXNCLFVBQVUsRUFBRSxNQUFNLFNBQVMsd0RBQXdELGlCQUFpQixJQUFJLDJEQUEyRCx1QkFBdUIsa0NBQWtDLHNCQUFzQixVQUFVLEVBQUUsTUFBTSxTQUFTLHlEQUF5RCxrQkFBa0IsSUFBSSwwREFBMEQsdUJBQXVCLGtDQUFrQyxzQkFBc0IsbUJBQW1CLEVBQUUsTUFBTSxTQUFTLHdEQUF3RCxjQUFjLElBQUksc0RBQXNELEVBQUUsR0FBRyxzQkFBc0IsbUNBQW1DLEVBQUUsZ0RBQWdELFNBQVMsR0FBRyxRQUFRLHVDQUF1QyxTQUFTLG9EQUFvRCx3QkFBd0IsNkJBQTZCLElBQUksa0VBQWtFLHVCQUF1QixrQ0FBa0Msc0JBQXNCLDRCQUE0QixFQUFFLEVBQUUsZ0RBQWdELFNBQVMsR0FBRyxRQUFRLHVDQUF1QyxTQUFTLDREQUE0RCx3QkFBd0IsZ0JBQWdCLElBQUksMERBQTBELHVCQUF1QixrQ0FBa0Msc0JBQXNCLFVBQVUsRUFBRSxNQUFNLFNBQVMsMERBQTBELEdBQUcsS0FBSyxrREFBQyxXQUFXLGtEQUFDLFdBQVcsa0RBQUMsZUFBZSxrREFBQyxVQUFVLGtEQUFDLGNBQWMsa0RBQUMsRUFBRSxFQUFFLG9EQUFvRCwwQkFBMEIsWUFBWSw4SEFBOEgsRUFBRSxVQUFVLEVBQUUsdUNBQXVDLFdBQVcsaURBQWlELE9BQU8sc0RBQXNELDZCQUE2QixVQUFVLGdDQUFnQyxVQUFVLEdBQUcsR0FBRyxzQkFBc0IsNEJBQTRCLGdDQUFnQyxJQUFJLE1BQU0sRUFBRSw0QkFBNEIsNkJBQTZCLEVBQUUsMkJBQTJCLFFBQVEscUNBQXFDLFlBQVksZUFBZSw4QkFBOEIsSUFBSSx5RkFBeUYsc0JBQXNCLG1EQUFtRCxNQUFNLGdCQUFnQiwwQkFBMEIsMktBQTJLLEdBQUcsb0NBQW9DLEdBQUcsZUFBZSxrQ0FBa0MsRUFBRSxRQUFRLG1FQUFtRSxTQUFTLHNGQUFzRiwwQ0FBMEMsaUNBQWlDLElBQUksaUJBQWlCLCtEQUErRCxJQUFJLHNCQUFzQixFQUFFLFVBQVUsdUJBQXVCLGtDQUFrQyx3QkFBd0IsK0NBQStDLGlDQUFpQyxLQUFLLE1BQU0sZUFBZSx5QkFBeUIsa0NBQWtDLElBQUksaUJBQWlCLDBEQUEwRCxvQ0FBb0Msc0dBQXNHLE1BQU0sV0FBVywyQkFBMkIsRUFBRSxzQ0FBc0MsRUFBRSxFQUFFLElBQUkscUJBQXFCLFVBQVUsdUJBQXVCLElBQUksdURBQXVELHdCQUF3QixHQUFHLE1BQU0sdUNBQXVDLHdCQUF3QixHQUFHLHVDQUF1QyxTQUFTLEdBQUcscUJBQXFCLG1DQUFtQyxpRkFBaUYsT0FBTyxTQUFTLHVEQUF1RCxPQUFPLGVBQWUsSUFBSSxzREFBc0Qsc0JBQXNCLG1DQUFtQyxFQUFFLGdEQUFnRCxTQUFTLEdBQUcsUUFBUSx1Q0FBdUMsU0FBUyxpREFBaUQsd0JBQXdCLG9CQUFvQixJQUFJLDBEQUEwRCx1QkFBdUIsa0NBQWtDLHNCQUFzQixNQUFNLEVBQUUsRUFBRSxnREFBZ0QsU0FBUyxHQUFHLFFBQVEsdUNBQXVDLFNBQVMsb0RBQW9ELHdCQUF3QixrQkFBa0IsSUFBSSwwREFBMEQsdUJBQXVCLGtDQUFrQyxzQkFBc0IsVUFBVSxFQUFFLE1BQU0sU0FBUyx3REFBd0QsbUJBQW1CLElBQUksMkRBQTJELHVCQUF1QixrQ0FBa0Msc0JBQXNCLFVBQVUsRUFBRSxNQUFNLFNBQVMseURBQXlELHFCQUFxQixJQUFJLDBEQUEwRCx1QkFBdUIsa0NBQWtDLHNCQUFzQixtQkFBbUIsRUFBRSxNQUFNLFNBQVMsd0RBQXdELGdCQUFnQixJQUFJLHNEQUFzRCxFQUFFLEdBQUcsc0JBQXNCLG1DQUFtQyxFQUFFLGdEQUFnRCxTQUFTLEdBQUcsUUFBUSx1Q0FBdUMsU0FBUyxvREFBb0Qsd0JBQXdCLGdDQUFnQyxJQUFJLGtGQUFrRixFQUFFLDBCQUEwQix1QkFBdUIsa0NBQWtDLHNCQUFzQiw0QkFBNEIsRUFBRSxFQUFFLGdEQUFnRCxTQUFTLEdBQUcsUUFBUSx1Q0FBdUMsU0FBUyw0REFBNEQsd0JBQXdCLGtCQUFrQixJQUFJLGlFQUFpRSx1QkFBdUIsRUFBRSxnQkFBZ0IsdUJBQXVCLGtDQUFrQyxzQkFBc0IsVUFBVSxFQUFFLE1BQU0sU0FBUywwREFBMEQsNkJBQTZCLElBQUksTUFBTSxxREFBcUQsYUFBYSxPQUFPLCtHQUErRyxrRUFBa0UsWUFBWSwrRUFBK0UsU0FBUyxxREFBcUQsNEJBQTRCLElBQUksdUVBQXVFLGFBQWEsT0FBTyxzQkFBc0IsU0FBUyw2Q0FBNkMsd0JBQXdCLElBQUksbUJBQW1CLG9DQUFvQyxtQkFBbUIsbUNBQW1DLElBQUksbUJBQW1CLG9DQUFvQyxtQkFBbUIsNEJBQTRCLGlDQUFpQyxhQUFhLDZDQUE2Qyx5QkFBeUIsd0JBQXdCLDJCQUEyQixJQUFJLFNBQVMsWUFBWSxXQUFXLDhCQUE4Qix5Q0FBeUMseURBQXlELHVCQUF1QixrQ0FBa0Msc0JBQXNCLDJFQUEyRSxFQUFFLEVBQUUsZ0RBQWdELFNBQVMsR0FBRyxxQkFBcUIsdUJBQXVCLGdEQUFnRCxHQUFHLCtCQUErQixZQUFZLFdBQVcsS0FBSyxxQkFBcUIsT0FBTywyQkFBMkIsU0FBUyxjQUFjLFdBQVcsS0FBSyxxQkFBcUIsMEVBQTBFLCtCQUErQixRQUFRLFVBQVUsU0FBUyx3REFBd0Qsa0NBQWtDLGdCQUFnQixnSEFBZ0gsZUFBZSx3QkFBd0I7QUFDbjR1QjtBQUMxQyIsInNvdXJjZXMiOlsiRzpcXHByb2plY3RzXFxzY3JlZW5waXBlXFxzY3JlZW5waXBlXFxwaXBlc1xcZm9jdXMtYW5hbHl0aWNzLWNvYWNoXFxub2RlX21vZHVsZXNcXEBzY3JlZW5waXBlXFxicm93c2VyXFxkaXN0XFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeSBmcm9tJ3Bvc3Rob2ctanMnO2Z1bmN0aW9uIEMoYSl7cmV0dXJuIGEucmVwbGFjZSgvKFstX11bYS16XSkvZyxlPT5lLnRvVXBwZXJDYXNlKCkucmVwbGFjZShcIi1cIixcIlwiKS5yZXBsYWNlKFwiX1wiLFwiXCIpKX1mdW5jdGlvbiB3KGEpe3JldHVybiBhLnJlcGxhY2UoL1tBLVpdL2csZT0+YF8ke2UudG9Mb3dlckNhc2UoKX1gKX1mdW5jdGlvbiBoKGEpe3JldHVybiBPYmplY3Qua2V5cyhhKS5yZWR1Y2UoKGUsdCk9PntsZXQgcj13KHQpO3JldHVybiBlW3JdPWgoYVt0XSksZX0se30pfWZ1bmN0aW9uIHUoYSl7cmV0dXJuIEFycmF5LmlzQXJyYXkoYSk/YS5tYXAodSk6YSE9PW51bGwmJnR5cGVvZiBhPT1cIm9iamVjdFwiP09iamVjdC5rZXlzKGEpLnJlZHVjZSgoZSx0KT0+e2xldCByPUModCk7cmV0dXJuIGVbcl09dShhW3RdKSxlfSx7fSk6YX1mdW5jdGlvbiBBKCl7cmV0dXJuIHtvcGVuYWlBcGlLZXk6XCJcIixkZWVwZ3JhbUFwaUtleTpcIlwiLGFpTW9kZWw6XCJncHQtNG9cIixhaVVybDpcImh0dHBzOi8vYXBpLm9wZW5haS5jb20vdjFcIixjdXN0b21Qcm9tcHQ6XCJSdWxlczpcXG4gICAgLSBZb3UgY2FuIGFuYWx5emUvdmlldy9zaG93L2FjY2VzcyB2aWRlb3MgdG8gdGhlIHVzZXIgYnkgcHV0dGluZyAubXA0IGZpbGVzIGluIGEgY29kZSBibG9jayAod2UnbGwgcmVuZGVyIGl0KSBsaWtlIHRoaXM6IGAvdXNlcnMvdmlkZW8ubXA0YCwgdXNlIHRoZSBleGFjdCwgYWJzb2x1dGUsIGZpbGUgcGF0aCBmcm9tIGZpbGVfcGF0aCBwcm9wZXJ0eVxcbiAgICAtIERvIG5vdCB0cnkgdG8gZW1iZWQgdmlkZW8gaW4gbGlua3MgKGUuZy4gW10oLm1wNCkgb3IgaHR0cHM6Ly8ubXA0KSBpbnN0ZWFkIHB1dCB0aGUgZmlsZV9wYXRoIGluIGEgY29kZSBibG9jayB1c2luZyBiYWNrdGlja3NcXG4gICAgLSBEbyBub3QgcHV0IHZpZGVvIGluIG11bHRpbGluZSBjb2RlIGJsb2NrIGl0IHdpbGwgbm90IHJlbmRlciB0aGUgdmlkZW8gKGUuZy4gYGBgYmFzaFxcbi5tcDRgYGAgSVMgV1JPTkcpIGluc3RlYWQgdXNpbmcgaW5saW5lIGNvZGUgYmxvY2sgd2l0aCBzaW5nbGUgYmFja3RpY2tcXG4gICAgLSBBbHdheXMgYW5zd2VyIG15IHF1ZXN0aW9uL2ludGVudCwgZG8gbm90IG1ha2UgdXAgdGhpbmdzXFxuICAgIFwiLHBvcnQ6MzAzMCxkYXRhRGlyOlwiZGVmYXVsdFwiLGRpc2FibGVBdWRpbzpmYWxzZSxpZ25vcmVkV2luZG93czpbXSxpbmNsdWRlZFdpbmRvd3M6W10sYWlQcm92aWRlclR5cGU6XCJvcGVuYWlcIixlbWJlZGRlZExMTTp7ZW5hYmxlZDpmYWxzZSxtb2RlbDpcImxsYW1hMy4yOjFiLWluc3RydWN0LXE0X0tfTVwiLHBvcnQ6MTE0MzR9LGVuYWJsZUZyYW1lQ2FjaGU6dHJ1ZSxlbmFibGVVaU1vbml0b3Jpbmc6ZmFsc2UsYWlNYXhDb250ZXh0Q2hhcnM6NTEyZTMsYW5hbHl0aWNzRW5hYmxlZDp0cnVlLHVzZXI6e3Rva2VuOlwiXCJ9LGN1c3RvbVNldHRpbmdzOnt9LG1vbml0b3JJZHM6W1wiZGVmYXVsdFwiXSxhdWRpb0RldmljZXM6W1wiZGVmYXVsdFwiXSxhdWRpb1RyYW5zY3JpcHRpb25FbmdpbmU6XCJ3aGlzcGVyLWxhcmdlLXYzLXR1cmJvXCIsZW5hYmxlUmVhbHRpbWVBdWRpb1RyYW5zY3JpcHRpb246ZmFsc2UscmVhbHRpbWVBdWRpb1RyYW5zY3JpcHRpb25FbmdpbmU6XCJkZWVwZ3JhbVwiLGRpc2FibGVWaXNpb246ZmFsc2UsYWlQcmVzZXRzOltdfX12YXIgdj1mYWxzZSxtPW51bGwsTj1cInBoY19CdDhHb1RCUGdrQ3BEcmJhSVp6SklFWXQwQ3JKamhCaXVMYUJjazFjbGNlXCIsST1cImh0dHBzOi8vZXUuaS5wb3N0aG9nLmNvbVwiO2Z1bmN0aW9uIE8oYSl7bT1hO31mdW5jdGlvbiAkKGEsZSl7IXYmJm0mJihtLmluaXQoTix7YXBpX2hvc3Q6SSxkaXN0aW5jdF9pZDphLGVtYWlsOmV9KSxtLmlkZW50aWZ5KGEse2VtYWlsOmV9KSx2PXRydWUpO31hc3luYyBmdW5jdGlvbiBUKGEsZSl7aWYoIW0pcmV0dXJuOyQoZT8uZGlzdGluY3RfaWQsZT8uZW1haWwpO2xldHtkaXN0aW5jdF9pZDp0LC4uLnJ9PWV8fHt9O20uY2FwdHVyZShhLHIpO31hc3luYyBmdW5jdGlvbiBSKGEsZSl7aWYoIW0pcmV0dXJuOyQoZT8uZGlzdGluY3RfaWQsZT8uZW1haWwpO2xldHtkaXN0aW5jdF9pZDp0LC4uLnJ9PWV8fHt9O20uY2FwdHVyZShhLHtmZWF0dXJlOlwibWFpblwiLC4uLnJ9KTt9dmFyIGI9Y2xhc3N7YmFzZVVybDtwaXhlbDtjb25zdHJ1Y3RvcihlPVwiaHR0cDovL2xvY2FsaG9zdDozMDMwXCIpe3RoaXMuYmFzZVVybD1lLHRoaXMucGl4ZWw9e3R5cGU6dD0+dGhpcy5zZW5kSW5wdXRDb250cm9sKHt0eXBlOlwiV3JpdGVUZXh0XCIsZGF0YTp0fSkscHJlc3M6dD0+dGhpcy5zZW5kSW5wdXRDb250cm9sKHt0eXBlOlwiS2V5UHJlc3NcIixkYXRhOnR9KSxtb3ZlTW91c2U6KHQscik9PnRoaXMuc2VuZElucHV0Q29udHJvbCh7dHlwZTpcIk1vdXNlTW92ZVwiLGRhdGE6e3g6dCx5OnJ9fSksY2xpY2s6dD0+dGhpcy5zZW5kSW5wdXRDb250cm9sKHt0eXBlOlwiTW91c2VDbGlja1wiLGRhdGE6dH0pfTt9YXN5bmMgc2VuZElucHV0Q29udHJvbChlKXt0cnl7bGV0IHQ9YXdhaXQgZmV0Y2goYCR7dGhpcy5iYXNlVXJsfS9leHBlcmltZW50YWwvb3BlcmF0b3IvcGl4ZWxgLHttZXRob2Q6XCJQT1NUXCIsaGVhZGVyczp7XCJDb250ZW50LVR5cGVcIjpcImFwcGxpY2F0aW9uL2pzb25cIn0sYm9keTpKU09OLnN0cmluZ2lmeSh7YWN0aW9uOmV9KX0pO2lmKCF0Lm9rKXRocm93IG5ldyBFcnJvcihgaHR0cCBlcnJvciEgc3RhdHVzOiAke3Quc3RhdHVzfWApO3JldHVybiAoYXdhaXQgdC5qc29uKCkpLnN1Y2Nlc3N9Y2F0Y2godCl7cmV0dXJuIGNvbnNvbGUuZXJyb3IoXCJmYWlsZWQgdG8gY29udHJvbCBpbnB1dDpcIix0KSxmYWxzZX19bG9jYXRvcihlKXtpZihlLnJvbGUmJmUuaWQpdGhyb3cgbmV3IEVycm9yKFwib25seSBvbmUgb2YgJ3JvbGUnIG9yICdpZCcgY2FuIGJlIHNwZWNpZmllZC4gbmVlZCBib3RoPyBkbSB1cyFcIik7bGV0IHQ9e2FwcF9uYW1lOmUuYXBwLHdpbmRvd19uYW1lOmUud2luZG93LGxvY2F0b3I6ZS5yb2xlfHwoZS5pZD9gIyR7ZS5pZH1gOlwiXCIpLHVzZV9iYWNrZ3JvdW5kX2FwcHM6ZS51c2VCYWNrZ3JvdW5kQXBwcyxhY3RpdmF0ZV9hcHA6ZS5hY3RpdmF0ZUFwcH07cmV0dXJuIG5ldyBFKHRoaXMuYmFzZVVybCx0KX1hc3luYyBjbGljayhlKXtsZXQgdD17YXBwX25hbWU6ZS5hcHAsd2luZG93X25hbWU6ZS53aW5kb3csbG9jYXRvcjpgIyR7ZS5pZH1gLHVzZV9iYWNrZ3JvdW5kX2FwcHM6ZS51c2VCYWNrZ3JvdW5kQXBwcyxhY3RpdmF0ZV9hcHA6ZS5hY3RpdmF0ZUFwcCE9PWZhbHNlfSxyPWF3YWl0IGZldGNoKGAke3RoaXMuYmFzZVVybH0vZXhwZXJpbWVudGFsL29wZXJhdG9yL2NsaWNrYCx7bWV0aG9kOlwiUE9TVFwiLGhlYWRlcnM6e1wiQ29udGVudC1UeXBlXCI6XCJhcHBsaWNhdGlvbi9qc29uXCJ9LGJvZHk6SlNPTi5zdHJpbmdpZnkoe3NlbGVjdG9yOnR9KX0pO2lmKCFyLm9rKXtsZXQgcz1hd2FpdCByLnRleHQoKTtjb25zb2xlLmxvZyhcImVycm9yIHJlc3BvbnNlOlwiLHMpO3RyeXtsZXQgbz1KU09OLnBhcnNlKHMpO3Rocm93IG5ldyBFcnJvcihgZmFpbGVkIHRvIGNsaWNrIGVsZW1lbnQ6ICR7by5lcnJvcnx8ci5zdGF0dXNUZXh0fWApfWNhdGNoe3Rocm93IG5ldyBFcnJvcihgZmFpbGVkIHRvIGNsaWNrIGVsZW1lbnQgKHN0YXR1cyAke3Iuc3RhdHVzfSk6ICR7c3x8ci5zdGF0dXNUZXh0fWApfX1sZXQgbj1hd2FpdCByLmpzb24oKTtpZihjb25zb2xlLmxvZyhcImRlYnVnOiBjbGljayByZXNwb25zZSBkYXRhOlwiLEpTT04uc3RyaW5naWZ5KG4sbnVsbCwyKSksIW4uc3VjY2Vzcyl0aHJvdyBuZXcgRXJyb3IoYGNsaWNrIG9wZXJhdGlvbiBmYWlsZWQ6ICR7bi5lcnJvcnx8XCJ1bmtub3duIGVycm9yXCJ9YCk7cmV0dXJuIG4ucmVzdWx0P3Uobi5yZXN1bHQpOm4ubWV0aG9kP3ttZXRob2Q6bi5tZXRob2QsY29vcmRpbmF0ZXM6bi5jb29yZGluYXRlcyxkZXRhaWxzOm4uZGV0YWlsc3x8XCJDbGljayBvcGVyYXRpb24gc3VjY2VlZGVkXCJ9Oihjb25zb2xlLmxvZyhcIndhcm5pbmc6IGNsaWNrIHJlc3BvbnNlIG1pc3NpbmcgZXhwZWN0ZWQgc3RydWN0dXJlLCBjcmVhdGluZyBmYWxsYmFjayBvYmplY3RcIikse21ldGhvZDpcIk1vdXNlU2ltdWxhdGlvblwiLGNvb3JkaW5hdGVzOnVuZGVmaW5lZCxkZXRhaWxzOlwiQ2xpY2sgb3BlcmF0aW9uIHN1Y2NlZWRlZCBidXQgcmV0dXJuZWQgdW5leHBlY3RlZCBkYXRhIHN0cnVjdHVyZVwifSl9YXN5bmMgZmlsbChlKXtsZXQgdD17YXBwX25hbWU6ZS5hcHAsbG9jYXRvcjpgIyR7ZS5pZH1gLHVzZV9iYWNrZ3JvdW5kX2FwcHM6ZS51c2VCYWNrZ3JvdW5kQXBwcyxhY3RpdmF0ZV9hcHA6ZS5hY3RpdmF0ZUFwcCE9PWZhbHNlfTtjb25zb2xlLmxvZyhcInNlbGVjdG9yXCIsdCk7bGV0IHI9YXdhaXQgZmV0Y2goYCR7dGhpcy5iYXNlVXJsfS9leHBlcmltZW50YWwvb3BlcmF0b3IvdHlwZWAse21ldGhvZDpcIlBPU1RcIixoZWFkZXJzOntcIkNvbnRlbnQtVHlwZVwiOlwiYXBwbGljYXRpb24vanNvblwifSxib2R5OkpTT04uc3RyaW5naWZ5KHtzZWxlY3Rvcjp0LHRleHQ6ZS52YWx1ZX0pfSk7aWYoIXIub2spe2xldCBzPWF3YWl0IHIudGV4dCgpO2NvbnNvbGUubG9nKFwiZXJyb3IgcmVzcG9uc2U6XCIscyk7dHJ5e2xldCBvPUpTT04ucGFyc2Uocyk7dGhyb3cgbmV3IEVycm9yKGBmYWlsZWQgdG8gdHlwZSB0ZXh0OiAke28uZXJyb3J8fHIuc3RhdHVzVGV4dH1gKX1jYXRjaHt0aHJvdyBuZXcgRXJyb3IoYGZhaWxlZCB0byB0eXBlIHRleHQgKHN0YXR1cyAke3Iuc3RhdHVzfSk6ICR7c3x8ci5zdGF0dXNUZXh0fWApfX1yZXR1cm4gKGF3YWl0IHIuanNvbigpKS5zdWNjZXNzfWdldEJ5Um9sZShlLHQpe3JldHVybiB0aGlzLmxvY2F0b3Ioe2FwcDp0Py5hcHB8fFwiXCIsd2luZG93OnQ/LndpbmRvdyxyb2xlOmUsdXNlQmFja2dyb3VuZEFwcHM6dD8udXNlQmFja2dyb3VuZEFwcHMsYWN0aXZhdGVBcHA6dD8uYWN0aXZhdGVBcHB9KX1nZXRCeUlkKGUsdCl7cmV0dXJuIHRoaXMubG9jYXRvcih7YXBwOnQ/LmFwcHx8XCJcIix3aW5kb3c6dD8ud2luZG93LGlkOmUsdXNlQmFja2dyb3VuZEFwcHM6dD8udXNlQmFja2dyb3VuZEFwcHMsYWN0aXZhdGVBcHA6dD8uYWN0aXZhdGVBcHB9KX1hc3luYyBnZXRUZXh0KGUpe2xldCB0PXthcHBOYW1lOmUuYXBwLHdpbmRvd05hbWU6ZS53aW5kb3csbWF4RGVwdGg6ZS5tYXhEZXB0aCx1c2VCYWNrZ3JvdW5kQXBwczplLnVzZUJhY2tncm91bmRBcHBzLGFjdGl2YXRlQXBwOmUuYWN0aXZhdGVBcHAhPT1mYWxzZX0scj1hd2FpdCBmZXRjaChgJHt0aGlzLmJhc2VVcmx9L2V4cGVyaW1lbnRhbC9vcGVyYXRvci9nZXRfdGV4dGAse21ldGhvZDpcIlBPU1RcIixoZWFkZXJzOntcIkNvbnRlbnQtVHlwZVwiOlwiYXBwbGljYXRpb24vanNvblwifSxib2R5OkpTT04uc3RyaW5naWZ5KGgodCkpfSk7aWYoIXIub2spe2xldCBzPWF3YWl0IHIudGV4dCgpO2NvbnNvbGUubG9nKFwiZXJyb3IgcmVzcG9uc2U6XCIscyk7dHJ5e2xldCBvPUpTT04ucGFyc2Uocyk7dGhyb3cgbmV3IEVycm9yKGBmYWlsZWQgdG8gZ2V0IHRleHQ6ICR7by5lcnJvcnx8ci5zdGF0dXNUZXh0fWApfWNhdGNoe3Rocm93IG5ldyBFcnJvcihgZmFpbGVkIHRvIGdldCB0ZXh0IChzdGF0dXMgJHtyLnN0YXR1c30pOiAke3N8fHIuc3RhdHVzVGV4dH1gKX19bGV0IG49YXdhaXQgci5qc29uKCk7aWYoY29uc29sZS5sb2coXCJkZWJ1ZzogdGV4dCByZXNwb25zZSBkYXRhOlwiLEpTT04uc3RyaW5naWZ5KG4sbnVsbCwyKSksIW4uc3VjY2Vzcyl0aHJvdyBuZXcgRXJyb3IoYGdldF90ZXh0IG9wZXJhdGlvbiBmYWlsZWQ6ICR7bi5lcnJvcnx8XCJ1bmtub3duIGVycm9yXCJ9YCk7cmV0dXJuIHUobil9YXN5bmMgZ2V0SW50ZXJhY3RhYmxlRWxlbWVudHMoZSl7bGV0IHQ9e2FwcE5hbWU6ZS5hcHAsd2luZG93TmFtZTplLndpbmRvdyx3aXRoVGV4dE9ubHk6ZS53aXRoVGV4dE9ubHksaW50ZXJhY3RhYmxlT25seTplLmludGVyYWN0YWJsZU9ubHksaW5jbHVkZVNvbWV0aW1lc0ludGVyYWN0YWJsZTplLmluY2x1ZGVTb21ldGltZXNJbnRlcmFjdGFibGUsbWF4RWxlbWVudHM6ZS5tYXhFbGVtZW50cyx1c2VCYWNrZ3JvdW5kQXBwczplLnVzZUJhY2tncm91bmRBcHBzLGFjdGl2YXRlQXBwOmUuYWN0aXZhdGVBcHB9LHI9YXdhaXQgZmV0Y2goYCR7dGhpcy5iYXNlVXJsfS9leHBlcmltZW50YWwvb3BlcmF0b3IvbGlzdC1pbnRlcmFjdGFibGUtZWxlbWVudHNgLHttZXRob2Q6XCJQT1NUXCIsaGVhZGVyczp7XCJDb250ZW50LVR5cGVcIjpcImFwcGxpY2F0aW9uL2pzb25cIn0sYm9keTpKU09OLnN0cmluZ2lmeShoKHQpKX0pO2lmKCFyLm9rKXtsZXQgcz1hd2FpdCByLnRleHQoKTtjb25zb2xlLmxvZyhcImVycm9yIHJlc3BvbnNlOlwiLHMpO3RyeXtsZXQgbz1KU09OLnBhcnNlKHMpO3Rocm93IG5ldyBFcnJvcihgZmFpbGVkIHRvIGdldCBpbnRlcmFjdGFibGUgZWxlbWVudHM6ICR7by5lcnJvcnx8ci5zdGF0dXNUZXh0fWApfWNhdGNoe3Rocm93IG5ldyBFcnJvcihgZmFpbGVkIHRvIGdldCBpbnRlcmFjdGFibGUgZWxlbWVudHMgKHN0YXR1cyAke3Iuc3RhdHVzfSk6ICR7c3x8ci5zdGF0dXNUZXh0fWApfX1sZXQgbj1hd2FpdCByLmpzb24oKTtyZXR1cm4gY29uc29sZS5sb2coXCJkZWJ1ZzogdGV4dCByZXNwb25zZSBkYXRhOlwiLEpTT04uc3RyaW5naWZ5KG4sbnVsbCwyKSksdShuKX1hc3luYyBjbGlja0J5SW5kZXgoZSl7bGV0IHQ9YXdhaXQgZmV0Y2goYCR7dGhpcy5iYXNlVXJsfS9leHBlcmltZW50YWwvb3BlcmF0b3IvY2xpY2stYnktaW5kZXhgLHttZXRob2Q6XCJQT1NUXCIsaGVhZGVyczp7XCJDb250ZW50LVR5cGVcIjpcImFwcGxpY2F0aW9uL2pzb25cIn0sYm9keTpKU09OLnN0cmluZ2lmeSh7ZWxlbWVudF9pbmRleDplfSl9KTtpZighdC5vayl7bGV0IG49YXdhaXQgdC50ZXh0KCk7Y29uc29sZS5sb2coXCJlcnJvciByZXNwb25zZTpcIixuKTt0cnl7bGV0IHM9SlNPTi5wYXJzZShuKTt0aHJvdyBuZXcgRXJyb3IoYGZhaWxlZCB0byBjbGljayBlbGVtZW50IGJ5IGluZGV4OiAke3MuZXJyb3J8fHQuc3RhdHVzVGV4dH1gKX1jYXRjaHt0aHJvdyBuZXcgRXJyb3IoYGZhaWxlZCB0byBjbGljayBlbGVtZW50IGJ5IGluZGV4IChzdGF0dXMgJHt0LnN0YXR1c30pOiAke258fHQuc3RhdHVzVGV4dH1gKX19bGV0IHI9YXdhaXQgdC5qc29uKCk7aWYoIXIuc3VjY2Vzcyl0aHJvdyBuZXcgRXJyb3IoYGNsaWNrIG9wZXJhdGlvbiBmYWlsZWQ6ICR7ci5tZXNzYWdlfHxcInVua25vd24gZXJyb3JcIn1gKTtyZXR1cm4gci5zdWNjZXNzfWFzeW5jIHR5cGVCeUluZGV4KGUsdCl7bGV0IHI9YXdhaXQgZmV0Y2goYCR7dGhpcy5iYXNlVXJsfS9leHBlcmltZW50YWwvb3BlcmF0b3IvdHlwZS1ieS1pbmRleGAse21ldGhvZDpcIlBPU1RcIixoZWFkZXJzOntcIkNvbnRlbnQtVHlwZVwiOlwiYXBwbGljYXRpb24vanNvblwifSxib2R5OkpTT04uc3RyaW5naWZ5KHtlbGVtZW50X2luZGV4OmUsdGV4dDp0fSl9KTtpZighci5vayl7bGV0IHM9YXdhaXQgci50ZXh0KCk7Y29uc29sZS5sb2coXCJlcnJvciByZXNwb25zZTpcIixzKTt0cnl7bGV0IG89SlNPTi5wYXJzZShzKTt0aHJvdyBuZXcgRXJyb3IoYGZhaWxlZCB0byB0eXBlIHRleHQgaW50byBlbGVtZW50IGJ5IGluZGV4OiAke28uZXJyb3J8fHIuc3RhdHVzVGV4dH1gKX1jYXRjaHt0aHJvdyBuZXcgRXJyb3IoYGZhaWxlZCB0byB0eXBlIHRleHQgaW50byBlbGVtZW50IGJ5IGluZGV4IChzdGF0dXMgJHtyLnN0YXR1c30pOiAke3N8fHIuc3RhdHVzVGV4dH1gKX19bGV0IG49YXdhaXQgci5qc29uKCk7aWYoIW4uc3VjY2Vzcyl0aHJvdyBuZXcgRXJyb3IoYHR5cGUgb3BlcmF0aW9uIGZhaWxlZDogJHtuLm1lc3NhZ2V8fFwidW5rbm93biBlcnJvclwifWApO3JldHVybiBuLnN1Y2Nlc3N9YXN5bmMgcHJlc3NLZXkoZSl7bGV0IHQ9e2FwcF9uYW1lOmUuYXBwLHdpbmRvd19uYW1lOmUud2luZG93LGxvY2F0b3I6YCMke2UuaWR9YCx1c2VfYmFja2dyb3VuZF9hcHBzOmUudXNlQmFja2dyb3VuZEFwcHMsYWN0aXZhdGVfYXBwOmUuYWN0aXZhdGVBcHAhPT1mYWxzZX0scj1hd2FpdCBmZXRjaChgJHt0aGlzLmJhc2VVcmx9L2V4cGVyaW1lbnRhbC9vcGVyYXRvci9wcmVzcy1rZXlgLHttZXRob2Q6XCJQT1NUXCIsaGVhZGVyczp7XCJDb250ZW50LVR5cGVcIjpcImFwcGxpY2F0aW9uL2pzb25cIn0sYm9keTpKU09OLnN0cmluZ2lmeShoKHtzZWxlY3Rvcjp0LGtleUNvbWJvOmUua2V5fSkpfSk7aWYoIXIub2spe2xldCBzPWF3YWl0IHIudGV4dCgpO2NvbnNvbGUubG9nKFwiZXJyb3IgcmVzcG9uc2U6XCIscyk7dHJ5e2xldCBvPUpTT04ucGFyc2Uocyk7dGhyb3cgbmV3IEVycm9yKGBmYWlsZWQgdG8gcHJlc3Mga2V5OiAke28uZXJyb3J8fHIuc3RhdHVzVGV4dH1gKX1jYXRjaHt0aHJvdyBuZXcgRXJyb3IoYGZhaWxlZCB0byBwcmVzcyBrZXkgKHN0YXR1cyAke3Iuc3RhdHVzfSk6ICR7c3x8ci5zdGF0dXNUZXh0fWApfX1yZXR1cm4gKGF3YWl0IHIuanNvbigpKS5zdWNjZXNzfWFzeW5jIHByZXNzS2V5QnlJbmRleChlLHQpe2xldCByPWF3YWl0IGZldGNoKGAke3RoaXMuYmFzZVVybH0vZXhwZXJpbWVudGFsL29wZXJhdG9yL3ByZXNzLWtleS1ieS1pbmRleGAse21ldGhvZDpcIlBPU1RcIixoZWFkZXJzOntcIkNvbnRlbnQtVHlwZVwiOlwiYXBwbGljYXRpb24vanNvblwifSxib2R5OkpTT04uc3RyaW5naWZ5KHtlbGVtZW50X2luZGV4OmUsa2V5X2NvbWJvOnR9KX0pO2lmKCFyLm9rKXtsZXQgcz1hd2FpdCByLnRleHQoKTtjb25zb2xlLmxvZyhcImVycm9yIHJlc3BvbnNlOlwiLHMpO3RyeXtsZXQgbz1KU09OLnBhcnNlKHMpO3Rocm93IG5ldyBFcnJvcihgZmFpbGVkIHRvIHByZXNzIGtleSBvbiBlbGVtZW50IGJ5IGluZGV4OiAke28uZXJyb3J8fHIuc3RhdHVzVGV4dH1gKX1jYXRjaHt0aHJvdyBuZXcgRXJyb3IoYGZhaWxlZCB0byBwcmVzcyBrZXkgb24gZWxlbWVudCBieSBpbmRleCAoc3RhdHVzICR7ci5zdGF0dXN9KTogJHtzfHxyLnN0YXR1c1RleHR9YCl9fWxldCBuPWF3YWl0IHIuanNvbigpO2lmKCFuLnN1Y2Nlc3MpdGhyb3cgbmV3IEVycm9yKGBwcmVzcyBrZXkgb3BlcmF0aW9uIGZhaWxlZDogJHtuLm1lc3NhZ2V8fFwidW5rbm93biBlcnJvclwifWApO3JldHVybiBuLnN1Y2Nlc3N9YXN5bmMgb3BlbkFwcGxpY2F0aW9uKGUpe2xldCB0PWF3YWl0IGZldGNoKGAke3RoaXMuYmFzZVVybH0vZXhwZXJpbWVudGFsL29wZXJhdG9yL29wZW4tYXBwbGljYXRpb25gLHttZXRob2Q6XCJQT1NUXCIsaGVhZGVyczp7XCJDb250ZW50LVR5cGVcIjpcImFwcGxpY2F0aW9uL2pzb25cIn0sYm9keTpKU09OLnN0cmluZ2lmeSh7YXBwX25hbWU6ZX0pfSk7aWYoIXQub2spe2xldCBuPWF3YWl0IHQudGV4dCgpO2NvbnNvbGUubG9nKFwiZXJyb3IgcmVzcG9uc2U6XCIsbik7dHJ5e2xldCBzPUpTT04ucGFyc2Uobik7dGhyb3cgbmV3IEVycm9yKGBmYWlsZWQgdG8gb3BlbiBhcHBsaWNhdGlvbjogJHtzLmVycm9yfHx0LnN0YXR1c1RleHR9YCl9Y2F0Y2h7dGhyb3cgbmV3IEVycm9yKGBmYWlsZWQgdG8gb3BlbiBhcHBsaWNhdGlvbiAoc3RhdHVzICR7dC5zdGF0dXN9KTogJHtufHx0LnN0YXR1c1RleHR9YCl9fWxldCByPWF3YWl0IHQuanNvbigpO2lmKCFyLnN1Y2Nlc3MpdGhyb3cgbmV3IEVycm9yKGBvcGVuIGFwcGxpY2F0aW9uIG9wZXJhdGlvbiBmYWlsZWQ6ICR7ci5tZXNzYWdlfHxcInVua25vd24gZXJyb3JcIn1gKTtyZXR1cm4gci5zdWNjZXNzfWFzeW5jIG9wZW5VcmwoZSx0KXtsZXQgcj1hd2FpdCBmZXRjaChgJHt0aGlzLmJhc2VVcmx9L2V4cGVyaW1lbnRhbC9vcGVyYXRvci9vcGVuLXVybGAse21ldGhvZDpcIlBPU1RcIixoZWFkZXJzOntcIkNvbnRlbnQtVHlwZVwiOlwiYXBwbGljYXRpb24vanNvblwifSxib2R5OkpTT04uc3RyaW5naWZ5KHt1cmw6ZSxicm93c2VyOnR9KX0pO2lmKCFyLm9rKXtsZXQgcz1hd2FpdCByLnRleHQoKTtjb25zb2xlLmxvZyhcImVycm9yIHJlc3BvbnNlOlwiLHMpO3RyeXtsZXQgbz1KU09OLnBhcnNlKHMpO3Rocm93IG5ldyBFcnJvcihgZmFpbGVkIHRvIG9wZW4gdXJsOiAke28uZXJyb3J8fHIuc3RhdHVzVGV4dH1gKX1jYXRjaHt0aHJvdyBuZXcgRXJyb3IoYGZhaWxlZCB0byBvcGVuIHVybCAoc3RhdHVzICR7ci5zdGF0dXN9KTogJHtzfHxyLnN0YXR1c1RleHR9YCl9fWxldCBuPWF3YWl0IHIuanNvbigpO2lmKCFuLnN1Y2Nlc3MpdGhyb3cgbmV3IEVycm9yKGBvcGVuIHVybCBvcGVyYXRpb24gZmFpbGVkOiAke24ubWVzc2FnZXx8XCJ1bmtub3duIGVycm9yXCJ9YCk7cmV0dXJuIG4uc3VjY2Vzc31hc3luYyBzY3JvbGwoZSl7bGV0IHQ9e2FwcF9uYW1lOmUuYXBwLHdpbmRvd19uYW1lOmUud2luZG93LGxvY2F0b3I6ZS5pZD9gIyR7ZS5pZH1gOlwiXCIsdXNlX2JhY2tncm91bmRfYXBwczplLnVzZUJhY2tncm91bmRBcHBzLGFjdGl2YXRlX2FwcDplLmFjdGl2YXRlQXBwIT09ZmFsc2V9LHI9YXdhaXQgZmV0Y2goYCR7dGhpcy5iYXNlVXJsfS9leHBlcmltZW50YWwvb3BlcmF0b3Ivc2Nyb2xsYCx7bWV0aG9kOlwiUE9TVFwiLGhlYWRlcnM6e1wiQ29udGVudC1UeXBlXCI6XCJhcHBsaWNhdGlvbi9qc29uXCJ9LGJvZHk6SlNPTi5zdHJpbmdpZnkoe3NlbGVjdG9yOnQsZGlyZWN0aW9uOmUuZGlyZWN0aW9uLGFtb3VudDplLmFtb3VudH0pfSk7aWYoIXIub2spe2xldCBzPWF3YWl0IHIudGV4dCgpO2NvbnNvbGUubG9nKFwiZXJyb3IgcmVzcG9uc2U6XCIscyk7dHJ5e2xldCBvPUpTT04ucGFyc2Uocyk7dGhyb3cgbmV3IEVycm9yKGBmYWlsZWQgdG8gc2Nyb2xsIGVsZW1lbnQ6ICR7by5lcnJvcnx8ci5zdGF0dXNUZXh0fWApfWNhdGNoe3Rocm93IG5ldyBFcnJvcihgZmFpbGVkIHRvIHNjcm9sbCBlbGVtZW50IChzdGF0dXMgJHtyLnN0YXR1c30pOiAke3N8fHIuc3RhdHVzVGV4dH1gKX19cmV0dXJuIChhd2FpdCByLmpzb24oKSkuc3VjY2Vzc319LEU9Y2xhc3N7YmFzZVVybDtzZWxlY3Rvcjtjb25zdHJ1Y3RvcihlLHQpe3RoaXMuYmFzZVVybD1lLHRoaXMuc2VsZWN0b3I9dDt9YXN5bmMgZmlyc3QoZSl7bGV0IHQ9YXdhaXQgdGhpcy5hbGwoMSxlKTtyZXR1cm4gdC5sZW5ndGg+MD90WzBdOm51bGx9YXN5bmMgYWxsKGUsdCl7bGV0IHI9YXdhaXQgZmV0Y2goYCR7dGhpcy5iYXNlVXJsfS9leHBlcmltZW50YWwvb3BlcmF0b3JgLHttZXRob2Q6XCJQT1NUXCIsaGVhZGVyczp7XCJDb250ZW50LVR5cGVcIjpcImFwcGxpY2F0aW9uL2pzb25cIn0sYm9keTpKU09OLnN0cmluZ2lmeSh7c2VsZWN0b3I6dGhpcy5zZWxlY3RvcixtYXhfcmVzdWx0czplLG1heF9kZXB0aDp0fSl9KTtpZighci5vayl7bGV0IHM9YXdhaXQgci50ZXh0KCk7Y29uc29sZS5sb2coXCJlcnJvciByZXNwb25zZTpcIixzKTt0cnl7bGV0IG89SlNPTi5wYXJzZShzKTt0aHJvdyBuZXcgRXJyb3IoYGZhaWxlZCB0byBmaW5kIGVsZW1lbnRzOiAke28uZXJyb3J8fHIuc3RhdHVzVGV4dH1gKX1jYXRjaHt0aHJvdyBuZXcgRXJyb3IoYGZhaWxlZCB0byBmaW5kIGVsZW1lbnRzIChzdGF0dXMgJHtyLnN0YXR1c30pOiAke3N8fHIuc3RhdHVzVGV4dH1gKX19cmV0dXJuIChhd2FpdCByLmpzb24oKSkuZGF0YX1hc3luYyBjbGljaygpe2xldCBlPWF3YWl0IGZldGNoKGAke3RoaXMuYmFzZVVybH0vZXhwZXJpbWVudGFsL29wZXJhdG9yL2NsaWNrYCx7bWV0aG9kOlwiUE9TVFwiLGhlYWRlcnM6e1wiQ29udGVudC1UeXBlXCI6XCJhcHBsaWNhdGlvbi9qc29uXCJ9LGJvZHk6SlNPTi5zdHJpbmdpZnkoe3NlbGVjdG9yOnsuLi50aGlzLnNlbGVjdG9yLGFjdGl2YXRlX2FwcDp0aGlzLnNlbGVjdG9yLmFjdGl2YXRlX2FwcCE9PWZhbHNlfX0pfSk7aWYoIWUub2spe2xldCByPWF3YWl0IGUudGV4dCgpO2NvbnNvbGUubG9nKFwiZXJyb3IgcmVzcG9uc2U6XCIscik7dHJ5e2xldCBuPUpTT04ucGFyc2Uocik7dGhyb3cgbmV3IEVycm9yKGBmYWlsZWQgdG8gY2xpY2sgZWxlbWVudDogJHtuLmVycm9yfHxlLnN0YXR1c1RleHR9YCl9Y2F0Y2h7dGhyb3cgbmV3IEVycm9yKGBmYWlsZWQgdG8gY2xpY2sgZWxlbWVudCAoc3RhdHVzICR7ZS5zdGF0dXN9KTogJHtyfHxlLnN0YXR1c1RleHR9YCl9fWxldCB0PWF3YWl0IGUuanNvbigpO2lmKGNvbnNvbGUubG9nKFwiZGVidWc6IGNsaWNrIHJlc3BvbnNlIGRhdGE6XCIsSlNPTi5zdHJpbmdpZnkodCxudWxsLDIpKSwhdC5zdWNjZXNzKXRocm93IG5ldyBFcnJvcihgY2xpY2sgb3BlcmF0aW9uIGZhaWxlZDogJHt0LmVycm9yfHxcInVua25vd24gZXJyb3JcIn1gKTtyZXR1cm4gdC5yZXN1bHQ/dC5yZXN1bHQ6dC5tZXRob2Q/e21ldGhvZDp0Lm1ldGhvZCxjb29yZGluYXRlczp0LmNvb3JkaW5hdGVzLGRldGFpbHM6dC5kZXRhaWxzfHxcIkNsaWNrIG9wZXJhdGlvbiBzdWNjZWVkZWRcIn06KGNvbnNvbGUubG9nKFwid2FybmluZzogY2xpY2sgcmVzcG9uc2UgbWlzc2luZyBleHBlY3RlZCBzdHJ1Y3R1cmUsIGNyZWF0aW5nIGZhbGxiYWNrIG9iamVjdFwiKSx7bWV0aG9kOlwiTW91c2VTaW11bGF0aW9uXCIsY29vcmRpbmF0ZXM6dW5kZWZpbmVkLGRldGFpbHM6XCJDbGljayBvcGVyYXRpb24gc3VjY2VlZGVkIGJ1dCByZXR1cm5lZCB1bmV4cGVjdGVkIGRhdGEgc3RydWN0dXJlXCJ9KX1hc3luYyBmaWxsKGUpe2xldCB0PWF3YWl0IGZldGNoKGAke3RoaXMuYmFzZVVybH0vZXhwZXJpbWVudGFsL29wZXJhdG9yL3R5cGVgLHttZXRob2Q6XCJQT1NUXCIsaGVhZGVyczp7XCJDb250ZW50LVR5cGVcIjpcImFwcGxpY2F0aW9uL2pzb25cIn0sYm9keTpKU09OLnN0cmluZ2lmeSh7c2VsZWN0b3I6ey4uLnRoaXMuc2VsZWN0b3IsYWN0aXZhdGVfYXBwOnRoaXMuc2VsZWN0b3IuYWN0aXZhdGVfYXBwIT09ZmFsc2V9LHRleHQ6ZX0pfSk7aWYoIXQub2spe2xldCBuPWF3YWl0IHQudGV4dCgpO2NvbnNvbGUubG9nKFwiZXJyb3IgcmVzcG9uc2U6XCIsbik7dHJ5e2xldCBzPUpTT04ucGFyc2Uobik7dGhyb3cgbmV3IEVycm9yKGBmYWlsZWQgdG8gdHlwZSB0ZXh0OiAke3MuZXJyb3J8fHQuc3RhdHVzVGV4dH1gKX1jYXRjaHt0aHJvdyBuZXcgRXJyb3IoYGZhaWxlZCB0byB0eXBlIHRleHQgKHN0YXR1cyAke3Quc3RhdHVzfSk6ICR7bnx8dC5zdGF0dXNUZXh0fWApfX1yZXR1cm4gKGF3YWl0IHQuanNvbigpKS5zdWNjZXNzfWFzeW5jIGV4aXN0cygpe3RyeXtyZXR1cm4gISFhd2FpdCB0aGlzLmZpcnN0KCl9Y2F0Y2h7cmV0dXJuICBmYWxzZX19YXN5bmMgd2FpdEZvcihlPXt9KXtsZXQgdD1EYXRlLm5vdygpLHI9ZS50aW1lb3V0fHwzZTQ7Zm9yKDtEYXRlLm5vdygpLXQ8cjspe3RyeXtsZXQgbj1hd2FpdCB0aGlzLmZpcnN0KCk7aWYobilyZXR1cm4gbn1jYXRjaHt9YXdhaXQgbmV3IFByb21pc2Uobj0+c2V0VGltZW91dChuLDEwMCkpO31yZXR1cm4gbnVsbH1hc3luYyBwcmVzc0tleShlKXtsZXQgdD1hd2FpdCBmZXRjaChgJHt0aGlzLmJhc2VVcmx9L2V4cGVyaW1lbnRhbC9vcGVyYXRvci9wcmVzcy1rZXlgLHttZXRob2Q6XCJQT1NUXCIsaGVhZGVyczp7XCJDb250ZW50LVR5cGVcIjpcImFwcGxpY2F0aW9uL2pzb25cIn0sYm9keTpKU09OLnN0cmluZ2lmeSh7c2VsZWN0b3I6ey4uLnRoaXMuc2VsZWN0b3IsYWN0aXZhdGVfYXBwOnRoaXMuc2VsZWN0b3IuYWN0aXZhdGVfYXBwIT09ZmFsc2V9LGtleV9jb21ibzplfSl9KTtpZighdC5vayl7bGV0IG49YXdhaXQgdC50ZXh0KCk7Y29uc29sZS5sb2coXCJlcnJvciByZXNwb25zZTpcIixuKTt0cnl7bGV0IHM9SlNPTi5wYXJzZShuKTt0aHJvdyBuZXcgRXJyb3IoYGZhaWxlZCB0byBwcmVzcyBrZXk6ICR7cy5lcnJvcnx8dC5zdGF0dXNUZXh0fWApfWNhdGNoe3Rocm93IG5ldyBFcnJvcihgZmFpbGVkIHRvIHByZXNzIGtleSAoc3RhdHVzICR7dC5zdGF0dXN9KTogJHtufHx0LnN0YXR1c1RleHR9YCl9fXJldHVybiAoYXdhaXQgdC5qc29uKCkpLnN1Y2Nlc3N9YXN5bmMgc2Nyb2xsKGUsdCl7bGV0IHI9YXdhaXQgZmV0Y2goYCR7dGhpcy5iYXNlVXJsfS9leHBlcmltZW50YWwvb3BlcmF0b3Ivc2Nyb2xsYCx7bWV0aG9kOlwiUE9TVFwiLGhlYWRlcnM6e1wiQ29udGVudC1UeXBlXCI6XCJhcHBsaWNhdGlvbi9qc29uXCJ9LGJvZHk6SlNPTi5zdHJpbmdpZnkoe3NlbGVjdG9yOnsuLi50aGlzLnNlbGVjdG9yLGFjdGl2YXRlX2FwcDp0aGlzLnNlbGVjdG9yLmFjdGl2YXRlX2FwcCE9PWZhbHNlfSxkaXJlY3Rpb246ZSxhbW91bnQ6dH0pfSk7aWYoIXIub2spe2xldCBzPWF3YWl0IHIudGV4dCgpO2NvbnNvbGUubG9nKFwiZXJyb3IgcmVzcG9uc2U6XCIscyk7dHJ5e2xldCBvPUpTT04ucGFyc2Uocyk7dGhyb3cgbmV3IEVycm9yKGBmYWlsZWQgdG8gc2Nyb2xsIGVsZW1lbnQ6ICR7by5lcnJvcnx8ci5zdGF0dXNUZXh0fWApfWNhdGNoe3Rocm93IG5ldyBFcnJvcihgZmFpbGVkIHRvIHNjcm9sbCBlbGVtZW50IChzdGF0dXMgJHtyLnN0YXR1c30pOiAke3N8fHIuc3RhdHVzVGV4dH1gKX19cmV0dXJuIChhd2FpdCByLmpzb24oKSkuc3VjY2Vzc319O3ZhciB4PWNsYXNze2FzeW5jIGxpc3QoKXt0cnl7bGV0IHQ9YXdhaXQgZmV0Y2goXCJodHRwOi8vbG9jYWxob3N0OjMwMzAvcGlwZXMvbGlzdFwiLHttZXRob2Q6XCJHRVRcIixoZWFkZXJzOntcIkNvbnRlbnQtVHlwZVwiOlwiYXBwbGljYXRpb24vanNvblwifX0pO2lmKCF0Lm9rKXRocm93IG5ldyBFcnJvcihgaHR0cCBlcnJvciEgc3RhdHVzOiAke3Quc3RhdHVzfWApO3JldHVybiB7c3VjY2VzczohMCxkYXRhOihhd2FpdCB0Lmpzb24oKSkuZGF0YX19Y2F0Y2goZSl7cmV0dXJuIGNvbnNvbGUuZXJyb3IoXCJmYWlsZWQgdG8gbGlzdCBwaXBlczpcIixlKSx7c3VjY2VzczpmYWxzZSxlcnJvcjplfX19YXN5bmMgZG93bmxvYWQoZSl7dHJ5e2xldCByPWF3YWl0IGZldGNoKFwiaHR0cDovL2xvY2FsaG9zdDozMDMwL3BpcGVzL2Rvd25sb2FkXCIse21ldGhvZDpcIlBPU1RcIixoZWFkZXJzOntcIkNvbnRlbnQtVHlwZVwiOlwiYXBwbGljYXRpb24vanNvblwifSxib2R5OkpTT04uc3RyaW5naWZ5KHt1cmw6ZX0pfSk7aWYoIXIub2spdGhyb3cgbmV3IEVycm9yKGBodHRwIGVycm9yISBzdGF0dXM6ICR7ci5zdGF0dXN9YCk7cmV0dXJuIHtzdWNjZXNzOiEwLGRhdGE6KGF3YWl0IHIuanNvbigpKS5kYXRhfX1jYXRjaCh0KXtyZXR1cm4gY29uc29sZS5lcnJvcihcImZhaWxlZCB0byBkb3dubG9hZCBwaXBlOlwiLHQpLHtzdWNjZXNzOmZhbHNlLGVycm9yOnR9fX1hc3luYyBlbmFibGUoZSl7dHJ5e3JldHVybiAoYXdhaXQgZmV0Y2goXCJodHRwOi8vbG9jYWxob3N0OjMwMzAvcGlwZXMvZW5hYmxlXCIse21ldGhvZDpcIlBPU1RcIixoZWFkZXJzOntcIkNvbnRlbnQtVHlwZVwiOlwiYXBwbGljYXRpb24vanNvblwifSxib2R5OkpTT04uc3RyaW5naWZ5KHtwaXBlX2lkOmV9KX0pKS5va31jYXRjaCh0KXtyZXR1cm4gY29uc29sZS5lcnJvcihcImZhaWxlZCB0byBlbmFibGUgcGlwZTpcIix0KSxmYWxzZX19YXN5bmMgZGlzYWJsZShlKXt0cnl7cmV0dXJuIChhd2FpdCBmZXRjaChcImh0dHA6Ly9sb2NhbGhvc3Q6MzAzMC9waXBlcy9kaXNhYmxlXCIse21ldGhvZDpcIlBPU1RcIixoZWFkZXJzOntcIkNvbnRlbnQtVHlwZVwiOlwiYXBwbGljYXRpb24vanNvblwifSxib2R5OkpTT04uc3RyaW5naWZ5KHtwaXBlX2lkOmV9KX0pKS5va31jYXRjaCh0KXtyZXR1cm4gY29uc29sZS5lcnJvcihcImZhaWxlZCB0byBkaXNhYmxlIHBpcGU6XCIsdCksZmFsc2V9fWFzeW5jIHVwZGF0ZShlLHQpe3RyeXtyZXR1cm4gKGF3YWl0IGZldGNoKFwiaHR0cDovL2xvY2FsaG9zdDozMDMwL3BpcGVzL3VwZGF0ZVwiLHttZXRob2Q6XCJQT1NUXCIsaGVhZGVyczp7XCJDb250ZW50LVR5cGVcIjpcImFwcGxpY2F0aW9uL2pzb25cIn0sYm9keTpKU09OLnN0cmluZ2lmeSh7cGlwZV9pZDplLGNvbmZpZzp0fSl9KSkub2t9Y2F0Y2gocil7cmV0dXJuIGNvbnNvbGUuZXJyb3IoXCJmYWlsZWQgdG8gdXBkYXRlIHBpcGU6XCIsciksZmFsc2V9fWFzeW5jIGluZm8oZSl7dHJ5e2xldCByPWF3YWl0IGZldGNoKGBodHRwOi8vbG9jYWxob3N0OjMwMzAvcGlwZXMvaW5mby8ke2V9YCx7bWV0aG9kOlwiR0VUXCIsaGVhZGVyczp7XCJDb250ZW50LVR5cGVcIjpcImFwcGxpY2F0aW9uL2pzb25cIn19KTtpZighci5vayl0aHJvdyBuZXcgRXJyb3IoYGh0dHAgZXJyb3IhIHN0YXR1czogJHtyLnN0YXR1c31gKTtyZXR1cm4ge3N1Y2Nlc3M6ITAsZGF0YTooYXdhaXQgci5qc29uKCkpLmRhdGF9fWNhdGNoKHQpe3JldHVybiBjb25zb2xlLmVycm9yKFwiZmFpbGVkIHRvIGdldCBwaXBlIGluZm86XCIsdCkse3N1Y2Nlc3M6ZmFsc2UsZXJyb3I6dH19fWFzeW5jIGRvd25sb2FkUHJpdmF0ZShlLHQscil7dHJ5e2xldCBzPWF3YWl0IGZldGNoKFwiaHR0cDovL2xvY2FsaG9zdDozMDMwL3BpcGVzL2Rvd25sb2FkLXByaXZhdGVcIix7bWV0aG9kOlwiUE9TVFwiLGhlYWRlcnM6e1wiQ29udGVudC1UeXBlXCI6XCJhcHBsaWNhdGlvbi9qc29uXCJ9LGJvZHk6SlNPTi5zdHJpbmdpZnkoe3VybDplLHBpcGVfbmFtZTp0LHBpcGVfaWQ6cn0pfSk7aWYoIXMub2spdGhyb3cgbmV3IEVycm9yKGBodHRwIGVycm9yISBzdGF0dXM6ICR7cy5zdGF0dXN9YCk7cmV0dXJuIHtzdWNjZXNzOiEwLGRhdGE6KGF3YWl0IHMuanNvbigpKS5kYXRhfX1jYXRjaChuKXtyZXR1cm4gY29uc29sZS5lcnJvcihcImZhaWxlZCB0byBkb3dubG9hZCBwcml2YXRlIHBpcGU6XCIsbikse3N1Y2Nlc3M6ZmFsc2UsZXJyb3I6bn19fWFzeW5jIGRlbGV0ZShlKXt0cnl7cmV0dXJuIChhd2FpdCBmZXRjaChcImh0dHA6Ly9sb2NhbGhvc3Q6MzAzMC9waXBlcy9kZWxldGVcIix7bWV0aG9kOlwiUE9TVFwiLGhlYWRlcnM6e1wiQ29udGVudC1UeXBlXCI6XCJhcHBsaWNhdGlvbi9qc29uXCJ9LGJvZHk6SlNPTi5zdHJpbmdpZnkoe3BpcGVfaWQ6ZX0pfSkpLm9rfWNhdGNoKHQpe3JldHVybiBjb25zb2xlLmVycm9yKFwiZmFpbGVkIHRvIGRlbGV0ZSBwaXBlOlwiLHQpLGZhbHNlfX19O08oe2luaXQ6eS5pbml0LmJpbmQoeSksaWRlbnRpZnk6eS5pZGVudGlmeS5iaW5kKHkpLGNhcHR1cmU6eS5jYXB0dXJlLmJpbmQoeSl9KTt2YXIgaj1cIndzOi8vbG9jYWxob3N0OjMwMzAvd3MvZXZlbnRzXCIsZz1udWxsLGY9bnVsbDthc3luYyBmdW5jdGlvbipTKGE9ZmFsc2Upe2xldCBlPWE/ZzpmOyghZXx8ZS5yZWFkeVN0YXRlPT09V2ViU29ja2V0LkNMT1NFRCkmJihjb25zb2xlLmxvZyhcImNyZWF0aW5nIG5ldyB3ZWJzb2NrZXQgY29ubmVjdGlvbiwgaW5jbHVkZUltYWdlczpcIixhKSxlPW5ldyBXZWJTb2NrZXQoYCR7an0/aW1hZ2VzPSR7YX1gKSxhP2c9ZTpmPWUsYXdhaXQgbmV3IFByb21pc2UoKHMsbyk9PntsZXQgYz0oKT0+e2NvbnNvbGUubG9nKFwid2Vic29ja2V0IGNvbm5lY3RlZFwiKSxzKHVuZGVmaW5lZCk7fSxwPWk9Pntjb25zb2xlLmVycm9yKFwid2Vic29ja2V0IGNvbm5lY3Rpb24gZXJyb3I6XCIsaSksbyhpKTt9O2UuYWRkRXZlbnRMaXN0ZW5lcihcIm9wZW5cIixjLHtvbmNlOnRydWV9KSxlLmFkZEV2ZW50TGlzdGVuZXIoXCJlcnJvclwiLHAse29uY2U6dHJ1ZX0pO30pKTtsZXQgdD1bXSxyPW51bGwsbj1zPT57cj8ocihzKSxyPW51bGwpOnQucHVzaChzKTt9O2UuYWRkRXZlbnRMaXN0ZW5lcihcIm1lc3NhZ2VcIixuKTt0cnl7Zm9yKDs7KXtsZXQgcz1hd2FpdCBuZXcgUHJvbWlzZShvPT57dC5sZW5ndGg+MD9vKHQuc2hpZnQoKSk6cj1vO30pO3lpZWxkIEpTT04ucGFyc2Uocy5kYXRhKTt9fWZpbmFsbHl7ZS5yZW1vdmVFdmVudExpc3RlbmVyKFwibWVzc2FnZVwiLG4pO319dmFyIFA9Y2xhc3N7b3BlcmF0b3I9bmV3IGI7YXN5bmMgaW5pdEFuYWx5dGljc0lmTmVlZGVkKCl7dHJ5e2xldCBlPW5ldyBFdmVudFNvdXJjZShcImh0dHA6Ly9sb2NhbGhvc3Q6MTE0MzUvc3NlL3NldHRpbmdzXCIpLHQ9YXdhaXQgbmV3IFByb21pc2UoKHIsbik9PntsZXQgcz1zZXRUaW1lb3V0KCgpPT57ZS5jbG9zZSgpLG4obmV3IEVycm9yKFwic2V0dGluZ3Mgc3RyZWFtIHRpbWVvdXRcIikpO30sNWUzKTtlLm9ubWVzc2FnZT1vPT57Y2xlYXJUaW1lb3V0KHMpLGUuY2xvc2UoKTtsZXQgYz1KU09OLnBhcnNlKG8uZGF0YSkscD1jLmZpbmQoKFtsXSk9Pmw9PT1cImFuYWx5dGljc0VuYWJsZWRcIik/LlsxXT8/ITEsaT1jLmZpbmQoKFtsXSk9Pmw9PT1cInVzZXIuY2xlcmtfaWRcIik/LlsxXT8/dm9pZCAwLGQ9Yy5maW5kKChbbF0pPT5sPT09XCJ1c2VyLmVtYWlsXCIpPy5bMV0/P3ZvaWQgMDtyKHthbmFseXRpY3NFbmFibGVkOnAsdXNlcklkOmksZW1haWw6ZH0pO30sZS5vbmVycm9yPW89PntjbGVhclRpbWVvdXQocyksZS5jbG9zZSgpLG4obyk7fTt9KTtyZXR1cm4ge2FuYWx5dGljc0VuYWJsZWQ6dC5hbmFseXRpY3NFbmFibGVkLHVzZXJJZDp0LnVzZXJJZCxlbWFpbDp0LmVtYWlsfX1jYXRjaChlKXtyZXR1cm4gY29uc29sZS5lcnJvcihcImZhaWxlZCB0byBmZXRjaCBzZXR0aW5ncywgZGVmYXVsdGluZyB0byBhbmFseXRpY3MgZW5hYmxlZDpcIixlKSx7YW5hbHl0aWNzRW5hYmxlZDpmYWxzZSx1c2VySWQ6dW5kZWZpbmVkfX19YXN5bmMgc2VuZERlc2t0b3BOb3RpZmljYXRpb24oZSl7bGV0e3VzZXJJZDp0LGVtYWlsOnJ9PWF3YWl0IHRoaXMuaW5pdEFuYWx5dGljc0lmTmVlZGVkKCksbj1cImh0dHA6Ly9sb2NhbGhvc3Q6MTE0MzVcIjt0cnl7cmV0dXJuIGF3YWl0IGZldGNoKGAke259L25vdGlmeWAse21ldGhvZDpcIlBPU1RcIixoZWFkZXJzOntcIkNvbnRlbnQtVHlwZVwiOlwiYXBwbGljYXRpb24vanNvblwifSxib2R5OkpTT04uc3RyaW5naWZ5KGUpfSksYXdhaXQgdGhpcy5jYXB0dXJlRXZlbnQoXCJub3RpZmljYXRpb25fc2VudFwiLHtkaXN0aW5jdF9pZDp0LGVtYWlsOnIsc3VjY2VzczohMH0pLCEwfWNhdGNoe3JldHVybiAgZmFsc2V9fWFzeW5jIHF1ZXJ5U2NyZWVucGlwZShlKXtjb25zb2xlLmxvZyhcInF1ZXJ5U2NyZWVucGlwZTpcIixlKTtsZXR7dXNlcklkOnQsZW1haWw6cn09YXdhaXQgdGhpcy5pbml0QW5hbHl0aWNzSWZOZWVkZWQoKSxuPW5ldyBVUkxTZWFyY2hQYXJhbXM7T2JqZWN0LmVudHJpZXMoZSkuZm9yRWFjaCgoW28sY10pPT57aWYoYyE9PXVuZGVmaW5lZCYmYyE9PVwiXCIpaWYobz09PVwic3BlYWtlcklkc1wiJiZBcnJheS5pc0FycmF5KGMpKWMubGVuZ3RoPjAmJm4uYXBwZW5kKHcobyksYy5qb2luKFwiLFwiKSk7ZWxzZSB7bGV0IHA9dyhvKTtuLmFwcGVuZChwLGMudG9TdHJpbmcoKSk7fX0pO2xldCBzPWBodHRwOi8vbG9jYWxob3N0OjMwMzAvc2VhcmNoPyR7bn1gO3RyeXtsZXQgbz1hd2FpdCBmZXRjaChzKTtpZighby5vayl7bGV0IHA9YXdhaXQgby50ZXh0KCksaTt0cnl7aT1KU09OLnBhcnNlKHApLGNvbnNvbGUuZXJyb3IoXCJzY3JlZW5waXBlIGFwaSBlcnJvcjpcIix7c3RhdHVzOm8uc3RhdHVzLGVycm9yOml9KTt9Y2F0Y2h7Y29uc29sZS5lcnJvcihcInNjcmVlbnBpcGUgYXBpIGVycm9yOlwiLHtzdGF0dXM6by5zdGF0dXMsZXJyb3I6cH0pO310aHJvdyBuZXcgRXJyb3IoYGh0dHAgZXJyb3IhIHN0YXR1czogJHtvLnN0YXR1c31gKX1sZXQgYz1hd2FpdCBvLmpzb24oKTtyZXR1cm4gYXdhaXQgVChcInNlYXJjaF9wZXJmb3JtZWRcIix7ZGlzdGluY3RfaWQ6dCxjb250ZW50X3R5cGU6ZS5jb250ZW50VHlwZSxyZXN1bHRfY291bnQ6Yy5wYWdpbmF0aW9uLnRvdGFsLGVtYWlsOnJ9KSx1KGMpfWNhdGNoKG8pe3Rocm93IGNvbnNvbGUuZXJyb3IoXCJlcnJvciBxdWVyeWluZyBzY3JlZW5waXBlOlwiLG8pLG99fXBpcGVzPXtsaXN0OmFzeW5jKCk9Pnt0cnl7bGV0IGU9YXdhaXQgZmV0Y2goXCJodHRwOi8vbG9jYWxob3N0OjMwMzAvcGlwZXMvbGlzdFwiLHttZXRob2Q6XCJHRVRcIixoZWFkZXJzOntcIkNvbnRlbnQtVHlwZVwiOlwiYXBwbGljYXRpb24vanNvblwifX0pO2lmKCFlLm9rKXRocm93IG5ldyBFcnJvcihgaHR0cCBlcnJvciEgc3RhdHVzOiAke2Uuc3RhdHVzfWApO3JldHVybiB7c3VjY2VzczohMCxkYXRhOihhd2FpdCBlLmpzb24oKSkuZGF0YX19Y2F0Y2goZSl7cmV0dXJuIGNvbnNvbGUuZXJyb3IoXCJmYWlsZWQgdG8gbGlzdCBwaXBlczpcIixlKSx7c3VjY2VzczpmYWxzZSxlcnJvcjplfX19LGRvd25sb2FkOmFzeW5jIGU9Pnt0cnl7bGV0IHQ9YXdhaXQgZmV0Y2goXCJodHRwOi8vbG9jYWxob3N0OjMwMzAvcGlwZXMvZG93bmxvYWRcIix7bWV0aG9kOlwiUE9TVFwiLGhlYWRlcnM6e1wiQ29udGVudC1UeXBlXCI6XCJhcHBsaWNhdGlvbi9qc29uXCJ9LGJvZHk6SlNPTi5zdHJpbmdpZnkoe3VybDplfSl9KTtpZighdC5vayl0aHJvdyBuZXcgRXJyb3IoYGh0dHAgZXJyb3IhIHN0YXR1czogJHt0LnN0YXR1c31gKTtyZXR1cm4ge3N1Y2Nlc3M6ITAsZGF0YTooYXdhaXQgdC5qc29uKCkpLmRhdGF9fWNhdGNoKHQpe3JldHVybiBjb25zb2xlLmVycm9yKFwiZmFpbGVkIHRvIGRvd25sb2FkIHBpcGU6XCIsdCkse3N1Y2Nlc3M6ZmFsc2UsZXJyb3I6dH19fSxlbmFibGU6YXN5bmMgZT0+e3RyeXtyZXR1cm4gKGF3YWl0IGZldGNoKFwiaHR0cDovL2xvY2FsaG9zdDozMDMwL3BpcGVzL2VuYWJsZVwiLHttZXRob2Q6XCJQT1NUXCIsaGVhZGVyczp7XCJDb250ZW50LVR5cGVcIjpcImFwcGxpY2F0aW9uL2pzb25cIn0sYm9keTpKU09OLnN0cmluZ2lmeSh7cGlwZV9pZDplfSl9KSkub2t9Y2F0Y2godCl7cmV0dXJuIGNvbnNvbGUuZXJyb3IoXCJmYWlsZWQgdG8gZW5hYmxlIHBpcGU6XCIsdCksZmFsc2V9fSxkaXNhYmxlOmFzeW5jIGU9Pnt0cnl7cmV0dXJuIChhd2FpdCBmZXRjaChcImh0dHA6Ly9sb2NhbGhvc3Q6MzAzMC9waXBlcy9kaXNhYmxlXCIse21ldGhvZDpcIlBPU1RcIixoZWFkZXJzOntcIkNvbnRlbnQtVHlwZVwiOlwiYXBwbGljYXRpb24vanNvblwifSxib2R5OkpTT04uc3RyaW5naWZ5KHtwaXBlX2lkOmV9KX0pKS5va31jYXRjaCh0KXtyZXR1cm4gY29uc29sZS5lcnJvcihcImZhaWxlZCB0byBkaXNhYmxlIHBpcGU6XCIsdCksZmFsc2V9fSx1cGRhdGU6YXN5bmMoZSx0KT0+e3RyeXtyZXR1cm4gKGF3YWl0IGZldGNoKFwiaHR0cDovL2xvY2FsaG9zdDozMDMwL3BpcGVzL3VwZGF0ZVwiLHttZXRob2Q6XCJQT1NUXCIsaGVhZGVyczp7XCJDb250ZW50LVR5cGVcIjpcImFwcGxpY2F0aW9uL2pzb25cIn0sYm9keTpKU09OLnN0cmluZ2lmeSh7cGlwZV9pZDplLGNvbmZpZzp0fSl9KSkub2t9Y2F0Y2gocil7cmV0dXJuIGNvbnNvbGUuZXJyb3IoXCJmYWlsZWQgdG8gdXBkYXRlIHBpcGU6XCIsciksZmFsc2V9fSxpbmZvOmFzeW5jIGU9Pnt0cnl7bGV0IHQ9YXdhaXQgZmV0Y2goYGh0dHA6Ly9sb2NhbGhvc3Q6MzAzMC9waXBlcy9pbmZvLyR7ZX1gLHttZXRob2Q6XCJHRVRcIixoZWFkZXJzOntcIkNvbnRlbnQtVHlwZVwiOlwiYXBwbGljYXRpb24vanNvblwifX0pO2lmKCF0Lm9rKXRocm93IG5ldyBFcnJvcihgaHR0cCBlcnJvciEgc3RhdHVzOiAke3Quc3RhdHVzfWApO3JldHVybiB7c3VjY2VzczohMCxkYXRhOihhd2FpdCB0Lmpzb24oKSkuZGF0YX19Y2F0Y2godCl7cmV0dXJuIGNvbnNvbGUuZXJyb3IoXCJmYWlsZWQgdG8gZ2V0IHBpcGUgaW5mbzpcIix0KSx7c3VjY2VzczpmYWxzZSxlcnJvcjp0fX19LGRvd25sb2FkUHJpdmF0ZTphc3luYyhlLHQscik9Pnt0cnl7bGV0IG49cHJvY2Vzcy5lbnYuU0NSRUVOUElQRV9TRVJWRVJfVVJMfHxcImh0dHA6Ly9sb2NhbGhvc3Q6MzAzMFwiLHM9YXdhaXQgZmV0Y2goYCR7bn0vcGlwZXMvZG93bmxvYWQtcHJpdmF0ZWAse21ldGhvZDpcIlBPU1RcIixoZWFkZXJzOntcIkNvbnRlbnQtVHlwZVwiOlwiYXBwbGljYXRpb24vanNvblwifSxib2R5OkpTT04uc3RyaW5naWZ5KHt1cmw6ZSxwaXBlX25hbWU6dCxwaXBlX2lkOnJ9KX0pO2lmKCFzLm9rKXRocm93IG5ldyBFcnJvcihgaHR0cCBlcnJvciEgc3RhdHVzOiAke3Muc3RhdHVzfWApO3JldHVybiB7c3VjY2VzczohMCxkYXRhOihhd2FpdCBzLmpzb24oKSkuZGF0YX19Y2F0Y2gobil7cmV0dXJuIGNvbnNvbGUuZXJyb3IoXCJmYWlsZWQgdG8gZG93bmxvYWQgcHJpdmF0ZSBwaXBlOlwiLG4pLHtzdWNjZXNzOmZhbHNlLGVycm9yOm59fX0sZGVsZXRlOmFzeW5jIGU9Pnt0cnl7bGV0IHQ9cHJvY2Vzcy5lbnYuU0NSRUVOUElQRV9TRVJWRVJfVVJMfHxcImh0dHA6Ly9sb2NhbGhvc3Q6MzAzMFwiO3JldHVybiAoYXdhaXQgZmV0Y2goYCR7dH0vcGlwZXMvZGVsZXRlYCx7bWV0aG9kOlwiUE9TVFwiLGhlYWRlcnM6e1wiQ29udGVudC1UeXBlXCI6XCJhcHBsaWNhdGlvbi9qc29uXCJ9LGJvZHk6SlNPTi5zdHJpbmdpZnkoe3BpcGVfaWQ6ZX0pfSkpLm9rfWNhdGNoKHQpe3JldHVybiBjb25zb2xlLmVycm9yKFwiZmFpbGVkIHRvIGRlbGV0ZSBwaXBlOlwiLHQpLGZhbHNlfX19O2FzeW5jKnN0cmVhbVRyYW5zY3JpcHRpb25zKCl7dHJ5e2Zvcig7Oylmb3IgYXdhaXQobGV0IGUgb2YgUygpKWlmKGUubmFtZT09PVwidHJhbnNjcmlwdGlvblwiKXtsZXQgdD1lLmRhdGE7eWllbGQge2lkOmNyeXB0by5yYW5kb21VVUlEKCksb2JqZWN0OlwidGV4dF9jb21wbGV0aW9uX2NodW5rXCIsY3JlYXRlZDpEYXRlLm5vdygpLG1vZGVsOlwic2NyZWVucGlwZS1yZWFsdGltZVwiLGNob2ljZXM6W3t0ZXh0OnQudHJhbnNjcmlwdGlvbixpbmRleDowLGZpbmlzaF9yZWFzb246dC5pc19maW5hbD9cInN0b3BcIjpudWxsfV0sbWV0YWRhdGE6e3RpbWVzdGFtcDp0LnRpbWVzdGFtcCxkZXZpY2U6dC5kZXZpY2UsaXNJbnB1dDp0LmlzX2lucHV0LHNwZWFrZXI6dC5zcGVha2VyfX07fX1jYXRjaChlKXtjb25zb2xlLmVycm9yKFwiZXJyb3Igc3RyZWFtaW5nIHRyYW5zY3JpcHRpb25zOlwiLGUpO319YXN5bmMqc3RyZWFtVmlzaW9uKGU9ZmFsc2Upe3RyeXtmb3IgYXdhaXQobGV0IHQgb2YgUyhlKSlpZih0Lm5hbWU9PT1cIm9jcl9yZXN1bHRcInx8dC5uYW1lPT09XCJ1aV9mcmFtZVwiKXtsZXQgcj10LmRhdGE7eWllbGQge3R5cGU6dC5uYW1lLGRhdGE6cn07fX1jYXRjaCh0KXtjb25zb2xlLmVycm9yKFwiZXJyb3Igc3RyZWFtaW5nIHZpc2lvbjpcIix0KTt9fWFzeW5jIGNhcHR1cmVFdmVudChlLHQpe2xldHthbmFseXRpY3NFbmFibGVkOnJ9PWF3YWl0IHRoaXMuaW5pdEFuYWx5dGljc0lmTmVlZGVkKCk7aWYocilyZXR1cm4gVChlLHQpfWFzeW5jIGNhcHR1cmVNYWluRmVhdHVyZUV2ZW50KGUsdCl7bGV0e2FuYWx5dGljc0VuYWJsZWQ6cn09YXdhaXQgdGhpcy5pbml0QW5hbHl0aWNzSWZOZWVkZWQoKTtpZihyKXJldHVybiBSKGUsdCl9YXN5bmMqc3RyZWFtRXZlbnRzKGU9ZmFsc2Upe2ZvciBhd2FpdChsZXQgdCBvZiBTKGUpKXlpZWxkIHQ7fWRpc2Nvbm5lY3QoKXtnJiYoZy5jbG9zZSgpLGc9bnVsbCksZiYmKGYuY2xvc2UoKSxmPW51bGwpO31hc3luYyBkZWR1cGxpY2F0ZVRleHQoZSl7aWYoZS5sZW5ndGg9PT0wKXJldHVybiB7Z3JvdXBzOltdLGVycm9yOnVuZGVmaW5lZH07dHJ5e2xldCByPVtdO2ZvcihsZXQgaT0wO2k8ZS5sZW5ndGg7aSs9NTApci5wdXNoKGUuc2xpY2UoaSxpKzUwKSk7bGV0IHM9KGF3YWl0IFByb21pc2UuYWxsKHIubWFwKGFzeW5jIGk9PntsZXQgZD1hd2FpdCBmZXRjaChcImh0dHA6Ly9sb2NhbGhvc3Q6MzAzMC92MS9lbWJlZGRpbmdzXCIse21ldGhvZDpcIlBPU1RcIixoZWFkZXJzOntcIkNvbnRlbnQtVHlwZVwiOlwiYXBwbGljYXRpb24vanNvblwifSxib2R5OkpTT04uc3RyaW5naWZ5KHttb2RlbDpcImFsbC1NaW5pTE0tTDYtdjJcIixpbnB1dDppLmxlbmd0aD09PTE/aVswXTppLGVuY29kaW5nX2Zvcm1hdDpcImZsb2F0XCJ9KX0pO2lmKCFkLm9rKXRocm93IG5ldyBFcnJvcihgaHR0cCBlcnJvciEgc3RhdHVzOiAke2Quc3RhdHVzfWApO2xldCBsPWF3YWl0IGQuanNvbigpO3JldHVybiBsLmRhdGEubWFwKGs9Pih7dGV4dDppW2wuZGF0YS5pbmRleE9mKGspXSxlbWJlZGRpbmc6ay5lbWJlZGRpbmd9KSl9KSkpLmZsYXQoKSxvPS45LGM9W10scD1uZXcgU2V0O2ZvcihsZXQgaT0wO2k8cy5sZW5ndGg7aSsrKXtpZihwLmhhcyhpKSljb250aW51ZTtsZXQgZD17dGV4dDpzW2ldLnRleHQsc2ltaWxhcjpbXX07cC5hZGQoaSk7Zm9yKGxldCBsPWkrMTtsPHMubGVuZ3RoO2wrKyl7aWYocC5oYXMobCkpY29udGludWU7SihzW2ldLmVtYmVkZGluZyxzW2xdLmVtYmVkZGluZyk+byYmKGQuc2ltaWxhci5wdXNoKHNbbF0udGV4dCkscC5hZGQobCkpO31kLnNpbWlsYXIubGVuZ3RoPjAmJmMucHVzaChkKTt9cmV0dXJuIHtncm91cHM6Y319Y2F0Y2godCl7cmV0dXJuIGNvbnNvbGUuZXJyb3IoXCJmYWlsZWQgdG8gZGVkdXBsaWNhdGUgdGV4dHM6XCIsdCkse2dyb3VwczpbXSxlcnJvcjp0Py50b1N0cmluZygpfX19fTtmdW5jdGlvbiBKKGEsZSl7bGV0IHQ9YS5yZWR1Y2UoKHMsbyxjKT0+cytvKmVbY10sMCkscj1NYXRoLnNxcnQoYS5yZWR1Y2UoKHMsbyk9PnMrbypvLDApKSxuPU1hdGguc3FydChlLnJlZHVjZSgocyxvKT0+cytvKm8sMCkpO3JldHVybiB0LyhyKm4pfXZhciBfPW5ldyBQLFU9bmV3IHgsSD1fO18ucGlwZXM9VTtcbmV4cG9ydHtBIGFzIGdldERlZmF1bHRTZXR0aW5ncyxIIGFzIHBpcGV9Oy8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcFxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@screenpipe/browser/dist/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@screenpipe/browser/dist/index.js":
/*!********************************************************!*\
  !*** ./node_modules/@screenpipe/browser/dist/index.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getDefaultSettings: () => (/* binding */ A),\n/* harmony export */   pipe: () => (/* binding */ H)\n/* harmony export */ });\n/* harmony import */ var posthog_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! posthog-js */ \"(ssr)/./node_modules/posthog-js/dist/module.js\");\nfunction C(a){return a.replace(/([-_][a-z])/g,e=>e.toUpperCase().replace(\"-\",\"\").replace(\"_\",\"\"))}function w(a){return a.replace(/[A-Z]/g,e=>`_${e.toLowerCase()}`)}function h(a){return Object.keys(a).reduce((e,t)=>{let r=w(t);return e[r]=h(a[t]),e},{})}function u(a){return Array.isArray(a)?a.map(u):a!==null&&typeof a==\"object\"?Object.keys(a).reduce((e,t)=>{let r=C(t);return e[r]=u(a[t]),e},{}):a}function A(){return {openaiApiKey:\"\",deepgramApiKey:\"\",aiModel:\"gpt-4o\",aiUrl:\"https://api.openai.com/v1\",customPrompt:\"Rules:\\n    - You can analyze/view/show/access videos to the user by putting .mp4 files in a code block (we'll render it) like this: `/users/video.mp4`, use the exact, absolute, file path from file_path property\\n    - Do not try to embed video in links (e.g. [](.mp4) or https://.mp4) instead put the file_path in a code block using backticks\\n    - Do not put video in multiline code block it will not render the video (e.g. ```bash\\n.mp4``` IS WRONG) instead using inline code block with single backtick\\n    - Always answer my question/intent, do not make up things\\n    \",port:3030,dataDir:\"default\",disableAudio:false,ignoredWindows:[],includedWindows:[],aiProviderType:\"openai\",embeddedLLM:{enabled:false,model:\"llama3.2:1b-instruct-q4_K_M\",port:11434},enableFrameCache:true,enableUiMonitoring:false,aiMaxContextChars:512e3,analyticsEnabled:true,user:{token:\"\"},customSettings:{},monitorIds:[\"default\"],audioDevices:[\"default\"],audioTranscriptionEngine:\"whisper-large-v3-turbo\",enableRealtimeAudioTranscription:false,realtimeAudioTranscriptionEngine:\"deepgram\",disableVision:false,aiPresets:[]}}var v=false,m=null,N=\"phc_Bt8GoTBPgkCpDrbaIZzJIEYt0CrJjhBiuLaBck1clce\",I=\"https://eu.i.posthog.com\";function O(a){m=a;}function $(a,e){!v&&m&&(m.init(N,{api_host:I,distinct_id:a,email:e}),m.identify(a,{email:e}),v=true);}async function T(a,e){if(!m)return;$(e?.distinct_id,e?.email);let{distinct_id:t,...r}=e||{};m.capture(a,r);}async function R(a,e){if(!m)return;$(e?.distinct_id,e?.email);let{distinct_id:t,...r}=e||{};m.capture(a,{feature:\"main\",...r});}var b=class{baseUrl;pixel;constructor(e=\"http://localhost:3030\"){this.baseUrl=e,this.pixel={type:t=>this.sendInputControl({type:\"WriteText\",data:t}),press:t=>this.sendInputControl({type:\"KeyPress\",data:t}),moveMouse:(t,r)=>this.sendInputControl({type:\"MouseMove\",data:{x:t,y:r}}),click:t=>this.sendInputControl({type:\"MouseClick\",data:t})};}async sendInputControl(e){try{let t=await fetch(`${this.baseUrl}/experimental/operator/pixel`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({action:e})});if(!t.ok)throw new Error(`http error! status: ${t.status}`);return (await t.json()).success}catch(t){return console.error(\"failed to control input:\",t),false}}locator(e){if(e.role&&e.id)throw new Error(\"only one of 'role' or 'id' can be specified. need both? dm us!\");let t={app_name:e.app,window_name:e.window,locator:e.role||(e.id?`#${e.id}`:\"\"),use_background_apps:e.useBackgroundApps,activate_app:e.activateApp};return new E(this.baseUrl,t)}async click(e){let t={app_name:e.app,window_name:e.window,locator:`#${e.id}`,use_background_apps:e.useBackgroundApps,activate_app:e.activateApp!==false},r=await fetch(`${this.baseUrl}/experimental/operator/click`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({selector:t})});if(!r.ok){let s=await r.text();console.log(\"error response:\",s);try{let o=JSON.parse(s);throw new Error(`failed to click element: ${o.error||r.statusText}`)}catch{throw new Error(`failed to click element (status ${r.status}): ${s||r.statusText}`)}}let n=await r.json();if(console.log(\"debug: click response data:\",JSON.stringify(n,null,2)),!n.success)throw new Error(`click operation failed: ${n.error||\"unknown error\"}`);return n.result?u(n.result):n.method?{method:n.method,coordinates:n.coordinates,details:n.details||\"Click operation succeeded\"}:(console.log(\"warning: click response missing expected structure, creating fallback object\"),{method:\"MouseSimulation\",coordinates:undefined,details:\"Click operation succeeded but returned unexpected data structure\"})}async fill(e){let t={app_name:e.app,locator:`#${e.id}`,use_background_apps:e.useBackgroundApps,activate_app:e.activateApp!==false};console.log(\"selector\",t);let r=await fetch(`${this.baseUrl}/experimental/operator/type`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({selector:t,text:e.value})});if(!r.ok){let s=await r.text();console.log(\"error response:\",s);try{let o=JSON.parse(s);throw new Error(`failed to type text: ${o.error||r.statusText}`)}catch{throw new Error(`failed to type text (status ${r.status}): ${s||r.statusText}`)}}return (await r.json()).success}getByRole(e,t){return this.locator({app:t?.app||\"\",window:t?.window,role:e,useBackgroundApps:t?.useBackgroundApps,activateApp:t?.activateApp})}getById(e,t){return this.locator({app:t?.app||\"\",window:t?.window,id:e,useBackgroundApps:t?.useBackgroundApps,activateApp:t?.activateApp})}async getText(e){let t={appName:e.app,windowName:e.window,maxDepth:e.maxDepth,useBackgroundApps:e.useBackgroundApps,activateApp:e.activateApp!==false},r=await fetch(`${this.baseUrl}/experimental/operator/get_text`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify(h(t))});if(!r.ok){let s=await r.text();console.log(\"error response:\",s);try{let o=JSON.parse(s);throw new Error(`failed to get text: ${o.error||r.statusText}`)}catch{throw new Error(`failed to get text (status ${r.status}): ${s||r.statusText}`)}}let n=await r.json();if(console.log(\"debug: text response data:\",JSON.stringify(n,null,2)),!n.success)throw new Error(`get_text operation failed: ${n.error||\"unknown error\"}`);return u(n)}async getInteractableElements(e){let t={appName:e.app,windowName:e.window,withTextOnly:e.withTextOnly,interactableOnly:e.interactableOnly,includeSometimesInteractable:e.includeSometimesInteractable,maxElements:e.maxElements,useBackgroundApps:e.useBackgroundApps,activateApp:e.activateApp},r=await fetch(`${this.baseUrl}/experimental/operator/list-interactable-elements`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify(h(t))});if(!r.ok){let s=await r.text();console.log(\"error response:\",s);try{let o=JSON.parse(s);throw new Error(`failed to get interactable elements: ${o.error||r.statusText}`)}catch{throw new Error(`failed to get interactable elements (status ${r.status}): ${s||r.statusText}`)}}let n=await r.json();return console.log(\"debug: text response data:\",JSON.stringify(n,null,2)),u(n)}async clickByIndex(e){let t=await fetch(`${this.baseUrl}/experimental/operator/click-by-index`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({element_index:e})});if(!t.ok){let n=await t.text();console.log(\"error response:\",n);try{let s=JSON.parse(n);throw new Error(`failed to click element by index: ${s.error||t.statusText}`)}catch{throw new Error(`failed to click element by index (status ${t.status}): ${n||t.statusText}`)}}let r=await t.json();if(!r.success)throw new Error(`click operation failed: ${r.message||\"unknown error\"}`);return r.success}async typeByIndex(e,t){let r=await fetch(`${this.baseUrl}/experimental/operator/type-by-index`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({element_index:e,text:t})});if(!r.ok){let s=await r.text();console.log(\"error response:\",s);try{let o=JSON.parse(s);throw new Error(`failed to type text into element by index: ${o.error||r.statusText}`)}catch{throw new Error(`failed to type text into element by index (status ${r.status}): ${s||r.statusText}`)}}let n=await r.json();if(!n.success)throw new Error(`type operation failed: ${n.message||\"unknown error\"}`);return n.success}async pressKey(e){let t={app_name:e.app,window_name:e.window,locator:`#${e.id}`,use_background_apps:e.useBackgroundApps,activate_app:e.activateApp!==false},r=await fetch(`${this.baseUrl}/experimental/operator/press-key`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify(h({selector:t,keyCombo:e.key}))});if(!r.ok){let s=await r.text();console.log(\"error response:\",s);try{let o=JSON.parse(s);throw new Error(`failed to press key: ${o.error||r.statusText}`)}catch{throw new Error(`failed to press key (status ${r.status}): ${s||r.statusText}`)}}return (await r.json()).success}async pressKeyByIndex(e,t){let r=await fetch(`${this.baseUrl}/experimental/operator/press-key-by-index`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({element_index:e,key_combo:t})});if(!r.ok){let s=await r.text();console.log(\"error response:\",s);try{let o=JSON.parse(s);throw new Error(`failed to press key on element by index: ${o.error||r.statusText}`)}catch{throw new Error(`failed to press key on element by index (status ${r.status}): ${s||r.statusText}`)}}let n=await r.json();if(!n.success)throw new Error(`press key operation failed: ${n.message||\"unknown error\"}`);return n.success}async openApplication(e){let t=await fetch(`${this.baseUrl}/experimental/operator/open-application`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({app_name:e})});if(!t.ok){let n=await t.text();console.log(\"error response:\",n);try{let s=JSON.parse(n);throw new Error(`failed to open application: ${s.error||t.statusText}`)}catch{throw new Error(`failed to open application (status ${t.status}): ${n||t.statusText}`)}}let r=await t.json();if(!r.success)throw new Error(`open application operation failed: ${r.message||\"unknown error\"}`);return r.success}async openUrl(e,t){let r=await fetch(`${this.baseUrl}/experimental/operator/open-url`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({url:e,browser:t})});if(!r.ok){let s=await r.text();console.log(\"error response:\",s);try{let o=JSON.parse(s);throw new Error(`failed to open url: ${o.error||r.statusText}`)}catch{throw new Error(`failed to open url (status ${r.status}): ${s||r.statusText}`)}}let n=await r.json();if(!n.success)throw new Error(`open url operation failed: ${n.message||\"unknown error\"}`);return n.success}async scroll(e){let t={app_name:e.app,window_name:e.window,locator:e.id?`#${e.id}`:\"\",use_background_apps:e.useBackgroundApps,activate_app:e.activateApp!==false},r=await fetch(`${this.baseUrl}/experimental/operator/scroll`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({selector:t,direction:e.direction,amount:e.amount})});if(!r.ok){let s=await r.text();console.log(\"error response:\",s);try{let o=JSON.parse(s);throw new Error(`failed to scroll element: ${o.error||r.statusText}`)}catch{throw new Error(`failed to scroll element (status ${r.status}): ${s||r.statusText}`)}}return (await r.json()).success}},E=class{baseUrl;selector;constructor(e,t){this.baseUrl=e,this.selector=t;}async first(e){let t=await this.all(1,e);return t.length>0?t[0]:null}async all(e,t){let r=await fetch(`${this.baseUrl}/experimental/operator`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({selector:this.selector,max_results:e,max_depth:t})});if(!r.ok){let s=await r.text();console.log(\"error response:\",s);try{let o=JSON.parse(s);throw new Error(`failed to find elements: ${o.error||r.statusText}`)}catch{throw new Error(`failed to find elements (status ${r.status}): ${s||r.statusText}`)}}return (await r.json()).data}async click(){let e=await fetch(`${this.baseUrl}/experimental/operator/click`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({selector:{...this.selector,activate_app:this.selector.activate_app!==false}})});if(!e.ok){let r=await e.text();console.log(\"error response:\",r);try{let n=JSON.parse(r);throw new Error(`failed to click element: ${n.error||e.statusText}`)}catch{throw new Error(`failed to click element (status ${e.status}): ${r||e.statusText}`)}}let t=await e.json();if(console.log(\"debug: click response data:\",JSON.stringify(t,null,2)),!t.success)throw new Error(`click operation failed: ${t.error||\"unknown error\"}`);return t.result?t.result:t.method?{method:t.method,coordinates:t.coordinates,details:t.details||\"Click operation succeeded\"}:(console.log(\"warning: click response missing expected structure, creating fallback object\"),{method:\"MouseSimulation\",coordinates:undefined,details:\"Click operation succeeded but returned unexpected data structure\"})}async fill(e){let t=await fetch(`${this.baseUrl}/experimental/operator/type`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({selector:{...this.selector,activate_app:this.selector.activate_app!==false},text:e})});if(!t.ok){let n=await t.text();console.log(\"error response:\",n);try{let s=JSON.parse(n);throw new Error(`failed to type text: ${s.error||t.statusText}`)}catch{throw new Error(`failed to type text (status ${t.status}): ${n||t.statusText}`)}}return (await t.json()).success}async exists(){try{return !!await this.first()}catch{return  false}}async waitFor(e={}){let t=Date.now(),r=e.timeout||3e4;for(;Date.now()-t<r;){try{let n=await this.first();if(n)return n}catch{}await new Promise(n=>setTimeout(n,100));}return null}async pressKey(e){let t=await fetch(`${this.baseUrl}/experimental/operator/press-key`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({selector:{...this.selector,activate_app:this.selector.activate_app!==false},key_combo:e})});if(!t.ok){let n=await t.text();console.log(\"error response:\",n);try{let s=JSON.parse(n);throw new Error(`failed to press key: ${s.error||t.statusText}`)}catch{throw new Error(`failed to press key (status ${t.status}): ${n||t.statusText}`)}}return (await t.json()).success}async scroll(e,t){let r=await fetch(`${this.baseUrl}/experimental/operator/scroll`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({selector:{...this.selector,activate_app:this.selector.activate_app!==false},direction:e,amount:t})});if(!r.ok){let s=await r.text();console.log(\"error response:\",s);try{let o=JSON.parse(s);throw new Error(`failed to scroll element: ${o.error||r.statusText}`)}catch{throw new Error(`failed to scroll element (status ${r.status}): ${s||r.statusText}`)}}return (await r.json()).success}};var x=class{async list(){try{let t=await fetch(\"http://localhost:3030/pipes/list\",{method:\"GET\",headers:{\"Content-Type\":\"application/json\"}});if(!t.ok)throw new Error(`http error! status: ${t.status}`);return {success:!0,data:(await t.json()).data}}catch(e){return console.error(\"failed to list pipes:\",e),{success:false,error:e}}}async download(e){try{let r=await fetch(\"http://localhost:3030/pipes/download\",{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({url:e})});if(!r.ok)throw new Error(`http error! status: ${r.status}`);return {success:!0,data:(await r.json()).data}}catch(t){return console.error(\"failed to download pipe:\",t),{success:false,error:t}}}async enable(e){try{return (await fetch(\"http://localhost:3030/pipes/enable\",{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({pipe_id:e})})).ok}catch(t){return console.error(\"failed to enable pipe:\",t),false}}async disable(e){try{return (await fetch(\"http://localhost:3030/pipes/disable\",{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({pipe_id:e})})).ok}catch(t){return console.error(\"failed to disable pipe:\",t),false}}async update(e,t){try{return (await fetch(\"http://localhost:3030/pipes/update\",{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({pipe_id:e,config:t})})).ok}catch(r){return console.error(\"failed to update pipe:\",r),false}}async info(e){try{let r=await fetch(`http://localhost:3030/pipes/info/${e}`,{method:\"GET\",headers:{\"Content-Type\":\"application/json\"}});if(!r.ok)throw new Error(`http error! status: ${r.status}`);return {success:!0,data:(await r.json()).data}}catch(t){return console.error(\"failed to get pipe info:\",t),{success:false,error:t}}}async downloadPrivate(e,t,r){try{let s=await fetch(\"http://localhost:3030/pipes/download-private\",{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({url:e,pipe_name:t,pipe_id:r})});if(!s.ok)throw new Error(`http error! status: ${s.status}`);return {success:!0,data:(await s.json()).data}}catch(n){return console.error(\"failed to download private pipe:\",n),{success:false,error:n}}}async delete(e){try{return (await fetch(\"http://localhost:3030/pipes/delete\",{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({pipe_id:e})})).ok}catch(t){return console.error(\"failed to delete pipe:\",t),false}}};O({init:posthog_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].init.bind(posthog_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),identify:posthog_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].identify.bind(posthog_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),capture:posthog_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].capture.bind(posthog_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])});var j=\"ws://localhost:3030/ws/events\",g=null,f=null;async function*S(a=false){let e=a?g:f;(!e||e.readyState===WebSocket.CLOSED)&&(console.log(\"creating new websocket connection, includeImages:\",a),e=new WebSocket(`${j}?images=${a}`),a?g=e:f=e,await new Promise((s,o)=>{let c=()=>{console.log(\"websocket connected\"),s(undefined);},p=i=>{console.error(\"websocket connection error:\",i),o(i);};e.addEventListener(\"open\",c,{once:true}),e.addEventListener(\"error\",p,{once:true});}));let t=[],r=null,n=s=>{r?(r(s),r=null):t.push(s);};e.addEventListener(\"message\",n);try{for(;;){let s=await new Promise(o=>{t.length>0?o(t.shift()):r=o;});yield JSON.parse(s.data);}}finally{e.removeEventListener(\"message\",n);}}var P=class{operator=new b;async initAnalyticsIfNeeded(){try{let e=new EventSource(\"http://localhost:11435/sse/settings\"),t=await new Promise((r,n)=>{let s=setTimeout(()=>{e.close(),n(new Error(\"settings stream timeout\"));},5e3);e.onmessage=o=>{clearTimeout(s),e.close();let c=JSON.parse(o.data),p=c.find(([l])=>l===\"analyticsEnabled\")?.[1]??!1,i=c.find(([l])=>l===\"user.clerk_id\")?.[1]??void 0,d=c.find(([l])=>l===\"user.email\")?.[1]??void 0;r({analyticsEnabled:p,userId:i,email:d});},e.onerror=o=>{clearTimeout(s),e.close(),n(o);};});return {analyticsEnabled:t.analyticsEnabled,userId:t.userId,email:t.email}}catch(e){return console.error(\"failed to fetch settings, defaulting to analytics enabled:\",e),{analyticsEnabled:false,userId:undefined}}}async sendDesktopNotification(e){let{userId:t,email:r}=await this.initAnalyticsIfNeeded(),n=\"http://localhost:11435\";try{return await fetch(`${n}/notify`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify(e)}),await this.captureEvent(\"notification_sent\",{distinct_id:t,email:r,success:!0}),!0}catch{return  false}}async queryScreenpipe(e){console.log(\"queryScreenpipe:\",e);let{userId:t,email:r}=await this.initAnalyticsIfNeeded(),n=new URLSearchParams;Object.entries(e).forEach(([o,c])=>{if(c!==undefined&&c!==\"\")if(o===\"speakerIds\"&&Array.isArray(c))c.length>0&&n.append(w(o),c.join(\",\"));else {let p=w(o);n.append(p,c.toString());}});let s=`http://localhost:3030/search?${n}`;try{let o=await fetch(s);if(!o.ok){let p=await o.text(),i;try{i=JSON.parse(p),console.error(\"screenpipe api error:\",{status:o.status,error:i});}catch{console.error(\"screenpipe api error:\",{status:o.status,error:p});}throw new Error(`http error! status: ${o.status}`)}let c=await o.json();return await T(\"search_performed\",{distinct_id:t,content_type:e.contentType,result_count:c.pagination.total,email:r}),u(c)}catch(o){throw console.error(\"error querying screenpipe:\",o),o}}pipes={list:async()=>{try{let e=await fetch(\"http://localhost:3030/pipes/list\",{method:\"GET\",headers:{\"Content-Type\":\"application/json\"}});if(!e.ok)throw new Error(`http error! status: ${e.status}`);return {success:!0,data:(await e.json()).data}}catch(e){return console.error(\"failed to list pipes:\",e),{success:false,error:e}}},download:async e=>{try{let t=await fetch(\"http://localhost:3030/pipes/download\",{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({url:e})});if(!t.ok)throw new Error(`http error! status: ${t.status}`);return {success:!0,data:(await t.json()).data}}catch(t){return console.error(\"failed to download pipe:\",t),{success:false,error:t}}},enable:async e=>{try{return (await fetch(\"http://localhost:3030/pipes/enable\",{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({pipe_id:e})})).ok}catch(t){return console.error(\"failed to enable pipe:\",t),false}},disable:async e=>{try{return (await fetch(\"http://localhost:3030/pipes/disable\",{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({pipe_id:e})})).ok}catch(t){return console.error(\"failed to disable pipe:\",t),false}},update:async(e,t)=>{try{return (await fetch(\"http://localhost:3030/pipes/update\",{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({pipe_id:e,config:t})})).ok}catch(r){return console.error(\"failed to update pipe:\",r),false}},info:async e=>{try{let t=await fetch(`http://localhost:3030/pipes/info/${e}`,{method:\"GET\",headers:{\"Content-Type\":\"application/json\"}});if(!t.ok)throw new Error(`http error! status: ${t.status}`);return {success:!0,data:(await t.json()).data}}catch(t){return console.error(\"failed to get pipe info:\",t),{success:false,error:t}}},downloadPrivate:async(e,t,r)=>{try{let n=process.env.SCREENPIPE_SERVER_URL||\"http://localhost:3030\",s=await fetch(`${n}/pipes/download-private`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({url:e,pipe_name:t,pipe_id:r})});if(!s.ok)throw new Error(`http error! status: ${s.status}`);return {success:!0,data:(await s.json()).data}}catch(n){return console.error(\"failed to download private pipe:\",n),{success:false,error:n}}},delete:async e=>{try{let t=process.env.SCREENPIPE_SERVER_URL||\"http://localhost:3030\";return (await fetch(`${t}/pipes/delete`,{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({pipe_id:e})})).ok}catch(t){return console.error(\"failed to delete pipe:\",t),false}}};async*streamTranscriptions(){try{for(;;)for await(let e of S())if(e.name===\"transcription\"){let t=e.data;yield {id:crypto.randomUUID(),object:\"text_completion_chunk\",created:Date.now(),model:\"screenpipe-realtime\",choices:[{text:t.transcription,index:0,finish_reason:t.is_final?\"stop\":null}],metadata:{timestamp:t.timestamp,device:t.device,isInput:t.is_input,speaker:t.speaker}};}}catch(e){console.error(\"error streaming transcriptions:\",e);}}async*streamVision(e=false){try{for await(let t of S(e))if(t.name===\"ocr_result\"||t.name===\"ui_frame\"){let r=t.data;yield {type:t.name,data:r};}}catch(t){console.error(\"error streaming vision:\",t);}}async captureEvent(e,t){let{analyticsEnabled:r}=await this.initAnalyticsIfNeeded();if(r)return T(e,t)}async captureMainFeatureEvent(e,t){let{analyticsEnabled:r}=await this.initAnalyticsIfNeeded();if(r)return R(e,t)}async*streamEvents(e=false){for await(let t of S(e))yield t;}disconnect(){g&&(g.close(),g=null),f&&(f.close(),f=null);}async deduplicateText(e){if(e.length===0)return {groups:[],error:undefined};try{let r=[];for(let i=0;i<e.length;i+=50)r.push(e.slice(i,i+50));let s=(await Promise.all(r.map(async i=>{let d=await fetch(\"http://localhost:3030/v1/embeddings\",{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({model:\"all-MiniLM-L6-v2\",input:i.length===1?i[0]:i,encoding_format:\"float\"})});if(!d.ok)throw new Error(`http error! status: ${d.status}`);let l=await d.json();return l.data.map(k=>({text:i[l.data.indexOf(k)],embedding:k.embedding}))}))).flat(),o=.9,c=[],p=new Set;for(let i=0;i<s.length;i++){if(p.has(i))continue;let d={text:s[i].text,similar:[]};p.add(i);for(let l=i+1;l<s.length;l++){if(p.has(l))continue;J(s[i].embedding,s[l].embedding)>o&&(d.similar.push(s[l].text),p.add(l));}d.similar.length>0&&c.push(d);}return {groups:c}}catch(t){return console.error(\"failed to deduplicate texts:\",t),{groups:[],error:t?.toString()}}}};function J(a,e){let t=a.reduce((s,o,c)=>s+o*e[c],0),r=Math.sqrt(a.reduce((s,o)=>s+o*o,0)),n=Math.sqrt(e.reduce((s,o)=>s+o*o,0));return t/(r*n)}var _=new P,U=new x,H=_;_.pipes=U;\n//# sourceMappingURL=index.js.map\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@screenpipe/browser/dist/index.js\n");

/***/ })

};
;