### **New Features:**
- **Added Pixel API to Hello World:** Introduced a Pixel API to enhance functionality in the Hello World example.

### **Improvements:**
- **Updated example pipes:** Improved and updated multiple example pipes for better user guidance.
- **Refactored input API:** Moved input API to Pixel API for better documentation and integration with MCP.

### **Fixes:**
- **Fixed Hello World Custom AI issues:** Resolved multiple issues related to the Hello World custom AI implementation.
- **Fixed prompts:** Corrected issues with prompts to ensure accurate responses.

#### **Full Changelog:** [fbddd..b2d54](https://github.com/mediar-ai/screenpipe/compare/fbddd..b2d54)

