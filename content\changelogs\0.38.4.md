Based on your commits, here's the changelog for the new Screenpipe update:

### **Fixes:**
- **Resolved onboarding display issue:** Fixed the issue where onboarding was showing up again unexpectedly (#1459).
- **Fixed PowerShell infinite loop:** Addressed a potential infinite loop problem in PowerShell (#1497).
- **Corrected sideload branch functionality:** Fixed issues related to the sideload branch in the application.

### **Improvements:**
- **Updated search and rewind version:** Bumped the version for the search and rewind functionality for better performance.

#### **Full Changelog:** [94b10..53acb](https://github.com/mediar-ai/screenpipe/compare/94b10..53acb)

