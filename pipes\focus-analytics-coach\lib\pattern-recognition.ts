import { 
  FocusSession, 
  ProductivityPattern, 
  EnergyLevel, 
  ProductivityMetrics,
  TaskCategoryType 
} from "./types";
import { 
  startOfWeek, 
  endOfWeek, 
  eachDayOfInterval, 
  format, 
  getHours, 
  getDay,
  differenceInDays,
  subWeeks,
  addDays
} from "date-fns";
import { Matrix } from "ml-matrix";
import * as tf from "@tensorflow/tfjs";

export class PatternRecognitionEngine {
  private patterns: ProductivityPattern[] = [];
  private energyLevels: EnergyLevel[] = [];
  private model: tf.LayersModel | null = null;

  constructor() {
    this.initializeModel();
  }

  /**
   * Initialize TensorFlow model for pattern prediction
   */
  private async initializeModel(): Promise<void> {
    try {
      // Create a simple neural network for productivity prediction
      this.model = tf.sequential({
        layers: [
          tf.layers.dense({ inputShape: [7], units: 16, activation: 'relu' }),
          tf.layers.dropout({ rate: 0.2 }),
          tf.layers.dense({ units: 8, activation: 'relu' }),
          tf.layers.dense({ units: 1, activation: 'sigmoid' })
        ]
      });

      this.model.compile({
        optimizer: 'adam',
        loss: 'meanSquaredError',
        metrics: ['mae']
      });

      console.log("Pattern recognition model initialized");
    } catch (error) {
      console.error("Error initializing ML model:", error);
    }
  }

  /**
   * Analyze productivity patterns from historical data
   */
  async analyzeProductivityPatterns(
    sessions: FocusSession[], 
    timeRange: { start: Date; end: Date }
  ): Promise<ProductivityPattern[]> {
    if (sessions.length < 10) {
      console.warn("Insufficient data for pattern analysis");
      return [];
    }

    // Group sessions by time patterns
    const timePatterns = this.groupSessionsByTimePatterns(sessions);
    const patterns: ProductivityPattern[] = [];

    for (const [patternKey, patternSessions] of Object.entries(timePatterns)) {
      if (patternSessions.length < 3) continue;

      const pattern = await this.analyzeTimePattern(patternKey, patternSessions);
      if (pattern) {
        patterns.push(pattern);
      }
    }

    // Analyze weekly patterns
    const weeklyPatterns = this.analyzeWeeklyPatterns(sessions);
    patterns.push(...weeklyPatterns);

    // Analyze task-specific patterns
    const taskPatterns = this.analyzeTaskPatterns(sessions);
    patterns.push(...taskPatterns);

    // Sort by confidence and relevance
    return patterns.sort((a, b) => b.confidence - a.confidence);
  }

  /**
   * Group sessions by time patterns (hour blocks and days)
   */
  private groupSessionsByTimePatterns(sessions: FocusSession[]): Record<string, FocusSession[]> {
    const patterns: Record<string, FocusSession[]> = {};

    sessions.forEach(session => {
      const hour = getHours(session.startTime);
      const dayOfWeek = getDay(session.startTime);
      
      // Create 2-hour time blocks
      const timeBlock = Math.floor(hour / 2) * 2;
      const endBlock = timeBlock + 2;
      
      // Categorize days: weekday vs weekend
      const dayCategory = dayOfWeek === 0 || dayOfWeek === 6 ? 'weekend' : 'weekday';
      
      const patternKey = `${timeBlock}-${endBlock}_${dayCategory}`;
      
      if (!patterns[patternKey]) {
        patterns[patternKey] = [];
      }
      patterns[patternKey].push(session);
    });

    return patterns;
  }

  /**
   * Analyze a specific time pattern
   */
  private async analyzeTimePattern(
    patternKey: string, 
    sessions: FocusSession[]
  ): Promise<ProductivityPattern | null> {
    const [timeRange, dayCategory] = patternKey.split('_');
    const [startHour, endHour] = timeRange.split('-').map(Number);

    // Calculate pattern metrics
    const averageProductivity = sessions.reduce((sum, s) => sum + s.focusScore, 0) / sessions.length;
    const averageDuration = sessions.reduce((sum, s) => sum + s.duration, 0) / sessions.length;
    const totalSessions = sessions.length;
    
    // Calculate confidence based on consistency and sample size
    const productivityVariance = this.calculateVariance(sessions.map(s => s.focusScore));
    const consistency = Math.max(0, 100 - productivityVariance);
    const sampleSizeScore = Math.min(100, (totalSessions / 10) * 100);
    const confidence = (consistency + sampleSizeScore) / 2;

    if (confidence < 30) return null; // Too low confidence

    // Generate insights and recommendations
    const insights = this.generatePatternInsights(sessions, averageProductivity, averageDuration);
    const recommendations = this.generatePatternRecommendations(
      averageProductivity, 
      startHour, 
      endHour, 
      dayCategory
    );

    return {
      id: `pattern_${patternKey}`,
      name: this.generatePatternName(startHour, endHour, dayCategory),
      description: `${dayCategory} productivity pattern (${totalSessions} sessions)`,
      timePattern: {
        startHour,
        endHour,
        daysOfWeek: dayCategory === 'weekend' ? [0, 6] : [1, 2, 3, 4, 5]
      },
      averageProductivity,
      confidence,
      recommendations: [...insights, ...recommendations]
    };
  }

  /**
   * Analyze weekly productivity patterns
   */
  private analyzeWeeklyPatterns(sessions: FocusSession[]): ProductivityPattern[] {
    const weeklyData: Record<number, FocusSession[]> = {};
    
    // Group by day of week
    sessions.forEach(session => {
      const dayOfWeek = getDay(session.startTime);
      if (!weeklyData[dayOfWeek]) {
        weeklyData[dayOfWeek] = [];
      }
      weeklyData[dayOfWeek].push(session);
    });

    const patterns: ProductivityPattern[] = [];
    const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

    Object.entries(weeklyData).forEach(([day, daySessions]) => {
      if (daySessions.length < 3) return;

      const dayNum = parseInt(day);
      const averageProductivity = daySessions.reduce((sum, s) => sum + s.focusScore, 0) / daySessions.length;
      const confidence = Math.min(100, (daySessions.length / sessions.length) * 100 * 7);

      if (confidence >= 20) {
        patterns.push({
          id: `weekly_${day}`,
          name: `${dayNames[dayNum]} Pattern`,
          description: `Productivity pattern for ${dayNames[dayNum]}s`,
          timePattern: {
            startHour: 0,
            endHour: 24,
            daysOfWeek: [dayNum]
          },
          averageProductivity,
          confidence,
          recommendations: this.generateDaySpecificRecommendations(dayNum, averageProductivity)
        });
      }
    });

    return patterns;
  }

  /**
   * Analyze task-specific productivity patterns
   */
  private analyzeTaskPatterns(sessions: FocusSession[]): ProductivityPattern[] {
    const taskData: Record<TaskCategoryType, FocusSession[]> = {} as any;
    
    sessions.forEach(session => {
      if (!taskData[session.taskCategory]) {
        taskData[session.taskCategory] = [];
      }
      taskData[session.taskCategory].push(session);
    });

    const patterns: ProductivityPattern[] = [];

    Object.entries(taskData).forEach(([taskType, taskSessions]) => {
      if (taskSessions.length < 5) return;

      const averageProductivity = taskSessions.reduce((sum, s) => sum + s.focusScore, 0) / taskSessions.length;
      const confidence = Math.min(100, (taskSessions.length / sessions.length) * 100 * 5);

      // Find optimal time patterns for this task type
      const hourlyPerformance = this.analyzeHourlyPerformance(taskSessions);
      const bestHours = this.findBestPerformanceHours(hourlyPerformance);

      if (confidence >= 30 && bestHours.length > 0) {
        patterns.push({
          id: `task_${taskType}`,
          name: `${taskType.replace('-', ' ')} Optimization`,
          description: `Best times for ${taskType.replace('-', ' ')} tasks`,
          timePattern: {
            startHour: Math.min(...bestHours),
            endHour: Math.max(...bestHours) + 1,
            daysOfWeek: [1, 2, 3, 4, 5] // Weekdays
          },
          averageProductivity,
          confidence,
          recommendations: this.generateTaskSpecificRecommendations(taskType as TaskCategoryType, bestHours)
        });
      }
    });

    return patterns;
  }

  /**
   * Predict optimal schedule based on patterns
   */
  async predictOptimalSchedule(
    date: Date, 
    taskTypes: TaskCategoryType[]
  ): Promise<Array<{
    hour: number;
    taskType: TaskCategoryType;
    predictedProductivity: number;
    confidence: number;
    reasoning: string;
  }>> {
    const schedule: Array<{
      hour: number;
      taskType: TaskCategoryType;
      predictedProductivity: number;
      confidence: number;
      reasoning: string;
    }> = [];

    if (!this.model) {
      console.warn("ML model not available for predictions");
      return this.generateRuleBasedSchedule(date, taskTypes);
    }

    try {
      // Prepare features for prediction
      const dayOfWeek = getDay(date);
      const isWeekend = dayOfWeek === 0 || dayOfWeek === 6 ? 1 : 0;

      for (let hour = 8; hour < 18; hour++) { // Business hours
        for (const taskType of taskTypes) {
          // Create feature vector: [hour, dayOfWeek, isWeekend, taskTypeEncoded, ...]
          const features = [
            hour / 24, // Normalized hour
            dayOfWeek / 7, // Normalized day
            isWeekend,
            this.encodeTaskType(taskType),
            this.getHistoricalPerformance(hour, dayOfWeek, taskType),
            this.getEnergyLevel(hour),
            this.getContextualFactors(date, hour)
          ];

          const prediction = this.model.predict(tf.tensor2d([features])) as tf.Tensor;
          const predictedProductivity = (await prediction.data())[0] * 100;
          prediction.dispose();

          const confidence = this.calculatePredictionConfidence(hour, dayOfWeek, taskType);

          schedule.push({
            hour,
            taskType,
            predictedProductivity,
            confidence,
            reasoning: this.generatePredictionReasoning(hour, taskType, predictedProductivity)
          });
        }
      }

      // Sort by predicted productivity and return top recommendations
      return schedule
        .sort((a, b) => b.predictedProductivity - a.predictedProductivity)
        .slice(0, 8); // Top 8 time slots

    } catch (error) {
      console.error("Error in ML prediction:", error);
      return this.generateRuleBasedSchedule(date, taskTypes);
    }
  }

  /**
   * Generate rule-based schedule as fallback
   */
  private generateRuleBasedSchedule(
    date: Date, 
    taskTypes: TaskCategoryType[]
  ): Array<{
    hour: number;
    taskType: TaskCategoryType;
    predictedProductivity: number;
    confidence: number;
    reasoning: string;
  }> {
    const schedule: Array<{
      hour: number;
      taskType: TaskCategoryType;
      predictedProductivity: number;
      confidence: number;
      reasoning: string;
    }> = [];
    const dayOfWeek = getDay(date);
    const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;

    // Rule-based optimal times
    const optimalTimes: Record<TaskCategoryType, number[]> = {
      'deep-work': isWeekend ? [10, 11, 14, 15] : [9, 10, 14, 15],
      'creative': [10, 11, 16, 17],
      'communication': [11, 12, 14, 16],
      'administrative': [13, 14, 15, 16],
      'learning': [9, 10, 11, 15],
      'research': [9, 10, 14, 15],
      'entertainment': [18, 19, 20, 21],
      'social': [12, 13, 17, 18],
      'distraction': [16, 17, 18, 19],
      'unknown': [10, 11, 14, 15]
    };

    taskTypes.forEach(taskType => {
      const optimalHours = optimalTimes[taskType] || [10, 11, 14, 15];
      
      optimalHours.forEach(hour => {
        schedule.push({
          hour,
          taskType,
          predictedProductivity: this.getRuleBasedProductivity(hour, taskType, isWeekend),
          confidence: 70,
          reasoning: `Based on general productivity patterns for ${taskType.replace('-', ' ')}`
        });
      });
    });

    return schedule.sort((a, b) => b.predictedProductivity - a.predictedProductivity);
  }

  // Helper methods
  private calculateVariance(values: number[]): number {
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const squaredDiffs = values.map(val => Math.pow(val - mean, 2));
    return squaredDiffs.reduce((sum, diff) => sum + diff, 0) / values.length;
  }

  private generatePatternName(startHour: number, endHour: number, dayCategory: string): string {
    const timeStr = `${startHour}:00-${endHour}:00`;
    const dayStr = dayCategory === 'weekend' ? 'Weekend' : 'Weekday';
    return `${dayStr} ${timeStr}`;
  }

  private generatePatternInsights(
    sessions: FocusSession[], 
    avgProductivity: number, 
    avgDuration: number
  ): string[] {
    const insights = [];
    
    if (avgProductivity >= 80) {
      insights.push("High-performance time window - excellent for important tasks");
    } else if (avgProductivity >= 60) {
      insights.push("Good productivity window - suitable for focused work");
    } else {
      insights.push("Lower productivity period - consider lighter tasks");
    }

    if (avgDuration >= 60) {
      insights.push("Long focus sessions typical - good for deep work");
    } else if (avgDuration >= 30) {
      insights.push("Moderate session length - good for regular tasks");
    } else {
      insights.push("Short sessions typical - better for quick tasks");
    }

    return insights;
  }

  private generatePatternRecommendations(
    productivity: number, 
    startHour: number, 
    endHour: number, 
    dayCategory: string
  ): string[] {
    const recommendations = [];

    if (productivity >= 80) {
      recommendations.push("Schedule your most important work during this time");
      recommendations.push("Minimize interruptions and distractions");
    } else if (productivity >= 60) {
      recommendations.push("Good time for regular focused work");
      recommendations.push("Consider optimizing environment for better focus");
    } else {
      recommendations.push("Use for lighter tasks or administrative work");
      recommendations.push("Consider taking breaks or doing physical activity");
    }

    // Time-specific recommendations
    if (startHour <= 10) {
      recommendations.push("Morning energy - great for analytical tasks");
    } else if (startHour >= 14 && startHour <= 16) {
      recommendations.push("Post-lunch period - may need energy boost");
    }

    return recommendations;
  }

  private generateDaySpecificRecommendations(day: number, productivity: number): string[] {
    const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    const dayName = dayNames[day];
    
    const recommendations = [`Optimize ${dayName} productivity`];
    
    if (day === 1) { // Monday
      recommendations.push("Start week with planning and goal setting");
    } else if (day === 5) { // Friday
      recommendations.push("Wrap up weekly tasks and plan for next week");
    } else if (day === 0 || day === 6) { // Weekend
      recommendations.push("Focus on personal projects and learning");
    }

    return recommendations;
  }

  private generateTaskSpecificRecommendations(taskType: TaskCategoryType, bestHours: number[]): string[] {
    const recommendations = [];
    const timeStr = bestHours.map(h => `${h}:00`).join(', ');
    
    recommendations.push(`Best performance for ${taskType.replace('-', ' ')} at ${timeStr}`);
    
    switch (taskType) {
      case 'deep-work':
        recommendations.push("Minimize distractions and notifications");
        recommendations.push("Ensure comfortable environment and good lighting");
        break;
      case 'creative':
        recommendations.push("Allow for inspiration and experimentation");
        recommendations.push("Consider background music or change of scenery");
        break;
      case 'communication':
        recommendations.push("Batch similar communication tasks together");
        recommendations.push("Set specific times for checking messages");
        break;
    }

    return recommendations;
  }

  private analyzeHourlyPerformance(sessions: FocusSession[]): Record<number, number> {
    const hourlyData: Record<number, { total: number; count: number }> = {};
    
    sessions.forEach(session => {
      const hour = getHours(session.startTime);
      if (!hourlyData[hour]) {
        hourlyData[hour] = { total: 0, count: 0 };
      }
      hourlyData[hour].total += session.focusScore;
      hourlyData[hour].count += 1;
    });

    const performance: Record<number, number> = {};
    Object.entries(hourlyData).forEach(([hour, data]) => {
      performance[parseInt(hour)] = data.total / data.count;
    });

    return performance;
  }

  private findBestPerformanceHours(hourlyPerformance: Record<number, number>): number[] {
    const avgPerformance = Object.values(hourlyPerformance).reduce((sum, val) => sum + val, 0) / Object.values(hourlyPerformance).length;
    
    return Object.entries(hourlyPerformance)
      .filter(([, performance]) => performance > avgPerformance * 1.1) // 10% above average
      .map(([hour]) => parseInt(hour));
  }

  private encodeTaskType(taskType: TaskCategoryType): number {
    const encoding = {
      'deep-work': 0.9,
      'creative': 0.8,
      'research': 0.7,
      'learning': 0.6,
      'administrative': 0.5,
      'communication': 0.4,
      'social': 0.3,
      'entertainment': 0.2,
      'distraction': 0.1,
      'unknown': 0.0
    };
    return encoding[taskType] || 0.0;
  }

  private getHistoricalPerformance(hour: number, dayOfWeek: number, taskType: TaskCategoryType): number {
    // Simplified - would use actual historical data
    return 0.7; // Default performance score
  }

  private getEnergyLevel(hour: number): number {
    // Simplified energy curve based on circadian rhythms
    if (hour >= 9 && hour <= 11) return 0.9; // Morning peak
    if (hour >= 14 && hour <= 16) return 0.8; // Afternoon peak
    if (hour >= 20 || hour <= 6) return 0.3; // Low energy
    return 0.6; // Moderate energy
  }

  private getContextualFactors(date: Date, hour: number): number {
    // Consider factors like meetings, deadlines, etc.
    // Simplified implementation
    return 0.5;
  }

  private calculatePredictionConfidence(hour: number, dayOfWeek: number, taskType: TaskCategoryType): number {
    // Calculate confidence based on historical data availability
    // Simplified implementation
    return 75;
  }

  private generatePredictionReasoning(hour: number, taskType: TaskCategoryType, productivity: number): string {
    if (productivity >= 80) {
      return `Optimal time for ${taskType.replace('-', ' ')} based on historical performance`;
    } else if (productivity >= 60) {
      return `Good time for ${taskType.replace('-', ' ')} with moderate productivity expected`;
    } else {
      return `Lower productivity expected for ${taskType.replace('-', ' ')} at this time`;
    }
  }

  private getRuleBasedProductivity(hour: number, taskType: TaskCategoryType, isWeekend: boolean): number {
    // Simple rule-based productivity scoring
    let score = 70; // Base score

    // Time of day adjustments
    if (hour >= 9 && hour <= 11) score += 15; // Morning peak
    if (hour >= 14 && hour <= 16) score += 10; // Afternoon peak
    if (hour <= 8 || hour >= 18) score -= 20; // Early/late penalties

    // Task type adjustments
    if (taskType === 'deep-work' && (hour >= 9 && hour <= 11)) score += 10;
    if (taskType === 'creative' && (hour >= 10 && hour <= 12)) score += 10;
    if (taskType === 'communication' && (hour >= 11 && hour <= 16)) score += 5;

    // Weekend adjustments
    if (isWeekend) score -= 10;

    return Math.max(0, Math.min(100, score));
  }
}
