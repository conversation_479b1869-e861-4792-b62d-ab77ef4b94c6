Based on your commits, here’s the changelog for the new Screenpipe update:

### **New Features:**
- **Cursor-like context in Obsidian pipe:** Introduced a new feature that provides a cursor-like context within the Obsidian pipe for enhanced user interaction.

### **Improvements:**
- **Renamed timeline to rewind:** The timeline feature has been renamed to 'rewind' for clearer functionality.

### **Fixes:**
- **Resolved favicon issues and metadata in pipes:** Fixed various issues related to favicons and metadata handling within the pipes.
- **Fixed bad hallucinated timestamp in Obsidian pipe:** Corrected erroneous timestamp behavior in the Obsidian pipe.
- **Fixed dark mode issue on Simple pipe:** Addressed compatibility problems with dark mode in the Simple pipe.

#### **Full Changelog:** [c05a2..6a3a2](https://github.com/mediar-ai/screenpipe/compare/c05a2..6a3a2)

