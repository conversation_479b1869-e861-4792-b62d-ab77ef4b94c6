---
title: "python sdk reference"
icon: "python"
---

## quick start

### hello world

```bash
pip install terminator-py
```

```python
import terminator
desktop = terminator.Desktop()
```

## basic usage

### opening applications

```python
# open calculator
desktop.open_application('calc')

# open notepad
desktop.open_application('notepad')

# open url
desktop.open_url('https://github.com/mediar-ai/terminator')
```

### locating and interacting with elements

```python
# locate calculator window and buttons
calc_window = desktop.locator('window:Calculator')
seven = calc_window.locator('name:Seven')
plus = calc_window.locator('name:Plus')

# click buttons
seven.click()
plus.click()

# type text in notepad
editor = desktop.locator('window:Notepad').locator('name:Edit')
editor.type_text('hello from terminator!')

# get text content
result = editor.get_text()
print(result.text)
```

### selector patterns

terminator supports various selector patterns for finding elements:

```python
# by name
desktop.locator('name:Seven')

# by role
desktop.locator('role:Button')

# by automation id
desktop.locator('automationid:CalculatorResults')

# by window title
desktop.locator('window:Calculator')

# chaining selectors
desktop.locator('window:Calculator').locator('role:Button').locator('name:Seven')
```

### element state and attributes

```python
# check if element is visible
is_visible = seven.is_visible()

# get element bounds
bounds = seven.get_bounds()
print(f'element at: {bounds.x}, {bounds.y}, size: {bounds.width}x{bounds.height}')

# get all attributes
attributes = seven.get_attributes()
print('element attributes:', attributes)
```

### expectations (waiting for conditions)

```python
# wait for element to be visible
element = editor_locator.expect_visible()

# wait for element to be enabled with timeout
editor_locator.expect_enabled(timeout=5000)

# wait for specific text
editor_locator.expect_text_equals('expected text', timeout=3000)

# wait for element to contain text
editor_locator.expect_text_contains('partial text')
```

### complete example

```python
import terminator

def automate_calculator():
    try:
        # create desktop instance
        desktop = terminator.Desktop()
        
        # open calculator
        desktop.open_application('calc')
        
        # wait for calculator to be ready
        calc_window = desktop.locator('window:Calculator')
        calc_window.expect_visible()
        
        # perform calculation: 7 + 7 = 14
        desktop.locator('name:Seven').click()
        desktop.locator('name:Plus').click()
        desktop.locator('name:Seven').click()
        desktop.locator('name:Equals').click()
        
        # get result
        result = desktop.locator('automationid:CalculatorResults').get_text()
        print(f'calculation result: {result.text}')
        
    except Exception as e:
        print(f'automation failed: {e}')

automate_calculator()
```

### form automation example

```python
import terminator

def automate_form():
    desktop = terminator.Desktop()
    
    # open application with form
    desktop.open_application('notepad')
    
    # find form fields and fill them
    editor = desktop.locator('name:Edit')
    
    # type structured data
    form_data = """
    name: john doe
    email: <EMAIL>
    phone: 555-0123
    """
    
    editor.type_text(form_data)
    
    # verify content was entered
    content = editor.get_text()
    print('form content:', content.text)

automate_form()
```

### error handling

```python
try:
    desktop.locator('name:DoesNotExist').click()
except ApiError as e:
    print(f'api error (status: {e.status}): {e}')
except Exception as e:
    print(f'unexpected error: {e}')
```

more examples

https://github.com/mediar-ai/terminator-python-examples
