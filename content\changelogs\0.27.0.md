Based on the provided commits, here is the changelog for the new Screenpipe update:

### **New Features:**
- **Added experimental perplexity-style AI search in rewind timeline:** Introduced a new AI-driven search feature to enhance user experience while navigating the rewind timeline.
- **Supported json_object in screenpipe-cloud/anthropic non-streaming OpenAI SDK:** Enhanced integration with the Anthropic OpenAI SDK to support JSON objects.
- **Listed available Obsidian vaults on your computer:** Added functionality to list available Obsidian vaults to reduce friction and improve user navigation.

### **Improvements:**
- **Improved UX for Obsidian pipe:** Made small user experience adjustments to enhance the usability of the Obsidian pipe.
- **Enhanced error handling for Anthropoc provider:** Integrated Sentry for better error tracking and handling in the screenpipe-cloud environment.

### **Fixes:**
- **Resolved compilation issues on some pipes:** Fixed compilation problems that affected certain pipes.
- **Validated Obsidian vault presence:** Properly validated folder paths for Obsidian vaults and provided user messages based on the existence of the vault.
- **Fixed telemetry issue:** Addressed issues related to telemetry data collection.
- **Fixed Obsidian prompt:** Resolved bugs affecting the Obsidian prompt functionality.
- **Fixed various Obsidian-related issues:** Addressed multiple fixes related to the Obsidian integration.

#### **Full Changelog:** [9e0cb..3dcf7](https://github.com/mediar-ai/screenpipe/compare/9e0cb..3dcf7)

