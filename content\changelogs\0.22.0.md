Based on the provided commits, here is the changelog for the new Screenpipe update:

### **New Features:**
- **Swapped configurations:** Enhanced flexibility by swapping configuration settings for improved user customization.

### **Improvements:**
- **Small improvement on store:** Made optimizations to improve the performance and efficiency of the store.

### **Fixes:**
- **Resolved issue with Claude MCP time prompts:** Fixed prompting issues related to Claude MCP time functionality.
- **Fixed error in video component:** Addressed an error in the video component to ensure proper functionality.

#### **Full Changelog:** [12de8..0d277](https://github.com/mediar-ai/screenpipe/compare/12de8..0d277)

