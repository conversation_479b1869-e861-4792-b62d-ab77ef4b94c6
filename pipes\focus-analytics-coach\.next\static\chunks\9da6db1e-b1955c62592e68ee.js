"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[229],{72341:(e,t,i)=>{i.d(t,{Ay:()=>ns});var s,r="undefined"!=typeof window?window:void 0,n="undefined"!=typeof globalThis?globalThis:r,o=Array.prototype,a=o.forEach,l=o.indexOf,c=null==n?void 0:n.navigator,u=null==n?void 0:n.document,d=null==n?void 0:n.location,h=null==n?void 0:n.fetch,_=null!=n&&n.XMLHttpRequest&&"withCredentials"in new n.XMLHttpRequest?n.XMLHttpRequest:void 0,p=null==n?void 0:n.AbortController,g=null==c?void 0:c.userAgent,v=null!=r?r:{},f={DEBUG:!1,LIB_VERSION:"1.207.2"},m="$copy_autocapture",b=["$snapshot","$pageview","$pageleave","$set","survey dismissed","survey sent","survey shown","$identify","$groupidentify","$create_alias","$$client_ingestion_warning","$web_experiment_applied","$feature_enrollment_update","$feature_flag_called"];function y(e,t){return -1!==e.indexOf(t)}!function(e){e.GZipJS="gzip-js",e.Base64="base64"}(s||(s={}));var w=function(e){return e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")},S=function(e){return e.replace(/^\$/,"")},E=Array.isArray,k=Object.prototype,x=k.hasOwnProperty,I=k.toString,C=E||function(e){return"[object Array]"===I.call(e)},P=e=>"function"==typeof e,F=e=>e===Object(e)&&!C(e),R=e=>{if(F(e)){for(var t in e)if(x.call(e,t))return!1;return!0}return!1},T=e=>void 0===e,$=e=>"[object String]"==I.call(e),O=e=>$(e)&&0===e.trim().length,L=e=>null===e,A=e=>T(e)||L(e),M=e=>"[object Number]"==I.call(e),D=e=>"[object Boolean]"===I.call(e),N=e=>e instanceof FormData,q=e=>y(b,e),B=e=>{var t={_log:function(t){if(r&&(f.DEBUG||v.POSTHOG_DEBUG)&&!T(r.console)&&r.console){for(var i=("__rrweb_original__"in r.console[t])?r.console[t].__rrweb_original__:r.console[t],s=arguments.length,n=Array(s>1?s-1:0),o=1;o<s;o++)n[o-1]=arguments[o];i(e,...n)}},info:function(){for(var e=arguments.length,i=Array(e),s=0;s<e;s++)i[s]=arguments[s];t._log("log",...i)},warn:function(){for(var e=arguments.length,i=Array(e),s=0;s<e;s++)i[s]=arguments[s];t._log("warn",...i)},error:function(){for(var e=arguments.length,i=Array(e),s=0;s<e;s++)i[s]=arguments[s];t._log("error",...i)},critical:function(){for(var t=arguments.length,i=Array(t),s=0;s<t;s++)i[s]=arguments[s];console.error(e,...i)},uninitializedWarning:e=>{t.error("You must initialize PostHog before calling ".concat(e))},createLogger:t=>B("".concat(e," ").concat(t))};return t},H=B("[PostHog.js]"),U=H.createLogger,z=U("[ExternalScriptsLoader]"),j=(e,t,i)=>{if(e.config.disable_external_dependency_loading)return z.warn("".concat(t," was requested but loading of external scripts is disabled.")),i("Loading of external scripts is disabled");var s=()=>{if(!u)return i("document not found");var s=u.createElement("script");if(s.type="text/javascript",s.crossOrigin="anonymous",s.src=t,s.onload=e=>i(void 0,e),s.onerror=e=>i(e),e.config.prepare_external_dependency_script&&(s=e.config.prepare_external_dependency_script(s)),!s)return i("prepare_external_dependency_script returned null");var r,n=u.querySelectorAll("body > script");n.length>0?null===(r=n[0].parentNode)||void 0===r||r.insertBefore(s,n[0]):u.body.appendChild(s)};null!=u&&u.body?s():null==u||u.addEventListener("DOMContentLoaded",s)};function W(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),i.push.apply(i,s)}return i}function V(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?W(Object(i),!0).forEach(function(t){G(e,t,i[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):W(Object(i)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))})}return e}function G(e,t,i){return t in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}function J(e,t){if(null==e)return{};var i,s,r=function(e,t){if(null==e)return{};var i,s,r={},n=Object.keys(e);for(s=0;s<n.length;s++)i=n[s],t.indexOf(i)>=0||(r[i]=e[i]);return r}(e,t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);for(s=0;s<n.length;s++)i=n[s],t.indexOf(i)>=0||Object.prototype.propertyIsEnumerable.call(e,i)&&(r[i]=e[i])}return r}v.__PosthogExtensions__=v.__PosthogExtensions__||{},v.__PosthogExtensions__.loadExternalDependency=(e,t,i)=>{var s="/static/".concat(t,".js")+"?v=".concat(e.version);if("remote-config"===t&&(s="/array/".concat(e.config.token,"/config.js")),"toolbar"===t){var r=3e5*Math.floor(Date.now()/3e5);s="".concat(s,"&t=").concat(r)}var n=e.requestRouter.endpointFor("assets",s);j(e,n,i)},v.__PosthogExtensions__.loadSiteApp=(e,t,i)=>{var s=e.requestRouter.endpointFor("api",t);j(e,s,i)};var Y={};function K(e,t,i){if(C(e)){if(a&&e.forEach===a)e.forEach(t,i);else if("length"in e&&e.length===+e.length){for(var s=0,r=e.length;s<r;s++)if(s in e&&t.call(i,e[s],s)===Y)return}}}function X(e,t,i){if(!A(e)){if(C(e))return K(e,t,i);if(N(e)){for(var s of e.entries())if(t.call(i,s[1],s[0])===Y)return}else for(var r in e)if(x.call(e,r)&&t.call(i,e[r],r)===Y)return}}var Q=function(e){for(var t=arguments.length,i=Array(t>1?t-1:0),s=1;s<t;s++)i[s-1]=arguments[s];return K(i,function(t){for(var i in t)void 0!==t[i]&&(e[i]=t[i])}),e},Z=function(e){for(var t=arguments.length,i=Array(t>1?t-1:0),s=1;s<t;s++)i[s-1]=arguments[s];return K(i,function(t){K(t,function(t){e.push(t)})}),e};function ee(e){for(var t=Object.keys(e),i=t.length,s=Array(i);i--;)s[i]=[t[i],e[t[i]]];return s}var et=function(e){try{return e()}catch(e){return}},ei=function(e){return function(){try{for(var t=arguments.length,i=Array(t),s=0;s<t;s++)i[s]=arguments[s];return e.apply(this,i)}catch(e){H.critical("Implementation error. Please turn on debug mode and open a ticket on https://app.posthog.com/home#panel=support%3Asupport%3A."),H.critical(e)}}},es=function(e){var t={};return X(e,function(e,i){$(e)&&e.length>0&&(t[i]=e)}),t},er=function(){function e(t){return t&&(t.preventDefault=e.preventDefault,t.stopPropagation=e.stopPropagation),t}return e.preventDefault=function(){this.returnValue=!1},e.stopPropagation=function(){this.cancelBubble=!0},function(t,i,s,n,o){if(t){if(t.addEventListener&&!n)t.addEventListener(i,s,!!o);else{var a="on"+i,l=t[a];t[a]=function(i){if(i=i||e(null==r?void 0:r.event)){var n,o=!0;P(l)&&(n=l(i));var a=s.call(t,i);return!1!==n&&!1!==a||(o=!1),o}}}}else H.error("No valid element provided to register_event")}}();function en(e,t){for(var i=0;i<e.length;i++)if(t(e[i]))return e[i]}var eo="$people_distinct_id",ea="__alias",el="__timers",ec="$autocapture_disabled_server_side",eu="$heatmaps_enabled_server_side",ed="$exception_capture_enabled_server_side",eh="$web_vitals_enabled_server_side",e_="$dead_clicks_enabled_server_side",ep="$web_vitals_allowed_metrics",eg="$session_recording_enabled_server_side",ev="$console_log_recording_enabled_server_side",ef="$session_recording_network_payload_capture",em="$session_recording_canvas_recording",eb="$replay_sample_rate",ey="$replay_minimum_duration",ew="$replay_script_config",eS="$sesid",eE="$session_is_sampled",ek="$session_recording_url_trigger_activated_session",ex="$session_recording_event_trigger_activated_session",eI="$enabled_feature_flags",eC="$early_access_features",eP="$stored_person_properties",eF="$stored_group_properties",eR="$surveys",eT="$surveys_activated",e$="$flag_call_reported",eO="$user_state",eL="$client_session_props",eA="$capture_rate_limit",eM="$initial_campaign_params",eD="$initial_referrer_info",eN="$initial_person_info",eq="$epp",eB="__POSTHOG_TOOLBAR__",eH="$posthog_cookieless",eU=[eo,ea,"__cmpns",el,eg,eu,eS,eI,eO,eC,eF,eP,eR,e$,eL,eA,eM,eD,eq],ez=U("[FeatureFlags]"),ej="$active_feature_flags",eW="$override_feature_flags",eV="$feature_flag_payloads",eG=e=>{var t={};for(var[i,s]of ee(e||{}))s&&(t[i]=s);return t};class eJ{constructor(e){G(this,"_override_warning",!1),G(this,"_hasLoadedFlags",!1),G(this,"_requestInFlight",!1),G(this,"_reloadingDisabled",!1),G(this,"_additionalReloadRequested",!1),G(this,"_decideCalled",!1),G(this,"_flagsLoadedFromRemote",!1),this.instance=e,this.featureFlagEventHandlers=[]}decide(){if(this.instance.config.__preview_remote_config)this._decideCalled=!0;else{var e=!this._reloadDebouncer&&(this.instance.config.advanced_disable_feature_flags||this.instance.config.advanced_disable_feature_flags_on_first_load);this._callDecideEndpoint({disableFlags:e})}}get hasLoadedFlags(){return this._hasLoadedFlags}getFlags(){return Object.keys(this.getFlagVariants())}getFlagVariants(){var e=this.instance.get_property(eI),t=this.instance.get_property(eW);if(!t)return e||{};for(var i=Q({},e),s=Object.keys(t),r=0;r<s.length;r++)i[s[r]]=t[s[r]];return this._override_warning||(ez.warn(" Overriding feature flags!",{enabledFlags:e,overriddenFlags:t,finalFlags:i}),this._override_warning=!0),i}getFlagPayloads(){return this.instance.get_property(eV)||{}}reloadFeatureFlags(){this._reloadingDisabled||this.instance.config.advanced_disable_feature_flags||this._reloadDebouncer||(this._reloadDebouncer=setTimeout(()=>{this._callDecideEndpoint()},5))}clearDebouncer(){clearTimeout(this._reloadDebouncer),this._reloadDebouncer=void 0}ensureFlagsLoaded(){this._hasLoadedFlags||this._requestInFlight||this._reloadDebouncer||this.reloadFeatureFlags()}setAnonymousDistinctId(e){this.$anon_distinct_id=e}setReloadingPaused(e){this._reloadingDisabled=e}_callDecideEndpoint(e){if(this.clearDebouncer(),!this.instance.config.advanced_disable_decide){if(this._requestInFlight)this._additionalReloadRequested=!0;else{var t={token:this.instance.config.token,distinct_id:this.instance.get_distinct_id(),groups:this.instance.getGroups(),$anon_distinct_id:this.$anon_distinct_id,person_properties:this.instance.get_property(eP),group_properties:this.instance.get_property(eF)};(null!=e&&e.disableFlags||this.instance.config.advanced_disable_feature_flags)&&(t.disable_flags=!0),this._requestInFlight=!0,this.instance._send_request({method:"POST",url:this.instance.requestRouter.endpointFor("api","/decide/?v=3"),data:t,compression:this.instance.config.disable_compression?void 0:s.Base64,timeout:this.instance.config.feature_flag_request_timeout_ms,callback:e=>{var i,s,r=!0;200===e.statusCode&&(this.$anon_distinct_id=void 0,r=!1),this._requestInFlight=!1,this._decideCalled||(this._decideCalled=!0,this.instance._onRemoteConfig(null!==(s=e.json)&&void 0!==s?s:{})),t.disable_flags||(this._flagsLoadedFromRemote=!r,this.receivedFeatureFlags(null!==(i=e.json)&&void 0!==i?i:{},r),this._additionalReloadRequested&&(this._additionalReloadRequested=!1,this._callDecideEndpoint()))}})}}}getFeatureFlag(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(this._hasLoadedFlags||this.getFlags()&&this.getFlags().length>0){var i,s,r,n,o,a=this.getFlagVariants()[e],l="".concat(a),c=this.instance.get_property(e$)||{};return!t.send_event&&"send_event"in t||e in c&&c[e].includes(l)||(C(c[e])?c[e].push(l):c[e]=[l],null===(i=this.instance.persistence)||void 0===i||i.register({[e$]:c}),this.instance.capture("$feature_flag_called",{$feature_flag:e,$feature_flag_response:a,$feature_flag_payload:this.getFeatureFlagPayload(e)||null,$feature_flag_bootstrapped_response:(null===(s=this.instance.config.bootstrap)||void 0===s||null===(r=s.featureFlags)||void 0===r?void 0:r[e])||null,$feature_flag_bootstrapped_payload:(null===(n=this.instance.config.bootstrap)||void 0===n||null===(o=n.featureFlagPayloads)||void 0===o?void 0:o[e])||null,$used_bootstrap_value:!this._flagsLoadedFromRemote})),a}ez.warn('getFeatureFlag for key "'+e+"\" failed. Feature flags didn't load in time.")}getFeatureFlagPayload(e){return this.getFlagPayloads()[e]}isFeatureEnabled(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(this._hasLoadedFlags||this.getFlags()&&this.getFlags().length>0)return!!this.getFeatureFlag(e,t);ez.warn('isFeatureEnabled for key "'+e+"\" failed. Feature flags didn't load in time.")}addFeatureFlagsHandler(e){this.featureFlagEventHandlers.push(e)}removeFeatureFlagsHandler(e){this.featureFlagEventHandlers=this.featureFlagEventHandlers.filter(t=>t!==e)}receivedFeatureFlags(e,t){if(this.instance.persistence){this._hasLoadedFlags=!0;var i=this.getFlagVariants(),s=this.getFlagPayloads();!function(e,t){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},r=e.featureFlags,n=e.featureFlagPayloads;if(r){if(C(r)){var o={};if(r)for(var a=0;a<r.length;a++)o[r[a]]=!0;t&&t.register({[ej]:r,[eI]:o})}else{var l=r,c=n;e.errorsWhileComputingFlags&&(l=V(V({},i),l),c=V(V({},s),c)),t&&t.register({[ej]:Object.keys(eG(l)),[eI]:l||{},[eV]:c||{}})}}}(e,this.instance.persistence,i,s),this._fireFeatureFlagsCallbacks(t)}}override(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!this.instance.__loaded||!this.instance.persistence)return ez.uninitializedWarning("posthog.feature_flags.override");if(this._override_warning=t,!1===e)this.instance.persistence.unregister(eW);else if(C(e)){for(var i={},s=0;s<e.length;s++)i[e[s]]=!0;this.instance.persistence.register({[eW]:i})}else this.instance.persistence.register({[eW]:e})}onFeatureFlags(e){if(this.addFeatureFlagsHandler(e),this._hasLoadedFlags){var{flags:t,flagVariants:i}=this._prepareFeatureFlagsForCallbacks();e(t,i)}return()=>this.removeFeatureFlagsHandler(e)}updateEarlyAccessFeatureEnrollment(e,t){var i,s=(this.instance.get_property(eC)||[]).find(t=>t.flagKey===e),r={["$feature_enrollment/".concat(e)]:t},n={$feature_flag:e,$feature_enrollment:t,$set:r};s&&(n.$early_access_feature_name=s.name),this.instance.capture("$feature_enrollment_update",n),this.setPersonPropertiesForFlags(r,!1);var o=V(V({},this.getFlagVariants()),{},{[e]:t});null===(i=this.instance.persistence)||void 0===i||i.register({[ej]:Object.keys(eG(o)),[eI]:o}),this._fireFeatureFlagsCallbacks()}getEarlyAccessFeatures(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=this.instance.get_property(eC);if(i&&!t)return e(i);this.instance._send_request({url:this.instance.requestRouter.endpointFor("api","/api/early_access_features/?token=".concat(this.instance.config.token)),method:"GET",callback:t=>{var i;if(t.json){var s=t.json.earlyAccessFeatures;return null===(i=this.instance.persistence)||void 0===i||i.register({[eC]:s}),e(s)}}})}_prepareFeatureFlagsForCallbacks(){var e=this.getFlags(),t=this.getFlagVariants();return{flags:e.filter(e=>t[e]),flagVariants:Object.keys(t).filter(e=>t[e]).reduce((e,i)=>(e[i]=t[i],e),{})}}_fireFeatureFlagsCallbacks(e){var{flags:t,flagVariants:i}=this._prepareFeatureFlagsForCallbacks();this.featureFlagEventHandlers.forEach(s=>s(t,i,{errorsLoading:e}))}setPersonPropertiesForFlags(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],i=this.instance.get_property(eP)||{};this.instance.register({[eP]:V(V({},i),e)}),t&&this.instance.reloadFeatureFlags()}resetPersonPropertiesForFlags(){this.instance.unregister(eP)}setGroupPropertiesForFlags(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],i=this.instance.get_property(eF)||{};0!==Object.keys(i).length&&Object.keys(i).forEach(t=>{i[t]=V(V({},i[t]),e[t]),delete e[t]}),this.instance.register({[eF]:V(V({},i),e)}),t&&this.instance.reloadFeatureFlags()}resetGroupPropertiesForFlags(e){if(e){var t=this.instance.get_property(eF)||{};this.instance.register({[eF]:V(V({},t),{},{[e]:{}})})}else this.instance.unregister(eF)}}Math.trunc||(Math.trunc=function(e){return e<0?Math.ceil(e):Math.floor(e)}),Number.isInteger||(Number.isInteger=function(e){return M(e)&&isFinite(e)&&Math.floor(e)===e});var eY="0123456789abcdef";class eK{constructor(e){if(this.bytes=e,16!==e.length)throw TypeError("not 128-bit length")}static fromFieldsV7(e,t,i,s){if(!Number.isInteger(e)||!Number.isInteger(t)||!Number.isInteger(i)||!Number.isInteger(s)||e<0||t<0||i<0||s<0||e>0xffffffffffff||t>4095||i>0x3fffffff||s>0xffffffff)throw RangeError("invalid field value");var r=new Uint8Array(16);return r[0]=e/0x10000000000,r[1]=e/0x100000000,r[2]=e/0x1000000,r[3]=e/65536,r[4]=e/256,r[5]=e,r[6]=112|t>>>8,r[7]=t,r[8]=128|i>>>24,r[9]=i>>>16,r[10]=i>>>8,r[11]=i,r[12]=s>>>24,r[13]=s>>>16,r[14]=s>>>8,r[15]=s,new eK(r)}toString(){for(var e="",t=0;t<this.bytes.length;t++)e=e+eY.charAt(this.bytes[t]>>>4)+eY.charAt(15&this.bytes[t]),3!==t&&5!==t&&7!==t&&9!==t||(e+="-");if(36!==e.length)throw Error("Invalid UUIDv7 was generated");return e}clone(){return new eK(this.bytes.slice(0))}equals(e){return 0===this.compareTo(e)}compareTo(e){for(var t=0;t<16;t++){var i=this.bytes[t]-e.bytes[t];if(0!==i)return Math.sign(i)}return 0}}class eX{constructor(){G(this,"timestamp",0),G(this,"counter",0),G(this,"random",new e0)}generate(){var e=this.generateOrAbort();if(T(e)){this.timestamp=0;var t=this.generateOrAbort();if(T(t))throw Error("Could not generate UUID after timestamp reset");return t}return e}generateOrAbort(){var e=Date.now();if(e>this.timestamp)this.timestamp=e,this.resetCounter();else{if(!(e+1e4>this.timestamp))return;this.counter++,this.counter>0x3ffffffffff&&(this.timestamp++,this.resetCounter())}return eK.fromFieldsV7(this.timestamp,Math.trunc(this.counter/0x40000000),0x3fffffff&this.counter,this.random.nextUint32())}resetCounter(){this.counter=1024*this.random.nextUint32()+(1023&this.random.nextUint32())}}var eQ,eZ=e=>{if("undefined"!=typeof UUIDV7_DENY_WEAK_RNG&&UUIDV7_DENY_WEAK_RNG)throw Error("no cryptographically strong RNG available");for(var t=0;t<e.length;t++)e[t]=65536*Math.trunc(65536*Math.random())+Math.trunc(65536*Math.random());return e};r&&!T(r.crypto)&&crypto.getRandomValues&&(eZ=e=>crypto.getRandomValues(e));class e0{constructor(){G(this,"buffer",new Uint32Array(8)),G(this,"cursor",1/0)}nextUint32(){return this.cursor>=this.buffer.length&&(eZ(this.buffer),this.cursor=0),this.buffer[this.cursor++]}}var e1=()=>e2().toString(),e2=()=>(eQ||(eQ=new eX)).generate(),e3="",e5=/[a-z0-9][a-z0-9-]+\.[a-z]{2,}$/i,e6={is_supported:()=>!!u,error:function(e){H.error("cookieStore error: "+e)},get:function(e){if(u){try{for(var t=e+"=",i=u.cookie.split(";").filter(e=>e.length),s=0;s<i.length;s++){for(var r=i[s];" "==r.charAt(0);)r=r.substring(1,r.length);if(0===r.indexOf(t))return decodeURIComponent(r.substring(t.length,r.length))}}catch(e){}return null}},parse:function(e){var t;try{t=JSON.parse(e6.get(e))||{}}catch(e){}return t},set:function(e,t,i,s,r){if(u)try{var n="",o="",a=function(e,t){if(t){var i=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:u;if(e3)return e3;if(!t||["localhost","127.0.0.1"].includes(e))return"";for(var i=e.split("."),s=Math.min(i.length,8),r="dmn_chk_"+e1(),n=RegExp("(^|;)\\s*"+r+"=1");!e3&&s--;){var o=i.slice(s).join("."),a=r+"=1;domain=."+o;t.cookie=a,n.test(t.cookie)&&(t.cookie=a+";expires=Thu, 01 Jan 1970 00:00:00 GMT",e3=o)}return e3}(e);if(!i){var s,r=(s=e.match(e5))?s[0]:"";r!==i&&H.info("Warning: cookie subdomain discovery mismatch",r,i),i=r}return i?"; domain=."+i:""}return""}(u.location.hostname,s);if(i){var l=new Date;l.setTime(l.getTime()+24*i*36e5),n="; expires="+l.toUTCString()}r&&(o="; secure");var c=e+"="+encodeURIComponent(JSON.stringify(t))+n+"; SameSite=Lax; path=/"+a+o;return c.length>3686.4&&H.warn("cookieStore warning: large cookie, len="+c.length),u.cookie=c,c}catch(e){return}},remove:function(e,t){try{e6.set(e,"",-1,t)}catch(e){return}}},e8=null,e4={is_supported:function(){if(!L(e8))return e8;var e=!0;if(T(r))e=!1;else try{var t="__mplssupport__";e4.set(t,"xyz"),'"xyz"'!==e4.get(t)&&(e=!1),e4.remove(t)}catch(t){e=!1}return e||H.error("localStorage unsupported; falling back to cookie store"),e8=e,e},error:function(e){H.error("localStorage error: "+e)},get:function(e){try{return null==r?void 0:r.localStorage.getItem(e)}catch(e){e4.error(e)}return null},parse:function(e){try{return JSON.parse(e4.get(e))||{}}catch(e){}return null},set:function(e,t){try{null==r||r.localStorage.setItem(e,JSON.stringify(t))}catch(e){e4.error(e)}},remove:function(e){try{null==r||r.localStorage.removeItem(e)}catch(e){e4.error(e)}}},e7=["distinct_id",eS,eE,eq,eN],e9=V(V({},e4),{},{parse:function(e){try{var t={};try{t=e6.parse(e)||{}}catch(e){}var i=Q(t,JSON.parse(e4.get(e)||"{}"));return e4.set(e,i),i}catch(e){}return null},set:function(e,t,i,s,r,n){try{e4.set(e,t,void 0,void 0,n);var o={};e7.forEach(e=>{t[e]&&(o[e]=t[e])}),Object.keys(o).length&&e6.set(e,o,i,s,r,n)}catch(e){e4.error(e)}},remove:function(e,t){try{null==r||r.localStorage.removeItem(e),e6.remove(e,t)}catch(e){e4.error(e)}}}),te={},tt={is_supported:function(){return!0},error:function(e){H.error("memoryStorage error: "+e)},get:function(e){return te[e]||null},parse:function(e){return te[e]||null},set:function(e,t){te[e]=t},remove:function(e){delete te[e]}},ti=null,ts={is_supported:function(){if(!L(ti))return ti;if(ti=!0,T(r))ti=!1;else try{var e="__support__";ts.set(e,"xyz"),'"xyz"'!==ts.get(e)&&(ti=!1),ts.remove(e)}catch(e){ti=!1}return ti},error:function(e){H.error("sessionStorage error: ",e)},get:function(e){try{return null==r?void 0:r.sessionStorage.getItem(e)}catch(e){ts.error(e)}return null},parse:function(e){try{return JSON.parse(ts.get(e))||null}catch(e){}return null},set:function(e,t){try{null==r||r.sessionStorage.setItem(e,JSON.stringify(t))}catch(e){ts.error(e)}},remove:function(e){try{null==r||r.sessionStorage.removeItem(e)}catch(e){ts.error(e)}}},tr=["localhost","127.0.0.1"],tn=e=>{var t=null==u?void 0:u.createElement("a");return T(t)?null:(t.href=e,t)},to=function(e,t){return!!function(e){try{new RegExp(e)}catch(e){return!1}return!0}(t)&&new RegExp(t).test(e)},ta=function(e){var t,i,s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"&",r=[];return X(e,function(e,s){T(e)||T(s)||"undefined"===s||(t=encodeURIComponent(e instanceof File?e.name:e.toString()),i=encodeURIComponent(s),r[r.length]=i+"="+t)}),r.join(s)},tl=function(e,t){for(var i,s=((e.split("#")[0]||"").split("?")[1]||"").split("&"),r=0;r<s.length;r++){var n=s[r].split("=");if(n[0]===t){i=n;break}}if(!C(i)||i.length<2)return"";var o=i[1];try{o=decodeURIComponent(o)}catch(e){H.error("Skipping decoding for malformed query param: "+o)}return o.replace(/\+/g," ")},tc=function(e,t,i){if(!e||!t||!t.length)return e;for(var s=e.split("#"),r=s[0]||"",n=s[1],o=r.split("?"),a=o[1],l=o[0],c=(a||"").split("&"),u=[],d=0;d<c.length;d++){var h=c[d].split("=");C(h)&&(t.includes(h[0])?u.push(h[0]+"="+i):u.push(c[d]))}var _=l;return null!=a&&(_+="?"+u.join("&")),null!=n&&(_+="#"+n),_},tu=function(e,t){var i=e.match(RegExp(t+"=([^&]*)"));return i?i[1]:null},td="Mobile",th="Android",t_="Tablet",tp=th+" "+t_,tg="iPad",tv="Apple",tf=tv+" Watch",tm="Safari",tb="BlackBerry",ty="Samsung",tw=ty+"Browser",tS=ty+" Internet",tE="Chrome",tk=tE+" OS",tx=tE+" iOS",tI="Internet Explorer",tC=tI+" "+td,tP="Opera",tF=tP+" Mini",tR="Edge",tT="Microsoft "+tR,t$="Firefox",tO=t$+" iOS",tL="Nintendo",tA="PlayStation",tM="Xbox",tD=th+" "+td,tN=td+" "+tm,tq="Windows",tB=tq+" Phone",tH="Nokia",tU="Ouya",tz="Generic",tj=tz+" "+td.toLowerCase(),tW=tz+" "+t_.toLowerCase(),tV="Konqueror",tG="(\\d+(\\.\\d+)?)",tJ=RegExp("Version/"+tG),tY=RegExp(tM,"i"),tK=RegExp(tA+" \\w+","i"),tX=RegExp(tL+" \\w+","i"),tQ=RegExp(tb+"|PlayBook|BB10","i"),tZ={"NT3.51":"NT 3.11","NT4.0":"NT 4.0","5.0":"2000",5.1:"XP",5.2:"XP","6.0":"Vista",6.1:"7",6.2:"8",6.3:"8.1",6.4:"10","10.0":"10"},t0=(e,t)=>t&&y(t,tv)||function(e){return y(e,tm)&&!y(e,tE)&&!y(e,th)}(e),t1=function(e,t){return t=t||"",y(e," OPR/")&&y(e,"Mini")?tF:y(e," OPR/")?tP:tQ.test(e)?tb:y(e,"IE"+td)||y(e,"WPDesktop")?tC:y(e,tw)?tS:y(e,tR)||y(e,"Edg/")?tT:y(e,"FBIOS")?"Facebook "+td:y(e,"UCWEB")||y(e,"UCBrowser")?"UC Browser":y(e,"CriOS")?tx:y(e,"CrMo")||y(e,tE)?tE:y(e,th)&&y(e,tm)?tD:y(e,"FxiOS")?tO:y(e.toLowerCase(),tV.toLowerCase())?tV:t0(e,t)?y(e,td)?tN:tm:y(e,t$)?t$:y(e,"MSIE")||y(e,"Trident/")?tI:y(e,"Gecko")?t$:""},t2={[tC]:[RegExp("rv:"+tG)],[tT]:[RegExp(tR+"?\\/"+tG)],[tE]:[RegExp("("+tE+"|CrMo)\\/"+tG)],[tx]:[RegExp("CriOS\\/"+tG)],"UC Browser":[RegExp("(UCBrowser|UCWEB)\\/"+tG)],[tm]:[tJ],[tN]:[tJ],[tP]:[RegExp("(Opera|OPR)\\/"+tG)],[t$]:[RegExp(t$+"\\/"+tG)],[tO]:[RegExp("FxiOS\\/"+tG)],[tV]:[RegExp("Konqueror[:/]?"+tG,"i")],[tb]:[RegExp(tb+" "+tG),tJ],[tD]:[RegExp("android\\s"+tG,"i")],[tS]:[RegExp(tw+"\\/"+tG)],[tI]:[RegExp("(rv:|MSIE )"+tG)],Mozilla:[RegExp("rv:"+tG)]},t3=[[RegExp(tM+"; "+tM+" (.*?)[);]","i"),e=>[tM,e&&e[1]||""]],[RegExp(tL,"i"),[tL,""]],[RegExp(tA,"i"),[tA,""]],[tQ,[tb,""]],[RegExp(tq,"i"),(e,t)=>{if(/Phone/.test(t)||/WPDesktop/.test(t))return[tB,""];if(new RegExp(td).test(t)&&!/IEMobile\b/.test(t))return[tq+" "+td,""];var i=/Windows NT ([0-9.]+)/i.exec(t);if(i&&i[1]){var s=tZ[i[1]]||"";return/arm/i.test(t)&&(s="RT"),[tq,s]}return[tq,""]}],[/((iPhone|iPad|iPod).*?OS (\d+)_(\d+)_?(\d+)?|iPhone)/,e=>e&&e[3]?["iOS",[e[3],e[4],e[5]||"0"].join(".")]:["iOS",""]],[/(watch.*\/(\d+\.\d+\.\d+)|watch os,(\d+\.\d+),)/i,e=>{var t="";return e&&e.length>=3&&(t=T(e[2])?e[3]:e[2]),["watchOS",t]}],[RegExp("("+th+" (\\d+)\\.(\\d+)\\.?(\\d+)?|"+th+")","i"),e=>e&&e[2]?[th,[e[2],e[3],e[4]||"0"].join(".")]:[th,""]],[/Mac OS X (\d+)[_.](\d+)[_.]?(\d+)?/i,e=>{var t=["Mac OS X",""];if(e&&e[1]){var i=[e[1],e[2],e[3]||"0"];t[1]=i.join(".")}return t}],[/Mac/i,["Mac OS X",""]],[/CrOS/,[tk,""]],[/Linux|debian/i,["Linux",""]]],t5=function(e){return tX.test(e)?tL:tK.test(e)?tA:tY.test(e)?tM:RegExp(tU,"i").test(e)?tU:RegExp("("+tB+"|WPDesktop)","i").test(e)?tB:/iPad/.test(e)?tg:/iPod/.test(e)?"iPod Touch":/iPhone/.test(e)?"iPhone":/(watch)(?: ?os[,/]|\d,\d\/)[\d.]+/i.test(e)?tf:tQ.test(e)?tb:/(kobo)\s(ereader|touch)/i.test(e)?"Kobo":RegExp(tH,"i").test(e)?tH:/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i.test(e)||/(kf[a-z]+)( bui|\)).+silk\//i.test(e)?"Kindle Fire":/(Android|ZTE)/i.test(e)?!new RegExp(td).test(e)||/(9138B|TB782B|Nexus [97]|pixel c|HUAWEISHT|BTV|noble nook|smart ultra 6)/i.test(e)?/pixel[\daxl ]{1,6}/i.test(e)&&!/pixel c/i.test(e)||/(huaweimed-al00|tah-|APA|SM-G92|i980|zte|U304AA)/i.test(e)||/lmy47v/i.test(e)&&!/QTAQZ3/i.test(e)?th:tp:th:RegExp("(pda|"+td+")","i").test(e)?tj:RegExp(t_,"i").test(e)&&!RegExp(t_+" pc","i").test(e)?tW:""},t6="https?://(.*)",t8=["gclid","gclsrc","dclid","gbraid","wbraid","fbclid","msclkid","twclid","li_fat_id","igshid","ttclid","rdt_cid","irclid","_kx"],t4=Z(["utm_source","utm_medium","utm_campaign","utm_content","utm_term","gad_source","mc_cid"],t8),t7="<masked>",t9={campaignParams:function(){var{customTrackedParams:e,maskPersonalDataProperties:t,customPersonalDataProperties:i}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!u)return{};var s=t?Z([],t8,i||[]):[];return this._campaignParamsFromUrl(tc(u.URL,s,t7),e)},_campaignParamsFromUrl:function(e,t){var i=t4.concat(t||[]),s={};return X(i,function(t){var i=tl(e,t);s[t]=i||null}),s},_searchEngine:function(e){return e?0===e.search(t6+"google.([^/?]*)")?"google":0===e.search(t6+"bing.com")?"bing":0===e.search(t6+"yahoo.com")?"yahoo":0===e.search(t6+"duckduckgo.com")?"duckduckgo":null:null},_searchInfoFromReferrer:function(e){var t=t9._searchEngine(e),i={};if(!L(t)){i.$search_engine=t;var s=u?tl(u.referrer,"yahoo"!=t?"q":"p"):"";s.length&&(i.ph_keyword=s)}return i},searchInfo:function(){var e=null==u?void 0:u.referrer;return e?this._searchInfoFromReferrer(e):{}},browser:t1,browserVersion:function(e,t){var i=t2[t1(e,t)];if(T(i))return null;for(var s=0;s<i.length;s++){var r=i[s],n=e.match(r);if(n)return parseFloat(n[n.length-2])}return null},browserLanguage:function(){return navigator.language||navigator.userLanguage},browserLanguagePrefix:function(){var e=this.browserLanguage();return"string"==typeof e?e.split("-")[0]:void 0},os:function(e){for(var t=0;t<t3.length;t++){var[i,s]=t3[t],r=i.exec(e),n=r&&(P(s)?s(r,e):s);if(n)return n}return["",""]},device:t5,deviceType:function(e){var t=t5(e);return t===tg||t===tp||"Kobo"===t||"Kindle Fire"===t||t===tW?t_:t===tL||t===tM||t===tA||t===tU?"Console":t===tf?"Wearable":t?td:"Desktop"},referrer:function(){return(null==u?void 0:u.referrer)||"$direct"},referringDomain:function(){var e;return null!=u&&u.referrer&&(null===(e=tn(u.referrer))||void 0===e?void 0:e.host)||"$direct"},referrerInfo:function(){return{$referrer:this.referrer(),$referring_domain:this.referringDomain()}},initialPersonInfo:function(){return{r:this.referrer().substring(0,1e3),u:null==d?void 0:d.href.substring(0,1e3)}},initialPersonPropsFromInfo:function(e){var t,{r:i,u:s}=e,r={$initial_referrer:i,$initial_referring_domain:null==i?void 0:"$direct"==i?"$direct":null===(t=tn(i))||void 0===t?void 0:t.host};if(s){r.$initial_current_url=s;var n=tn(s);r.$initial_host=null==n?void 0:n.host,r.$initial_pathname=null==n?void 0:n.pathname,X(this._campaignParamsFromUrl(s),function(e,t){r["$initial_"+S(t)]=e})}return i&&X(this._searchInfoFromReferrer(i),function(e,t){r["$initial_"+S(t)]=e}),r},timezone:function(){try{return Intl.DateTimeFormat().resolvedOptions().timeZone}catch(e){return}},timezoneOffset:function(){try{return(new Date).getTimezoneOffset()}catch(e){return}},properties:function(){var{maskPersonalDataProperties:e,customPersonalDataProperties:t}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!g)return{};var i=e?Z([],t8,t||[]):[],[s,n]=t9.os(g);return Q(es({$os:s,$os_version:n,$browser:t9.browser(g,navigator.vendor),$device:t9.device(g),$device_type:t9.deviceType(g),$timezone:t9.timezone(),$timezone_offset:t9.timezoneOffset()}),{$current_url:tc(null==d?void 0:d.href,i,t7),$host:null==d?void 0:d.host,$pathname:null==d?void 0:d.pathname,$raw_user_agent:g.length>1e3?g.substring(0,997)+"...":g,$browser_version:t9.browserVersion(g,navigator.vendor),$browser_language:t9.browserLanguage(),$browser_language_prefix:t9.browserLanguagePrefix(),$screen_height:null==r?void 0:r.screen.height,$screen_width:null==r?void 0:r.screen.width,$viewport_height:null==r?void 0:r.innerHeight,$viewport_width:null==r?void 0:r.innerWidth,$lib:"web",$lib_version:f.LIB_VERSION,$insert_id:Math.random().toString(36).substring(2,10)+Math.random().toString(36).substring(2,10),$time:Date.now()/1e3})},people_properties:function(){if(!g)return{};var[e,t]=t9.os(g);return Q(es({$os:e,$os_version:t,$browser:t9.browser(g,navigator.vendor)}),{$browser_version:t9.browserVersion(g,navigator.vendor)})}},ie=["cookie","localstorage","localstorage+cookie","sessionstorage","memory"];class it{constructor(e){this.config=e,this.props={},this.campaign_params_saved=!1,this.name=(e=>{var t="";return e.token&&(t=e.token.replace(/\+/g,"PL").replace(/\//g,"SL").replace(/=/g,"EQ")),e.persistence_name?"ph_"+e.persistence_name:"ph_"+t+"_posthog"})(e),this.storage=this.buildStorage(e),this.load(),e.debug&&H.info("Persistence loaded",e.persistence,V({},this.props)),this.update_config(e,e),this.save()}buildStorage(e){-1===ie.indexOf(e.persistence.toLowerCase())&&(H.critical("Unknown persistence type "+e.persistence+"; falling back to localStorage+cookie"),e.persistence="localStorage+cookie");var t=e.persistence.toLowerCase();return"localstorage"===t&&e4.is_supported()?e4:"localstorage+cookie"===t&&e9.is_supported()?e9:"sessionstorage"===t&&ts.is_supported()?ts:"memory"===t?tt:"cookie"===t?e6:e9.is_supported()?e9:e6}properties(){var e={};return X(this.props,function(t,i){if(i===eI&&F(t))for(var s,r=Object.keys(t),n=0;n<r.length;n++)e["$feature/".concat(r[n])]=t[r[n]];else s=!1,(L(eU)?s:l&&eU.indexOf===l?-1!=eU.indexOf(i):(X(eU,function(e){if(s||(s=e===i))return Y}),s))||(e[i]=t)}),e}load(){if(!this.disabled){var e=this.storage.parse(this.name);e&&(this.props=Q({},e))}}save(){this.disabled||this.storage.set(this.name,this.props,this.expire_days,this.cross_subdomain,this.secure,this.config.debug)}remove(){this.storage.remove(this.name,!1),this.storage.remove(this.name,!0)}clear(){this.remove(),this.props={}}register_once(e,t,i){if(F(e)){T(t)&&(t="None"),this.expire_days=T(i)?this.default_expiry:i;var s=!1;if(X(e,(e,i)=>{this.props.hasOwnProperty(i)&&this.props[i]!==t||(this.props[i]=e,s=!0)}),s)return this.save(),!0}return!1}register(e,t){if(F(e)){this.expire_days=T(t)?this.default_expiry:t;var i=!1;if(X(e,(t,s)=>{e.hasOwnProperty(s)&&this.props[s]!==t&&(this.props[s]=t,i=!0)}),i)return this.save(),!0}return!1}unregister(e){e in this.props&&(delete this.props[e],this.save())}update_campaign_params(){if(!this.campaign_params_saved){var e=t9.campaignParams({customTrackedParams:this.config.custom_campaign_params,maskPersonalDataProperties:this.config.mask_personal_data_properties,customPersonalDataProperties:this.config.custom_personal_data_properties});R(es(e))||this.register(e),this.campaign_params_saved=!0}}update_search_keyword(){this.register(t9.searchInfo())}update_referrer_info(){this.register_once(t9.referrerInfo(),void 0)}set_initial_person_info(){this.props[eM]||this.props[eD]||this.register_once({[eN]:t9.initialPersonInfo()},void 0)}get_referrer_info(){return es({$referrer:this.props.$referrer,$referring_domain:this.props.$referring_domain})}get_initial_props(){var e={};X([eD,eM],t=>{var i=this.props[t];i&&X(i,function(t,i){e["$initial_"+S(i)]=t})});var t=this.props[eN];return t&&Q(e,t9.initialPersonPropsFromInfo(t)),e}safe_merge(e){return X(this.props,function(t,i){i in e||(e[i]=t)}),e}update_config(e,t){if(this.default_expiry=this.expire_days=e.cookie_expiration,this.set_disabled(e.disable_persistence),this.set_cross_subdomain(e.cross_subdomain_cookie),this.set_secure(e.secure_cookie),e.persistence!==t.persistence){var i=this.buildStorage(e),s=this.props;this.clear(),this.storage=i,this.props=s,this.save()}}set_disabled(e){this.disabled=e,this.disabled?this.remove():this.save()}set_cross_subdomain(e){e!==this.cross_subdomain&&(this.cross_subdomain=e,this.remove(),this.save())}get_cross_subdomain(){return!!this.cross_subdomain}set_secure(e){e!==this.secure&&(this.secure=e,this.remove(),this.save())}set_event_timer(e,t){var i=this.props[el]||{};i[e]=t,this.props[el]=i,this.save()}remove_event_timer(e){var t=(this.props[el]||{})[e];return T(t)||(delete this.props[el][e],this.save()),t}get_property(e){return this.props[e]}set_property(e,t){this.props[e]=t,this.save()}}function ii(e){var t,i;return(null===(t=JSON.stringify(e,(i=[],function(e,t){if(F(t)){for(;i.length>0&&i[i.length-1]!==this;)i.pop();return i.includes(t)?"[Circular]":(i.push(t),t)}return t})))||void 0===t?void 0:t.length)||0}var is=(e=>(e[e.DomContentLoaded=0]="DomContentLoaded",e[e.Load=1]="Load",e[e.FullSnapshot=2]="FullSnapshot",e[e.IncrementalSnapshot=3]="IncrementalSnapshot",e[e.Meta=4]="Meta",e[e.Custom=5]="Custom",e[e.Plugin=6]="Plugin",e))(is||{}),ir=(e=>(e[e.Mutation=0]="Mutation",e[e.MouseMove=1]="MouseMove",e[e.MouseInteraction=2]="MouseInteraction",e[e.Scroll=3]="Scroll",e[e.ViewportResize=4]="ViewportResize",e[e.Input=5]="Input",e[e.TouchMove=6]="TouchMove",e[e.MediaInteraction=7]="MediaInteraction",e[e.StyleSheetRule=8]="StyleSheetRule",e[e.CanvasMutation=9]="CanvasMutation",e[e.Font=10]="Font",e[e.Log=11]="Log",e[e.Drag=12]="Drag",e[e.StyleDeclaration=13]="StyleDeclaration",e[e.Selection=14]="Selection",e[e.AdoptedStyleSheet=15]="AdoptedStyleSheet",e[e.CustomElement=16]="CustomElement",e))(ir||{});function io(e){var t;return e instanceof Element&&(e.id===eB||!(null===(t=e.closest)||void 0===t||!t.call(e,".toolbar-global-fade-container")))}function ia(e){return!!e&&1===e.nodeType}function il(e,t){return!!e&&!!e.tagName&&e.tagName.toLowerCase()===t.toLowerCase()}function ic(e){return!!e&&3===e.nodeType}function iu(e){return!!e&&11===e.nodeType}function id(e){return e?w(e).split(/\s+/):[]}function ih(e){var t=null==r?void 0:r.location.href;return!!(t&&e&&e.some(e=>t.match(e)))}function i_(e){var t="";switch(typeof e.className){case"string":t=e.className;break;case"object":t=(e.className&&"baseVal"in e.className?e.className.baseVal:null)||e.getAttribute("class")||"";break;default:t=""}return id(t)}function ip(e){return A(e)?null:w(e).split(/(\s+)/).filter(e=>iP(e)).join("").replace(/[\r\n]/g," ").replace(/[ ]+/g," ").substring(0,255)}function ig(e){var t="";return iy(e)&&!iw(e)&&e.childNodes&&e.childNodes.length&&X(e.childNodes,function(e){var i;ic(e)&&e.textContent&&(t+=null!==(i=ip(e.textContent))&&void 0!==i?i:"")}),w(t)}function iv(e){var t;return T(e.target)?e.srcElement||null:null!==(t=e.target)&&void 0!==t&&t.shadowRoot?e.composedPath()[0]||null:e.target||null}var im=["a","button","form","input","select","textarea","label"];function ib(e){var t=e.parentNode;return!(!t||!ia(t))&&t}function iy(e){for(var t=e;t.parentNode&&!il(t,"body");t=t.parentNode){var i=i_(t);if(y(i,"ph-sensitive")||y(i,"ph-no-capture"))return!1}if(y(i_(e),"ph-include"))return!0;var s=e.type||"";if($(s))switch(s.toLowerCase()){case"hidden":case"password":return!1}var r=e.name||e.id||"";return!($(r)&&/^cc|cardnum|ccnum|creditcard|csc|cvc|cvv|exp|pass|pwd|routing|seccode|securitycode|securitynum|socialsec|socsec|ssn/i.test(r.replace(/[^a-zA-Z0-9]/g,"")))}function iw(e){return!!(il(e,"input")&&!["button","checkbox","submit","reset"].includes(e.type)||il(e,"select")||il(e,"textarea")||"true"===e.getAttribute("contenteditable"))}var iS="(4[0-9]{12}(?:[0-9]{3})?)|(5[1-5][0-9]{14})|(6(?:011|5[0-9]{2})[0-9]{12})|(3[47][0-9]{13})|(3(?:0[0-5]|[68][0-9])[0-9]{11})|((?:2131|1800|35[0-9]{3})[0-9]{11})",iE=new RegExp("^(?:".concat(iS,")$")),ik=new RegExp(iS),ix="\\d{3}-?\\d{2}-?\\d{4}",iI=new RegExp("^(".concat(ix,")$")),iC=new RegExp("(".concat(ix,")"));function iP(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return!(A(e)||$(e)&&(e=w(e),(t?iE:ik).test((e||"").replace(/[- ]/g,""))||(t?iI:iC).test(e)))}function iF(e){var t=ig(e);return iP(t="".concat(t," ").concat(function e(t){var i="";return t&&t.childNodes&&t.childNodes.length&&X(t.childNodes,function(t){var s;if(t&&"span"===(null===(s=t.tagName)||void 0===s?void 0:s.toLowerCase()))try{var r=ig(t);i="".concat(i," ").concat(r).trim(),t.childNodes&&t.childNodes.length&&(i="".concat(i," ").concat(e(t)).trim())}catch(e){H.error("[AutoCapture]",e)}}),i}(e)).trim())?t:""}function iR(e){return e.replace(/"|\\"/g,'\\"')}var iT="[SessionRecording]",i$="redacted",iO={initiatorTypes:["audio","beacon","body","css","early-hint","embed","fetch","frame","iframe","icon","image","img","input","link","navigation","object","ping","script","track","video","xmlhttprequest"],maskRequestFn:e=>e,recordHeaders:!1,recordBody:!1,recordInitialRequests:!1,recordPerformance:!1,performanceEntryTypeToObserve:["first-input","navigation","paint","resource"],payloadSizeLimitBytes:1e6,payloadHostDenyList:[".lr-ingest.io",".ingest.sentry.io",".clarity.ms","analytics.google.com"]},iL=["authorization","x-forwarded-for","authorization","cookie","set-cookie","x-api-key","x-real-ip","remote-addr","forwarded","proxy-authorization","x-csrf-token","x-csrftoken","x-xsrf-token"],iA=["password","secret","passwd","api_key","apikey","auth","credentials","mysql_pwd","privatekey","private_key","token"],iM=["/s/","/e/","/i/"];function iD(e,t,i,s){if(A(e))return e;var r=(null==t?void 0:t["content-length"])||new Blob([e]).size;return $(r)&&(r=parseInt(r)),r>i?iT+" ".concat(s," body too large to record (").concat(r," bytes)"):e}function iN(e,t){if(A(e))return e;var i=e;return iP(i,!1)||(i=iT+" "+t+" body "+i$),X(iA,e=>{var s,r;null!==(s=i)&&void 0!==s&&s.length&&-1!==(null===(r=i)||void 0===r?void 0:r.indexOf(e))&&(i=iT+" "+t+" body "+i$+" as might contain: "+e)}),i}var iq=(e,t)=>{var i,s,r={payloadSizeLimitBytes:iO.payloadSizeLimitBytes,performanceEntryTypeToObserve:[...iO.performanceEntryTypeToObserve],payloadHostDenyList:[...t.payloadHostDenyList||[],...iO.payloadHostDenyList]},n=!1!==e.session_recording.recordHeaders&&t.recordHeaders,o=!1!==e.session_recording.recordBody&&t.recordBody,a=!1!==e.capture_performance&&t.recordPerformance,l=(s=Math.min(1e6,null!==(i=r.payloadSizeLimitBytes)&&void 0!==i?i:1e6),e=>(null!=e&&e.requestBody&&(e.requestBody=iD(e.requestBody,e.requestHeaders,s,"Request")),null!=e&&e.responseBody&&(e.responseBody=iD(e.responseBody,e.responseHeaders,s,"Response")),e)),c=t=>{var i;return l(((e,t)=>{var i,s=tn(e.name),r=0===t.indexOf("http")?null===(i=tn(t))||void 0===i?void 0:i.pathname:t;"/"===r&&(r="");var n=null==s?void 0:s.pathname.replace(r||"","");if(!(s&&n&&iM.some(e=>0===n.indexOf(e))))return e})((A(i=t.requestHeaders)||X(Object.keys(null!=i?i:{}),e=>{iL.includes(e.toLowerCase())&&(i[e]=i$)}),t),e.api_host))},u=P(e.session_recording.maskNetworkRequestFn);return u&&P(e.session_recording.maskCapturedNetworkRequestFn)&&H.warn("Both `maskNetworkRequestFn` and `maskCapturedNetworkRequestFn` are defined. `maskNetworkRequestFn` will be ignored."),u&&(e.session_recording.maskCapturedNetworkRequestFn=t=>{var i=e.session_recording.maskNetworkRequestFn({url:t.name});return V(V({},t),{},{name:null==i?void 0:i.url})}),r.maskRequestFn=P(e.session_recording.maskCapturedNetworkRequestFn)?t=>{var i,s,r,n=c(t);return n&&null!==(i=null===(s=(r=e.session_recording).maskCapturedNetworkRequestFn)||void 0===s?void 0:s.call(r,n))&&void 0!==i?i:void 0}:e=>(function(e){if(!T(e))return e.requestBody=iN(e.requestBody,"Request"),e.responseBody=iN(e.responseBody,"Response"),e})(c(e)),V(V(V({},iO),r),{},{recordHeaders:n,recordBody:o,recordPerformance:a,recordInitialRequests:a})};function iB(e,t,i,s,r){return t>i&&(H.warn("min cannot be greater than max."),t=i),M(e)?e>i?(s&&H.warn(s+" cannot be  greater than max: "+i+". Using max value instead."),i):e<t?(s&&H.warn(s+" cannot be less than min: "+t+". Using min value instead."),t):e:(s&&H.warn(s+" must be a number. using max or fallback. max: "+i+", fallback: "+r),iB(r||i,t,i,s))}class iH{constructor(e){var t,i,s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};G(this,"bucketSize",100),G(this,"refillRate",10),G(this,"mutationBuckets",{}),G(this,"loggedTracker",{}),G(this,"refillBuckets",()=>{Object.keys(this.mutationBuckets).forEach(e=>{this.mutationBuckets[e]=this.mutationBuckets[e]+this.refillRate,this.mutationBuckets[e]>=this.bucketSize&&delete this.mutationBuckets[e]})}),G(this,"getNodeOrRelevantParent",e=>{var t=this.rrweb.mirror.getNode(e);if("svg"!==(null==t?void 0:t.nodeName)&&t instanceof Element){var i=t.closest("svg");if(i)return[this.rrweb.mirror.getId(i),i]}return[e,t]}),G(this,"numberOfChanges",e=>{var t,i,s,r,n,o,a,l;return(null!==(t=null===(i=e.removes)||void 0===i?void 0:i.length)&&void 0!==t?t:0)+(null!==(s=null===(r=e.attributes)||void 0===r?void 0:r.length)&&void 0!==s?s:0)+(null!==(n=null===(o=e.texts)||void 0===o?void 0:o.length)&&void 0!==n?n:0)+(null!==(a=null===(l=e.adds)||void 0===l?void 0:l.length)&&void 0!==a?a:0)}),G(this,"throttleMutations",e=>{if(3!==e.type||0!==e.data.source)return e;var t=e.data,i=this.numberOfChanges(t);t.attributes&&(t.attributes=t.attributes.filter(e=>{var t,i,s,[r,n]=this.getNodeOrRelevantParent(e.id);return 0!==this.mutationBuckets[r]&&(this.mutationBuckets[r]=null!==(t=this.mutationBuckets[r])&&void 0!==t?t:this.bucketSize,this.mutationBuckets[r]=Math.max(this.mutationBuckets[r]-1,0),0===this.mutationBuckets[r]&&(this.loggedTracker[r]||(this.loggedTracker[r]=!0,null===(i=(s=this.options).onBlockedNode)||void 0===i||i.call(s,r,n))),e)}));var s=this.numberOfChanges(t);return 0!==s||i===s?e:void 0}),this.rrweb=e,this.options=s,this.refillRate=iB(null!==(t=this.options.refillRate)&&void 0!==t?t:this.refillRate,0,100,"mutation throttling refill rate"),this.bucketSize=iB(null!==(i=this.options.bucketSize)&&void 0!==i?i:this.bucketSize,0,100,"mutation throttling bucket size"),setInterval(()=>{this.refillBuckets()},1e3)}}var iU=Uint8Array,iz=Uint16Array,ij=Uint32Array,iW=new iU([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),iV=new iU([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),iG=new iU([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),iJ=function(e,t){for(var i=new iz(31),s=0;s<31;++s)i[s]=t+=1<<e[s-1];var r=new ij(i[30]);for(s=1;s<30;++s)for(var n=i[s];n<i[s+1];++n)r[n]=n-i[s]<<5|s;return[i,r]},iY=iJ(iW,2),iK=iY[0],iX=iY[1];iK[28]=258,iX[258]=28;for(var iQ=iJ(iV,0)[1],iZ=new iz(32768),i0=0;i0<32768;++i0){var i1=(43690&i0)>>>1|(21845&i0)<<1;i1=(61680&(i1=(52428&i1)>>>2|(13107&i1)<<2))>>>4|(3855&i1)<<4,iZ[i0]=((65280&i1)>>>8|(255&i1)<<8)>>>1}var i2=function(e,t,i){for(var s=e.length,r=0,n=new iz(t);r<s;++r)++n[e[r]-1];var o,a=new iz(t);for(r=0;r<t;++r)a[r]=a[r-1]+n[r-1]<<1;if(i){o=new iz(1<<t);var l=15-t;for(r=0;r<s;++r)if(e[r])for(var c=r<<4|e[r],u=t-e[r],d=a[e[r]-1]++<<u,h=d|(1<<u)-1;d<=h;++d)o[iZ[d]>>>l]=c}else for(o=new iz(s),r=0;r<s;++r)o[r]=iZ[a[e[r]-1]++]>>>15-e[r];return o},i3=new iU(288);for(i0=0;i0<144;++i0)i3[i0]=8;for(i0=144;i0<256;++i0)i3[i0]=9;for(i0=256;i0<280;++i0)i3[i0]=7;for(i0=280;i0<288;++i0)i3[i0]=8;var i5=new iU(32);for(i0=0;i0<32;++i0)i5[i0]=5;var i6=i2(i3,9,0),i8=i2(i5,5,0),i4=function(e){return(e/8>>0)+(7&e&&1)},i7=function(e,t,i){(null==i||i>e.length)&&(i=e.length);var s=new(e instanceof iz?iz:e instanceof ij?ij:iU)(i-t);return s.set(e.subarray(t,i)),s},i9=function(e,t,i){i<<=7&t;var s=t/8>>0;e[s]|=i,e[s+1]|=i>>>8},se=function(e,t,i){i<<=7&t;var s=t/8>>0;e[s]|=i,e[s+1]|=i>>>8,e[s+2]|=i>>>16},st=function(e,t){for(var i=[],s=0;s<e.length;++s)e[s]&&i.push({s:s,f:e[s]});var r=i.length,n=i.slice();if(!r)return[new iU(0),0];if(1==r){var o=new iU(i[0].s+1);return o[i[0].s]=1,[o,1]}i.sort(function(e,t){return e.f-t.f}),i.push({s:-1,f:25001});var a=i[0],l=i[1],c=0,u=1,d=2;for(i[0]={s:-1,f:a.f+l.f,l:a,r:l};u!=r-1;)a=i[i[c].f<i[d].f?c++:d++],l=i[c!=u&&i[c].f<i[d].f?c++:d++],i[u++]={s:-1,f:a.f+l.f,l:a,r:l};var h=n[0].s;for(s=1;s<r;++s)n[s].s>h&&(h=n[s].s);var _=new iz(h+1),p=si(i[u-1],_,0);if(p>t){s=0;var g=0,v=p-t,f=1<<v;for(n.sort(function(e,t){return _[t.s]-_[e.s]||e.f-t.f});s<r;++s){var m=n[s].s;if(!(_[m]>t))break;g+=f-(1<<p-_[m]),_[m]=t}for(g>>>=v;g>0;){var b=n[s].s;_[b]<t?g-=1<<t-_[b]++-1:++s}for(;s>=0&&g;--s){var y=n[s].s;_[y]==t&&(--_[y],++g)}p=t}return[new iU(_),p]},si=function(e,t,i){return -1==e.s?Math.max(si(e.l,t,i+1),si(e.r,t,i+1)):t[e.s]=i},ss=function(e){for(var t=e.length;t&&!e[--t];);for(var i=new iz(++t),s=0,r=e[0],n=1,o=function(e){i[s++]=e},a=1;a<=t;++a)if(e[a]==r&&a!=t)++n;else{if(!r&&n>2){for(;n>138;n-=138)o(32754);n>2&&(o(n>10?n-11<<5|28690:n-3<<5|12305),n=0)}else if(n>3){for(o(r),--n;n>6;n-=6)o(8304);n>2&&(o(n-3<<5|8208),n=0)}for(;n--;)o(r);n=1,r=e[a]}return[i.subarray(0,s),t]},sr=function(e,t){for(var i=0,s=0;s<t.length;++s)i+=e[s]*t[s];return i},sn=function(e,t,i){var s=i.length,r=i4(t+2);e[r]=255&s,e[r+1]=s>>>8,e[r+2]=255^e[r],e[r+3]=255^e[r+1];for(var n=0;n<s;++n)e[r+n+4]=i[n];return 8*(r+4+s)},so=function(e,t,i,s,r,n,o,a,l,c,u){i9(t,u++,i),++r[256];for(var d=st(r,15),h=d[0],_=d[1],p=st(n,15),g=p[0],v=p[1],f=ss(h),m=f[0],b=f[1],y=ss(g),w=y[0],S=y[1],E=new iz(19),k=0;k<m.length;++k)E[31&m[k]]++;for(k=0;k<w.length;++k)E[31&w[k]]++;for(var x=st(E,7),I=x[0],C=x[1],P=19;P>4&&!I[iG[P-1]];--P);var F,R,T,$,O=c+5<<3,L=sr(r,i3)+sr(n,i5)+o,A=sr(r,h)+sr(n,g)+o+14+3*P+sr(E,I)+(2*E[16]+3*E[17]+7*E[18]);if(O<=L&&O<=A)return sn(t,u,e.subarray(l,l+c));if(i9(t,u,1+(A<L)),u+=2,A<L){F=i2(h,_,0),R=h,T=i2(g,v,0),$=g;var M=i2(I,C,0);for(i9(t,u,b-257),i9(t,u+5,S-1),i9(t,u+10,P-4),u+=14,k=0;k<P;++k)i9(t,u+3*k,I[iG[k]]);u+=3*P;for(var D=[m,w],N=0;N<2;++N){var q=D[N];for(k=0;k<q.length;++k){var B=31&q[k];i9(t,u,M[B]),u+=I[B],B>15&&(i9(t,u,q[k]>>>5&127),u+=q[k]>>>12)}}}else F=i6,R=i3,T=i8,$=i5;for(k=0;k<a;++k)if(s[k]>255){se(t,u,F[(B=s[k]>>>18&31)+257]),u+=R[B+257],B>7&&(i9(t,u,s[k]>>>23&31),u+=iW[B]);var H=31&s[k];se(t,u,T[H]),u+=$[H],H>3&&(se(t,u,s[k]>>>5&8191),u+=iV[H])}else se(t,u,F[s[k]]),u+=R[s[k]];return se(t,u,F[256]),u+R[256]},sa=new ij([65540,131080,131088,131104,262176,1048704,1048832,2114560,2117632]),sl=function(){for(var e=new ij(256),t=0;t<256;++t){for(var i=t,s=9;--s;)i=(1&i&&0xedb88320)^i>>>1;e[t]=i}return e}(),sc=function(){var e=0xffffffff;return{p:function(t){for(var i=e,s=0;s<t.length;++s)i=sl[255&i^t[s]]^i>>>8;e=i},d:function(){return 0xffffffff^e}}},su=function(e,t,i){for(;i;++t)e[t]=i,i>>>=8},sd=function(e,t){var i=t.filename;if(e[0]=31,e[1]=139,e[2]=8,e[8]=t.level<2?4:2*(9==t.level),e[9]=3,0!=t.mtime&&su(e,4,Math.floor(new Date(t.mtime||Date.now())/1e3)),i){e[3]=8;for(var s=0;s<=i.length;++s)e[s+10]=i.charCodeAt(s)}};function sh(e,t){void 0===t&&(t={});var i,s,r,n=sc(),o=e.length;n.p(e);var a=(s=t,r=10+((i=t).filename&&i.filename.length+1||0),function(e,t,i,s,r,n){var o=e.length,a=new iU(s+o+5*(1+Math.floor(o/7e3))+8),l=a.subarray(s,a.length-r),c=0;if(!t||o<8)for(var u=0;u<=o;u+=65535){var d=u+65535;d<o?c=sn(l,c,e.subarray(u,d)):(l[u]=n,c=sn(l,c,e.subarray(u,o)))}else{for(var h=sa[t-1],_=h>>>13,p=8191&h,g=(1<<i)-1,v=new iz(32768),f=new iz(g+1),m=Math.ceil(i/3),b=2*m,y=function(t){return(e[t]^e[t+1]<<m^e[t+2]<<b)&g},w=new ij(25e3),S=new iz(288),E=new iz(32),k=0,x=0,I=(u=0,0),C=0,P=0;u<o;++u){var F=y(u),R=32767&u,T=f[F];if(v[R]=T,f[F]=R,C<=u){var $=o-u;if((k>7e3||I>24576)&&$>423){c=so(e,l,0,w,S,E,x,I,P,u-P,c),I=k=x=0,P=u;for(var O=0;O<286;++O)S[O]=0;for(O=0;O<30;++O)E[O]=0}var L=2,A=0,M=p,D=R-T&32767;if($>2&&F==y(u-D))for(var N=Math.min(_,$)-1,q=Math.min(32767,u),B=Math.min(258,$);D<=q&&--M&&R!=T;){if(e[u+L]==e[u+L-D]){for(var H=0;H<B&&e[u+H]==e[u+H-D];++H);if(H>L){if(L=H,A=D,H>N)break;var U=Math.min(D,H-2),z=0;for(O=0;O<U;++O){var j=u-D+O+32768&32767,W=j-v[j]+32768&32767;W>z&&(z=W,T=j)}}}D+=(R=T)-(T=v[R])+32768&32767}if(A){w[I++]=0x10000000|iX[L]<<18|iQ[A];var V=31&iX[L],G=31&iQ[A];x+=iW[V]+iV[G],++S[257+V],++E[G],C=u+L,++k}else w[I++]=e[u],++S[e[u]]}}c=so(e,l,n,w,S,E,x,I,P,u-P,c)}return i7(a,0,s+i4(c)+r)}(e,null==s.level?6:s.level,null==s.mem?Math.ceil(1.5*Math.max(8,Math.min(13,Math.log(e.length)))):12+s.mem,r,8,!0)),l=a.length;return sd(a,t),su(a,l-8,n.d()),su(a,l-4,o),a}function s_(e,t){var i=e.length;if("undefined"!=typeof TextEncoder)return(new TextEncoder).encode(e);for(var s=new iU(e.length+(e.length>>>1)),r=0,n=function(e){s[r++]=e},o=0;o<i;++o){if(r+5>s.length){var a=new iU(r+8+(i-o<<1));a.set(s),s=a}var l=e.charCodeAt(o);l<128||t?n(l):(l<2048?n(192|l>>>6):(l>55295&&l<57344?(n(240|(l=65536+(1047552&l)|1023&e.charCodeAt(++o))>>>18),n(128|l>>>12&63)):n(224|l>>>12),n(128|l>>>6&63)),n(128|63&l))}return i7(s,0,r)}var sp="[SessionRecording]",sg=U(sp),sv=[ir.MouseMove,ir.MouseInteraction,ir.Scroll,ir.ViewportResize,ir.Input,ir.TouchMove,ir.MediaInteraction,ir.Drag],sf=e=>({rrwebMethod:e,enqueuedAt:Date.now(),attempt:1});function sm(e){return function(e,t){for(var i="",s=0;s<e.length;){var r=e[s++];r<128||t?i+=String.fromCharCode(r):r<224?i+=String.fromCharCode((31&r)<<6|63&e[s++]):r<240?i+=String.fromCharCode((15&r)<<12|(63&e[s++])<<6|63&e[s++]):i+=String.fromCharCode(55296|(r=((15&r)<<18|(63&e[s++])<<12|(63&e[s++])<<6|63&e[s++])-65536)>>10,56320|1023&r)}return i}(sh(s_(JSON.stringify(e))),!0)}function sb(e){return e.type===is.Custom&&"sessionIdle"===e.data.tag}function sy(e,t){return t.some(t=>"regex"===t.matching&&new RegExp(t.url).test(e))}class sw{get sessionIdleThresholdMilliseconds(){return this.instance.config.session_recording.session_idle_threshold_ms||3e5}get rrwebRecord(){var e,t;return null==v||null===(e=v.__PosthogExtensions__)||void 0===e||null===(t=e.rrweb)||void 0===t?void 0:t.record}get started(){return this._captureStarted}get sessionManager(){if(!this.instance.sessionManager)throw Error(sp+" must be started with a valid sessionManager.");return this.instance.sessionManager}get fullSnapshotIntervalMillis(){var e,t;return"trigger_pending"===this.triggerStatus?6e4:null!==(e=null===(t=this.instance.config.session_recording)||void 0===t?void 0:t.full_snapshot_interval_millis)&&void 0!==e?e:3e5}get isSampled(){var e=this.instance.get_property(eE);return D(e)?e:null}get sessionDuration(){var e,t,i=null===(e=this.buffer)||void 0===e?void 0:e.data[(null===(t=this.buffer)||void 0===t?void 0:t.data.length)-1],{sessionStartTimestamp:s}=this.sessionManager.checkAndGetSessionAndWindowId(!0);return i?i.timestamp-s:null}get isRecordingEnabled(){var e=!!this.instance.get_property(eg),t=!this.instance.config.disable_session_recording;return r&&e&&t}get isConsoleLogCaptureEnabled(){var e=!!this.instance.get_property(ev),t=this.instance.config.enable_recording_console_log;return null!=t?t:e}get canvasRecording(){var e,t,i,s,r,n,o=this.instance.config.session_recording.captureCanvas,a=this.instance.get_property(em),l=null!==(e=null!==(t=null==o?void 0:o.recordCanvas)&&void 0!==t?t:null==a?void 0:a.enabled)&&void 0!==e&&e,c=null!==(i=null!==(s=null==o?void 0:o.canvasFps)&&void 0!==s?s:null==a?void 0:a.fps)&&void 0!==i?i:0,u=null!==(r=null!==(n=null==o?void 0:o.canvasQuality)&&void 0!==n?n:null==a?void 0:a.quality)&&void 0!==r?r:0;return{enabled:l,fps:iB(c,0,12,"canvas recording fps"),quality:iB(u,0,1,"canvas recording quality")}}get networkPayloadCapture(){var e,t,i=this.instance.get_property(ef),s={recordHeaders:null===(e=this.instance.config.session_recording)||void 0===e?void 0:e.recordHeaders,recordBody:null===(t=this.instance.config.session_recording)||void 0===t?void 0:t.recordBody},r=(null==s?void 0:s.recordHeaders)||(null==i?void 0:i.recordHeaders),n=(null==s?void 0:s.recordBody)||(null==i?void 0:i.recordBody),o=F(this.instance.config.capture_performance)?this.instance.config.capture_performance.network_timing:this.instance.config.capture_performance,a=!!(D(o)?o:null==i?void 0:i.capturePerformance);return r||n||a?{recordHeaders:r,recordBody:n,recordPerformance:a}:void 0}get sampleRate(){var e=this.instance.get_property(eb);return M(e)?e:null}get minimumDuration(){var e=this.instance.get_property(ey);return M(e)?e:null}get status(){return this.receivedDecide?this.isRecordingEnabled?this._urlBlocked?"paused":A(this._linkedFlag)||this._linkedFlagSeen?"trigger_pending"===this.triggerStatus?"buffering":D(this.isSampled)?this.isSampled?"sampled":"disabled":"active":"buffering":"disabled":"buffering"}get urlTriggerStatus(){var e;return 0===this._urlTriggers.length?"trigger_disabled":(null===(e=this.instance)||void 0===e?void 0:e.get_property(ek))===this.sessionId?"trigger_activated":"trigger_pending"}get eventTriggerStatus(){var e;return 0===this._eventTriggers.length?"trigger_disabled":(null===(e=this.instance)||void 0===e?void 0:e.get_property(ex))===this.sessionId?"trigger_activated":"trigger_pending"}get triggerStatus(){var e="trigger_activated"===this.eventTriggerStatus||"trigger_activated"===this.urlTriggerStatus,t="trigger_pending"===this.eventTriggerStatus||"trigger_pending"===this.urlTriggerStatus;return e?"trigger_activated":t?"trigger_pending":"trigger_disabled"}constructor(e){if(G(this,"queuedRRWebEvents",[]),G(this,"isIdle",!1),G(this,"_linkedFlagSeen",!1),G(this,"_lastActivityTimestamp",Date.now()),G(this,"_linkedFlag",null),G(this,"_removePageViewCaptureHook",void 0),G(this,"_onSessionIdListener",void 0),G(this,"_persistDecideOnSessionListener",void 0),G(this,"_samplingSessionListener",void 0),G(this,"_urlTriggers",[]),G(this,"_urlBlocklist",[]),G(this,"_urlBlocked",!1),G(this,"_eventTriggers",[]),G(this,"_removeEventTriggerCaptureHook",void 0),G(this,"_forceAllowLocalhostNetworkCapture",!1),G(this,"_onBeforeUnload",()=>{this._flushBuffer()}),G(this,"_onOffline",()=>{this._tryAddCustomEvent("browser offline",{})}),G(this,"_onOnline",()=>{this._tryAddCustomEvent("browser online",{})}),G(this,"_onVisibilityChange",()=>{if(null!=u&&u.visibilityState){var e="window "+u.visibilityState;this._tryAddCustomEvent(e,{})}}),this.instance=e,this._captureStarted=!1,this._endpoint="/s/",this.stopRrweb=void 0,this.receivedDecide=!1,!this.instance.sessionManager)throw sg.error("started without valid sessionManager"),Error(sp+" started without valid sessionManager. This is a bug.");if(this.instance.config.__preview_experimental_cookieless_mode)throw Error(sp+" cannot be used with __preview_experimental_cookieless_mode.");var{sessionId:t,windowId:i}=this.sessionManager.checkAndGetSessionAndWindowId();this.sessionId=t,this.windowId=i,this.buffer=this.clearBuffer(),this.sessionIdleThresholdMilliseconds>=this.sessionManager.sessionTimeoutMs&&sg.warn("session_idle_threshold_ms (".concat(this.sessionIdleThresholdMilliseconds,") is greater than the session timeout (").concat(this.sessionManager.sessionTimeoutMs,"). Session will never be detected as idle"))}startIfEnabledOrStop(e){this.isRecordingEnabled?(this._startCapture(e),null==r||r.addEventListener("beforeunload",this._onBeforeUnload),null==r||r.addEventListener("offline",this._onOffline),null==r||r.addEventListener("online",this._onOnline),null==r||r.addEventListener("visibilitychange",this._onVisibilityChange),this._setupSampling(),this._addEventTriggerListener(),A(this._removePageViewCaptureHook)&&(this._removePageViewCaptureHook=this.instance.on("eventCaptured",e=>{try{if("$pageview"===e.event){var t=null!=e&&e.properties.$current_url?this._maskUrl(null==e?void 0:e.properties.$current_url):"";if(!t)return;this._tryAddCustomEvent("$pageview",{href:t})}}catch(e){sg.error("Could not add $pageview to rrweb session",e)}})),this._onSessionIdListener||(this._onSessionIdListener=this.sessionManager.onSessionId((e,t,i)=>{var s,r,n,o;i&&(this._tryAddCustomEvent("$session_id_change",{sessionId:e,windowId:t,changeReason:i}),null===(s=this.instance)||void 0===s||null===(r=s.persistence)||void 0===r||r.unregister(ex),null===(n=this.instance)||void 0===n||null===(o=n.persistence)||void 0===o||o.unregister(ek))}))):this.stopRecording()}stopRecording(){var e,t,i,s;this._captureStarted&&this.stopRrweb&&(this.stopRrweb(),this.stopRrweb=void 0,this._captureStarted=!1,null==r||r.removeEventListener("beforeunload",this._onBeforeUnload),null==r||r.removeEventListener("offline",this._onOffline),null==r||r.removeEventListener("online",this._onOnline),null==r||r.removeEventListener("visibilitychange",this._onVisibilityChange),this.clearBuffer(),clearInterval(this._fullSnapshotTimer),null===(e=this._removePageViewCaptureHook)||void 0===e||e.call(this),this._removePageViewCaptureHook=void 0,null===(t=this._removeEventTriggerCaptureHook)||void 0===t||t.call(this),this._removeEventTriggerCaptureHook=void 0,null===(i=this._onSessionIdListener)||void 0===i||i.call(this),this._onSessionIdListener=void 0,null===(s=this._samplingSessionListener)||void 0===s||s.call(this),this._samplingSessionListener=void 0,sg.info("stopped"))}makeSamplingDecision(e){var t,i,s=this.sessionId!==e,r=this.sampleRate;if(M(r)){var n,o=this.isSampled,a=s||!D(o);n=a?Math.random()<r:o,a&&(n?this._reportStarted("sampled"):sg.warn("Sample rate (".concat(r,") has determined that this sessionId (").concat(e,") will not be sent to the server.")),this._tryAddCustomEvent("samplingDecisionMade",{sampleRate:r,isSampled:n})),null===(i=this.instance.persistence)||void 0===i||i.register({[eE]:n})}else null===(t=this.instance.persistence)||void 0===t||t.register({[eE]:null})}onRemoteConfig(e){var t,i,s,r,n,o;if(this._tryAddCustomEvent("$remote_config_received",e),this._persistRemoteConfig(e),this._linkedFlag=(null===(t=e.sessionRecording)||void 0===t?void 0:t.linkedFlag)||null,null!==(i=e.sessionRecording)&&void 0!==i&&i.endpoint&&(this._endpoint=null===(o=e.sessionRecording)||void 0===o?void 0:o.endpoint),this._setupSampling(),!A(this._linkedFlag)&&!this._linkedFlagSeen){var a=$(this._linkedFlag)?this._linkedFlag:this._linkedFlag.flag,l=$(this._linkedFlag)?null:this._linkedFlag.variant;this.instance.onFeatureFlags((e,t)=>{var i=F(t)&&a in t,s=l?t[a]===l:i;s&&this._reportStarted("linked_flag_matched",{linkedFlag:a,linkedVariant:l}),this._linkedFlagSeen=s})}null!==(s=e.sessionRecording)&&void 0!==s&&s.urlTriggers&&(this._urlTriggers=e.sessionRecording.urlTriggers),null!==(r=e.sessionRecording)&&void 0!==r&&r.urlBlocklist&&(this._urlBlocklist=e.sessionRecording.urlBlocklist),null!==(n=e.sessionRecording)&&void 0!==n&&n.eventTriggers&&(this._eventTriggers=e.sessionRecording.eventTriggers),this.receivedDecide=!0,this.startIfEnabledOrStop()}_setupSampling(){M(this.sampleRate)&&A(this._samplingSessionListener)&&(this._samplingSessionListener=this.sessionManager.onSessionId(e=>{this.makeSamplingDecision(e)}))}_persistRemoteConfig(e){if(this.instance.persistence){var t,i=this.instance.persistence,s=()=>{var t,s,r,n,o,a,l,c,u=null===(t=e.sessionRecording)||void 0===t?void 0:t.sampleRate,d=A(u)?null:parseFloat(u),h=null===(s=e.sessionRecording)||void 0===s?void 0:s.minimumDurationMilliseconds;i.register({[eg]:!!e.sessionRecording,[ev]:null===(r=e.sessionRecording)||void 0===r?void 0:r.consoleLogRecordingEnabled,[ef]:V({capturePerformance:e.capturePerformance},null===(n=e.sessionRecording)||void 0===n?void 0:n.networkPayloadCapture),[em]:{enabled:null===(o=e.sessionRecording)||void 0===o?void 0:o.recordCanvas,fps:null===(a=e.sessionRecording)||void 0===a?void 0:a.canvasFps,quality:null===(l=e.sessionRecording)||void 0===l?void 0:l.canvasQuality},[eb]:d,[ey]:T(h)?null:h,[ew]:null===(c=e.sessionRecording)||void 0===c?void 0:c.scriptConfig})};s(),null===(t=this._persistDecideOnSessionListener)||void 0===t||t.call(this),this._persistDecideOnSessionListener=this.sessionManager.onSessionId(s)}}log(e){var t,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"log";null===(t=this.instance.sessionRecording)||void 0===t||t.onRRwebEmit({type:6,data:{plugin:"rrweb/console@1",payload:{level:i,trace:[],payload:[JSON.stringify(e)]}},timestamp:Date.now()})}_startCapture(e){if(!T(Object.assign)&&!T(Array.from)&&!(this._captureStarted||this.instance.config.disable_session_recording||this.instance.consent.isOptedOut())){var t,i;(this._captureStarted=!0,this.sessionManager.checkAndGetSessionAndWindowId(),this.rrwebRecord)?this._onScriptLoaded():null===(t=v.__PosthogExtensions__)||void 0===t||null===(i=t.loadExternalDependency)||void 0===i||i.call(t,this.instance,this.scriptName,e=>{if(e)return sg.error("could not load recorder",e);this._onScriptLoaded()}),sg.info("starting"),"active"===this.status&&this._reportStarted(e||"recording_initialized")}}get scriptName(){var e,t,i;return(null===(e=this.instance)||void 0===e||null===(t=e.persistence)||void 0===t||null===(i=t.get_property(ew))||void 0===i?void 0:i.script)||"recorder"}isInteractiveEvent(e){var t;return 3===e.type&&-1!==sv.indexOf(null===(t=e.data)||void 0===t?void 0:t.source)}_updateWindowAndSessionIds(e){var t=this.isInteractiveEvent(e);t||this.isIdle||e.timestamp-this._lastActivityTimestamp>this.sessionIdleThresholdMilliseconds&&(this.isIdle=!0,clearInterval(this._fullSnapshotTimer),this._tryAddCustomEvent("sessionIdle",{eventTimestamp:e.timestamp,lastActivityTimestamp:this._lastActivityTimestamp,threshold:this.sessionIdleThresholdMilliseconds,bufferLength:this.buffer.data.length,bufferSize:this.buffer.size}),this._flushBuffer());var i=!1;if(t&&(this._lastActivityTimestamp=e.timestamp,this.isIdle&&(this.isIdle=!1,this._tryAddCustomEvent("sessionNoLongerIdle",{reason:"user activity",type:e.type}),i=!0)),!this.isIdle){var{windowId:s,sessionId:r}=this.sessionManager.checkAndGetSessionAndWindowId(!t,e.timestamp),n=this.sessionId!==r,o=this.windowId!==s;this.windowId=s,this.sessionId=r,n||o?(this.stopRecording(),this.startIfEnabledOrStop("session_id_changed")):i&&this._scheduleFullSnapshot()}}_tryRRWebMethod(e){try{return e.rrwebMethod(),!0}catch(t){return this.queuedRRWebEvents.length<10?this.queuedRRWebEvents.push({enqueuedAt:e.enqueuedAt||Date.now(),attempt:e.attempt++,rrwebMethod:e.rrwebMethod}):sg.warn("could not emit queued rrweb event.",t,e),!1}}_tryAddCustomEvent(e,t){return this._tryRRWebMethod(sf(()=>this.rrwebRecord.addCustomEvent(e,t)))}_tryTakeFullSnapshot(){return this._tryRRWebMethod(sf(()=>this.rrwebRecord.takeFullSnapshot()))}_onScriptLoaded(){var e,t={blockClass:"ph-no-capture",blockSelector:void 0,ignoreClass:"ph-ignore-input",maskTextClass:"ph-mask",maskTextSelector:void 0,maskTextFn:void 0,maskAllInputs:!0,maskInputOptions:{password:!0},maskInputFn:void 0,slimDOMOptions:{},collectFonts:!1,inlineStylesheet:!0,recordCrossOriginIframes:!1};for(var[i,s]of Object.entries(this.instance.config.session_recording||{}))i in t&&("maskInputOptions"===i?t.maskInputOptions=V({password:!0},s):t[i]=s);if(this.canvasRecording&&this.canvasRecording.enabled&&(t.recordCanvas=!0,t.sampling={canvas:this.canvasRecording.fps},t.dataURLOptions={type:"image/webp",quality:this.canvasRecording.quality}),this.rrwebRecord){this.mutationRateLimiter=null!==(e=this.mutationRateLimiter)&&void 0!==e?e:new iH(this.rrwebRecord,{refillRate:this.instance.config.session_recording.__mutationRateLimiterRefillRate,bucketSize:this.instance.config.session_recording.__mutationRateLimiterBucketSize,onBlockedNode:(e,t)=>{var i="Too many mutations on node '".concat(e,"'. Rate limiting. This could be due to SVG animations or something similar");sg.info(i,{node:t}),this.log(sp+" "+i,"warn")}});var r=this._gatherRRWebPlugins();this.stopRrweb=this.rrwebRecord(V({emit:e=>{this.onRRwebEmit(e)},plugins:r},t)),this._lastActivityTimestamp=Date.now(),this.isIdle=!1,this._tryAddCustomEvent("$session_options",{sessionRecordingOptions:t,activePlugins:r.map(e=>null==e?void 0:e.name)}),this._tryAddCustomEvent("$posthog_config",{config:this.instance.config})}else sg.error("onScriptLoaded was called but rrwebRecord is not available. This indicates something has gone wrong.")}_scheduleFullSnapshot(){if(this._fullSnapshotTimer&&clearInterval(this._fullSnapshotTimer),!this.isIdle){var e=this.fullSnapshotIntervalMillis;e&&(this._fullSnapshotTimer=setInterval(()=>{this._tryTakeFullSnapshot()},e))}}_gatherRRWebPlugins(){var e,t,i,s,r=[],n=null===(e=v.__PosthogExtensions__)||void 0===e||null===(t=e.rrwebPlugins)||void 0===t?void 0:t.getRecordConsolePlugin;n&&this.isConsoleLogCaptureEnabled&&r.push(n());var o=null===(i=v.__PosthogExtensions__)||void 0===i||null===(s=i.rrwebPlugins)||void 0===s?void 0:s.getRecordNetworkPlugin;return this.networkPayloadCapture&&P(o)&&(!tr.includes(location.hostname)||this._forceAllowLocalhostNetworkCapture?r.push(o(iq(this.instance.config,this.networkPayloadCapture))):sg.info("NetworkCapture not started because we are on localhost.")),r}onRRwebEmit(e){var t;if(this._processQueuedEvents(),e&&F(e)){if(e.type===is.Meta){var i=this._maskUrl(e.data.href);if(this._lastHref=i,!i)return;e.data.href=i}else this._pageViewFallBack();if(this._checkUrlTriggerConditions(),"paused"!==this.status||e.type===is.Custom&&"recording paused"===e.data.tag){e.type===is.FullSnapshot&&this._scheduleFullSnapshot(),e.type===is.FullSnapshot&&"trigger_pending"===this.triggerStatus&&this.clearBuffer();var s=this.mutationRateLimiter?this.mutationRateLimiter.throttleMutations(e):e;if(s){var r=function(e){if(e&&F(e)&&6===e.type&&F(e.data)&&"rrweb/console@1"===e.data.plugin){e.data.payload.payload.length>10&&(e.data.payload.payload=e.data.payload.payload.slice(0,10),e.data.payload.payload.push("...[truncated]"));for(var t=[],i=0;i<e.data.payload.payload.length;i++)e.data.payload.payload[i]&&e.data.payload.payload[i].length>2e3?t.push(e.data.payload.payload[i].slice(0,2e3)+"...[truncated]"):t.push(e.data.payload.payload[i]);return e.data.payload.payload=t,e}return e}(s);if(this._updateWindowAndSessionIds(r),!this.isIdle||sb(r)){if(sb(r)){var n=r.data.payload;n&&(r.timestamp=n.lastActivityTimestamp+n.threshold)}var o=null===(t=this.instance.config.session_recording.compress_events)||void 0===t||t?function(e){if(1024>ii(e))return e;try{if(e.type===is.FullSnapshot)return V(V({},e),{},{data:sm(e.data),cv:"2024-10"});if(e.type===is.IncrementalSnapshot&&e.data.source===ir.Mutation)return V(V({},e),{},{cv:"2024-10",data:V(V({},e.data),{},{texts:sm(e.data.texts),attributes:sm(e.data.attributes),removes:sm(e.data.removes),adds:sm(e.data.adds)})});if(e.type===is.IncrementalSnapshot&&e.data.source===ir.StyleSheetRule)return V(V({},e),{},{cv:"2024-10",data:V(V({},e.data),{},{adds:sm(e.data.adds),removes:sm(e.data.removes)})})}catch(e){sg.error("could not compress event - will use uncompressed event",e)}return e}(r):r,a={$snapshot_bytes:ii(o),$snapshot_data:o,$session_id:this.sessionId,$window_id:this.windowId};"disabled"!==this.status?this._captureSnapshotBuffered(a):this.clearBuffer()}}}}}_pageViewFallBack(){if(!this.instance.config.capture_pageview&&r){var e=this._maskUrl(r.location.href);this._lastHref!==e&&(this._tryAddCustomEvent("$url_changed",{href:e}),this._lastHref=e)}}_processQueuedEvents(){if(this.queuedRRWebEvents.length){var e=[...this.queuedRRWebEvents];this.queuedRRWebEvents=[],e.forEach(e=>{Date.now()-e.enqueuedAt<=2e3&&this._tryRRWebMethod(e)})}}_maskUrl(e){var t=this.instance.config.session_recording;if(t.maskNetworkRequestFn){var i,s={url:e};return null===(i=s=t.maskNetworkRequestFn(s))||void 0===i?void 0:i.url}return e}clearBuffer(){return this.buffer={size:0,data:[],sessionId:this.sessionId,windowId:this.windowId},this.buffer}_flushBuffer(){this.flushBufferTimer&&(clearTimeout(this.flushBufferTimer),this.flushBufferTimer=void 0);var e=this.minimumDuration,t=this.sessionDuration,i=M(t)&&t>=0,s=M(e)&&i&&t<e;return"buffering"===this.status||"paused"===this.status||s?(this.flushBufferTimer=setTimeout(()=>{this._flushBuffer()},2e3),this.buffer):(this.buffer.data.length>0&&(function e(t){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6606028.8;if(t.size>=i&&t.data.length>1){var s=Math.floor(t.data.length/2),r=t.data.slice(0,s),n=t.data.slice(s);return[e({size:ii(r),data:r,sessionId:t.sessionId,windowId:t.windowId}),e({size:ii(n),data:n,sessionId:t.sessionId,windowId:t.windowId})].flatMap(e=>e)}return[t]})(this.buffer).forEach(e=>{this._captureSnapshot({$snapshot_bytes:e.size,$snapshot_data:e.data,$session_id:e.sessionId,$window_id:e.windowId,$lib:"web",$lib_version:f.LIB_VERSION})}),this.clearBuffer())}_captureSnapshotBuffered(e){var t,i=2+((null===(t=this.buffer)||void 0===t?void 0:t.data.length)||0);!this.isIdle&&(this.buffer.size+e.$snapshot_bytes+i>943718.4||this.buffer.sessionId!==this.sessionId)&&(this.buffer=this._flushBuffer()),this.buffer.size+=e.$snapshot_bytes,this.buffer.data.push(e.$snapshot_data),this.flushBufferTimer||this.isIdle||(this.flushBufferTimer=setTimeout(()=>{this._flushBuffer()},2e3))}_captureSnapshot(e){this.instance.capture("$snapshot",e,{_url:this.instance.requestRouter.endpointFor("api",this._endpoint),_noTruncate:!0,_batchKey:"recordings",skip_client_rate_limiting:!0})}_checkUrlTriggerConditions(){if(void 0!==r&&r.location.href){var e=r.location.href,t="paused"===this.status,i=sy(e,this._urlBlocklist);i&&!t?this._pauseRecording():!i&&t&&this._resumeRecording(),sy(e,this._urlTriggers)&&this._activateTrigger("url")}}_activateTrigger(e){var t,i;"trigger_pending"===this.triggerStatus&&(null===(t=this.instance)||void 0===t||null===(i=t.persistence)||void 0===i||i.register({["url"===e?ek:ex]:this.sessionId}),this._flushBuffer(),this._reportStarted(e+"_trigger_matched"))}_pauseRecording(){"paused"!==this.status&&(this._urlBlocked=!0,clearInterval(this._fullSnapshotTimer),sg.info("recording paused due to URL blocker"),this._tryAddCustomEvent("recording paused",{reason:"url blocker"}))}_resumeRecording(){"paused"===this.status&&(this._urlBlocked=!1,this._tryTakeFullSnapshot(),this._scheduleFullSnapshot(),this._tryAddCustomEvent("recording resumed",{reason:"left blocked url"}),sg.info("recording resumed"))}_addEventTriggerListener(){0!==this._eventTriggers.length&&A(this._removeEventTriggerCaptureHook)&&(this._removeEventTriggerCaptureHook=this.instance.on("eventCaptured",e=>{try{this._eventTriggers.includes(e.event)&&this._activateTrigger("event")}catch(e){sg.error("Could not activate event trigger",e)}}))}overrideLinkedFlag(){this._linkedFlagSeen=!0,this._tryTakeFullSnapshot(),this._reportStarted("linked_flag_overridden")}overrideSampling(){var e;null===(e=this.instance.persistence)||void 0===e||e.register({[eE]:!0}),this._tryTakeFullSnapshot(),this._reportStarted("sampling_overridden")}overrideTrigger(e){this._activateTrigger(e)}_reportStarted(e,t){this.instance.register_for_session({$session_recording_start_reason:e}),sg.info(e.replace("_"," "),t),y(["recording_initialized","session_id_changed"],e)||this._tryAddCustomEvent(e,t)}}var sS=U("[RemoteConfig]");class sE{constructor(e){this.instance=e}get remoteConfig(){var e,t;return null===(e=v._POSTHOG_REMOTE_CONFIG)||void 0===e||null===(t=e[this.instance.config.token])||void 0===t?void 0:t.config}_loadRemoteConfigJs(e){var t,i,s;null!==(t=v.__PosthogExtensions__)&&void 0!==t&&t.loadExternalDependency?null===(i=v.__PosthogExtensions__)||void 0===i||null===(s=i.loadExternalDependency)||void 0===s||s.call(i,this.instance,"remote-config",()=>e(this.remoteConfig)):(sS.error("PostHog Extensions not found. Cannot load remote config."),e())}_loadRemoteConfigJSON(e){this.instance._send_request({method:"GET",url:this.instance.requestRouter.endpointFor("assets","/array/".concat(this.instance.config.token,"/config")),callback:t=>{e(t.json)}})}load(){try{if(this.remoteConfig)return sS.info("Using preloaded remote config",this.remoteConfig),void this.onRemoteConfig(this.remoteConfig);if(this.instance.config.advanced_disable_decide)return void sS.warn("Remote config is disabled. Falling back to local config.");this._loadRemoteConfigJs(e=>{if(!e)return sS.info("No config found after loading remote JS config. Falling back to JSON."),void this._loadRemoteConfigJSON(e=>{this.onRemoteConfig(e)});this.onRemoteConfig(e)})}catch(e){sS.error("Error loading remote config",e)}}onRemoteConfig(e){e?this.instance.config.__preview_remote_config?(this.instance._onRemoteConfig(e),!1!==e.hasFeatureFlags&&this.instance.featureFlags.ensureFlagsLoaded()):sS.info("__preview_remote_config is disabled. Logging config instead",e):sS.error("Failed to fetch remote config from PostHog.")}}var sk,sx=null!=r&&r.location?tu(r.location.hash,"__posthog")||tu(location.hash,"state"):null,sI="_postHogToolbarParams",sC=U("[Toolbar]");!function(e){e[e.UNINITIALIZED=0]="UNINITIALIZED",e[e.LOADING=1]="LOADING",e[e.LOADED=2]="LOADED"}(sk||(sk={}));class sP{constructor(e){this.instance=e}setToolbarState(e){v.ph_toolbar_state=e}getToolbarState(){var e;return null!==(e=v.ph_toolbar_state)&&void 0!==e?e:sk.UNINITIALIZED}maybeLoadToolbar(){var e,t,i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:void 0,s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0;if(!r||!u)return!1;i=null!==(e=i)&&void 0!==e?e:r.location,n=null!==(t=n)&&void 0!==t?t:r.history;try{if(!s){try{r.localStorage.setItem("test","test"),r.localStorage.removeItem("test")}catch(e){return!1}s=null==r?void 0:r.localStorage}var o,a=sx||tu(i.hash,"__posthog")||tu(i.hash,"state"),l=a?et(()=>JSON.parse(atob(decodeURIComponent(a))))||et(()=>JSON.parse(decodeURIComponent(a))):null;return l&&"ph_authorize"===l.action?((o=l).source="url",o&&Object.keys(o).length>0&&(l.desiredHash?i.hash=l.desiredHash:n?n.replaceState(n.state,"",i.pathname+i.search):i.hash="")):((o=JSON.parse(s.getItem(sI)||"{}")).source="localstorage",delete o.userIntent),!(!o.token||this.instance.config.token!==o.token)&&(this.loadToolbar(o),!0)}catch(e){return!1}}_callLoadToolbar(e){var t=v.ph_load_toolbar||v.ph_load_editor;!A(t)&&P(t)?t(e,this.instance):sC.warn("No toolbar load function found")}loadToolbar(e){var t,i,s=!(null==u||!u.getElementById(eB));if(!r||s)return!1;var n="custom"===this.instance.requestRouter.region&&this.instance.config.advanced_disable_toolbar_metrics,o=V(V({token:this.instance.config.token},e),{},{apiURL:this.instance.requestRouter.endpointFor("ui")},n?{instrument:!1}:{});return(r.localStorage.setItem(sI,JSON.stringify(V(V({},o),{},{source:void 0}))),this.getToolbarState()===sk.LOADED)?this._callLoadToolbar(o):this.getToolbarState()===sk.UNINITIALIZED&&(this.setToolbarState(sk.LOADING),null===(t=v.__PosthogExtensions__)||void 0===t||null===(i=t.loadExternalDependency)||void 0===i||i.call(t,this.instance,"toolbar",e=>{if(e)return sC.error("[Toolbar] Failed to load",e),void this.setToolbarState(sk.UNINITIALIZED);this.setToolbarState(sk.LOADED),this._callLoadToolbar(o)}),er(r,"turbolinks:load",()=>{this.setToolbarState(sk.UNINITIALIZED),this.loadToolbar(o)})),!0}_loadEditor(e){return this.loadToolbar(e)}maybeLoadEditor(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:void 0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0;return this.maybeLoadToolbar(e,t,i)}}class sF{constructor(e){G(this,"isPaused",!0),G(this,"queue",[]),G(this,"flushTimeoutMs",3e3),this.sendRequest=e}enqueue(e){this.queue.push(e),this.flushTimeout||this.setFlushTimeout()}unload(){this.clearFlushTimeout();var e=Object.values(this.queue.length>0?this.formatQueue():{});[...e.filter(e=>0===e.url.indexOf("/e")),...e.filter(e=>0!==e.url.indexOf("/e"))].map(e=>{this.sendRequest(V(V({},e),{},{transport:"sendBeacon"}))})}enable(){this.isPaused=!1,this.setFlushTimeout()}setFlushTimeout(){var e=this;this.isPaused||(this.flushTimeout=setTimeout(()=>{if(this.clearFlushTimeout(),this.queue.length>0){var t=this.formatQueue();for(var i in t)!function(i){var s=t[i],r=(new Date).getTime();s.data&&C(s.data)&&X(s.data,e=>{e.offset=Math.abs(e.timestamp-r),delete e.timestamp}),e.sendRequest(s)}(i)}},this.flushTimeoutMs))}clearFlushTimeout(){clearTimeout(this.flushTimeout),this.flushTimeout=void 0}formatQueue(){var e={};return X(this.queue,t=>{var i,s=(t?t.batchKey:null)||t.url;T(e[s])&&(e[s]=V(V({},t),{},{data:[]})),null===(i=e[s].data)||void 0===i||i.push(t.data)}),this.queue=[],e}}var sR=function(e){var t,i,s,r,n="";for(t=i=0,s=(e=(e+"").replace(/\r\n/g,"\n").replace(/\r/g,"\n")).length,r=0;r<s;r++){var o=e.charCodeAt(r),a=null;o<128?i++:a=o>127&&o<2048?String.fromCharCode(o>>6|192,63&o|128):String.fromCharCode(o>>12|224,o>>6&63|128,63&o|128),L(a)||(i>t&&(n+=e.substring(t,i)),n+=a,t=i=r+1)}return i>t&&(n+=e.substring(t,e.length)),n},sT=!!_||!!h,s$="text/plain",sO=(e,t)=>{var[i,s]=e.split("?"),r=V({},t);null==s||s.split("&").forEach(e=>{var[t]=e.split("=");delete r[t]});var n=ta(r);return n=n?(s?s+"&":"")+n:s,"".concat(i,"?").concat(n)},sL=(e,t)=>JSON.stringify(e,(e,t)=>"bigint"==typeof t?t.toString():t,t),sA=e=>{var{data:t,compression:i}=e;if(t){if(i===s.GZipJS){var r=new Blob([sh(s_(sL(t)),{mtime:0})],{type:s$});return{contentType:s$,body:r,estimatedSize:r.size}}if(i===s.Base64){var n=(e=>"data="+encodeURIComponent("string"==typeof e?e:sL(e)))(function(e){var t,i,s,r,n,o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",a=0,l=0,c="",u=[];if(!e)return e;e=sR(e);do t=(n=e.charCodeAt(a++)<<16|e.charCodeAt(a++)<<8|e.charCodeAt(a++))>>18&63,i=n>>12&63,s=n>>6&63,r=63&n,u[l++]=o.charAt(t)+o.charAt(i)+o.charAt(s)+o.charAt(r);while(a<e.length);switch(c=u.join(""),e.length%3){case 1:c=c.slice(0,-2)+"==";break;case 2:c=c.slice(0,-1)+"="}return c}(sL(t)));return{contentType:"application/x-www-form-urlencoded",body:n,estimatedSize:new Blob([n]).size}}var o=sL(t);return{contentType:"application/json",body:o,estimatedSize:new Blob([o]).size}}},sM=[];h&&sM.push({transport:"fetch",method:e=>{var t,i,{contentType:s,body:r,estimatedSize:n}=null!==(t=sA(e))&&void 0!==t?t:{},o=new Headers;X(e.headers,function(e,t){o.append(t,e)}),s&&o.append("Content-Type",s);var a=e.url,l=null;if(p){var c=new p;l={signal:c.signal,timeout:setTimeout(()=>c.abort(),e.timeout)}}h(a,V({method:(null==e?void 0:e.method)||"GET",headers:o,keepalive:"POST"===e.method&&52428.8>(n||0),body:r,signal:null===(i=l)||void 0===i?void 0:i.signal},e.fetchOptions)).then(t=>t.text().then(i=>{var s,r={statusCode:t.status,text:i};if(200===t.status)try{r.json=JSON.parse(i)}catch(e){H.error(e)}null===(s=e.callback)||void 0===s||s.call(e,r)})).catch(t=>{var i;H.error(t),null===(i=e.callback)||void 0===i||i.call(e,{statusCode:0,text:t})}).finally(()=>l?clearTimeout(l.timeout):null)}}),_&&sM.push({transport:"XHR",method:e=>{var t,i=new _;i.open(e.method||"GET",e.url,!0);var{contentType:s,body:r}=null!==(t=sA(e))&&void 0!==t?t:{};X(e.headers,function(e,t){i.setRequestHeader(t,e)}),s&&i.setRequestHeader("Content-Type",s),e.timeout&&(i.timeout=e.timeout),i.withCredentials=!0,i.onreadystatechange=()=>{if(4===i.readyState){var t,s={statusCode:i.status,text:i.responseText};if(200===i.status)try{s.json=JSON.parse(i.responseText)}catch(e){}null===(t=e.callback)||void 0===t||t.call(e,s)}},i.send(r)}}),null!=c&&c.sendBeacon&&sM.push({transport:"sendBeacon",method:e=>{var t=sO(e.url,{beacon:"1"});try{var i,{contentType:s,body:r}=null!==(i=sA(e))&&void 0!==i?i:{},n="string"==typeof r?new Blob([r],{type:s}):r;c.sendBeacon(t,n)}catch(e){}}});var sD=["retriesPerformedSoFar"];class sN{constructor(e){G(this,"isPolling",!1),G(this,"pollIntervalMs",3e3),G(this,"queue",[]),this.instance=e,this.queue=[],this.areWeOnline=!0,!T(r)&&"onLine"in r.navigator&&(this.areWeOnline=r.navigator.onLine,r.addEventListener("online",()=>{this.areWeOnline=!0,this.flush()}),r.addEventListener("offline",()=>{this.areWeOnline=!1}))}retriableRequest(e){var{retriesPerformedSoFar:t}=e,i=J(e,sD);M(t)&&t>0&&(i.url=sO(i.url,{retry_count:t})),this.instance._send_request(V(V({},i),{},{callback:e=>{var s;200!==e.statusCode&&(e.statusCode<400||e.statusCode>=500)&&(null!=t?t:0)<10?this.enqueue(V({retriesPerformedSoFar:t},i)):null===(s=i.callback)||void 0===s||s.call(i,e)}}))}enqueue(e){var t,i,s,r=e.retriesPerformedSoFar||0;e.retriesPerformedSoFar=r+1;var n=(s=(Math.random()-.5)*((i=Math.min(18e5,t=3e3*Math.pow(2,r)))-t/2),Math.ceil(i+s)),o=Date.now()+n;this.queue.push({retryAt:o,requestOptions:e});var a="Enqueued failed request for retry in ".concat(n);navigator.onLine||(a+=" (Browser is offline)"),H.warn(a),this.isPolling||(this.isPolling=!0,this.poll())}poll(){this.poller&&clearTimeout(this.poller),this.poller=setTimeout(()=>{this.areWeOnline&&this.queue.length>0&&this.flush(),this.poll()},this.pollIntervalMs)}flush(){var e=Date.now(),t=[],i=this.queue.filter(i=>i.retryAt<e||(t.push(i),!1));if(this.queue=t,i.length>0)for(var{requestOptions:s}of i)this.retriableRequest(s)}unload(){for(var{requestOptions:e}of(this.poller&&(clearTimeout(this.poller),this.poller=void 0),this.queue))try{this.instance._send_request(V(V({},e),{},{transport:"sendBeacon"}))}catch(e){H.error(e)}this.queue=[]}}var sq,sB=U("[SessionId]");class sH{constructor(e,t,i){if(G(this,"_sessionIdChangedHandlers",[]),!e.persistence)throw Error("SessionIdManager requires a PostHogPersistence instance");if(e.config.__preview_experimental_cookieless_mode)throw Error("SessionIdManager cannot be used with __preview_experimental_cookieless_mode");this.config=e.config,this.persistence=e.persistence,this._windowId=void 0,this._sessionId=void 0,this._sessionStartTimestamp=null,this._sessionActivityTimestamp=null,this._sessionIdGenerator=t||e1,this._windowIdGenerator=i||e1;var s,r=this.config.persistence_name||this.config.token,n=this.config.session_idle_timeout_seconds||1800;if(this._sessionTimeoutMs=1e3*iB(n,60,36e3,"session_idle_timeout_seconds",1800),e.register({$configured_session_timeout_ms:this._sessionTimeoutMs}),this.resetIdleTimer(),this._window_id_storage_key="ph_"+r+"_window_id",this._primary_window_exists_storage_key="ph_"+r+"_primary_window_exists",this._canUseSessionStorage()){var o=ts.parse(this._window_id_storage_key),a=ts.parse(this._primary_window_exists_storage_key);o&&!a?this._windowId=o:ts.remove(this._window_id_storage_key),ts.set(this._primary_window_exists_storage_key,!0)}if(null!==(s=this.config.bootstrap)&&void 0!==s&&s.sessionID)try{var l=(e=>{var t=e.replace(/-/g,"");if(32!==t.length)throw Error("Not a valid UUID");if("7"!==t[12])throw Error("Not a UUIDv7");return parseInt(t.substring(0,12),16)})(this.config.bootstrap.sessionID);this._setSessionId(this.config.bootstrap.sessionID,(new Date).getTime(),l)}catch(e){sB.error("Invalid sessionID in bootstrap",e)}this._listenToReloadWindow()}get sessionTimeoutMs(){return this._sessionTimeoutMs}onSessionId(e){return T(this._sessionIdChangedHandlers)&&(this._sessionIdChangedHandlers=[]),this._sessionIdChangedHandlers.push(e),this._sessionId&&e(this._sessionId,this._windowId),()=>{this._sessionIdChangedHandlers=this._sessionIdChangedHandlers.filter(t=>t!==e)}}_canUseSessionStorage(){return"memory"!==this.config.persistence&&!this.persistence.disabled&&ts.is_supported()}_setWindowId(e){e!==this._windowId&&(this._windowId=e,this._canUseSessionStorage()&&ts.set(this._window_id_storage_key,e))}_getWindowId(){return this._windowId?this._windowId:this._canUseSessionStorage()?ts.parse(this._window_id_storage_key):null}_setSessionId(e,t,i){e===this._sessionId&&t===this._sessionActivityTimestamp&&i===this._sessionStartTimestamp||(this._sessionStartTimestamp=i,this._sessionActivityTimestamp=t,this._sessionId=e,this.persistence.register({[eS]:[t,e,i]}))}_getSessionId(){if(this._sessionId&&this._sessionActivityTimestamp&&this._sessionStartTimestamp)return[this._sessionActivityTimestamp,this._sessionId,this._sessionStartTimestamp];var e=this.persistence.props[eS];return C(e)&&2===e.length&&e.push(e[0]),e||[0,null,0]}resetSessionId(){this._setSessionId(null,null,null)}_listenToReloadWindow(){null==r||r.addEventListener("beforeunload",()=>{this._canUseSessionStorage()&&ts.remove(this._primary_window_exists_storage_key)})}checkAndGetSessionAndWindowId(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(this.config.__preview_experimental_cookieless_mode)throw Error("checkAndGetSessionAndWindowId should not be called in __preview_experimental_cookieless_mode");var i=t||(new Date).getTime(),[s,r,n]=this._getSessionId(),o=this._getWindowId(),a=M(n)&&n>0&&Math.abs(i-n)>864e5,l=!1,c=!r,u=!e&&Math.abs(i-s)>this.sessionTimeoutMs;c||u||a?(r=this._sessionIdGenerator(),o=this._windowIdGenerator(),sB.info("new session ID generated",{sessionId:r,windowId:o,changeReason:{noSessionId:c,activityTimeout:u,sessionPastMaximumLength:a}}),n=i,l=!0):o||(o=this._windowIdGenerator(),l=!0);var d=0===s||!e||a?i:s,h=0===n?(new Date).getTime():n;return this._setWindowId(o),this._setSessionId(r,d,h),e||this.resetIdleTimer(),l&&this._sessionIdChangedHandlers.forEach(e=>e(r,o,l?{noSessionId:c,activityTimeout:u,sessionPastMaximumLength:a}:void 0)),{sessionId:r,windowId:o,sessionStartTimestamp:h,changeReason:l?{noSessionId:c,activityTimeout:u,sessionPastMaximumLength:a}:void 0,lastActivityTimestamp:s}}resetIdleTimer(){clearTimeout(this._enforceIdleTimeout),this._enforceIdleTimeout=setTimeout(()=>{this.resetSessionId()},1.1*this.sessionTimeoutMs)}}!function(e){e.US="us",e.EU="eu",e.CUSTOM="custom"}(sq||(sq={}));var sU="i.posthog.com";class sz{constructor(e){G(this,"_regionCache",{}),this.instance=e}get apiHost(){var e=this.instance.config.api_host.trim().replace(/\/$/,"");return"https://app.posthog.com"===e?"https://us.i.posthog.com":e}get uiHost(){var e,t=null===(e=this.instance.config.ui_host)||void 0===e?void 0:e.replace(/\/$/,"");return t||(t=this.apiHost.replace(".".concat(sU),".posthog.com")),"https://app.posthog.com"===t?"https://us.posthog.com":t}get region(){return this._regionCache[this.apiHost]||(/https:\/\/(app|us|us-assets)(\.i)?\.posthog\.com/i.test(this.apiHost)?this._regionCache[this.apiHost]=sq.US:/https:\/\/(eu|eu-assets)(\.i)?\.posthog\.com/i.test(this.apiHost)?this._regionCache[this.apiHost]=sq.EU:this._regionCache[this.apiHost]=sq.CUSTOM),this._regionCache[this.apiHost]}endpointFor(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";if(t&&(t="/"===t[0]?t:"/".concat(t)),"ui"===e)return this.uiHost+t;if(this.region===sq.CUSTOM)return this.apiHost+t;var i=sU+t;switch(e){case"assets":return"https://".concat(this.region,"-assets.").concat(i);case"api":return"https://".concat(this.region,".").concat(i)}}}var sj="posthog-js";function sW(e){var{organization:t,projectId:i,prefix:s,severityAllowList:r=["error"]}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return n=>{if(!("*"===r||r.includes(n.level))||!e.__loaded)return n;n.tags||(n.tags={});var o,a,l,c,u,d=e.requestRouter.endpointFor("ui","/project/".concat(e.config.token,"/person/").concat(e.get_distinct_id()));n.tags["PostHog Person URL"]=d,e.sessionRecordingStarted()&&(n.tags["PostHog Recording URL"]=e.get_session_replay_url({withTimestamp:!0}));var h=(null===(o=n.exception)||void 0===o?void 0:o.values)||[];h.map(e=>{e.stacktrace&&(e.stacktrace.type="raw")});var _={$exception_message:(null===(a=h[0])||void 0===a?void 0:a.value)||n.message,$exception_type:null===(l=h[0])||void 0===l?void 0:l.type,$exception_personURL:d,$exception_level:n.level,$exception_list:h,$sentry_event_id:n.event_id,$sentry_exception:n.exception,$sentry_exception_message:(null===(c=h[0])||void 0===c?void 0:c.value)||n.message,$sentry_exception_type:null===(u=h[0])||void 0===u?void 0:u.type,$sentry_tags:n.tags};return t&&i&&(_.$sentry_url=(s||"https://sentry.io/organizations/")+t+"/issues/?project="+i+"&query="+n.event_id),e.exceptions.sendExceptionEvent(_),n}}class sV{constructor(e,t,i,s,r){this.name=sj,this.setupOnce=function(n){n(sW(e,{organization:t,projectId:i,prefix:s,severityAllowList:r}))}}}var sG=U("[SegmentIntegration]");class sJ{constructor(e){this._instance=e}doPageView(e,t){var i,s=this._previousPageViewProperties(e,t);return this._currentPageview={pathname:null!==(i=null==r?void 0:r.location.pathname)&&void 0!==i?i:"",pageViewId:t,timestamp:e},this._instance.scrollManager.resetContext(),s}doPageLeave(e){var t;return this._previousPageViewProperties(e,null===(t=this._currentPageview)||void 0===t?void 0:t.pageViewId)}doEvent(){var e;return{$pageview_id:null===(e=this._currentPageview)||void 0===e?void 0:e.pageViewId}}_previousPageViewProperties(e,t){var i=this._currentPageview;if(!i)return{$pageview_id:t};var s={$pageview_id:t,$prev_pageview_id:i.pageViewId},r=this._instance.scrollManager.getContext();if(r&&!this._instance.config.disable_scroll_properties){var{maxScrollHeight:n,lastScrollY:o,maxScrollY:a,maxContentHeight:l,lastContentY:c,maxContentY:u}=r;if(!(T(n)||T(o)||T(a)||T(l)||T(c)||T(u))){n=Math.ceil(n),o=Math.ceil(o),a=Math.ceil(a),l=Math.ceil(l),c=Math.ceil(c),u=Math.ceil(u);var d=n<=1?1:iB(o/n,0,1),h=n<=1?1:iB(a/n,0,1),_=l<=1?1:iB(c/l,0,1),p=l<=1?1:iB(u/l,0,1);s=Q(s,{$prev_pageview_last_scroll:o,$prev_pageview_last_scroll_percentage:d,$prev_pageview_max_scroll:a,$prev_pageview_max_scroll_percentage:h,$prev_pageview_last_content:c,$prev_pageview_last_content_percentage:_,$prev_pageview_max_content:u,$prev_pageview_max_content_percentage:p})}}return i.pathname&&(s.$prev_pageview_pathname=i.pathname),i.timestamp&&(s.$prev_pageview_duration=(e.getTime()-i.timestamp.getTime())/1e3),s}}var sY,sK,sX,sQ,sZ,s0,s1,s2,s3={},s5=[],s6=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,s8=Array.isArray;function s4(e,t){for(var i in t)e[i]=t[i];return e}function s7(e){var t=e.parentNode;t&&t.removeChild(e)}function s9(e,t,i,s,r){var n={type:e,props:t,key:i,ref:s,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:null==r?++sX:r,__i:-1,__u:0};return null==r&&null!=sK.vnode&&sK.vnode(n),n}function re(e){return e.children}function rt(e,t){this.props=e,this.context=t}function ri(e,t){if(null==t)return e.__?ri(e.__,e.__i+1):null;for(var i;t<e.__k.length;t++)if(null!=(i=e.__k[t])&&null!=i.__e)return i.__e;return"function"==typeof e.type?ri(e):null}function rs(e){(!e.__d&&(e.__d=!0)&&sQ.push(e)&&!rr.__r++||sZ!==sK.debounceRendering)&&((sZ=sK.debounceRendering)||s0)(rr)}function rr(){var e,t,i,s,r,n,o,a,l;for(sQ.sort(s1);e=sQ.shift();)e.__d&&(t=sQ.length,s=void 0,n=(r=(i=e).__v).__e,a=[],l=[],(o=i.__P)&&((s=s4({},r)).__v=r.__v+1,sK.vnode&&sK.vnode(s),ru(o,s,r,i.__n,void 0!==o.ownerSVGElement,32&r.__u?[n]:null,a,null==n?ri(r):n,!!(32&r.__u),l),s.__.__k[s.__i]=s,function(e,t,i){t.__d=void 0;for(var s=0;s<i.length;s++)rd(i[s],i[++s],i[++s]);sK.__c&&sK.__c(t,e),e.some(function(t){try{e=t.__h,t.__h=[],e.some(function(e){e.call(t)})}catch(e){sK.__e(e,t.__v)}})}(a,s,l),s.__e!=n&&function e(t){var i,s;if(null!=(t=t.__)&&null!=t.__c){for(t.__e=t.__c.base=null,i=0;i<t.__k.length;i++)if(null!=(s=t.__k[i])&&null!=s.__e){t.__e=t.__c.base=s.__e;break}return e(t)}}(s)),sQ.length>t&&sQ.sort(s1));rr.__r=0}function rn(e,t,i,s,r,n,o,a,l,c,u){var d,h,_,p,g,v=s&&s.__k||s5,f=t.length;for(i.__d=l,function(e,t,i){var s,r,n,o,a,l=t.length,c=i.length,u=c,d=0;for(e.__k=[],s=0;s<l;s++)null!=(r=e.__k[s]=null==(r=t[s])||"boolean"==typeof r||"function"==typeof r?null:"string"==typeof r||"number"==typeof r||"bigint"==typeof r||r.constructor==String?s9(null,r,null,null,r):s8(r)?s9(re,{children:r},null,null,null):void 0===r.constructor&&r.__b>0?s9(r.type,r.props,r.key,r.ref?r.ref:null,r.__v):r)?(r.__=e,r.__b=e.__b+1,a=function(e,t,i,s){var r=e.key,n=e.type,o=i-1,a=i+1,l=t[i];if(null===l||l&&r==l.key&&n===l.type)return i;if(s>+(null!=l&&0==(131072&l.__u)))for(;o>=0||a<t.length;){if(o>=0){if((l=t[o])&&0==(131072&l.__u)&&r==l.key&&n===l.type)return o;o--}if(a<t.length){if((l=t[a])&&0==(131072&l.__u)&&r==l.key&&n===l.type)return a;a++}}return -1}(r,i,o=s+d,u),r.__i=a,n=null,-1!==a&&(u--,(n=i[a])&&(n.__u|=131072)),null==n||null===n.__v?(-1==a&&d--,"function"!=typeof r.type&&(r.__u|=65536)):a!==o&&(a===o+1?d++:a>o?u>l-o?d+=a-o:d--:d=a<o&&a==o-1?a-o:0,a!==s+d&&(r.__u|=65536))):(n=i[s])&&null==n.key&&n.__e&&(n.__e==e.__d&&(e.__d=ri(n)),rh(n,n,!1),i[s]=null,u--);if(u)for(s=0;s<c;s++)null!=(n=i[s])&&0==(131072&n.__u)&&(n.__e==e.__d&&(e.__d=ri(n)),rh(n,n))}(i,t,v),l=i.__d,d=0;d<f;d++)null!=(_=i.__k[d])&&"boolean"!=typeof _&&"function"!=typeof _&&(h=-1===_.__i?s3:v[_.__i]||s3,_.__i=d,ru(e,_,h,r,n,o,a,l,c,u),p=_.__e,_.ref&&h.ref!=_.ref&&(h.ref&&rd(h.ref,null,_),u.push(_.ref,_.__c||p,_)),null==g&&null!=p&&(g=p),65536&_.__u||h.__k===_.__k?l=function e(t,i,s){var r,n;if("function"==typeof t.type){for(r=t.__k,n=0;r&&n<r.length;n++)r[n]&&(r[n].__=t,i=e(r[n],i,s));return i}return t.__e!=i&&(s.insertBefore(t.__e,i||null),i=t.__e),i&&i.nextSibling}(_,l,e):"function"==typeof _.type&&void 0!==_.__d?l=_.__d:p&&(l=p.nextSibling),_.__d=void 0,_.__u&=-196609);i.__d=l,i.__e=g}function ro(e,t,i){"-"===t[0]?e.setProperty(t,null==i?"":i):e[t]=null==i?"":"number"!=typeof i||s6.test(t)?i:i+"px"}function ra(e,t,i,s,r){var n;e:if("style"===t){if("string"==typeof i)e.style.cssText=i;else{if("string"==typeof s&&(e.style.cssText=s=""),s)for(t in s)i&&t in i||ro(e.style,t,"");if(i)for(t in i)s&&i[t]===s[t]||ro(e.style,t,i[t])}}else if("o"===t[0]&&"n"===t[1])n=t!==(t=t.replace(/(PointerCapture)$|Capture$/,"$1")),t=t.toLowerCase()in e?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+n]=i,i?s?i.u=s.u:(i.u=Date.now(),e.addEventListener(t,n?rc:rl,n)):e.removeEventListener(t,n?rc:rl,n);else{if(r)t=t.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!==t&&"height"!==t&&"href"!==t&&"list"!==t&&"form"!==t&&"tabIndex"!==t&&"download"!==t&&"rowSpan"!==t&&"colSpan"!==t&&"role"!==t&&t in e)try{e[t]=null==i?"":i;break e}catch(e){}"function"==typeof i||(null==i||!1===i&&"-"!==t[4]?e.removeAttribute(t):e.setAttribute(t,i))}}function rl(e){var t=this.l[e.type+!1];if(e.t){if(e.t<=t.u)return}else e.t=Date.now();return t(sK.event?sK.event(e):e)}function rc(e){return this.l[e.type+!0](sK.event?sK.event(e):e)}function ru(e,t,i,s,r,n,o,a,l,c){var u,d,h,_,p,g,v,f,m,b,y,w,S,E,k,x=t.type;if(void 0!==t.constructor)return null;128&i.__u&&(l=!!(32&i.__u),n=[a=t.__e=i.__e]),(u=sK.__b)&&u(t);e:if("function"==typeof x)try{if(f=t.props,m=(u=x.contextType)&&s[u.__c],b=u?m?m.props.value:u.__:s,i.__c?v=(d=t.__c=i.__c).__=d.__E:("prototype"in x&&x.prototype.render?t.__c=d=new x(f,b):(t.__c=d=new rt(f,b),d.constructor=x,d.render=r_),m&&m.sub(d),d.props=f,d.state||(d.state={}),d.context=b,d.__n=s,h=d.__d=!0,d.__h=[],d._sb=[]),null==d.__s&&(d.__s=d.state),null!=x.getDerivedStateFromProps&&(d.__s==d.state&&(d.__s=s4({},d.__s)),s4(d.__s,x.getDerivedStateFromProps(f,d.__s))),_=d.props,p=d.state,d.__v=t,h)null==x.getDerivedStateFromProps&&null!=d.componentWillMount&&d.componentWillMount(),null!=d.componentDidMount&&d.__h.push(d.componentDidMount);else{if(null==x.getDerivedStateFromProps&&f!==_&&null!=d.componentWillReceiveProps&&d.componentWillReceiveProps(f,b),!d.__e&&(null!=d.shouldComponentUpdate&&!1===d.shouldComponentUpdate(f,d.__s,b)||t.__v===i.__v)){for(t.__v!==i.__v&&(d.props=f,d.state=d.__s,d.__d=!1),t.__e=i.__e,t.__k=i.__k,t.__k.forEach(function(e){e&&(e.__=t)}),y=0;y<d._sb.length;y++)d.__h.push(d._sb[y]);d._sb=[],d.__h.length&&o.push(d);break e}null!=d.componentWillUpdate&&d.componentWillUpdate(f,d.__s,b),null!=d.componentDidUpdate&&d.__h.push(function(){d.componentDidUpdate(_,p,g)})}if(d.context=b,d.props=f,d.__P=e,d.__e=!1,w=sK.__r,S=0,"prototype"in x&&x.prototype.render){for(d.state=d.__s,d.__d=!1,w&&w(t),u=d.render(d.props,d.state,d.context),E=0;E<d._sb.length;E++)d.__h.push(d._sb[E]);d._sb=[]}else do d.__d=!1,w&&w(t),u=d.render(d.props,d.state,d.context),d.state=d.__s;while(d.__d&&++S<25);d.state=d.__s,null!=d.getChildContext&&(s=s4(s4({},s),d.getChildContext())),h||null==d.getSnapshotBeforeUpdate||(g=d.getSnapshotBeforeUpdate(_,p)),rn(e,s8(k=null!=u&&u.type===re&&null==u.key?u.props.children:u)?k:[k],t,i,s,r,n,o,a,l,c),d.base=t.__e,t.__u&=-161,d.__h.length&&o.push(d),v&&(d.__E=d.__=null)}catch(e){t.__v=null,l||null!=n?(t.__e=a,t.__u|=l?160:32,n[n.indexOf(a)]=null):(t.__e=i.__e,t.__k=i.__k),sK.__e(e,t,i)}else null==n&&t.__v===i.__v?(t.__k=i.__k,t.__e=i.__e):t.__e=function(e,t,i,s,r,n,o,a,l){var c,u,d,h,_,p,g,v=i.props,f=t.props,m=t.type;if("svg"===m&&(r=!0),null!=n){for(c=0;c<n.length;c++)if((_=n[c])&&"setAttribute"in _==!!m&&(m?_.localName===m:3===_.nodeType)){e=_,n[c]=null;break}}if(null==e){if(null===m)return document.createTextNode(f);e=r?document.createElementNS("http://www.w3.org/2000/svg",m):document.createElement(m,f.is&&f),n=null,a=!1}if(null===m)v===f||a&&e.data===f||(e.data=f);else{if(n=n&&sY.call(e.childNodes),v=i.props||s3,!a&&null!=n)for(v={},c=0;c<e.attributes.length;c++)v[(_=e.attributes[c]).name]=_.value;for(c in v)_=v[c],"children"==c||("dangerouslySetInnerHTML"==c?d=_:"key"===c||c in f||ra(e,c,null,_,r));for(c in f)_=f[c],"children"==c?h=_:"dangerouslySetInnerHTML"==c?u=_:"value"==c?p=_:"checked"==c?g=_:"key"===c||a&&"function"!=typeof _||v[c]===_||ra(e,c,_,v[c],r);if(u)a||d&&(u.__html===d.__html||u.__html===e.innerHTML)||(e.innerHTML=u.__html),t.__k=[];else if(d&&(e.innerHTML=""),rn(e,s8(h)?h:[h],t,i,s,r&&"foreignObject"!==m,n,o,n?n[0]:i.__k&&ri(i,0),a,l),null!=n)for(c=n.length;c--;)null!=n[c]&&s7(n[c]);a||(c="value",void 0===p||p===e[c]&&("progress"!==m||p)&&("option"!==m||p===v[c])||ra(e,c,p,v[c],!1),c="checked",void 0!==g&&g!==e[c]&&ra(e,c,g,v[c],!1))}return e}(i.__e,t,i,s,r,n,o,l,c);(u=sK.diffed)&&u(t)}function rd(e,t,i){try{"function"==typeof e?e(t):e.current=t}catch(e){sK.__e(e,i)}}function rh(e,t,i){var s,r;if(sK.unmount&&sK.unmount(e),(s=e.ref)&&(s.current&&s.current!==e.__e||rd(s,null,t)),null!=(s=e.__c)){if(s.componentWillUnmount)try{s.componentWillUnmount()}catch(e){sK.__e(e,t)}s.base=s.__P=null,e.__c=void 0}if(s=e.__k)for(r=0;r<s.length;r++)s[r]&&rh(s[r],t,i||"function"!=typeof e.type);i||null==e.__e||s7(e.__e),e.__=e.__e=e.__d=void 0}function r_(e,t,i){return this.constructor(e,i)}sY=s5.slice,sK={__e:function(e,t,i,s){for(var r,n,o;t=t.__;)if((r=t.__c)&&!r.__)try{if((n=r.constructor)&&null!=n.getDerivedStateFromError&&(r.setState(n.getDerivedStateFromError(e)),o=r.__d),null!=r.componentDidCatch&&(r.componentDidCatch(e,s||{}),o=r.__d),o)return r.__E=r}catch(t){e=t}throw e}},sX=0,rt.prototype.setState=function(e,t){var i;i=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=s4({},this.state),"function"==typeof e&&(e=e(s4({},i),this.props)),e&&s4(i,e),null!=e&&this.__v&&(t&&this._sb.push(t),rs(this))},rt.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),rs(this))},rt.prototype.render=re,sQ=[],s0="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,s1=function(e,t){return e.__v.__b-t.__v.__b},rr.__r=0,s2=0,function(e,t){var i={__c:t="__cC"+s2++,__:e,Consumer:function(e,t){return e.children(t)},Provider:function(e){var i,s;return this.getChildContext||(i=[],(s={})[t]=this,this.getChildContext=function(){return s},this.shouldComponentUpdate=function(e){this.props.value!==e.value&&i.some(function(e){e.__e=!0,rs(e)})},this.sub=function(e){i.push(e);var t=e.componentWillUnmount;e.componentWillUnmount=function(){i.splice(i.indexOf(e),1),t&&t.call(e)}}),e.children}};i.Provider.__=i.Consumer.contextType=i}({isPreviewMode:!1,previewPageIndex:0,handleCloseSurveyPopup:()=>{},isPopup:!0,onPreviewSubmit:()=>{}}),function(e){e.Popover="popover",e.API="api",e.Widget="widget"}(r7||(r7={})),function(e){e.Open="open",e.MultipleChoice="multiple_choice",e.SingleChoice="single_choice",e.Rating="rating",e.Link="link"}(r9||(r9={})),function(e){e.NextQuestion="next_question",e.End="end",e.ResponseBased="response_based",e.SpecificQuestion="specific_question"}(ne||(ne={}));class rp{constructor(){G(this,"events",{}),this.events={}}on(e,t){return this.events[e]||(this.events[e]=[]),this.events[e].push(t),()=>{this.events[e]=this.events[e].filter(e=>e!==t)}}emit(e,t){for(var i of this.events[e]||[])i(t);for(var s of this.events["*"]||[])s(e,t)}}class rg{constructor(e){G(this,"_debugEventEmitter",new rp),G(this,"checkStep",(e,t)=>this.checkStepEvent(e,t)&&this.checkStepUrl(e,t)&&this.checkStepElement(e,t)),G(this,"checkStepEvent",(e,t)=>null==t||!t.event||(null==e?void 0:e.event)===(null==t?void 0:t.event)),this.instance=e,this.actionEvents=new Set,this.actionRegistry=new Set}init(){var e,t;T(null===(e=this.instance)||void 0===e?void 0:e._addCaptureHook)||null===(t=this.instance)||void 0===t||t._addCaptureHook((e,t)=>{this.on(e,t)})}register(e){var t,i;if(!T(null===(t=this.instance)||void 0===t?void 0:t._addCaptureHook)&&(e.forEach(e=>{var t,i;null===(t=this.actionRegistry)||void 0===t||t.add(e),null===(i=e.steps)||void 0===i||i.forEach(e=>{var t;null===(t=this.actionEvents)||void 0===t||t.add((null==e?void 0:e.event)||"")})}),null!==(i=this.instance)&&void 0!==i&&i.autocapture)){var s,r=new Set;e.forEach(e=>{var t;null===(t=e.steps)||void 0===t||t.forEach(e=>{null!=e&&e.selector&&r.add(null==e?void 0:e.selector)})}),null===(s=this.instance)||void 0===s||s.autocapture.setElementSelectors(r)}}on(e,t){var i;null!=t&&0!=e.length&&(this.actionEvents.has(e)||this.actionEvents.has(null==t?void 0:t.event))&&this.actionRegistry&&(null===(i=this.actionRegistry)||void 0===i?void 0:i.size)>0&&this.actionRegistry.forEach(e=>{this.checkAction(t,e)&&this._debugEventEmitter.emit("actionCaptured",e.name)})}_addActionHook(e){this.onAction("actionCaptured",t=>e(t))}checkAction(e,t){if(null==(null==t?void 0:t.steps))return!1;for(var i of t.steps)if(this.checkStep(e,i))return!0;return!1}onAction(e,t){return this._debugEventEmitter.on(e,t)}checkStepUrl(e,t){if(null!=t&&t.url){var i,s=null==e||null===(i=e.properties)||void 0===i?void 0:i.$current_url;if(!s||"string"!=typeof s||!rg.matchString(s,null==t?void 0:t.url,(null==t?void 0:t.url_matching)||"contains"))return!1}return!0}static matchString(e,t,i){switch(i){case"regex":return!!r&&to(e,t);case"exact":return t===e;case"contains":return to(e,rg.escapeStringRegexp(t).replace(/_/g,".").replace(/%/g,".*"));default:return!1}}static escapeStringRegexp(e){return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}checkStepElement(e,t){if((null!=t&&t.href||null!=t&&t.tag_name||null!=t&&t.text)&&!this.getElementsList(e).some(e=>!(null!=t&&t.href&&!rg.matchString(e.href||"",null==t?void 0:t.href,(null==t?void 0:t.href_matching)||"exact"))&&(null==t||!t.tag_name||e.tag_name===(null==t?void 0:t.tag_name))&&!(null!=t&&t.text&&!rg.matchString(e.text||"",null==t?void 0:t.text,(null==t?void 0:t.text_matching)||"exact")&&!rg.matchString(e.$el_text||"",null==t?void 0:t.text,(null==t?void 0:t.text_matching)||"exact"))))return!1;if(null!=t&&t.selector){var i,s=null==e||null===(i=e.properties)||void 0===i?void 0:i.$element_selectors;if(!s||!s.includes(null==t?void 0:t.selector))return!1}return!0}getElementsList(e){return null==(null==e?void 0:e.properties.$elements)?[]:null==e?void 0:e.properties.$elements}}class rv{constructor(e){this.instance=e,this.eventToSurveys=new Map,this.actionToSurveys=new Map}register(e){var t;T(null===(t=this.instance)||void 0===t?void 0:t._addCaptureHook)||(this.setupEventBasedSurveys(e),this.setupActionBasedSurveys(e))}setupActionBasedSurveys(e){var t=e.filter(e=>{var t,i,s,r;return(null===(t=e.conditions)||void 0===t?void 0:t.actions)&&(null===(i=e.conditions)||void 0===i||null===(s=i.actions)||void 0===s||null===(r=s.values)||void 0===r?void 0:r.length)>0});0!==t.length&&(null==this.actionMatcher&&(this.actionMatcher=new rg(this.instance),this.actionMatcher.init(),this.actionMatcher._addActionHook(e=>{this.onAction(e)})),t.forEach(e=>{var t,i,s,r,n,o,a,l,c,u;e.conditions&&null!==(t=e.conditions)&&void 0!==t&&t.actions&&null!==(i=e.conditions)&&void 0!==i&&null!==(s=i.actions)&&void 0!==s&&s.values&&(null===(r=e.conditions)||void 0===r||null===(n=r.actions)||void 0===n||null===(o=n.values)||void 0===o?void 0:o.length)>0&&(null===(a=this.actionMatcher)||void 0===a||a.register(e.conditions.actions.values),null===(l=e.conditions)||void 0===l||null===(c=l.actions)||void 0===c||null===(u=c.values)||void 0===u||u.forEach(t=>{if(t&&t.name){var i=this.actionToSurveys.get(t.name);i&&i.push(e.id),this.actionToSurveys.set(t.name,i||[e.id])}}))}))}setupEventBasedSurveys(e){var t;0!==e.filter(e=>{var t,i,s,r;return(null===(t=e.conditions)||void 0===t?void 0:t.events)&&(null===(i=e.conditions)||void 0===i||null===(s=i.events)||void 0===s||null===(r=s.values)||void 0===r?void 0:r.length)>0}).length&&(null===(t=this.instance)||void 0===t||t._addCaptureHook((e,t)=>{this.onEvent(e,t)}),e.forEach(e=>{var t,i,s;null===(t=e.conditions)||void 0===t||null===(i=t.events)||void 0===i||null===(s=i.values)||void 0===s||s.forEach(t=>{if(t&&t.name){var i=this.eventToSurveys.get(t.name);i&&i.push(e.id),this.eventToSurveys.set(t.name,i||[e.id])}})}))}onEvent(e,t){var i,s,r=(null===(i=this.instance)||void 0===i||null===(s=i.persistence)||void 0===s?void 0:s.props[eT])||[];if(rv.SURVEY_SHOWN_EVENT_NAME==e&&t&&r.length>0){var n,o=null==t||null===(n=t.properties)||void 0===n?void 0:n.$survey_id;if(o){var a=r.indexOf(o);a>=0&&(r.splice(a,1),this._updateActivatedSurveys(r))}}else this.eventToSurveys.has(e)&&this._updateActivatedSurveys(r.concat(this.eventToSurveys.get(e)||[]))}onAction(e){var t,i,s=(null===(t=this.instance)||void 0===t||null===(i=t.persistence)||void 0===i?void 0:i.props[eT])||[];this.actionToSurveys.has(e)&&this._updateActivatedSurveys(s.concat(this.actionToSurveys.get(e)||[]))}_updateActivatedSurveys(e){var t,i;null===(t=this.instance)||void 0===t||null===(i=t.persistence)||void 0===i||i.register({[eT]:[...new Set(e)]})}getSurveys(){var e,t;return(null===(e=this.instance)||void 0===e||null===(t=e.persistence)||void 0===t?void 0:t.props[eT])||[]}getEventToSurveys(){return this.eventToSurveys}_getActionMatcher(){return this.actionMatcher}}G(rv,"SURVEY_SHOWN_EVENT_NAME","survey shown");var rf=U("[Surveys]"),rm={icontains:e=>!!r&&r.location.href.toLowerCase().indexOf(e.toLowerCase())>-1,not_icontains:e=>!!r&&-1===r.location.href.toLowerCase().indexOf(e.toLowerCase()),regex:e=>!!r&&to(r.location.href,e),not_regex:e=>!!r&&!to(r.location.href,e),exact:e=>(null==r?void 0:r.location.href)===e,is_not:e=>(null==r?void 0:r.location.href)!==e};function rb(e,t,i){var s,r=e.questions[t],n=t+1;if(null===(s=r.branching)||void 0===s||!s.type)return t===e.questions.length-1?ne.End:n;if(r.branching.type===ne.End)return ne.End;if(r.branching.type===ne.SpecificQuestion){if(Number.isInteger(r.branching.index))return r.branching.index}else if(r.branching.type===ne.ResponseBased){if(r.type===r9.SingleChoice){var o,a,l=r.choices.indexOf("".concat(i));if(null!==(o=r.branching)&&void 0!==o&&null!==(a=o.responseValues)&&void 0!==a&&a.hasOwnProperty(l)){var c=r.branching.responseValues[l];return Number.isInteger(c)?c:c===ne.End?ne.End:n}}else if(r.type===r9.Rating){if("number"!=typeof i||!Number.isInteger(i))throw Error("The response type must be an integer");var u,d,h=function(e,t){if(3===t){if(e<1||e>3)throw Error("The response must be in range 1-3");return 1===e?"negative":2===e?"neutral":"positive"}if(5===t){if(e<1||e>5)throw Error("The response must be in range 1-5");return e<=2?"negative":3===e?"neutral":"positive"}if(7===t){if(e<1||e>7)throw Error("The response must be in range 1-7");return e<=3?"negative":4===e?"neutral":"positive"}if(10===t){if(e<0||e>10)throw Error("The response must be in range 0-10");return e<=6?"detractors":e<=8?"passives":"promoters"}throw Error("The scale must be one of: 3, 5, 7, 10")}(i,r.scale);if(null!==(u=r.branching)&&void 0!==u&&null!==(d=u.responseValues)&&void 0!==d&&d.hasOwnProperty(h)){var _=r.branching.responseValues[h];return Number.isInteger(_)?_:_===ne.End?ne.End:n}}return n}return rf.warn("Falling back to next question index due to unexpected branching type"),n}class ry{constructor(e){G(this,"getNextSurveyStep",rb),this.instance=e,this._surveyEventReceiver=null}onRemoteConfig(e){this._decideServerResponse=!!e.surveys,this.loadIfEnabled()}reset(){localStorage.removeItem("lastSeenSurveyDate"),(()=>{for(var e=[],t=0;t<localStorage.length;t++){var i=localStorage.key(t);null!=i&&i.startsWith("seenSurvey_")&&e.push(i)}return e})().forEach(e=>localStorage.removeItem(e))}loadIfEnabled(){var e,t,i,s=null==v||null===(e=v.__PosthogExtensions__)||void 0===e?void 0:e.generateSurveys;this.instance.config.disable_surveys||!this._decideServerResponse||s||(null==this._surveyEventReceiver&&(this._surveyEventReceiver=new rv(this.instance)),null===(t=v.__PosthogExtensions__)||void 0===t||null===(i=t.loadExternalDependency)||void 0===i||i.call(t,this.instance,"surveys",e=>{var t,i;if(e)return rf.error("Could not load surveys script",e);this._surveyManager=null===(t=v.__PosthogExtensions__)||void 0===t||null===(i=t.generateSurveys)||void 0===i?void 0:i.call(t,this.instance)}))}getSurveys(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(this.instance.config.disable_surveys)return e([]);null==this._surveyEventReceiver&&(this._surveyEventReceiver=new rv(this.instance));var i=this.instance.get_property(eR);if(i&&!t)return e(i);this.instance._send_request({url:this.instance.requestRouter.endpointFor("api","/api/surveys/?token=".concat(this.instance.config.token)),method:"GET",callback:t=>{if(200!==t.statusCode||!t.json)return e([]);var i,s,r=t.json.surveys||[],n=r.filter(e=>{var t,i,s,r,n,o,a,l,c,u,d,h;return(null===(t=e.conditions)||void 0===t?void 0:t.events)&&(null===(i=e.conditions)||void 0===i||null===(s=i.events)||void 0===s?void 0:s.values)&&(null===(r=e.conditions)||void 0===r||null===(n=r.events)||void 0===n||null===(o=n.values)||void 0===o?void 0:o.length)>0||(null===(a=e.conditions)||void 0===a?void 0:a.actions)&&(null===(l=e.conditions)||void 0===l||null===(c=l.actions)||void 0===c?void 0:c.values)&&(null===(u=e.conditions)||void 0===u||null===(d=u.actions)||void 0===d||null===(h=d.values)||void 0===h?void 0:h.length)>0});return n.length>0&&(null===(s=this._surveyEventReceiver)||void 0===s||s.register(n)),null===(i=this.instance.persistence)||void 0===i||i.register({[eR]:r}),e(r)}})}getActiveMatchingSurveys(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];this.getSurveys(t=>{var i,s=t.filter(e=>!(!e.start_date||e.end_date)).filter(e=>{if(!e.conditions)return!0;var t,i,s,r,n=null===(t=e.conditions)||void 0===t||!t.url||rm[null!==(i=null===(s=e.conditions)||void 0===s?void 0:s.urlMatchType)&&void 0!==i?i:"icontains"](e.conditions.url),o=null===(r=e.conditions)||void 0===r||!r.selector||(null==u?void 0:u.querySelector(e.conditions.selector));return n&&o}),r=null===(i=this._surveyEventReceiver)||void 0===i?void 0:i.getSurveys();return e(s.filter(e=>{if(!(e.linked_flag_key||e.targeting_flag_key||e.internal_targeting_flag_key||null!==(t=e.feature_flag_keys)&&void 0!==t&&t.length))return!0;var t,i,s,n,o,a,l,c,u,d,h,_=!e.linked_flag_key||this.instance.featureFlags.isFeatureEnabled(e.linked_flag_key),p=!e.targeting_flag_key||this.instance.featureFlags.isFeatureEnabled(e.targeting_flag_key),g=(null===(i=e.conditions)||void 0===i?void 0:i.events)&&(null===(s=e.conditions)||void 0===s||null===(n=s.events)||void 0===n?void 0:n.values)&&(null===(o=e.conditions)||void 0===o||null===(a=o.events)||void 0===a?void 0:a.values.length)>0,v=(null===(l=e.conditions)||void 0===l?void 0:l.actions)&&(null===(c=e.conditions)||void 0===c||null===(u=c.actions)||void 0===u?void 0:u.values)&&(null===(d=e.conditions)||void 0===d||null===(h=d.actions)||void 0===h?void 0:h.values.length)>0,f=!g&&!v||(null==r?void 0:r.includes(e.id)),m=this._canActivateRepeatedly(e),b=!(e.internal_targeting_flag_key&&!m)||this.instance.featureFlags.isFeatureEnabled(e.internal_targeting_flag_key),y=this.checkFlags(e);return _&&p&&b&&f&&y}))},t)}checkFlags(e){var t;return null===(t=e.feature_flag_keys)||void 0===t||!t.length||e.feature_flag_keys.every(e=>{var{key:t,value:i}=e;return!t||!i||this.instance.featureFlags.isFeatureEnabled(i)})}_canActivateRepeatedly(e){var t;return A(null===(t=v.__PosthogExtensions__)||void 0===t?void 0:t.canActivateRepeatedly)?(rf.warn("init was not called"),!1):v.__PosthogExtensions__.canActivateRepeatedly(e)}canRenderSurvey(e){A(this._surveyManager)?rf.warn("init was not called"):this.getSurveys(t=>{var i=t.filter(t=>t.id===e)[0];this._surveyManager.canRenderSurvey(i)})}renderSurvey(e,t){A(this._surveyManager)?rf.warn("init was not called"):this.getSurveys(i=>{var s=i.filter(t=>t.id===e)[0];this._surveyManager.renderSurvey(s,null==u?void 0:u.querySelector(t))})}}var rw=U("[RateLimiter]");class rS{constructor(e){var t,i;G(this,"serverLimits",{}),G(this,"lastEventRateLimited",!1),G(this,"checkForLimiting",e=>{var t=e.text;if(t&&t.length)try{(JSON.parse(t).quota_limited||[]).forEach(e=>{rw.info("".concat(e||"events"," is quota limited.")),this.serverLimits[e]=(new Date).getTime()+6e4})}catch(e){return void rw.warn('could not rate limit - continuing. Error: "'.concat(null==e?void 0:e.message,'"'),{text:t})}}),this.instance=e,this.captureEventsPerSecond=(null===(t=e.config.rate_limiting)||void 0===t?void 0:t.events_per_second)||10,this.captureEventsBurstLimit=Math.max((null===(i=e.config.rate_limiting)||void 0===i?void 0:i.events_burst_limit)||10*this.captureEventsPerSecond,this.captureEventsPerSecond),this.lastEventRateLimited=this.clientRateLimitContext(!0).isRateLimited}clientRateLimitContext(){var e,t,i,s=arguments.length>0&&void 0!==arguments[0]&&arguments[0],r=(new Date).getTime(),n=null!==(e=null===(t=this.instance.persistence)||void 0===t?void 0:t.get_property(eA))&&void 0!==e?e:{tokens:this.captureEventsBurstLimit,last:r};n.tokens+=(r-n.last)/1e3*this.captureEventsPerSecond,n.last=r,n.tokens>this.captureEventsBurstLimit&&(n.tokens=this.captureEventsBurstLimit);var o=n.tokens<1;return o||s||(n.tokens=Math.max(0,n.tokens-1)),!o||this.lastEventRateLimited||s||this.instance.capture("$$client_ingestion_warning",{$$client_ingestion_warning_message:"posthog-js client rate limited. Config is set to ".concat(this.captureEventsPerSecond," events per second and ").concat(this.captureEventsBurstLimit," events burst limit.")},{skip_client_rate_limiting:!0}),this.lastEventRateLimited=o,null===(i=this.instance.persistence)||void 0===i||i.set_property(eA,n),{isRateLimited:o,remainingTokens:n.tokens}}isServerRateLimited(e){var t=this.serverLimits[e||"events"]||!1;return!1!==t&&(new Date).getTime()<t}}var rE=e=>{var t=null==e?void 0:e.config;return V({initialPathName:(null==d?void 0:d.pathname)||"",referringDomain:t9.referringDomain()},t9.campaignParams({customTrackedParams:null==t?void 0:t.custom_campaign_params,maskPersonalDataProperties:null==t?void 0:t.mask_personal_data_properties,customPersonalDataProperties:null==t?void 0:t.custom_personal_data_properties}))};class rk{constructor(e,t,i,s){G(this,"_onSessionIdCallback",e=>{var t=this._getStoredProps();if(!t||t.sessionId!==e){var i={sessionId:e,props:this._sessionSourceParamGenerator(this.instance)};this._persistence.register({[eL]:i})}}),this.instance=e,this._sessionIdManager=t,this._persistence=i,this._sessionSourceParamGenerator=s||rE,this._sessionIdManager.onSessionId(this._onSessionIdCallback)}_getStoredProps(){return this._persistence.props[eL]}getSessionProps(){var e,t=null===(e=this._getStoredProps())||void 0===e?void 0:e.props;return t?{$client_session_initial_referring_host:t.referringDomain,$client_session_initial_pathname:t.initialPathName,$client_session_initial_utm_source:t.utm_source,$client_session_initial_utm_campaign:t.utm_campaign,$client_session_initial_utm_medium:t.utm_medium,$client_session_initial_utm_content:t.utm_content,$client_session_initial_utm_term:t.utm_term}:{}}}var rx=["ahrefsbot","ahrefssiteaudit","applebot","baiduspider","better uptime bot","bingbot","bingpreview","bot.htm","bot.php","crawler","deepscan","duckduckbot","facebookexternal","facebookcatalog","gptbot","http://yandex.com/bots","hubspot","ia_archiver","linkedinbot","mj12bot","msnbot","nessus","petalbot","pinterest","prerender","rogerbot","screaming frog","semrushbot","sitebulb","slurp","turnitin","twitterbot","vercelbot","yahoo! slurp","yandexbot","headlesschrome","cypress","Google-HotelAdsVerifier","adsbot-google","apis-google","duplexweb-google","feedfetcher-google","google favicon","google web preview","google-read-aloud","googlebot","googleweblight","mediapartners-google","storebot-google","Bytespider;"],rI=function(e,t){if(!e)return!1;var i=e.toLowerCase();return rx.concat(t||[]).some(e=>{var t=e.toLowerCase();return -1!==i.indexOf(t)})},rC=function(e,t){if(!e)return!1;var i=e.userAgent;if(i&&rI(i,t))return!0;try{var s=null==e?void 0:e.userAgentData;if(null!=s&&s.brands&&s.brands.some(e=>rI(null==e?void 0:e.brand,t)))return!0}catch(e){}return!!e.webdriver};class rP{constructor(){this.clicks=[]}isRageClick(e,t,i){var s=this.clicks[this.clicks.length-1];if(s&&Math.abs(e-s.x)+Math.abs(t-s.y)<30&&i-s.timestamp<1e3){if(this.clicks.push({x:e,y:t,timestamp:i}),3===this.clicks.length)return!0}else this.clicks=[{x:e,y:t,timestamp:i}];return!1}}var rF=U("[Dead Clicks]"),rR=()=>!0,rT=e=>{var t,i=!(null===(t=e.instance.persistence)||void 0===t||!t.get_property(e_)),s=e.instance.config.capture_dead_clicks;return D(s)?s:i};class r${get lazyLoadedDeadClicksAutocapture(){return this._lazyLoadedDeadClicksAutocapture}constructor(e,t,i){this.instance=e,this.isEnabled=t,this.onCapture=i,this.startIfEnabled()}onRemoteConfig(e){this.instance.persistence&&this.instance.persistence.register({[e_]:null==e?void 0:e.captureDeadClicks}),this.startIfEnabled()}startIfEnabled(){this.isEnabled(this)&&this.loadScript(()=>{this.start()})}loadScript(e){var t,i,s;null!==(t=v.__PosthogExtensions__)&&void 0!==t&&t.initDeadClicksAutocapture&&e(),null===(i=v.__PosthogExtensions__)||void 0===i||null===(s=i.loadExternalDependency)||void 0===s||s.call(i,this.instance,"dead-clicks-autocapture",t=>{t?rF.error("failed to load script",t):e()})}start(){var e;if(u){if(!this._lazyLoadedDeadClicksAutocapture&&null!==(e=v.__PosthogExtensions__)&&void 0!==e&&e.initDeadClicksAutocapture){var t=F(this.instance.config.capture_dead_clicks)?this.instance.config.capture_dead_clicks:{};t.__onCapture=this.onCapture,this._lazyLoadedDeadClicksAutocapture=v.__PosthogExtensions__.initDeadClicksAutocapture(this.instance,t),this._lazyLoadedDeadClicksAutocapture.start(u),rF.info("starting...")}}else rF.error("`document` not found. Cannot start.")}stop(){this._lazyLoadedDeadClicksAutocapture&&(this._lazyLoadedDeadClicksAutocapture.stop(),this._lazyLoadedDeadClicksAutocapture=void 0,rF.info("stopping..."))}}var rO=U("[Heatmaps]");function rL(e){return F(e)&&"clientX"in e&&"clientY"in e&&M(e.clientX)&&M(e.clientY)}class rA{constructor(e){var t;G(this,"rageclicks",new rP),G(this,"_enabledServerSide",!1),G(this,"_initialized",!1),G(this,"_flushInterval",null),this.instance=e,this._enabledServerSide=!(null===(t=this.instance.persistence)||void 0===t||!t.props[eu]),null==r||r.addEventListener("beforeunload",()=>{this.flush()})}get flushIntervalMilliseconds(){var e=5e3;return F(this.instance.config.capture_heatmaps)&&this.instance.config.capture_heatmaps.flush_interval_milliseconds&&(e=this.instance.config.capture_heatmaps.flush_interval_milliseconds),e}get isEnabled(){return T(this.instance.config.capture_heatmaps)?T(this.instance.config.enable_heatmaps)?this._enabledServerSide:this.instance.config.enable_heatmaps:!1!==this.instance.config.capture_heatmaps}startIfEnabled(){if(this.isEnabled)this._initialized||(rO.info("starting..."),this._setupListeners(),this._flushInterval=setInterval(this.flush.bind(this),this.flushIntervalMilliseconds));else{var e,t;clearInterval(null!==(e=this._flushInterval)&&void 0!==e?e:void 0),null===(t=this.deadClicksCapture)||void 0===t||t.stop(),this.getAndClearBuffer()}}onRemoteConfig(e){var t=!!e.heatmaps;this.instance.persistence&&this.instance.persistence.register({[eu]:t}),this._enabledServerSide=t,this.startIfEnabled()}getAndClearBuffer(){var e=this.buffer;return this.buffer=void 0,e}_onDeadClick(e){this._onClick(e.originalEvent,"deadclick")}_setupListeners(){r&&u&&(er(u,"click",e=>this._onClick(e||(null==r?void 0:r.event)),!1,!0),er(u,"mousemove",e=>this._onMouseMove(e||(null==r?void 0:r.event)),!1,!0),this.deadClicksCapture=new r$(this.instance,rR,this._onDeadClick.bind(this)),this.deadClicksCapture.startIfEnabled(),this._initialized=!0)}_getProperties(e,t){var i=this.instance.scrollManager.scrollY(),s=this.instance.scrollManager.scrollX(),n=this.instance.scrollManager.scrollElement(),o=function(e,t,i){for(var s=e;s&&ia(s)&&!il(s,"body")&&s!==i;){if(y(t,null==r?void 0:r.getComputedStyle(s).position))return!0;s=ib(s)}return!1}(iv(e),["fixed","sticky"],n);return{x:e.clientX+(o?0:s),y:e.clientY+(o?0:i),target_fixed:o,type:t}}_onClick(e){var t,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"click";if(!io(e.target)&&rL(e)){var s=this._getProperties(e,i);null!==(t=this.rageclicks)&&void 0!==t&&t.isRageClick(e.clientX,e.clientY,(new Date).getTime())&&this._capture(V(V({},s),{},{type:"rageclick"})),this._capture(s)}}_onMouseMove(e){!io(e.target)&&rL(e)&&(clearTimeout(this._mouseMoveTimeout),this._mouseMoveTimeout=setTimeout(()=>{this._capture(this._getProperties(e,"mousemove"))},500))}_capture(e){if(r){var t=r.location.href;this.buffer=this.buffer||{},this.buffer[t]||(this.buffer[t]=[]),this.buffer[t].push(e)}}flush(){this.buffer&&!R(this.buffer)&&this.instance.capture("$$heatmap",{$heatmap_data:this.getAndClearBuffer()})}}class rM{constructor(e){G(this,"_updateScrollData",()=>{this.context||(this.context={});var e,t,i,s,r=this.scrollElement(),n=this.scrollY(),o=r?Math.max(0,r.scrollHeight-r.clientHeight):0,a=n+((null==r?void 0:r.clientHeight)||0),l=(null==r?void 0:r.scrollHeight)||0;this.context.lastScrollY=Math.ceil(n),this.context.maxScrollY=Math.max(n,null!==(e=this.context.maxScrollY)&&void 0!==e?e:0),this.context.maxScrollHeight=Math.max(o,null!==(t=this.context.maxScrollHeight)&&void 0!==t?t:0),this.context.lastContentY=a,this.context.maxContentY=Math.max(a,null!==(i=this.context.maxContentY)&&void 0!==i?i:0),this.context.maxContentHeight=Math.max(l,null!==(s=this.context.maxContentHeight)&&void 0!==s?s:0)}),this.instance=e}getContext(){return this.context}resetContext(){var e=this.context;return setTimeout(this._updateScrollData,0),e}startMeasuringScrollPosition(){null==r||r.addEventListener("scroll",this._updateScrollData,!0),null==r||r.addEventListener("scrollend",this._updateScrollData,!0),null==r||r.addEventListener("resize",this._updateScrollData)}scrollElement(){if(!this.instance.config.scroll_root_selector)return null==r?void 0:r.document.documentElement;for(var e of C(this.instance.config.scroll_root_selector)?this.instance.config.scroll_root_selector:[this.instance.config.scroll_root_selector]){var t=null==r?void 0:r.document.querySelector(e);if(t)return t}}scrollY(){if(this.instance.config.scroll_root_selector){var e=this.scrollElement();return e&&e.scrollTop||0}return r&&(r.scrollY||r.pageYOffset||r.document.documentElement.scrollTop)||0}scrollX(){if(this.instance.config.scroll_root_selector){var e=this.scrollElement();return e&&e.scrollLeft||0}return r&&(r.scrollX||r.pageXOffset||r.document.documentElement.scrollLeft)||0}}var rD=U("[AutoCapture]");function rN(e,t){return t.length>e?t.slice(0,e)+"...":t}class rq{constructor(e){G(this,"_initialized",!1),G(this,"_isDisabledServerSide",null),G(this,"rageclicks",new rP),G(this,"_elementsChainAsString",!1),this.instance=e,this._elementSelectors=null}get config(){var e,t,i=F(this.instance.config.autocapture)?this.instance.config.autocapture:{};return i.url_allowlist=null===(e=i.url_allowlist)||void 0===e?void 0:e.map(e=>new RegExp(e)),i.url_ignorelist=null===(t=i.url_ignorelist)||void 0===t?void 0:t.map(e=>new RegExp(e)),i}_addDomEventHandlers(){if(this.isBrowserSupported()){if(r&&u){var e=e=>{e=e||(null==r?void 0:r.event);try{this._captureEvent(e)}catch(e){rD.error("Failed to capture event",e)}},t=e=>{e=e||(null==r?void 0:r.event),this._captureEvent(e,m)};er(u,"submit",e,!1,!0),er(u,"change",e,!1,!0),er(u,"click",e,!1,!0),this.config.capture_copied_text&&(er(u,"copy",t,!1,!0),er(u,"cut",t,!1,!0))}}else rD.info("Disabling Automatic Event Collection because this browser is not supported")}startIfEnabled(){this.isEnabled&&!this._initialized&&(this._addDomEventHandlers(),this._initialized=!0)}onRemoteConfig(e){e.elementsChainAsString&&(this._elementsChainAsString=e.elementsChainAsString),this.instance.persistence&&this.instance.persistence.register({[ec]:!!e.autocapture_opt_out}),this._isDisabledServerSide=!!e.autocapture_opt_out,this.startIfEnabled()}setElementSelectors(e){this._elementSelectors=e}getElementSelectors(e){var t,i=[];return null===(t=this._elementSelectors)||void 0===t||t.forEach(t=>{var s=null==u?void 0:u.querySelectorAll(t);null==s||s.forEach(s=>{e===s&&i.push(t)})}),i}get isEnabled(){var e,t,i=null===(e=this.instance.persistence)||void 0===e?void 0:e.props[ec];if(L(this._isDisabledServerSide)&&!D(i)&&!this.instance.config.advanced_disable_decide)return!1;var s=null!==(t=this._isDisabledServerSide)&&void 0!==t?t:!!i;return!!this.instance.config.autocapture&&!s}_captureEvent(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"$autocapture";if(this.isEnabled){var i,s=iv(e);ic(s)&&(s=s.parentNode||null),"$autocapture"===t&&"click"===e.type&&e instanceof MouseEvent&&this.instance.config.rageclick&&null!==(i=this.rageclicks)&&void 0!==i&&i.isRageClick(e.clientX,e.clientY,(new Date).getTime())&&this._captureEvent(e,"$rageclick");var n=t===m;if(s&&function(e,t){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0,s=arguments.length>3?arguments[3]:void 0,n=arguments.length>4?arguments[4]:void 0;if(!r||!e||il(e,"html")||!ia(e)||null!=i&&i.url_allowlist&&!ih(i.url_allowlist)||null!=i&&i.url_ignorelist&&ih(i.url_ignorelist))return!1;if(null!=i&&i.dom_event_allowlist){var o=i.dom_event_allowlist;if(o&&!o.some(e=>t.type===e))return!1}for(var a=!1,l=[e],c=!0,u=e;u.parentNode&&!il(u,"body");)if(iu(u.parentNode))l.push(u.parentNode.host),u=u.parentNode.host;else{if(!(c=ib(u)))break;if(s||im.indexOf(c.tagName.toLowerCase())>-1)a=!0;else{var d=r.getComputedStyle(c);d&&"pointer"===d.getPropertyValue("cursor")&&(a=!0)}l.push(c),u=c}if(!function(e,t){var i=null==t?void 0:t.element_allowlist;if(T(i))return!0;var s=function(e){if(i.some(t=>e.tagName.toLowerCase()===t))return{v:!0}};for(var r of e){var n=s(r);if("object"==typeof n)return n.v}return!1}(l,i)||!function(e,t){var i=null==t?void 0:t.css_selector_allowlist;if(T(i))return!0;var s=function(e){if(i.some(t=>e.matches(t)))return{v:!0}};for(var r of e){var n=s(r);if("object"==typeof n)return n.v}return!1}(l,i))return!1;var h=r.getComputedStyle(e);if(h&&"pointer"===h.getPropertyValue("cursor")&&"click"===t.type)return!0;var _=e.tagName.toLowerCase();switch(_){case"html":return!1;case"form":return(n||["submit"]).indexOf(t.type)>=0;case"input":case"select":case"textarea":return(n||["change","click"]).indexOf(t.type)>=0;default:return a?(n||["click"]).indexOf(t.type)>=0:(n||["click"]).indexOf(t.type)>=0&&(im.indexOf(_)>-1||"true"===e.getAttribute("contenteditable"))}}(s,e,this.config,n,n?["copy","cut"]:void 0)){var{props:o,explicitNoCapture:a}=function(e,t){for(var i,s,{e:n,maskAllElementAttributes:o,maskAllText:a,elementAttributeIgnoreList:l,elementsChainAsString:c}=t,u=[e],d=e;d.parentNode&&!il(d,"body");)iu(d.parentNode)?(u.push(d.parentNode.host),d=d.parentNode.host):(u.push(d.parentNode),d=d.parentNode);var h,_=[],p={},g=!1,v=!1;if(X(u,e=>{var t=iy(e);"a"===e.tagName.toLowerCase()&&(g=e.getAttribute("href"),g=t&&g&&iP(g)&&g),y(i_(e),"ph-no-capture")&&(v=!0),_.push(function(e,t,i,s){var r=e.tagName.toLowerCase(),n={tag_name:r};im.indexOf(r)>-1&&!i&&("a"===r.toLowerCase()||"button"===r.toLowerCase()?n.$el_text=rN(1024,iF(e)):n.$el_text=rN(1024,ig(e)));var o=i_(e);o.length>0&&(n.classes=o.filter(function(e){return""!==e})),X(e.attributes,function(i){var r;if((!iw(e)||-1!==["name","id","class","aria-label"].indexOf(i.name))&&(null==s||!s.includes(i.name))&&!t&&iP(i.value)&&(!$(r=i.name)||"_ngcontent"!==r.substring(0,10)&&"_nghost"!==r.substring(0,7))){var o=i.value;"class"===i.name&&(o=id(o).join(" ")),n["attr__"+i.name]=rN(1024,o)}});for(var a=1,l=1,c=e;c=function(e){if(e.previousElementSibling)return e.previousElementSibling;var t=e;do t=t.previousSibling;while(t&&!ia(t));return t}(c);)a++,c.tagName===e.tagName&&l++;return n.nth_child=a,n.nth_of_type=l,n}(e,o,a,l)),Q(p,function(e){if(!iy(e))return{};var t={};return X(e.attributes,function(e){if(e.name&&0===e.name.indexOf("data-ph-capture-attribute")){var i=e.name.replace("data-ph-capture-attribute-",""),s=e.value;i&&s&&iP(s)&&(t[i]=s)}}),t}(e))}),v)return{props:{},explicitNoCapture:v};if(a||("a"===e.tagName.toLowerCase()||"button"===e.tagName.toLowerCase()?_[0].$el_text=iF(e):_[0].$el_text=ig(e)),g){_[0].attr__href=g;var f,m,b=null===(f=tn(g))||void 0===f?void 0:f.host,w=null==r||null===(m=r.location)||void 0===m?void 0:m.host;b&&w&&b!==w&&(h=g)}return{props:Q({$event_type:n.type,$ce_version:1},c?{}:{$elements:_},{$elements_chain:_.map(e=>{var t,i,s,r={text:null===(i=e.$el_text)||void 0===i?void 0:i.slice(0,400),tag_name:e.tag_name,href:null===(s=e.attr__href)||void 0===s?void 0:s.slice(0,2048),attr_class:(t=e.attr__class)?C(t)?t:id(t):void 0,attr_id:e.attr__id,nth_child:e.nth_child,nth_of_type:e.nth_of_type,attributes:{}};return ee(e).filter(e=>{var[t]=e;return 0===t.indexOf("attr__")}).forEach(e=>{var[t,i]=e;return r.attributes[t]=i}),r}).map(e=>{var t,i,s="";if(e.tag_name&&(s+=e.tag_name),e.attr_class)for(var r of(e.attr_class.sort(),e.attr_class))s+=".".concat(r.replace(/"/g,""));var n=V(V(V(V({},e.text?{text:e.text}:{}),{},{"nth-child":null!==(t=e.nth_child)&&void 0!==t?t:0,"nth-of-type":null!==(i=e.nth_of_type)&&void 0!==i?i:0},e.href?{href:e.href}:{}),e.attr_id?{attr_id:e.attr_id}:{}),e.attributes),o={};return ee(n).sort((e,t)=>{var[i]=e,[s]=t;return i.localeCompare(s)}).forEach(e=>{var[t,i]=e;return o[iR(t.toString())]=iR(i.toString())}),s+=":",s+=ee(n).map(e=>{var[t,i]=e;return"".concat(t,'="').concat(i,'"')}).join("")}).join(";")},null!==(i=_[0])&&void 0!==i&&i.$el_text?{$el_text:null===(s=_[0])||void 0===s?void 0:s.$el_text}:{},h&&"click"===n.type?{$external_click_url:h}:{},p)}}(s,{e:e,maskAllElementAttributes:this.instance.config.mask_all_element_attributes,maskAllText:this.instance.config.mask_all_text,elementAttributeIgnoreList:this.config.element_attribute_ignorelist,elementsChainAsString:this._elementsChainAsString});if(a)return!1;var l=this.getElementSelectors(s);if(l&&l.length>0&&(o.$element_selectors=l),t===m){var c,u=ip(null==r||null===(c=r.getSelection())||void 0===c?void 0:c.toString()),d=e.type||"clipboard";if(!u)return!1;o.$selected_content=u,o.$copy_type=d}return this.instance.capture(t,o),!0}}}isBrowserSupported(){return P(null==u?void 0:u.querySelectorAll)}}var rB=U("[TracingHeaders]");class rH{constructor(e){G(this,"_restoreXHRPatch",void 0),G(this,"_restoreFetchPatch",void 0),G(this,"_startCapturing",()=>{var e,t,i,s;T(this._restoreXHRPatch)&&(null===(e=v.__PosthogExtensions__)||void 0===e||null===(t=e.tracingHeadersPatchFns)||void 0===t||t._patchXHR(this.instance.sessionManager)),T(this._restoreFetchPatch)&&(null===(i=v.__PosthogExtensions__)||void 0===i||null===(s=i.tracingHeadersPatchFns)||void 0===s||s._patchFetch(this.instance.sessionManager))}),this.instance=e}_loadScript(e){var t,i,s;null!==(t=v.__PosthogExtensions__)&&void 0!==t&&t.tracingHeadersPatchFns&&e(),null===(i=v.__PosthogExtensions__)||void 0===i||null===(s=i.loadExternalDependency)||void 0===s||s.call(i,this.instance,"tracing-headers",t=>{if(t)return rB.error("failed to load script",t);e()})}startIfEnabledOrStop(){var e,t;this.instance.config.__add_tracing_headers?this._loadScript(this._startCapturing):(null===(e=this._restoreXHRPatch)||void 0===e||e.call(this),null===(t=this._restoreFetchPatch)||void 0===t||t.call(this),this._restoreXHRPatch=void 0,this._restoreFetchPatch=void 0)}}!function(e){e[e.PENDING=-1]="PENDING",e[e.DENIED=0]="DENIED",e[e.GRANTED=1]="GRANTED"}(nt||(nt={}));class rU{constructor(e){this.instance=e}get config(){return this.instance.config}get consent(){return this.getDnt()?nt.DENIED:this.storedConsent}isOptedOut(){return this.consent===nt.DENIED||this.consent===nt.PENDING&&this.config.opt_out_capturing_by_default}isOptedIn(){return!this.isOptedOut()}optInOut(e){this.storage.set(this.storageKey,+!!e,this.config.cookie_expiration,this.config.cross_subdomain_cookie,this.config.secure_cookie)}reset(){this.storage.remove(this.storageKey,this.config.cross_subdomain_cookie)}get storageKey(){var{token:e,opt_out_capturing_cookie_prefix:t}=this.instance.config;return(t||"__ph_opt_in_out_")+e}get storedConsent(){var e=this.storage.get(this.storageKey);return"1"===e?nt.GRANTED:"0"===e?nt.DENIED:nt.PENDING}get storage(){if(!this._storage){var e=this.config.opt_out_capturing_persistence_type;this._storage="localStorage"===e?e4:e6;var t="localStorage"===e?e6:e4;t.get(this.storageKey)&&(this._storage.get(this.storageKey)||this.optInOut("1"===t.get(this.storageKey)),t.remove(this.storageKey,this.config.cross_subdomain_cookie))}return this._storage}getDnt(){return!!this.config.respect_dnt&&!!en([null==c?void 0:c.doNotTrack,null==c?void 0:c.msDoNotTrack,v.doNotTrack],e=>y([!0,1,"1","yes"],e))}}var rz=U("[ExceptionAutocapture]");class rj{constructor(e){var t;G(this,"originalOnUnhandledRejectionHandler",void 0),G(this,"startCapturing",()=>{var e,t,i,s;if(r&&this.isEnabled&&!this.hasHandlers&&!this.isCapturing){var n=null===(e=v.__PosthogExtensions__)||void 0===e||null===(t=e.errorWrappingFunctions)||void 0===t?void 0:t.wrapOnError,o=null===(i=v.__PosthogExtensions__)||void 0===i||null===(s=i.errorWrappingFunctions)||void 0===s?void 0:s.wrapUnhandledRejection;if(n&&o)try{this.unwrapOnError=n(this.captureException.bind(this)),this.unwrapUnhandledRejection=o(this.captureException.bind(this))}catch(e){rz.error("failed to start",e),this.stopCapturing()}else rz.error("failed to load error wrapping functions - cannot start")}}),this.instance=e,this.remoteEnabled=!(null===(t=this.instance.persistence)||void 0===t||!t.props[ed]),this.startIfEnabled()}get isEnabled(){var e;return null!==(e=this.remoteEnabled)&&void 0!==e&&e}get isCapturing(){var e;return!(null==r||null===(e=r.onerror)||void 0===e||!e.__POSTHOG_INSTRUMENTED__)}get hasHandlers(){return this.originalOnUnhandledRejectionHandler||this.unwrapOnError}startIfEnabled(){this.isEnabled&&!this.isCapturing&&(rz.info("enabled, starting..."),this.loadScript(this.startCapturing))}loadScript(e){var t,i;this.hasHandlers&&e(),null===(t=v.__PosthogExtensions__)||void 0===t||null===(i=t.loadExternalDependency)||void 0===i||i.call(t,this.instance,"exception-autocapture",t=>{if(t)return rz.error("failed to load script",t);e()})}stopCapturing(){var e,t;null===(e=this.unwrapOnError)||void 0===e||e.call(this),null===(t=this.unwrapUnhandledRejection)||void 0===t||t.call(this)}onRemoteConfig(e){var t=e.autocaptureExceptions;this.remoteEnabled=!!t,this.instance.persistence&&this.instance.persistence.register({[ed]:this.remoteEnabled}),this.startIfEnabled()}captureException(e){var t=this.instance.requestRouter.endpointFor("ui");e.$exception_personURL="".concat(t,"/project/").concat(this.instance.config.token,"/person/").concat(this.instance.get_distinct_id()),this.instance.exceptions.sendExceptionEvent(e)}}var rW=U("[Web Vitals]");class rV{constructor(e){var t;G(this,"_enabledServerSide",!1),G(this,"_initialized",!1),G(this,"buffer",{url:void 0,metrics:[],firstMetricTimestamp:void 0}),G(this,"_flushToCapture",()=>{clearTimeout(this._delayedFlushTimer),0!==this.buffer.metrics.length&&(this.instance.capture("$web_vitals",this.buffer.metrics.reduce((e,t)=>V(V({},e),{},{["$web_vitals_".concat(t.name,"_event")]:V({},t),["$web_vitals_".concat(t.name,"_value")]:t.value}),{})),this.buffer={url:void 0,metrics:[],firstMetricTimestamp:void 0})}),G(this,"_addToBuffer",e=>{var t,i=null===(t=this.instance.sessionManager)||void 0===t?void 0:t.checkAndGetSessionAndWindowId(!0);if(T(i))rW.error("Could not read session ID. Dropping metrics!");else{this.buffer=this.buffer||{url:void 0,metrics:[],firstMetricTimestamp:void 0};var s=this._currentURL();T(s)||(A(null==e?void 0:e.name)||A(null==e?void 0:e.value)?rW.error("Invalid metric received",e):this._maxAllowedValue&&e.value>=this._maxAllowedValue?rW.error("Ignoring metric with value >= "+this._maxAllowedValue,e):(this.buffer.url!==s&&(this._flushToCapture(),this._delayedFlushTimer=setTimeout(this._flushToCapture,this.flushToCaptureTimeoutMs)),T(this.buffer.url)&&(this.buffer.url=s),this.buffer.firstMetricTimestamp=T(this.buffer.firstMetricTimestamp)?Date.now():this.buffer.firstMetricTimestamp,e.attribution&&e.attribution.interactionTargetElement&&(e.attribution.interactionTargetElement=void 0),this.buffer.metrics.push(V(V({},e),{},{$current_url:s,$session_id:i.sessionId,$window_id:i.windowId,timestamp:Date.now()})),this.buffer.metrics.length===this.allowedMetrics.length&&this._flushToCapture()))}}),G(this,"_startCapturing",()=>{var e,t,i,s,r=v.__PosthogExtensions__;T(r)||T(r.postHogWebVitalsCallbacks)||({onLCP:e,onCLS:t,onFCP:i,onINP:s}=r.postHogWebVitalsCallbacks),e&&t&&i&&s?(this.allowedMetrics.indexOf("LCP")>-1&&e(this._addToBuffer.bind(this)),this.allowedMetrics.indexOf("CLS")>-1&&t(this._addToBuffer.bind(this)),this.allowedMetrics.indexOf("FCP")>-1&&i(this._addToBuffer.bind(this)),this.allowedMetrics.indexOf("INP")>-1&&s(this._addToBuffer.bind(this)),this._initialized=!0):rW.error("web vitals callbacks not loaded - not starting")}),this.instance=e,this._enabledServerSide=!(null===(t=this.instance.persistence)||void 0===t||!t.props[eh]),this.startIfEnabled()}get allowedMetrics(){var e,t,i=F(this.instance.config.capture_performance)?null===(e=this.instance.config.capture_performance)||void 0===e?void 0:e.web_vitals_allowed_metrics:void 0;return T(i)?(null===(t=this.instance.persistence)||void 0===t?void 0:t.props[ep])||["CLS","FCP","INP","LCP"]:i}get flushToCaptureTimeoutMs(){return(F(this.instance.config.capture_performance)?this.instance.config.capture_performance.web_vitals_delayed_flush_ms:void 0)||5e3}get _maxAllowedValue(){var e=F(this.instance.config.capture_performance)&&M(this.instance.config.capture_performance.__web_vitals_max_value)?this.instance.config.capture_performance.__web_vitals_max_value:9e5;return 0<e&&e<=6e4?9e5:e}get isEnabled(){var e=F(this.instance.config.capture_performance)?this.instance.config.capture_performance.web_vitals:void 0;return D(e)?e:this._enabledServerSide}startIfEnabled(){this.isEnabled&&!this._initialized&&(rW.info("enabled, starting..."),this.loadScript(this._startCapturing))}onRemoteConfig(e){var t=F(e.capturePerformance)&&!!e.capturePerformance.web_vitals,i=F(e.capturePerformance)?e.capturePerformance.web_vitals_allowed_metrics:void 0;this.instance.persistence&&(this.instance.persistence.register({[eh]:t}),this.instance.persistence.register({[ep]:i})),this._enabledServerSide=t,this.startIfEnabled()}loadScript(e){var t,i,s;null!==(t=v.__PosthogExtensions__)&&void 0!==t&&t.postHogWebVitalsCallbacks&&e(),null===(i=v.__PosthogExtensions__)||void 0===i||null===(s=i.loadExternalDependency)||void 0===s||s.call(i,this.instance,"web-vitals",t=>{t?rW.error("failed to load script",t):e()})}_currentURL(){var e=r?r.location.href:void 0;return e||rW.error("Could not determine current URL"),e}}var rG={icontains:(e,t)=>!!r&&t.href.toLowerCase().indexOf(e.toLowerCase())>-1,not_icontains:(e,t)=>!!r&&-1===t.href.toLowerCase().indexOf(e.toLowerCase()),regex:(e,t)=>!!r&&to(t.href,e),not_regex:(e,t)=>!!r&&!to(t.href,e),exact:(e,t)=>t.href===e,is_not:(e,t)=>t.href!==e};class rJ{constructor(e){var t=this;G(this,"getWebExperimentsAndEvaluateDisplayLogic",function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];t.getWebExperiments(e=>{rJ.logInfo("retrieved web experiments from the server"),t._flagToExperiments=new Map,e.forEach(e=>{if(e.feature_flag_key){t._flagToExperiments&&(rJ.logInfo("setting flag key ",e.feature_flag_key," to web experiment ",e),null===(i=t._flagToExperiments)||void 0===i||i.set(e.feature_flag_key,e));var i,s=t.instance.getFeatureFlag(e.feature_flag_key);$(s)&&e.variants[s]&&t.applyTransforms(e.name,s,e.variants[s].transforms)}else if(e.variants)for(var r in e.variants){var n=e.variants[r];rJ.matchesTestVariant(n)&&t.applyTransforms(e.name,r,n.transforms)}})},e)}),this.instance=e,this.instance.onFeatureFlags(e=>{this.onFeatureFlags(e)})}onFeatureFlags(e){if(this._is_bot())rJ.logInfo("Refusing to render web experiment since the viewer is a likely bot");else if(!this.instance.config.disable_web_experiments){if(A(this._flagToExperiments))return this._flagToExperiments=new Map,this.loadIfEnabled(),void this.previewWebExperiment();rJ.logInfo("applying feature flags",e),e.forEach(e=>{var t;if(this._flagToExperiments&&null!==(t=this._flagToExperiments)&&void 0!==t&&t.has(e)){var i,s=this.instance.getFeatureFlag(e),r=null===(i=this._flagToExperiments)||void 0===i?void 0:i.get(e);s&&null!=r&&r.variants[s]&&this.applyTransforms(r.name,s,r.variants[s].transforms)}})}}previewWebExperiment(){var e=rJ.getWindowLocation();if(null!=e&&e.search){var t=tl(null==e?void 0:e.search,"__experiment_id"),i=tl(null==e?void 0:e.search,"__experiment_variant");t&&i&&(rJ.logInfo("previewing web experiments ".concat(t," && ").concat(i)),this.getWebExperiments(e=>{this.showPreviewWebExperiment(parseInt(t),i,e)},!1,!0))}}loadIfEnabled(){this.instance.config.disable_web_experiments||this.getWebExperimentsAndEvaluateDisplayLogic()}getWebExperiments(e,t,i){if(this.instance.config.disable_web_experiments&&!i)return e([]);var s=this.instance.get_property("$web_experiments");if(s&&!t)return e(s);this.instance._send_request({url:this.instance.requestRouter.endpointFor("api","/api/web_experiments/?token=".concat(this.instance.config.token)),method:"GET",callback:t=>200===t.statusCode&&t.json?e(t.json.experiments||[]):e([])})}showPreviewWebExperiment(e,t,i){var s=i.filter(t=>t.id===e);s&&s.length>0&&(rJ.logInfo("Previewing web experiment [".concat(s[0].name,"] with variant [").concat(t,"]")),this.applyTransforms(s[0].name,t,s[0].variants[t].transforms))}static matchesTestVariant(e){return!A(e.conditions)&&rJ.matchUrlConditions(e)&&rJ.matchUTMConditions(e)}static matchUrlConditions(e){if(A(e.conditions)||A(null===(t=e.conditions)||void 0===t?void 0:t.url))return!0;var t,i,s,r,n=rJ.getWindowLocation();return!!n&&(null===(i=e.conditions)||void 0===i||!i.url||rG[null!==(s=null===(r=e.conditions)||void 0===r?void 0:r.urlMatchType)&&void 0!==s?s:"icontains"](e.conditions.url,n))}static getWindowLocation(){return null==r?void 0:r.location}static matchUTMConditions(e){if(A(e.conditions)||A(null===(i=e.conditions)||void 0===i?void 0:i.utm))return!0;var t=t9.campaignParams();if(t.utm_source){var i,s,r,n,o,a,l,c,u,d,h,_,p,g,v,f,m,b=null===(s=e.conditions)||void 0===s||null===(r=s.utm)||void 0===r||!r.utm_campaign||(null===(n=e.conditions)||void 0===n||null===(o=n.utm)||void 0===o?void 0:o.utm_campaign)==t.utm_campaign,y=null===(a=e.conditions)||void 0===a||null===(l=a.utm)||void 0===l||!l.utm_source||(null===(c=e.conditions)||void 0===c||null===(u=c.utm)||void 0===u?void 0:u.utm_source)==t.utm_source,w=null===(d=e.conditions)||void 0===d||null===(h=d.utm)||void 0===h||!h.utm_medium||(null===(_=e.conditions)||void 0===_||null===(p=_.utm)||void 0===p?void 0:p.utm_medium)==t.utm_medium,S=null===(g=e.conditions)||void 0===g||null===(v=g.utm)||void 0===v||!v.utm_term||(null===(f=e.conditions)||void 0===f||null===(m=f.utm)||void 0===m?void 0:m.utm_term)==t.utm_term;return b&&w&&S&&y}return!1}static logInfo(e){for(var t=arguments.length,i=Array(t>1?t-1:0),s=1;s<t;s++)i[s-1]=arguments[s];H.info("[WebExperiments] ".concat(e),i)}applyTransforms(e,t,i){this._is_bot()?rJ.logInfo("Refusing to render web experiment since the viewer is a likely bot"):"control"!==t?i.forEach(i=>{if(i.selector){rJ.logInfo("applying transform of variant ".concat(t," for experiment ").concat(e," "),i);var s,r=null===(s=document)||void 0===s?void 0:s.querySelectorAll(i.selector);null==r||r.forEach(e=>{i.attributes&&i.attributes.forEach(t=>{switch(t.name){case"text":e.innerText=t.value;break;case"html":e.innerHTML=t.value;break;case"cssClass":e.className=t.value;break;default:e.setAttribute(t.name,t.value)}}),i.text&&(e.innerText=i.text),i.html&&(e.parentElement?e.parentElement.innerHTML=i.html:e.innerHTML=i.html),i.css&&e.setAttribute("style",i.css)})}}):rJ.logInfo("Control variants leave the page unmodified.")}_is_bot(){return c&&this.instance?rC(c,this.instance.config.custom_blocked_useragents):void 0}}class rY{constructor(e){this.instance=e}sendExceptionEvent(e){this.instance.capture("$exception",e,{_noTruncate:!0,_batchKey:"exceptionEvent"})}}var rK=["$set_once","$set"],rX=U("[SiteApps]");class rQ{constructor(e){this.instance=e,this.bufferedInvocations=[],this.apps={}}get isEnabled(){return!!this.instance.config.opt_in_site_apps}eventCollector(e,t){if(t){var i=this.globalsForEvent(t);this.bufferedInvocations.push(i),this.bufferedInvocations.length>1e3&&(this.bufferedInvocations=this.bufferedInvocations.slice(10))}}get siteAppLoaders(){var e,t;return null===(e=v._POSTHOG_REMOTE_CONFIG)||void 0===e||null===(t=e[this.instance.config.token])||void 0===t?void 0:t.siteApps}init(){if(this.isEnabled){var e=this.instance._addCaptureHook(this.eventCollector.bind(this));this.stopBuffering=()=>{e(),this.bufferedInvocations=[],this.stopBuffering=void 0}}}globalsForEvent(e){if(!e)throw Error("Event payload is required");var t,i,s,r,n,o,a,l={},c=this.instance.get_property("$groups")||[];for(var[u,d]of Object.entries(this.instance.get_property("$stored_group_properties")||{}))l[u]={id:c[u],type:u,properties:d};var{$set_once:h,$set:_}=e;return{event:V(V({},J(e,rK)),{},{properties:V(V(V({},e.properties),_?{$set:V(V({},null!==(t=null===(i=e.properties)||void 0===i?void 0:i.$set)&&void 0!==t?t:{}),_)}:{}),h?{$set_once:V(V({},null!==(s=null===(r=e.properties)||void 0===r?void 0:r.$set_once)&&void 0!==s?s:{}),h)}:{}),elements_chain:null!==(n=null===(o=e.properties)||void 0===o?void 0:o.$elements_chain)&&void 0!==n?n:"",distinct_id:null===(a=e.properties)||void 0===a?void 0:a.distinct_id}),person:{properties:this.instance.get_property("$stored_person_properties")},groups:l}}setupSiteApp(e){var t={id:e.id,loaded:!1,errored:!1};this.apps[e.id]=t;var i=i=>{var s;for(var r of(this.apps[e.id].errored=!i,this.apps[e.id].loaded=!0,rX.info("Site app with id ".concat(e.id," ").concat(i?"loaded":"errored")),i&&this.bufferedInvocations.length&&(rX.info("Processing ".concat(this.bufferedInvocations.length," events for site app with id ").concat(e.id)),this.bufferedInvocations.forEach(e=>{var i;return null===(i=t.processEvent)||void 0===i?void 0:i.call(t,e)})),Object.values(this.apps)))if(!r.loaded)return;null===(s=this.stopBuffering)||void 0===s||s.call(this)};try{var{processEvent:s}=e.init({posthog:this.instance,callback:e=>{i(e)}});s&&(t.processEvent=s)}catch(t){rX.error("Error while initializing PostHog app with config id ".concat(e.id),t),i(!1)}}onCapturedEvent(e){if(0!==Object.keys(this.apps).length){var t,i=this.globalsForEvent(e);for(var s of Object.values(this.apps))try{null===(t=s.processEvent)||void 0===t||t.call(s,i)}catch(t){rX.error("Error while processing event ".concat(e.event," for site app ").concat(s.id),t)}}}onRemoteConfig(e){var t,i,s,r=this;if(null!==(t=this.siteAppLoaders)&&void 0!==t&&t.length){if(!this.isEnabled)return void rX.error('PostHog site apps are disabled. Enable the "opt_in_site_apps" config to proceed.');for(var n of this.siteAppLoaders)this.setupSiteApp(n);this.instance.on("eventCaptured",e=>this.onCapturedEvent(e))}else if(null===(i=this.stopBuffering)||void 0===i||i.call(this),null!==(s=e.siteApps)&&void 0!==s&&s.length){if(this.isEnabled){var o=function(e,t){var i,s;v["__$$ph_site_app_".concat(e)]=r.instance,null===(i=v.__PosthogExtensions__)||void 0===i||null===(s=i.loadSiteApp)||void 0===s||s.call(i,r.instance,t,t=>{if(t)return rX.error("Error while initializing PostHog app with config id ".concat(e),t)})};for(var{id:a,url:l}of e.siteApps)o(a,l)}else rX.error('PostHog site apps are disabled. Enable the "opt_in_site_apps" config to proceed.')}}}function rZ(e,t,i){return sL({distinct_id:e,userPropertiesToSet:t,userPropertiesToSetOnce:i})}var r0={},r1=()=>{},r2="posthog",r3=!sT&&-1===(null==g?void 0:g.indexOf("MSIE"))&&-1===(null==g?void 0:g.indexOf("Mozilla")),r5=()=>{var e,t,i;return{api_host:"https://us.i.posthog.com",ui_host:null,token:"",autocapture:!0,rageclick:!0,cross_subdomain_cookie:!!$(i=null==(t=null==u?void 0:u.location)?void 0:t.hostname)&&"herokuapp.com"!==i.split(".").slice(-2).join("."),persistence:"localStorage+cookie",persistence_name:"",loaded:r1,store_google:!0,custom_campaign_params:[],custom_blocked_useragents:[],save_referrer:!0,capture_pageview:!0,capture_pageleave:"if_capture_pageview",debug:d&&$(null==d?void 0:d.search)&&-1!==d.search.indexOf("__posthog_debug=true")||!1,verbose:!1,cookie_expiration:365,upgrade:!1,disable_session_recording:!1,disable_persistence:!1,disable_web_experiments:!0,disable_surveys:!1,enable_recording_console_log:void 0,secure_cookie:"https:"===(null==r||null===(e=r.location)||void 0===e?void 0:e.protocol),ip:!0,opt_out_capturing_by_default:!1,opt_out_persistence_by_default:!1,opt_out_useragent_filter:!1,opt_out_capturing_persistence_type:"localStorage",opt_out_capturing_cookie_prefix:null,opt_in_site_apps:!1,property_denylist:[],respect_dnt:!1,sanitize_properties:null,request_headers:{},inapp_protocol:"//",inapp_link_new_window:!1,request_batching:!0,properties_string_max_length:65535,session_recording:{},mask_all_element_attributes:!1,mask_all_text:!1,mask_personal_data_properties:!1,custom_personal_data_properties:[],advanced_disable_decide:!1,advanced_disable_feature_flags:!1,advanced_disable_feature_flags_on_first_load:!1,advanced_disable_toolbar_metrics:!1,feature_flag_request_timeout_ms:3e3,on_request_error:e=>{var t="Bad HTTP status: "+e.statusCode+" "+e.text;H.error(t)},get_device_id:e=>e,_onCapture:r1,capture_performance:void 0,name:"posthog",bootstrap:{},disable_compression:!1,session_idle_timeout_seconds:1800,person_profiles:"identified_only",__add_tracing_headers:!1,before_send:void 0}},r6=e=>{var t={};T(e.process_person)||(t.person_profiles=e.process_person),T(e.xhr_headers)||(t.request_headers=e.xhr_headers),T(e.cookie_name)||(t.persistence_name=e.cookie_name),T(e.disable_cookie)||(t.disable_persistence=e.disable_cookie);var i=Q({},t,e);return C(e.property_blacklist)&&(T(e.property_denylist)?i.property_denylist=e.property_blacklist:C(e.property_denylist)?i.property_denylist=[...e.property_blacklist,...e.property_denylist]:H.error("Invalid value for property_denylist config: "+e.property_denylist)),i};class r8{constructor(){G(this,"__forceAllowLocalhost",!1)}get _forceAllowLocalhost(){return this.__forceAllowLocalhost}set _forceAllowLocalhost(e){H.error("WebPerformanceObserver is deprecated and has no impact on network capture. Use `_forceAllowLocalhostNetworkCapture` on `posthog.sessionRecording`"),this.__forceAllowLocalhost=e}}class r4{get decideEndpointWasHit(){var e,t;return null!==(e=null===(t=this.featureFlags)||void 0===t?void 0:t.hasLoadedFlags)&&void 0!==e&&e}constructor(){G(this,"webPerformance",new r8),G(this,"version",f.LIB_VERSION),G(this,"_internalEventEmitter",new rp),this.config=r5(),this.SentryIntegration=sV,this.sentryIntegration=e=>(function(e,t){var i=sW(e,t);return{name:sj,processEvent:e=>i(e)}})(this,e),this.__request_queue=[],this.__loaded=!1,this.analyticsDefaultEndpoint="/e/",this._initialPageviewCaptured=!1,this._initialPersonProfilesConfig=null,this._cachedIdentify=null,this.featureFlags=new eJ(this),this.toolbar=new sP(this),this.scrollManager=new rM(this),this.pageViewManager=new sJ(this),this.surveys=new ry(this),this.experiments=new rJ(this),this.exceptions=new rY(this),this.rateLimiter=new rS(this),this.requestRouter=new sz(this),this.consent=new rU(this),this.people={set:(e,t,i)=>{var s=$(e)?{[e]:t}:e;this.setPersonProperties(s),null==i||i({})},set_once:(e,t,i)=>{var s=$(e)?{[e]:t}:e;this.setPersonProperties(void 0,s),null==i||i({})}},this.on("eventCaptured",e=>H.info('send "'.concat(null==e?void 0:e.event,'"'),e))}init(e,t,i){if(i&&i!==r2){var s,r=null!==(s=r0[i])&&void 0!==s?s:new r4;return r._init(e,t,i),r0[i]=r,r0[r2][i]=r,r}return this._init(e,t,i)}_init(e){var t,i,n,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=arguments.length>2?arguments[2]:void 0;if(T(e)||O(e))return H.critical("PostHog was initialized without a token. This likely indicates a misconfiguration. Please check the first argument passed to posthog.init()"),this;if(this.__loaded)return H.warn("You have already initialized PostHog! Re-initializing is a no-op"),this;this.__loaded=!0,this.config={},this._triggered_notifs=[],o.person_profiles&&(this._initialPersonProfilesConfig=o.person_profiles),this.set_config(Q({},r5(),r6(o),{name:a,token:e})),this.config.on_xhr_error&&H.error("on_xhr_error is deprecated. Use on_request_error instead"),this.compression=o.disable_compression?void 0:s.GZipJS,this.persistence=new it(this.config),this.sessionPersistence="sessionStorage"===this.config.persistence||"memory"===this.config.persistence?this.persistence:new it(V(V({},this.config),{},{persistence:"sessionStorage"}));var l=V({},this.persistence.props),c=V({},this.sessionPersistence.props);if(this._requestQueue=new sF(e=>this._send_retriable_request(e)),this._retryQueue=new sN(this),this.__request_queue=[],this.config.__preview_experimental_cookieless_mode||(this.sessionManager=new sH(this),this.sessionPropsManager=new rk(this,this.sessionManager,this.persistence)),new rH(this).startIfEnabledOrStop(),this.siteApps=new rQ(this),null===(t=this.siteApps)||void 0===t||t.init(),this.config.__preview_experimental_cookieless_mode||(this.sessionRecording=new sw(this),this.sessionRecording.startIfEnabledOrStop()),this.config.disable_scroll_properties||this.scrollManager.startMeasuringScrollPosition(),this.autocapture=new rq(this),this.autocapture.startIfEnabled(),this.surveys.loadIfEnabled(),this.heatmaps=new rA(this),this.heatmaps.startIfEnabled(),this.webVitalsAutocapture=new rV(this),this.exceptionObserver=new rj(this),this.exceptionObserver.startIfEnabled(),this.deadClicksAutocapture=new r$(this,rT),this.deadClicksAutocapture.startIfEnabled(),f.DEBUG=f.DEBUG||this.config.debug,f.DEBUG&&H.info("Starting in debug mode",{this:this,config:o,thisC:V({},this.config),p:l,s:c}),this._sync_opt_out_with_persistence(),void 0!==(null===(i=o.bootstrap)||void 0===i?void 0:i.distinctID)){var u,d,h=this.config.get_device_id(e1()),_=null!==(u=o.bootstrap)&&void 0!==u&&u.isIdentifiedID?h:o.bootstrap.distinctID;this.persistence.set_property(eO,null!==(d=o.bootstrap)&&void 0!==d&&d.isIdentifiedID?"identified":"anonymous"),this.register({distinct_id:o.bootstrap.distinctID,$device_id:_})}if(this._hasBootstrappedFeatureFlags()){var p,g,v=Object.keys((null===(p=o.bootstrap)||void 0===p?void 0:p.featureFlags)||{}).filter(e=>{var t,i;return!(null===(t=o.bootstrap)||void 0===t||null===(i=t.featureFlags)||void 0===i||!i[e])}).reduce((e,t)=>{var i,s;return e[t]=(null===(i=o.bootstrap)||void 0===i||null===(s=i.featureFlags)||void 0===s?void 0:s[t])||!1,e},{}),m=Object.keys((null===(g=o.bootstrap)||void 0===g?void 0:g.featureFlagPayloads)||{}).filter(e=>v[e]).reduce((e,t)=>{var i,s,r,n;return null!==(i=o.bootstrap)&&void 0!==i&&null!==(s=i.featureFlagPayloads)&&void 0!==s&&s[t]&&(e[t]=null===(r=o.bootstrap)||void 0===r||null===(n=r.featureFlagPayloads)||void 0===n?void 0:n[t]),e},{});this.featureFlags.receivedFeatureFlags({featureFlags:v,featureFlagPayloads:m})}if(this.config.__preview_experimental_cookieless_mode)this.register_once({distinct_id:eH,$device_id:null},"");else if(!this.get_distinct_id()){var b=this.config.get_device_id(e1());this.register_once({distinct_id:b,$device_id:b},""),this.persistence.set_property(eO,"anonymous")}return null==r||null===(n=r.addEventListener)||void 0===n||n.call(r,"onpagehide"in self?"pagehide":"unload",this._handle_unload.bind(this)),this.toolbar.maybeLoadToolbar(),o.segment?function(e,t){var i=e.config.segment;if(!i)return t();!function(e,t){var i=e.config.segment;if(!i)return t();var s=i=>{var s=()=>i.anonymousId()||e1();e.config.get_device_id=s,i.id()&&(e.register({distinct_id:i.id(),$device_id:s()}),e.persistence.set_property(eO,"identified")),t()},r=i.user();"then"in r&&P(r.then)?r.then(e=>s(e)):s(r)}(e,()=>{var s;i.register((Promise&&Promise.resolve||sG.warn("This browser does not have Promise support, and can not use the segment integration"),s=(t,i)=>{if(!i)return t;t.event.userId||t.event.anonymousId===e.get_distinct_id()||(sG.info("No userId set, resetting PostHog"),e.reset()),t.event.userId&&t.event.userId!==e.get_distinct_id()&&(sG.info("UserId set, identifying with PostHog"),e.identify(t.event.userId));var s,r=e._calculate_event_properties(i,null!==(s=t.event.properties)&&void 0!==s?s:{},new Date);return t.event.properties=Object.assign({},r,t.event.properties),t},{name:"PostHog JS",type:"enrichment",version:"1.0.0",isLoaded:()=>!0,load:()=>Promise.resolve(),track:e=>s(e,e.event.event),page:e=>s(e,"$pageview"),identify:e=>s(e,"$identify"),screen:e=>s(e,"$screen")})).then(()=>{t()})})}(this,()=>this._loaded()):this._loaded(),P(this.config._onCapture)&&this.config._onCapture!==r1&&(H.warn("onCapture is deprecated. Please use `before_send` instead"),this.on("eventCaptured",e=>this.config._onCapture(e.event,e))),this}_onRemoteConfig(e){var t,i,r,n,o,a,l,c,d;if(!u||!u.body)return H.info("document not ready yet, trying again in 500 milliseconds..."),void setTimeout(()=>{this._onRemoteConfig(e)},500);this.compression=void 0,e.supportedCompression&&!this.config.disable_compression&&(this.compression=y(e.supportedCompression,s.GZipJS)?s.GZipJS:y(e.supportedCompression,s.Base64)?s.Base64:void 0),null!==(t=e.analytics)&&void 0!==t&&t.endpoint&&(this.analyticsDefaultEndpoint=e.analytics.endpoint),this.set_config({person_profiles:this._initialPersonProfilesConfig?this._initialPersonProfilesConfig:"identified_only"}),null===(i=this.siteApps)||void 0===i||i.onRemoteConfig(e),null===(r=this.sessionRecording)||void 0===r||r.onRemoteConfig(e),null===(n=this.autocapture)||void 0===n||n.onRemoteConfig(e),null===(o=this.heatmaps)||void 0===o||o.onRemoteConfig(e),null===(a=this.surveys)||void 0===a||a.onRemoteConfig(e),null===(l=this.webVitalsAutocapture)||void 0===l||l.onRemoteConfig(e),null===(c=this.exceptionObserver)||void 0===c||c.onRemoteConfig(e),null===(d=this.deadClicksAutocapture)||void 0===d||d.onRemoteConfig(e)}_loaded(){try{this.config.loaded(this)}catch(e){H.critical("`loaded` function failed",e)}this._start_queue_if_opted_in(),this.config.capture_pageview&&setTimeout(()=>{this.consent.isOptedIn()&&this._captureInitialPageview()},1),new sE(this).load(),this.featureFlags.decide()}_start_queue_if_opted_in(){var e;this.has_opted_out_capturing()||this.config.request_batching&&(null===(e=this._requestQueue)||void 0===e||e.enable())}_dom_loaded(){this.has_opted_out_capturing()||K(this.__request_queue,e=>this._send_retriable_request(e)),this.__request_queue=[],this._start_queue_if_opted_in()}_handle_unload(){var e,t;this.config.request_batching?(this._shouldCapturePageleave()&&this.capture("$pageleave"),null===(e=this._requestQueue)||void 0===e||e.unload(),null===(t=this._retryQueue)||void 0===t||t.unload()):this._shouldCapturePageleave()&&this.capture("$pageleave",null,{transport:"sendBeacon"})}_send_request(e){this.__loaded&&(r3?this.__request_queue.push(e):this.rateLimiter.isServerRateLimited(e.batchKey)||(e.transport=e.transport||this.config.api_transport,e.url=sO(e.url,{ip:+!!this.config.ip}),e.headers=V({},this.config.request_headers),e.compression="best-available"===e.compression?this.compression:e.compression,e.fetchOptions=e.fetchOptions||this.config.fetch_options,(e=>{var t,i,s,r=V({},e);r.timeout=r.timeout||6e4,r.url=sO(r.url,{_:(new Date).getTime().toString(),ver:f.LIB_VERSION,compression:r.compression});var n=null!==(t=r.transport)&&void 0!==t?t:"fetch",o=null!==(i=null===(s=en(sM,e=>e.transport===n))||void 0===s?void 0:s.method)&&void 0!==i?i:sM[0].method;if(!o)throw Error("No available transport method");o(r)})(V(V({},e),{},{callback:t=>{var i,s,r;this.rateLimiter.checkForLimiting(t),t.statusCode>=400&&(null===(s=(r=this.config).on_request_error)||void 0===s||s.call(r,t)),null===(i=e.callback)||void 0===i||i.call(e,t)}}))))}_send_retriable_request(e){this._retryQueue?this._retryQueue.retriableRequest(e):this._send_request(e)}_execute_array(e){var t,i=[],s=[],r=[];K(e,e=>{e&&(C(t=e[0])?r.push(e):P(e)?e.call(this):C(e)&&"alias"===t?i.push(e):C(e)&&-1!==t.indexOf("capture")&&P(this[t])?r.push(e):s.push(e))});var n=function(e,t){K(e,function(e){if(C(e[0])){var i=t;X(e,function(e){i=i[e[0]].apply(i,e.slice(1))})}else this[e[0]].apply(this,e.slice(1))},t)};n(i,this),n(s,this),n(r,this)}_hasBootstrappedFeatureFlags(){var e,t;return(null===(e=this.config.bootstrap)||void 0===e?void 0:e.featureFlags)&&Object.keys(null===(t=this.config.bootstrap)||void 0===t?void 0:t.featureFlags).length>0||!1}push(e){this._execute_array([e])}capture(e,t,i){var s;if(this.__loaded&&this.persistence&&this.sessionPersistence&&this._requestQueue){if(!this.consent.isOptedOut()){if(!T(e)&&$(e)){if(this.config.opt_out_useragent_filter||!this._is_bot()){var r=null!=i&&i.skip_client_rate_limiting?void 0:this.rateLimiter.clientRateLimitContext();if(null==r||!r.isRateLimited){this.sessionPersistence.update_search_keyword(),this.config.store_google&&this.sessionPersistence.update_campaign_params(),this.config.save_referrer&&this.sessionPersistence.update_referrer_info(),(this.config.store_google||this.config.save_referrer)&&this.persistence.set_initial_person_info();var n,o,a,l,c=new Date,u=(null==i?void 0:i.timestamp)||c,d=e1(),h={uuid:d,event:e,properties:this._calculate_event_properties(e,t||{},u,d)};r&&(h.properties.$lib_rate_limit_remaining_tokens=r.remainingTokens),(null==i?void 0:i.$set)&&(h.$set=null==i?void 0:i.$set);var _=this._calculate_set_once_properties(null==i?void 0:i.$set_once);_&&(h.$set_once=_),(n=h,o=null!=i&&i._noTruncate?null:this.config.properties_string_max_length,a=e=>$(e)&&!L(o)?e.slice(0,o):e,l=new Set,h=function e(t,i){var s;return t!==Object(t)?a?a(t,i):t:l.has(t)?void 0:(l.add(t),C(t)?(s=[],K(t,t=>{s.push(e(t))})):(s={},X(t,(t,i)=>{l.has(t)||(s[i]=e(t,i))})),s)}(n)).timestamp=u,T(null==i?void 0:i.timestamp)||(h.properties.$event_time_override_provided=!0,h.properties.$event_time_override_system_time=c);var p=V(V({},h.properties.$set),h.$set);if(R(p)||this.setPersonPropertiesForFlags(p),!A(this.config.before_send)){var g=this._runBeforeSend(h);if(!g)return;h=g}this._internalEventEmitter.emit("eventCaptured",h);var v={method:"POST",url:null!==(s=null==i?void 0:i._url)&&void 0!==s?s:this.requestRouter.endpointFor("api",this.analyticsDefaultEndpoint),data:h,compression:"best-available",batchKey:null==i?void 0:i._batchKey};return!this.config.request_batching||i&&(null==i||!i._batchKey)||null!=i&&i.send_instantly?this._send_retriable_request(v):this._requestQueue.enqueue(v),h}H.critical("This capture call is ignored due to client rate limiting.")}}else H.error("No event name provided to posthog.capture")}}else H.uninitializedWarning("posthog.capture")}_addCaptureHook(e){return this.on("eventCaptured",t=>e(t.event,t))}_calculate_event_properties(e,t,i,s){if(i=i||new Date,!this.persistence||!this.sessionPersistence)return t;var r=this.persistence.remove_event_timer(e),n=V({},t);if(n.token=this.config.token,this.config.__preview_experimental_cookieless_mode&&(n.$cookieless_mode=!0),"$snapshot"===e){var o=V(V({},this.persistence.properties()),this.sessionPersistence.properties());return n.distinct_id=o.distinct_id,(!$(n.distinct_id)&&!M(n.distinct_id)||O(n.distinct_id))&&H.error("Invalid distinct_id for replay event. This indicates a bug in your implementation"),n}var a,l=t9.properties({maskPersonalDataProperties:this.config.mask_personal_data_properties,customPersonalDataProperties:this.config.custom_personal_data_properties});if(this.sessionManager){var{sessionId:c,windowId:d}=this.sessionManager.checkAndGetSessionAndWindowId();n.$session_id=c,n.$window_id=d}if(this.sessionRecording&&(n.$recording_status=this.sessionRecording.status),this.requestRouter.region===sq.CUSTOM&&(n.$lib_custom_api_host=this.config.api_host),this.sessionPropsManager&&this.config.__preview_send_client_session_params&&("$pageview"===e||"$pageleave"===e||"$autocapture"===e)){var h=this.sessionPropsManager.getSessionProps();n=Q(n,h)}if(a="$pageview"===e?this.pageViewManager.doPageView(i,s):"$pageleave"===e?this.pageViewManager.doPageLeave(i):this.pageViewManager.doEvent(),n=Q(n,a),"$pageview"===e&&u&&(n.title=u.title),!T(r)){var _=i.getTime()-r;n.$duration=parseFloat((_/1e3).toFixed(3))}g&&this.config.opt_out_useragent_filter&&(n.$browser_type=this._is_bot()?"bot":"browser"),(n=Q({},l,this.persistence.properties(),this.sessionPersistence.properties(),n)).$is_identified=this._isIdentified(),C(this.config.property_denylist)?X(this.config.property_denylist,function(e){delete n[e]}):H.error("Invalid value for property_denylist config: "+this.config.property_denylist+" or property_blacklist config: "+this.config.property_blacklist);var p=this.config.sanitize_properties;p&&(H.error("sanitize_properties is deprecated. Use before_send instead"),n=p(n,e));var v=this._hasPersonProcessing();return n.$process_person_profile=v,v&&this._requirePersonProcessing("_calculate_event_properties"),n}_calculate_set_once_properties(e){if(!this.persistence||!this._hasPersonProcessing())return e;var t=Q({},this.persistence.get_initial_props(),e||{}),i=this.config.sanitize_properties;return i&&(H.error("sanitize_properties is deprecated. Use before_send instead"),t=i(t,"$set_once")),R(t)?void 0:t}register(e,t){var i;null===(i=this.persistence)||void 0===i||i.register(e,t)}register_once(e,t,i){var s;null===(s=this.persistence)||void 0===s||s.register_once(e,t,i)}register_for_session(e){var t;null===(t=this.sessionPersistence)||void 0===t||t.register(e)}unregister(e){var t;null===(t=this.persistence)||void 0===t||t.unregister(e)}unregister_for_session(e){var t;null===(t=this.sessionPersistence)||void 0===t||t.unregister(e)}_register_single(e,t){this.register({[e]:t})}getFeatureFlag(e,t){return this.featureFlags.getFeatureFlag(e,t)}getFeatureFlagPayload(e){var t=this.featureFlags.getFeatureFlagPayload(e);try{return JSON.parse(t)}catch(e){return t}}isFeatureEnabled(e,t){return this.featureFlags.isFeatureEnabled(e,t)}reloadFeatureFlags(){this.featureFlags.reloadFeatureFlags()}updateEarlyAccessFeatureEnrollment(e,t){this.featureFlags.updateEarlyAccessFeatureEnrollment(e,t)}getEarlyAccessFeatures(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return this.featureFlags.getEarlyAccessFeatures(e,t)}on(e,t){return this._internalEventEmitter.on(e,t)}onFeatureFlags(e){return this.featureFlags.onFeatureFlags(e)}onSessionId(e){var t,i;return null!==(t=null===(i=this.sessionManager)||void 0===i?void 0:i.onSessionId(e))&&void 0!==t?t:()=>{}}getSurveys(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];this.surveys.getSurveys(e,t)}getActiveMatchingSurveys(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];this.surveys.getActiveMatchingSurveys(e,t)}renderSurvey(e,t){this.surveys.renderSurvey(e,t)}canRenderSurvey(e){this.surveys.canRenderSurvey(e)}getNextSurveyStep(e,t,i){return this.surveys.getNextSurveyStep(e,t,i)}identify(e,t,i){if(!this.__loaded||!this.persistence)return H.uninitializedWarning("posthog.identify");if(M(e)&&(e=e.toString(),H.warn("The first argument to posthog.identify was a number, but it should be a string. It has been converted to a string.")),e){if(["distinct_id","distinctid"].includes(e.toLowerCase()))H.critical('The string "'.concat(e,'" was set in posthog.identify which indicates an error. This ID should be unique to the user and not a hardcoded string.'));else if(this._requirePersonProcessing("posthog.identify")){var s=this.get_distinct_id();this.register({$user_id:e}),this.get_property("$device_id")||this.register_once({$had_persisted_distinct_id:!0,$device_id:s},""),e!==s&&e!==this.get_property(ea)&&(this.unregister(ea),this.register({distinct_id:e}));var r="anonymous"===(this.persistence.get_property(eO)||"anonymous");e!==s&&r?(this.persistence.set_property(eO,"identified"),this.setPersonPropertiesForFlags(t||{},!1),this.capture("$identify",{distinct_id:e,$anon_distinct_id:s},{$set:t||{},$set_once:i||{}}),this.featureFlags.setAnonymousDistinctId(s),this._cachedIdentify=rZ(e,t,i)):(t||i)&&(this._cachedIdentify!==rZ(e,t,i)?(this.setPersonProperties(t,i),this._cachedIdentify=rZ(e,t,i)):H.info("A duplicate posthog.identify call was made with the same properties. It has been ignored.")),e!==s&&(this.reloadFeatureFlags(),this.unregister(e$))}}else H.error("Unique user id has not been set in posthog.identify")}setPersonProperties(e,t){(e||t)&&this._requirePersonProcessing("posthog.setPersonProperties")&&(this.setPersonPropertiesForFlags(e||{}),this.capture("$set",{$set:e||{},$set_once:t||{}}))}group(e,t,i){if(e&&t){if(this._requirePersonProcessing("posthog.group")){var s=this.getGroups();s[e]!==t&&this.resetGroupPropertiesForFlags(e),this.register({$groups:V(V({},s),{},{[e]:t})}),i&&(this.capture("$groupidentify",{$group_type:e,$group_key:t,$group_set:i}),this.setGroupPropertiesForFlags({[e]:i})),s[e]===t||i||this.reloadFeatureFlags()}}else H.error("posthog.group requires a group type and group key")}resetGroups(){this.register({$groups:{}}),this.resetGroupPropertiesForFlags(),this.reloadFeatureFlags()}setPersonPropertiesForFlags(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];this.featureFlags.setPersonPropertiesForFlags(e,t)}resetPersonPropertiesForFlags(){this.featureFlags.resetPersonPropertiesForFlags()}setGroupPropertiesForFlags(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];this._requirePersonProcessing("posthog.setGroupPropertiesForFlags")&&this.featureFlags.setGroupPropertiesForFlags(e,t)}resetGroupPropertiesForFlags(e){this.featureFlags.resetGroupPropertiesForFlags(e)}reset(e){if(H.info("reset"),!this.__loaded)return H.uninitializedWarning("posthog.reset");var t,i,s,r,n,o=this.get_property("$device_id");if(this.consent.reset(),null===(t=this.persistence)||void 0===t||t.clear(),null===(i=this.sessionPersistence)||void 0===i||i.clear(),null===(s=this.surveys)||void 0===s||s.reset(),null===(r=this.persistence)||void 0===r||r.set_property(eO,"anonymous"),null===(n=this.sessionManager)||void 0===n||n.resetSessionId(),this._cachedIdentify=null,this.config.__preview_experimental_cookieless_mode)this.register_once({distinct_id:eH,$device_id:null},"");else{var a=this.config.get_device_id(e1());this.register_once({distinct_id:a,$device_id:e?a:o},"")}}get_distinct_id(){return this.get_property("distinct_id")}getGroups(){return this.get_property("$groups")||{}}get_session_id(){var e,t;return null!==(e=null===(t=this.sessionManager)||void 0===t?void 0:t.checkAndGetSessionAndWindowId(!0).sessionId)&&void 0!==e?e:""}get_session_replay_url(e){if(!this.sessionManager)return"";var{sessionId:t,sessionStartTimestamp:i}=this.sessionManager.checkAndGetSessionAndWindowId(!0),s=this.requestRouter.endpointFor("ui","/project/".concat(this.config.token,"/replay/").concat(t));if(null!=e&&e.withTimestamp&&i){var r,n=null!==(r=e.timestampLookBack)&&void 0!==r?r:10;if(!i)return s;var o=Math.max(Math.floor(((new Date).getTime()-i)/1e3)-n,0);s+="?t=".concat(o)}return s}alias(e,t){return e===this.get_property(eo)?(H.critical("Attempting to create alias for existing People user - aborting."),-2):this._requirePersonProcessing("posthog.alias")?(T(t)&&(t=this.get_distinct_id()),e!==t?(this._register_single(ea,e),this.capture("$create_alias",{alias:e,distinct_id:t})):(H.warn("alias matches current distinct_id - skipping api call."),this.identify(e),-1)):void 0}set_config(e){var t,i,s,r,n=V({},this.config);F(e)&&(Q(this.config,r6(e)),null===(t=this.persistence)||void 0===t||t.update_config(this.config,n),this.sessionPersistence="sessionStorage"===this.config.persistence||"memory"===this.config.persistence?this.persistence:new it(V(V({},this.config),{},{persistence:"sessionStorage"})),e4.is_supported()&&"true"===e4.get("ph_debug")&&(this.config.debug=!0),this.config.debug&&(f.DEBUG=!0,H.info("set_config",{config:e,oldConfig:n,newConfig:V({},this.config)})),null===(i=this.sessionRecording)||void 0===i||i.startIfEnabledOrStop(),null===(s=this.autocapture)||void 0===s||s.startIfEnabled(),null===(r=this.heatmaps)||void 0===r||r.startIfEnabled(),this.surveys.loadIfEnabled(),this._sync_opt_out_with_persistence())}startSessionRecording(e){var t,i,s,r,n,o=!0===e,a={sampling:o||!(null==e||!e.sampling),linked_flag:o||!(null==e||!e.linked_flag),url_trigger:o||!(null==e||!e.url_trigger),event_trigger:o||!(null==e||!e.event_trigger)};Object.values(a).some(Boolean)&&(null===(t=this.sessionManager)||void 0===t||t.checkAndGetSessionAndWindowId(),a.sampling&&(null===(i=this.sessionRecording)||void 0===i||i.overrideSampling()),a.linked_flag&&(null===(s=this.sessionRecording)||void 0===s||s.overrideLinkedFlag()),a.url_trigger&&(null===(r=this.sessionRecording)||void 0===r||r.overrideTrigger("url")),a.event_trigger&&(null===(n=this.sessionRecording)||void 0===n||n.overrideTrigger("event"))),this.set_config({disable_session_recording:!1})}stopSessionRecording(){this.set_config({disable_session_recording:!0})}sessionRecordingStarted(){var e;return!(null===(e=this.sessionRecording)||void 0===e||!e.started)}captureException(e,t){var i,s=Error("PostHog syntheticException"),r=P(null===(i=v.__PosthogExtensions__)||void 0===i?void 0:i.parseErrorAsProperties)?v.__PosthogExtensions__.parseErrorAsProperties([e.message,void 0,void 0,void 0,e],{syntheticException:s}):V({$exception_level:"error",$exception_list:[{type:e.name,value:e.message,mechanism:{handled:!0,synthetic:!1}}]},t);this.exceptions.sendExceptionEvent(r)}loadToolbar(e){return this.toolbar.loadToolbar(e)}get_property(e){var t;return null===(t=this.persistence)||void 0===t?void 0:t.props[e]}getSessionProperty(e){var t;return null===(t=this.sessionPersistence)||void 0===t?void 0:t.props[e]}toString(){var e,t=null!==(e=this.config.name)&&void 0!==e?e:r2;return t!==r2&&(t=r2+"."+t),t}_isIdentified(){var e,t;return"identified"===(null===(e=this.persistence)||void 0===e?void 0:e.get_property(eO))||"identified"===(null===(t=this.sessionPersistence)||void 0===t?void 0:t.get_property(eO))}_hasPersonProcessing(){var e,t,i,s;return!("never"===this.config.person_profiles||"identified_only"===this.config.person_profiles&&!this._isIdentified()&&R(this.getGroups())&&(null===(e=this.persistence)||void 0===e||null===(t=e.props)||void 0===t||!t[ea])&&(null===(i=this.persistence)||void 0===i||null===(s=i.props)||void 0===s||!s[eq]))}_shouldCapturePageleave(){return!0===this.config.capture_pageleave||"if_capture_pageview"===this.config.capture_pageleave&&this.config.capture_pageview}createPersonProfile(){this._hasPersonProcessing()||this._requirePersonProcessing("posthog.createPersonProfile")&&this.setPersonProperties({},{})}_requirePersonProcessing(e){return"never"===this.config.person_profiles?(H.error(e+' was called, but process_person is set to "never". This call will be ignored.'),!1):(this._register_single(eq,!0),!0)}_sync_opt_out_with_persistence(){var e,t,i,s,r=this.consent.isOptedOut(),n=this.config.opt_out_persistence_by_default,o=this.config.disable_persistence||r&&!!n;(null===(e=this.persistence)||void 0===e?void 0:e.disabled)!==o&&(null===(i=this.persistence)||void 0===i||i.set_disabled(o)),(null===(t=this.sessionPersistence)||void 0===t?void 0:t.disabled)!==o&&(null===(s=this.sessionPersistence)||void 0===s||s.set_disabled(o))}opt_in_capturing(e){var t;this.consent.optInOut(!0),this._sync_opt_out_with_persistence(),(T(null==e?void 0:e.captureEventName)||null!=e&&e.captureEventName)&&this.capture(null!==(t=null==e?void 0:e.captureEventName)&&void 0!==t?t:"$opt_in",null==e?void 0:e.captureProperties,{send_instantly:!0}),this.config.capture_pageview&&this._captureInitialPageview()}opt_out_capturing(){this.consent.optInOut(!1),this._sync_opt_out_with_persistence()}has_opted_in_capturing(){return this.consent.isOptedIn()}has_opted_out_capturing(){return this.consent.isOptedOut()}clear_opt_in_out_capturing(){this.consent.reset(),this._sync_opt_out_with_persistence()}_is_bot(){return c?rC(c,this.config.custom_blocked_useragents):void 0}_captureInitialPageview(){u&&!this._initialPageviewCaptured&&(this._initialPageviewCaptured=!0,this.capture("$pageview",{title:u.title},{send_instantly:!0}))}debug(e){!1===e?(null==r||r.console.log("You've disabled debug mode."),localStorage&&localStorage.removeItem("ph_debug"),this.set_config({debug:!1})):(null==r||r.console.log("You're now in debug mode. All calls to PostHog will be logged in your console.\nYou can disable this with `posthog.debug(false)`."),localStorage&&localStorage.setItem("ph_debug","true"),this.set_config({debug:!0}))}_runBeforeSend(e){if(A(this.config.before_send))return e;var t=C(this.config.before_send)?this.config.before_send:[this.config.before_send],i=e;for(var s of t){if(A(i=s(i))){var r="Event '".concat(e.event,"' was rejected in beforeSend function");return q(e.event)?H.warn("".concat(r,". This can cause unexpected behavior.")):H.info(r),null}i.properties&&!R(i.properties)||H.warn("Event '".concat(e.event,"' has no properties after beforeSend function, this is likely an error."))}return i}getPageViewId(){var e;return null===(e=this.pageViewManager._currentPageview)||void 0===e?void 0:e.pageViewId}}!function(e,t){for(var i=0;i<t.length;i++)e.prototype[t[i]]=ei(e.prototype[t[i]])}(r4,["identify"]);var r7,r9,ne,nt,ni,ns=(ni=r0[r2]=new r4,function(){function e(){e.done||(e.done=!0,r3=!1,X(r0,function(e){e._dom_loaded()}))}null!=u&&u.addEventListener&&("complete"===u.readyState?e():u.addEventListener("DOMContentLoaded",e,!1)),r&&er(r,"load",e,!0)}(),ni)}}]);