### **New Features:**
- **Add device_name in frame table:** Implemented retrieval of device name in the frame table and search query return.
- **Make pipe manager optional:** Users can now choose whether to use the pipe manager, allowing for more flexible configurations.
- **Added e2e test for Windows and Linux:** Introduced end-to-end tests to enhance testing coverage and reliability on both platforms.

### **Improvements:**
- **Bundle Tesseract and FFmpeg for Linux:** Improved installation experience by bundling Tesseract and FFmpeg, enabling users to utilize these tools seamlessly.

### **Fixes:**
- **Fix getting app icons on Linux:** Resolved an issue with retrieving application icons on Linux.
- **Ignore minimized windows during capture:** Fixed the capture function to ignore minimized windows, improving the quality of recordings.

#### **Full Changelog:** [9478a..22fd2](https://github.com/mediar-ai/screenpipe/compare/9478a..22fd2)

