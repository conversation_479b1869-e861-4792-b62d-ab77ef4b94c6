### **New Features:**
- **Added speaker identification UI / API:** Improved user experience with speaker identification feature.
 
### **Improvements:**
- **Refactored code to add retry:** Enhanced reliability by adding retry functionality.
- **Mild edits:** Implemented minor improvements for better performance.
 
### **Fixes:**
- **Fixed audio transcriptions table:** Resolved issue with audio transcriptions table for accurate data processing.
- **Fixed audio search:** Addressed audio search issue for better search functionality.
- **Fixed header and unit test compilation:** Corrected header and unit test compilation issues for smoother operation.

#### **Full Changelog:** [e73cc..41756](https://github.com/mediar-ai/screenpipe/compare/e73cc..41756)

