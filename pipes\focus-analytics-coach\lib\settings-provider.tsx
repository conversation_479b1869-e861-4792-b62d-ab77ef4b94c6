"use client";

import { createContext, use<PERSON>ontext, ReactNode, useEffect, useState } from "react";
import { useSettings as useSettingsApp } from "@/lib/hooks/use-settings";

// Global type declaration
declare global {
  interface Window {
    _screenpipeErrorsSuppressed?: boolean;
  }
}

// Create a context for settings
type SettingsContextType = ReturnType<typeof useSettingsApp> & {
  screenpipeStatus: 'checking' | 'connected' | 'disconnected';
  screenpipeError?: string;
};

const SettingsContext = createContext<SettingsContextType | undefined>(
  undefined,
);

// Check if Screenpipe is running
async function checkScreenpipeConnection(): Promise<{ connected: boolean; error?: string }> {
  try {
    // Try to connect to Screenpipe health endpoint
    const response = await fetch('http://localhost:3030/health', {
      method: 'GET',
      signal: AbortSignal.timeout(3000), // 3 second timeout
    });

    if (response.ok) {
      return { connected: true };
    } else {
      return { connected: false, error: `Screenpipe responded with status ${response.status}` };
    }
  } catch (error) {
    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        return { connected: false, error: 'Connection timeout - Screenpipe may not be running' };
      } else if (error.message.includes('fetch')) {
        return { connected: false, error: 'Cannot connect to Screenpipe - check if it\'s running on port 3030' };
      }
      return { connected: false, error: error.message };
    }
    return { connected: false, error: 'Unknown connection error' };
  }
}

// Provider component
export function SettingsProvider({ children }: { children: ReactNode }) {
  const settingsData = useSettingsApp();
  const [screenpipeStatus, setScreenpipeStatus] = useState<'checking' | 'connected' | 'disconnected'>('checking');
  const [screenpipeError, setScreenpipeError] = useState<string>();

  // Check Screenpipe connection on mount and periodically
  useEffect(() => {
    let mounted = true;
    let intervalId: NodeJS.Timeout;

    const checkConnection = async () => {
      if (!mounted) return;

      const result = await checkScreenpipeConnection();

      if (!mounted) return;

      if (result.connected) {
        setScreenpipeStatus('connected');
        setScreenpipeError(undefined);
        console.log('✅ Screenpipe connection established');
      } else {
        setScreenpipeStatus('disconnected');
        setScreenpipeError(result.error);
        console.warn('⚠️ Screenpipe not available:', result.error);
        console.info('ℹ️ Plugin will run in demo mode with sample data');
      }
    };

    // Initial check
    checkConnection();

    // Check every 30 seconds
    intervalId = setInterval(checkConnection, 30000);

    return () => {
      mounted = false;
      if (intervalId) clearInterval(intervalId);
    };
  }, []);

  // Enhanced error handling for Screenpipe-related errors
  useEffect(() => {
    const originalConsoleError = console.error;
    console.error = function(msg, ...args) {
      // Only suppress errors if we know Screenpipe is disconnected
      if (screenpipeStatus === 'disconnected' && typeof msg === 'string') {
        const screenpipeErrors = [
          'settings stream timeout',
          'failed to fetch settings',
          'ERR_CONNECTION_REFUSED',
          'Settings timeout',
          'NetworkError when attempting to fetch resource',
          'websocket connection error',
          'error streaming vision'
        ];

        if (screenpipeErrors.some(error => msg.includes(error))) {
          // Log once that we're suppressing these errors
          if (!window._screenpipeErrorsSuppressed) {
            console.warn('🔇 Suppressing Screenpipe connection errors (plugin running in demo mode)');
            window._screenpipeErrorsSuppressed = true;
          }
          return;
        }
      }

      // Log all other errors normally
      originalConsoleError.apply(console, [msg, ...args]);
    };

    return () => {
      console.error = originalConsoleError;
    };
  }, [screenpipeStatus]);

  console.log(
    "settings provider initialized with data:",
    settingsData.loading ? "loading..." : "loaded",
    `| screenpipe: ${screenpipeStatus}`
  );

  const enhancedSettingsData = {
    ...settingsData,
    screenpipeStatus,
    screenpipeError,
  };

  return (
    <SettingsContext.Provider value={enhancedSettingsData}>
      {children}
    </SettingsContext.Provider>
  );
}

// Hook to use settings in any component
export function useSettings() {
  const context = useContext(SettingsContext);
  if (context === undefined) {
    throw new Error("useSettings must be used within a SettingsProvider");
  }
  return context;
}
