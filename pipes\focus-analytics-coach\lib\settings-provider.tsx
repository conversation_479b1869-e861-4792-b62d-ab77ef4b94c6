"use client";

import { createContext, useContext, ReactNode, useEffect } from "react";
import { useSettings as useSettingsApp } from "@/lib/hooks/use-settings";

// Create a context for settings
type SettingsContextType = ReturnType<typeof useSettingsApp>;

const SettingsContext = createContext<SettingsContextType | undefined>(
  undefined,
);

// Provider component
export function SettingsProvider({ children }: { children: ReactNode }) {
  const settingsData = useSettingsApp();

  // Suppress specific console errors related to Screenpipe connection issues
  useEffect(() => {
    const originalConsoleError = console.error;
    console.error = function(msg, ...args) {
      // Filter out known Screenpipe connection errors
      if (typeof msg === 'string' &&
         (msg.includes('settings stream timeout') ||
          msg.includes('failed to fetch settings') ||
          msg.includes('ERR_CONNECTION_REFUSED') ||
          msg.includes('Settings timeout') ||
          msg.includes('NetworkError'))) {
        // Suppress these specific errors in production
        return;
      }
      originalConsoleError.apply(console, [msg, ...args]);
    };

    return () => {
      console.error = originalConsoleError;
    };
  }, []);

  console.log(
    "settings provider initialized with data:",
    settingsData.loading ? "loading..." : "loaded",
  );

  return (
    <SettingsContext.Provider value={settingsData}>
      {children}
    </SettingsContext.Provider>
  );
}

// Hook to use settings in any component
export function useSettings() {
  const context = useContext(SettingsContext);
  if (context === undefined) {
    throw new Error("useSettings must be used within a SettingsProvider");
  }
  return context;
}
