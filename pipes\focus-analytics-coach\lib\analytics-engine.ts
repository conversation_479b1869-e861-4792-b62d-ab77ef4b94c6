import { pipe, type ContentItem } from "@screenpipe/browser";
import {
  FocusSession,
  ContextSwitch,
  ProductivityMetrics,
  AppUsage,
  TaskCategoryType,
  ProductivityLevel,
  AnalyticsConfig
} from "./types";
import { differenceInMinutes, differenceInSeconds } from "date-fns";
import { v4 as uuidv4 } from "uuid";

export class FocusAnalyticsEngine {
  private config: AnalyticsConfig;

  constructor(config: AnalyticsConfig) {
    this.config = config;
  }

  /**
   * Analyze screen activity data to detect focus sessions and context switches
   */
  async analyzeActivity(timeRange: { start: Date; end: Date }): Promise<{
    focusSessions: FocusSession[];
    contextSwitches: ContextSwitch[];
    metrics: ProductivityMetrics;
  }> {
    try {
      // Query Screenpipe for activity data
      const results = await pipe.queryScreenpipe({
        startTime: timeRange.start.toISOString(),
        endTime: timeRange.end.toISOString(),
        contentType: "all",
        limit: 1000,
        includeFrames: false
      });

      if (!results?.data) {
        throw new Error("No data received from Screenpipe");
      }

      // Process the data to extract focus sessions and context switches
      const processedData = this.processActivityData(results.data);
      
      // Calculate productivity metrics
      const metrics = this.calculateProductivityMetrics(
        processedData.focusSessions,
        processedData.contextSwitches,
        timeRange.start
      );

      return {
        focusSessions: processedData.focusSessions,
        contextSwitches: processedData.contextSwitches,
        metrics
      };
    } catch (error) {
      console.error("Error analyzing activity:", error);
      throw error;
    }
  }

  /**
   * Process raw activity data into focus sessions and context switches
   */
  private processActivityData(data: ContentItem[]): {
    focusSessions: FocusSession[];
    contextSwitches: ContextSwitch[];
  } {
    const sessions: FocusSession[] = [];
    const switches: ContextSwitch[] = [];
    let currentSession: Partial<FocusSession> | null = null;
    let lastActivity: { app: string; window: string; timestamp: Date } | null = null;

    // Sort data by timestamp
    const sortedData = data.sort((a, b) => 
      new Date(a.content.timestamp).getTime() - new Date(b.content.timestamp).getTime()
    );

    for (const item of sortedData) {
      const timestamp = new Date(item.content.timestamp);
      let appName = "";
      let windowName = "";

      // Extract app and window info based on content type
      if (item.type === "OCR") {
        appName = item.content.appName || "";
        windowName = item.content.windowName || "";
      } else if (item.type === "UI") {
        appName = item.content.appName || "";
        windowName = item.content.windowName || "";
      }

      if (!appName) continue;

      // Detect context switches
      if (lastActivity && (lastActivity.app !== appName || lastActivity.window !== windowName)) {
        const switchDuration = differenceInSeconds(timestamp, lastActivity.timestamp);
        
        if (switchDuration >= this.config.contextSwitchWindow) {
          switches.push({
            id: uuidv4(),
            timestamp,
            fromApp: lastActivity.app,
            toApp: appName,
            fromWindow: lastActivity.window,
            toWindow: windowName,
            duration: switchDuration,
            switchType: lastActivity.app !== appName ? 'app' : 'window'
          });

          // End current session if context switch indicates distraction
          if (currentSession && this.isDistractingSwitch(lastActivity.app, appName)) {
            this.finalizeSession(currentSession, lastActivity.timestamp, sessions);
            currentSession = null;
          }
        }
      }

      // Start or continue focus session
      if (!currentSession || this.shouldStartNewSession(currentSession, appName, windowName, timestamp)) {
        // Finalize previous session
        if (currentSession) {
          this.finalizeSession(currentSession, timestamp, sessions);
        }

        // Start new session
        currentSession = {
          id: uuidv4(),
          startTime: timestamp,
          appName,
          windowName,
          taskCategory: this.categorizeTask(appName, windowName),
          distractionCount: 0,
          contextSwitches: 0
        };
      }

      // Update current session
      if (currentSession) {
        currentSession.endTime = timestamp;
        
        // Count context switches within session
        if (switches.length > 0 && currentSession.startTime && switches[switches.length - 1].timestamp >= currentSession.startTime) {
          currentSession.contextSwitches = (currentSession.contextSwitches || 0) + 1;
        }
      }

      lastActivity = { app: appName, window: windowName, timestamp };
    }

    // Finalize last session
    if (currentSession && lastActivity) {
      this.finalizeSession(currentSession, lastActivity.timestamp, sessions);
    }

    return { focusSessions: sessions, contextSwitches: switches };
  }

  /**
   * Finalize a focus session with calculated metrics
   */
  private finalizeSession(
    session: Partial<FocusSession>, 
    endTime: Date, 
    sessions: FocusSession[]
  ): void {
    if (!session.startTime || !session.appName) return;

    const duration = differenceInMinutes(endTime, session.startTime);
    
    // Only consider sessions above threshold as focus sessions
    if (duration >= this.config.focusThreshold) {
      const focusScore = this.calculateFocusScore(
        duration,
        session.contextSwitches || 0,
        session.distractionCount || 0
      );

      sessions.push({
        id: session.id!,
        startTime: session.startTime,
        endTime,
        duration,
        appName: session.appName,
        windowName: session.windowName || "",
        taskCategory: session.taskCategory || 'unknown',
        focusScore,
        distractionCount: session.distractionCount || 0,
        contextSwitches: session.contextSwitches || 0,
        productivity: this.getProductivityLevel(focusScore)
      });
    }
  }

  /**
   * Calculate focus score based on session metrics
   */
  private calculateFocusScore(
    duration: number, 
    contextSwitches: number, 
    distractions: number
  ): number {
    let score = 100;
    
    // Penalize for context switches (more switches = lower focus)
    score -= Math.min(contextSwitches * 5, 30);
    
    // Penalize for distractions
    score -= Math.min(distractions * 10, 40);
    
    // Bonus for longer sessions
    if (duration >= this.config.deepWorkMinimum) {
      score += 10;
    }
    
    return Math.max(0, Math.min(100, score));
  }

  /**
   * Categorize task based on app and window name
   */
  private categorizeTask(appName: string, windowName: string): TaskCategoryType {
    const text = `${appName} ${windowName}`.toLowerCase();
    
    // Define categorization rules
    const categories: Record<TaskCategoryType, string[]> = {
      'deep-work': ['vscode', 'intellij', 'xcode', 'sublime', 'vim', 'emacs', 'figma', 'sketch'],
      'communication': ['slack', 'teams', 'discord', 'zoom', 'meet', 'skype', 'mail', 'outlook'],
      'research': ['chrome', 'firefox', 'safari', 'edge', 'browser', 'wikipedia', 'stackoverflow'],
      'creative': ['photoshop', 'illustrator', 'premiere', 'after effects', 'blender', 'canva'],
      'administrative': ['excel', 'sheets', 'word', 'docs', 'powerpoint', 'slides', 'notion'],
      'learning': ['coursera', 'udemy', 'youtube', 'khan academy', 'duolingo', 'anki'],
      'entertainment': ['netflix', 'youtube', 'spotify', 'twitch', 'gaming', 'steam'],
      'social': ['facebook', 'twitter', 'instagram', 'linkedin', 'reddit', 'tiktok'],
      'distraction': ['news', 'shopping', 'amazon', 'ebay', 'social media'],
      'unknown': []
    };

    for (const [category, keywords] of Object.entries(categories)) {
      if (keywords.some(keyword => text.includes(keyword))) {
        return category as TaskCategoryType;
      }
    }

    return 'unknown';
  }

  /**
   * Determine if a context switch indicates distraction
   */
  private isDistractingSwitch(fromApp: string, toApp: string): boolean {
    const distractingApps = ['chrome', 'firefox', 'safari', 'social', 'entertainment'];
    const productiveApps = ['vscode', 'intellij', 'figma', 'notion', 'excel'];
    
    return productiveApps.some(app => fromApp.toLowerCase().includes(app)) &&
           distractingApps.some(app => toApp.toLowerCase().includes(app));
  }

  /**
   * Determine if a new session should be started
   */
  private shouldStartNewSession(
    currentSession: Partial<FocusSession>,
    appName: string,
    _windowName: string,
    timestamp: Date
  ): boolean {
    if (!currentSession.startTime) return true;
    
    // Start new session if app changed significantly
    if (currentSession.appName !== appName) {
      const timeSinceStart = differenceInMinutes(timestamp, currentSession.startTime);
      return timeSinceStart >= this.config.focusThreshold;
    }
    
    return false;
  }

  /**
   * Convert focus score to productivity level
   */
  private getProductivityLevel(focusScore: number): ProductivityLevel {
    if (focusScore >= 90) return 'very-high';
    if (focusScore >= 75) return 'high';
    if (focusScore >= 50) return 'medium';
    if (focusScore >= 25) return 'low';
    return 'very-low';
  }

  /**
   * Calculate comprehensive productivity metrics
   */
  private calculateProductivityMetrics(
    sessions: FocusSession[],
    switches: ContextSwitch[],
    date: Date
  ): ProductivityMetrics {
    const totalFocusTime = sessions.reduce((sum, session) => sum + session.duration, 0);
    const averageFocusScore = sessions.length > 0 
      ? sessions.reduce((sum, session) => sum + session.focusScore, 0) / sessions.length 
      : 0;
    
    const deepWorkSessions = sessions.filter(s => s.duration >= this.config.deepWorkMinimum).length;
    const averageSessionLength = sessions.length > 0 ? totalFocusTime / sessions.length : 0;
    
    // Calculate hourly productivity
    const hourlyProductivity = this.calculateHourlyProductivity(sessions);
    const mostProductiveHour = this.getMostProductiveHour(hourlyProductivity);
    const leastProductiveHour = this.getLeastProductiveHour(hourlyProductivity);
    
    // Calculate app usage statistics
    const appUsage = this.calculateAppUsage(sessions);
    const topProductiveApps = appUsage
      .filter(app => app.productivityScore >= 70)
      .sort((a, b) => b.focusTime - a.focusTime)
      .slice(0, 5);
    
    const topDistractingApps = appUsage
      .filter(app => app.productivityScore < 50)
      .sort((a, b) => b.totalTime - a.totalTime)
      .slice(0, 5);

    return {
      date,
      totalFocusTime,
      totalDistractionTime: 0, // TODO: Calculate from distraction detection
      focusScore: averageFocusScore,
      contextSwitches: switches.length,
      deepWorkSessions,
      averageSessionLength,
      mostProductiveHour,
      leastProductiveHour,
      topProductiveApps,
      topDistractingApps
    };
  }

  private calculateHourlyProductivity(sessions: FocusSession[]): Record<number, number> {
    const hourlyData: Record<number, { totalTime: number; totalScore: number; count: number }> = {};
    
    for (let hour = 0; hour < 24; hour++) {
      hourlyData[hour] = { totalTime: 0, totalScore: 0, count: 0 };
    }
    
    sessions.forEach(session => {
      const hour = session.startTime.getHours();
      hourlyData[hour].totalTime += session.duration;
      hourlyData[hour].totalScore += session.focusScore;
      hourlyData[hour].count += 1;
    });
    
    const productivity: Record<number, number> = {};
    for (let hour = 0; hour < 24; hour++) {
      const data = hourlyData[hour];
      productivity[hour] = data.count > 0 ? data.totalScore / data.count : 0;
    }
    
    return productivity;
  }

  private getMostProductiveHour(hourlyProductivity: Record<number, number>): number {
    return Object.entries(hourlyProductivity)
      .reduce((max, [hour, productivity]) => 
        productivity > hourlyProductivity[max] ? parseInt(hour) : max, 0);
  }

  private getLeastProductiveHour(hourlyProductivity: Record<number, number>): number {
    return Object.entries(hourlyProductivity)
      .reduce((min, [hour, productivity]) => 
        productivity < hourlyProductivity[min] ? parseInt(hour) : min, 0);
  }

  private calculateAppUsage(sessions: FocusSession[]): AppUsage[] {
    const appData: Record<string, {
      totalTime: number;
      focusTime: number;
      sessionCount: number;
      totalScore: number;
    }> = {};

    sessions.forEach(session => {
      if (!appData[session.appName]) {
        appData[session.appName] = {
          totalTime: 0,
          focusTime: 0,
          sessionCount: 0,
          totalScore: 0
        };
      }

      const data = appData[session.appName];
      data.totalTime += session.duration;
      data.sessionCount += 1;
      data.totalScore += session.focusScore;
      
      if (session.focusScore >= 70) {
        data.focusTime += session.duration;
      }
    });

    return Object.entries(appData).map(([appName, data]) => ({
      appName,
      totalTime: data.totalTime,
      focusTime: data.focusTime,
      distractionTime: data.totalTime - data.focusTime,
      sessionCount: data.sessionCount,
      averageSessionLength: data.totalTime / data.sessionCount,
      productivityScore: data.totalScore / data.sessionCount
    }));
  }
}
