---
name: bug report
about: report a bug in screenpipe
title: "[bug] "
labels: bug
assignees: ''

---

**describe the bug**
brief description of the bug.

**system info**
- os: [e.g. macos 10.15.7, windows 10, ubuntu 20.04]
- screenpipe version: [e.g. 1.2.3]

**additional context**
add any other context, screenshots, or error logs here.

- you can find logs in `~/.screenpipe` on macOS/Linux, and `%APPDATA%\.screenpipe` on Windows.
- you can get app logs by right clicking in the app background and clicking the console tab.

