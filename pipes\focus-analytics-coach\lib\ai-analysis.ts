import { pipe, type ContentItem } from "@screenpipe/browser";
import { 
  FocusSession, 
  ProductivityPattern, 
  TaskCategoryType, 
  BreakRecommendation,
  CoachingNotification,
  NotificationType
} from "./types";
import Fuse from "fuse.js";
import { format, startOfWeek, endOfWeek, eachDayOfInterval } from "date-fns";
import { v4 as uuidv4 } from "uuid";

export class AIAnalysisEngine {
  private fuse: Fuse<any>;

  constructor() {
    // Initialize fuzzy search for task categorization
    this.fuse = new Fuse([], {
      keys: ['text', 'appName', 'windowName'],
      threshold: 0.3
    });
  }

  /**
   * Analyze OCR content to detect specific tasks and activities
   */
  async analyzeTaskContent(timeRange: { start: Date; end: Date }): Promise<{
    detectedTasks: Array<{
      id: string;
      timestamp: Date;
      taskType: TaskCategoryType;
      description: string;
      confidence: number;
      appName: string;
      windowName: string;
      ocrText: string;
    }>;
    insights: string[];
  }> {
    try {
      // Query OCR data for task detection
      const results = await pipe.queryScreenpipe({
        startTime: timeRange.start.toISOString(),
        endTime: timeRange.end.toISOString(),
        contentType: "ocr",
        limit: 500,
        includeFrames: false
      });

      if (!results?.data) {
        return { detectedTasks: [], insights: [] };
      }

      const detectedTasks = [];
      const insights = [];

      for (const item of results.data) {
        if (item.type === "OCR") {
          const ocrText = item.content.text || "";
          const appName = item.content.appName || "";
          const windowName = item.content.windowName || "";
          
          // Analyze OCR content for task detection
          const taskAnalysis = this.analyzeOCRForTasks(ocrText, appName, windowName);
          
          if (taskAnalysis.confidence > 0.6) {
            detectedTasks.push({
              id: uuidv4(),
              timestamp: new Date(item.content.timestamp),
              taskType: taskAnalysis.taskType,
              description: taskAnalysis.description,
              confidence: taskAnalysis.confidence,
              appName,
              windowName,
              ocrText: ocrText.substring(0, 200) // Truncate for storage
            });
          }
        }
      }

      // Generate insights from detected tasks
      const taskInsights = this.generateTaskInsights(detectedTasks);
      insights.push(...taskInsights);

      return { detectedTasks, insights };
    } catch (error) {
      console.error("Error analyzing task content:", error);
      return { detectedTasks: [], insights: [] };
    }
  }

  /**
   * Analyze OCR text to determine task type and extract meaningful information
   */
  private analyzeOCRForTasks(ocrText: string, appName: string, windowName: string): {
    taskType: TaskCategoryType;
    description: string;
    confidence: number;
  } {
    const text = ocrText.toLowerCase();
    const app = appName.toLowerCase();
    const window = windowName.toLowerCase();

    // Define task detection patterns
    const patterns = {
      'deep-work': {
        keywords: ['function', 'class', 'import', 'export', 'const', 'let', 'var', 'def', 'public', 'private'],
        apps: ['vscode', 'intellij', 'xcode', 'sublime', 'vim'],
        confidence: 0.9
      },
      'communication': {
        keywords: ['message', 'chat', 'email', 'meeting', 'call', 'video', 'send', 'reply'],
        apps: ['slack', 'teams', 'discord', 'zoom', 'mail'],
        confidence: 0.8
      },
      'research': {
        keywords: ['search', 'google', 'stackoverflow', 'documentation', 'tutorial', 'how to'],
        apps: ['chrome', 'firefox', 'safari', 'edge'],
        confidence: 0.7
      },
      'creative': {
        keywords: ['design', 'layer', 'brush', 'color', 'font', 'canvas', 'artboard'],
        apps: ['photoshop', 'illustrator', 'figma', 'sketch'],
        confidence: 0.8
      },
      'administrative': {
        keywords: ['spreadsheet', 'document', 'presentation', 'table', 'chart', 'formula'],
        apps: ['excel', 'word', 'powerpoint', 'sheets', 'docs'],
        confidence: 0.7
      },
      'learning': {
        keywords: ['course', 'lesson', 'tutorial', 'learn', 'study', 'education'],
        apps: ['coursera', 'udemy', 'khan'],
        confidence: 0.8
      },
      'entertainment': {
        keywords: ['watch', 'video', 'movie', 'music', 'game', 'play'],
        apps: ['netflix', 'youtube', 'spotify', 'steam'],
        confidence: 0.9
      },
      'social': {
        keywords: ['post', 'like', 'share', 'comment', 'follow', 'friend'],
        apps: ['facebook', 'twitter', 'instagram', 'linkedin'],
        confidence: 0.8
      }
    };

    let bestMatch: { taskType: TaskCategoryType; confidence: number; description: string } = {
      taskType: 'unknown',
      confidence: 0,
      description: 'Unknown activity'
    };

    // Check each pattern
    for (const [taskType, pattern] of Object.entries(patterns)) {
      let confidence = 0;
      let matchedKeywords: string[] = [];

      // Check app match
      if (pattern.apps.some(appPattern => app.includes(appPattern))) {
        confidence += 0.4;
      }

      // Check keyword matches
      const keywordMatches = pattern.keywords.filter(keyword => text.includes(keyword));
      if (keywordMatches.length > 0) {
        confidence += (keywordMatches.length / pattern.keywords.length) * 0.6;
        matchedKeywords = keywordMatches;
      }

      // Apply pattern-specific confidence modifier
      confidence *= pattern.confidence;

      if (confidence > bestMatch.confidence) {
        bestMatch = {
          taskType: taskType as TaskCategoryType,
          confidence,
          description: this.generateTaskDescription(taskType as TaskCategoryType, matchedKeywords, appName)
        };
      }
    }

    return bestMatch;
  }

  /**
   * Generate human-readable task description
   */
  private generateTaskDescription(taskType: TaskCategoryType, keywords: string[], appName: string): string {
    const descriptions = {
      'deep-work': `Coding/development work in ${appName}`,
      'communication': `Communication activity in ${appName}`,
      'research': `Research and information gathering in ${appName}`,
      'creative': `Creative/design work in ${appName}`,
      'administrative': `Administrative tasks in ${appName}`,
      'learning': `Learning activity in ${appName}`,
      'entertainment': `Entertainment/leisure in ${appName}`,
      'social': `Social media activity in ${appName}`,
      'distraction': `Potentially distracting activity in ${appName}`,
      'unknown': `Activity in ${appName}`
    };

    return descriptions[taskType] || descriptions.unknown;
  }

  /**
   * Generate insights from detected tasks
   */
  private generateTaskInsights(tasks: Array<any>): string[] {
    const insights: string[] = [];

    // Analyze task distribution
    const taskCounts = tasks.reduce((acc, task) => {
      acc[task.taskType] = (acc[task.taskType] || 0) + 1;
      return acc;
    }, {} as Record<TaskCategoryType, number>);

    const totalTasks = tasks.length;
    if (totalTasks === 0) return insights;

    // Find dominant task type
    const dominantTask = Object.entries(taskCounts)
      .sort(([,a], [,b]) => b - a)[0];

    if (dominantTask) {
      const [taskType, count] = dominantTask;
      const percentage = Math.round((count / totalTasks) * 100);
      insights.push(`${percentage}% of detected activities were ${taskType.replace('-', ' ')}`);
    }

    // Analyze productivity patterns
    const productiveTasks = tasks.filter(task => 
      ['deep-work', 'creative', 'learning', 'research'].includes(task.taskType)
    );
    
    const productivePercentage = Math.round((productiveTasks.length / totalTasks) * 100);
    
    if (productivePercentage >= 70) {
      insights.push("High productivity detected - great focus on meaningful work!");
    } else if (productivePercentage >= 40) {
      insights.push("Moderate productivity - consider reducing distractions");
    } else {
      insights.push("Low productivity detected - focus on high-value tasks");
    }

    // Analyze app switching patterns
    const appSwitches = this.analyzeAppSwitching(tasks);
    if (appSwitches > 10) {
      insights.push(`High app switching detected (${appSwitches} switches) - consider batching similar tasks`);
    }

    return insights;
  }

  /**
   * Analyze app switching patterns
   */
  private analyzeAppSwitching(tasks: Array<any>): number {
    let switches = 0;
    let lastApp = "";

    for (const task of tasks.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime())) {
      if (lastApp && lastApp !== task.appName) {
        switches++;
      }
      lastApp = task.appName;
    }

    return switches;
  }

  /**
   * Detect productivity patterns using AI analysis
   */
  async detectProductivityPatterns(sessions: FocusSession[]): Promise<ProductivityPattern[]> {
    const patterns: ProductivityPattern[] = [];

    // Group sessions by time patterns
    const timePatterns = this.groupSessionsByTimePattern(sessions);

    for (const [patternKey, patternSessions] of Object.entries(timePatterns)) {
      if (patternSessions.length < 3) continue; // Need minimum sessions for pattern

      const averageProductivity = patternSessions.reduce((sum, session) => 
        sum + session.focusScore, 0) / patternSessions.length;

      const confidence = Math.min(100, (patternSessions.length / sessions.length) * 100);

      const [timeRange, daysOfWeek] = patternKey.split('|');
      const [startHour, endHour] = timeRange.split('-').map(Number);

      patterns.push({
        id: uuidv4(),
        name: this.generatePatternName(startHour, endHour, daysOfWeek),
        description: this.generatePatternDescription(averageProductivity, patternSessions.length),
        timePattern: {
          startHour,
          endHour,
          daysOfWeek: daysOfWeek.split(',').map(Number)
        },
        averageProductivity,
        confidence,
        recommendations: this.generatePatternRecommendations(averageProductivity, startHour, endHour)
      });
    }

    return patterns.sort((a, b) => b.confidence - a.confidence);
  }

  /**
   * Group sessions by time patterns (hour ranges and days of week)
   */
  private groupSessionsByTimePattern(sessions: FocusSession[]): Record<string, FocusSession[]> {
    const patterns: Record<string, FocusSession[]> = {};

    sessions.forEach(session => {
      const hour = session.startTime.getHours();
      const dayOfWeek = session.startTime.getDay();
      
      // Create 2-hour time blocks
      const timeBlock = Math.floor(hour / 2) * 2;
      const timeRange = `${timeBlock}-${timeBlock + 2}`;
      
      // Group by weekday vs weekend
      const dayGroup = dayOfWeek === 0 || dayOfWeek === 6 ? '0,6' : '1,2,3,4,5';
      
      const patternKey = `${timeRange}|${dayGroup}`;
      
      if (!patterns[patternKey]) {
        patterns[patternKey] = [];
      }
      patterns[patternKey].push(session);
    });

    return patterns;
  }

  /**
   * Generate human-readable pattern name
   */
  private generatePatternName(startHour: number, endHour: number, daysOfWeek: string): string {
    const timeStr = `${startHour}:00-${endHour}:00`;
    const dayStr = daysOfWeek === '0,6' ? 'Weekends' : 'Weekdays';
    return `${dayStr} ${timeStr}`;
  }

  /**
   * Generate pattern description
   */
  private generatePatternDescription(averageProductivity: number, sessionCount: number): string {
    const productivityLevel = averageProductivity >= 80 ? 'high' : 
                             averageProductivity >= 60 ? 'moderate' : 'low';
    
    return `${productivityLevel} productivity pattern with ${sessionCount} sessions`;
  }

  /**
   * Generate recommendations based on pattern analysis
   */
  private generatePatternRecommendations(productivity: number, startHour: number, endHour: number): string[] {
    const recommendations: string[] = [];

    if (productivity >= 80) {
      recommendations.push("Excellent productivity window - schedule important tasks here");
      recommendations.push("Consider extending this time block if possible");
    } else if (productivity >= 60) {
      recommendations.push("Good productivity window - optimize environment for better focus");
      recommendations.push("Minimize distractions during this time");
    } else {
      recommendations.push("Low productivity window - consider rescheduling demanding tasks");
      recommendations.push("Use this time for lighter activities or breaks");
    }

    // Time-specific recommendations
    if (startHour >= 6 && startHour <= 10) {
      recommendations.push("Morning energy - great for creative and analytical work");
    } else if (startHour >= 14 && startHour <= 16) {
      recommendations.push("Post-lunch dip - consider light exercise or break");
    } else if (startHour >= 20) {
      recommendations.push("Evening hours - wind down with lighter tasks");
    }

    return recommendations;
  }

  /**
   * Generate break recommendations based on current activity
   */
  async generateBreakRecommendation(
    currentSession: FocusSession | null,
    recentSessions: FocusSession[]
  ): Promise<BreakRecommendation | null> {
    if (!currentSession) return null;

    const sessionDuration = Date.now() - currentSession.startTime.getTime();
    const sessionMinutes = sessionDuration / (1000 * 60);

    // Calculate urgency based on session length and recent activity
    let urgency = 0;
    let breakType: 'micro' | 'short' | 'long' = 'micro';
    let suggestedDuration = 5;
    let reason = "";

    if (sessionMinutes >= 120) { // 2+ hours
      urgency = 90;
      breakType = 'long';
      suggestedDuration = 15;
      reason = "Extended focus session - time for a longer break";
    } else if (sessionMinutes >= 60) { // 1+ hour
      urgency = 70;
      breakType = 'short';
      suggestedDuration = 10;
      reason = "Good focus session - take a short break to recharge";
    } else if (sessionMinutes >= 25) { // Pomodoro-style
      urgency = 50;
      breakType = 'micro';
      suggestedDuration = 5;
      reason = "Pomodoro break - quick refresh recommended";
    }

    // Adjust based on recent break patterns
    const recentBreaks = this.analyzeRecentBreaks(recentSessions);
    if (recentBreaks.timeSinceLastBreak > 180) { // 3+ hours without break
      urgency = Math.min(100, urgency + 30);
      reason = "Long period without breaks - rest is important";
    }

    if (urgency < 40) return null; // No break needed yet

    return {
      id: uuidv4(),
      timestamp: new Date(),
      reason,
      type: breakType,
      suggestedDuration,
      activities: this.getBreakActivities(breakType),
      urgency
    };
  }

  /**
   * Analyze recent break patterns
   */
  private analyzeRecentBreaks(sessions: FocusSession[]): {
    timeSinceLastBreak: number;
    averageBreakInterval: number;
  } {
    // This is a simplified implementation
    // In a real system, you'd track actual breaks
    const now = Date.now();
    const lastSession = sessions[sessions.length - 1];
    const timeSinceLastBreak = lastSession ? 
      (now - lastSession.endTime.getTime()) / (1000 * 60) : 0;

    return {
      timeSinceLastBreak,
      averageBreakInterval: 60 // Default assumption
    };
  }

  /**
   * Get appropriate break activities based on break type
   */
  private getBreakActivities(breakType: 'micro' | 'short' | 'long'): string[] {
    const activities = {
      micro: [
        "Look away from screen (20-20-20 rule)",
        "Deep breathing exercises",
        "Stretch your neck and shoulders",
        "Drink water"
      ],
      short: [
        "Walk around the room",
        "Light stretching",
        "Grab a healthy snack",
        "Step outside for fresh air",
        "Chat with a colleague"
      ],
      long: [
        "Take a walk outside",
        "Have a proper meal",
        "Do some exercise",
        "Meditate or relax",
        "Call a friend or family member"
      ]
    };

    return activities[breakType];
  }
}
