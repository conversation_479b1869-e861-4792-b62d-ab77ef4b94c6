[{"G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\app\\api\\analytics\\daily-summary\\route.ts": "1", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\app\\api\\analytics\\process\\route.ts": "2", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\app\\api\\component-source\\route.ts": "3", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\app\\api\\fetch-external\\route.ts": "4", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\app\\api\\settings\\route.ts": "5", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\app\\layout.tsx": "6", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\app\\page.tsx": "7", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ai-presets-dialog.tsx": "8", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ai-presets-selector.tsx": "9", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\dashboard\\productivity-overview.tsx": "10", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\focus-analytics-dashboard.tsx": "11", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\playground-card.tsx": "12", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ready-to-use-examples\\health-status.tsx": "13", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ready-to-use-examples\\last-audio-transcription.tsx": "14", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ready-to-use-examples\\last-ocr-image.tsx": "15", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ready-to-use-examples\\last-ui-record.tsx": "16", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ready-to-use-examples\\move-mouse-click.tsx": "17", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ready-to-use-examples\\open-app-get-text.tsx": "18", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ready-to-use-examples\\realtime-audio.tsx": "19", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ready-to-use-examples\\realtime-screen.tsx": "20", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\accordion.tsx": "21", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\badge.tsx": "22", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\button.tsx": "23", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\card.tsx": "24", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\checkbox.tsx": "25", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\codeblock.tsx": "26", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\command.tsx": "27", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\dialog.tsx": "28", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\icons.tsx": "29", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\input.tsx": "30", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\label.tsx": "31", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\popover.tsx": "32", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\progress.tsx": "33", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\select.tsx": "34", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\separator.tsx": "35", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\skeleton.tsx": "36", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\slider.tsx": "37", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\sonner.tsx": "38", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\switch.tsx": "39", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\tabs.tsx": "40", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\textarea.tsx": "41", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\toast.tsx": "42", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\toaster.tsx": "43", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\tooltip.tsx": "44", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\use-toast.ts": "45", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\actions\\get-screenpipe-app-settings.ts": "46", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\actions\\update-pipe-config.ts": "47", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\actions\\video-actions.ts": "48", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\ai-analysis.ts": "49", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\analytics-engine.ts": "50", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\client-only.tsx": "51", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\coaching-system.ts": "52", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\demo-data.ts": "53", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\hooks\\generic-settings.tsx": "54", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\hooks\\use-copy-to-clipboard.tsx": "55", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\hooks\\use-debounce.tsx": "56", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\hooks\\use-health-check.tsx": "57", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\hooks\\use-pipe-settings.tsx": "58", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\hooks\\use-settings.tsx": "59", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\hooks\\use-sql-autocomplete.tsx": "60", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\html-content-parser.ts": "61", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\ollama-models-list.tsx": "62", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\pattern-recognition.ts": "63", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\settings-provider.tsx": "64", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\types.ts": "65", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\utils.ts": "66", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\screenpipe-status.tsx": "67"}, {"size": 5130, "mtime": 1750495717551, "results": "68", "hashOfConfig": "69"}, {"size": 3286, "mtime": 1750495691745, "results": "70", "hashOfConfig": "69"}, {"size": 2216, "mtime": 1750495042133, "results": "71", "hashOfConfig": "69"}, {"size": 2154, "mtime": 1750495042134, "results": "72", "hashOfConfig": "69"}, {"size": 3610, "mtime": 1750495042134, "results": "73", "hashOfConfig": "69"}, {"size": 648, "mtime": 1750495042135, "results": "74", "hashOfConfig": "69"}, {"size": 650, "mtime": 1750495498420, "results": "75", "hashOfConfig": "69"}, {"size": 44832, "mtime": 1750495042138, "results": "76", "hashOfConfig": "69"}, {"size": 45095, "mtime": 1750495042138, "results": "77", "hashOfConfig": "69"}, {"size": 14459, "mtime": 1750495624785, "results": "78", "hashOfConfig": "69"}, {"size": 27459, "mtime": 1750500026387, "results": "79", "hashOfConfig": "69"}, {"size": 20367, "mtime": 1750495042139, "results": "80", "hashOfConfig": "69"}, {"size": 8318, "mtime": 1750495042140, "results": "81", "hashOfConfig": "69"}, {"size": 4642, "mtime": 1750495042140, "results": "82", "hashOfConfig": "69"}, {"size": 4218, "mtime": 1750495042140, "results": "83", "hashOfConfig": "69"}, {"size": 3807, "mtime": 1750495042140, "results": "84", "hashOfConfig": "69"}, {"size": 670, "mtime": 1750495042140, "results": "85", "hashOfConfig": "69"}, {"size": 1895, "mtime": 1750495042141, "results": "86", "hashOfConfig": "69"}, {"size": 7910, "mtime": 1750495042141, "results": "87", "hashOfConfig": "69"}, {"size": 7165, "mtime": 1750495042141, "results": "88", "hashOfConfig": "69"}, {"size": 1991, "mtime": 1750495042142, "results": "89", "hashOfConfig": "69"}, {"size": 1128, "mtime": 1750495042142, "results": "90", "hashOfConfig": "69"}, {"size": 1901, "mtime": 1750495042142, "results": "91", "hashOfConfig": "69"}, {"size": 1858, "mtime": 1750495042142, "results": "92", "hashOfConfig": "69"}, {"size": 1070, "mtime": 1750495042142, "results": "93", "hashOfConfig": "69"}, {"size": 4275, "mtime": 1750495042142, "results": "94", "hashOfConfig": "69"}, {"size": 4926, "mtime": 1750495042143, "results": "95", "hashOfConfig": "69"}, {"size": 3849, "mtime": 1750495042143, "results": "96", "hashOfConfig": "69"}, {"size": 38653, "mtime": 1750495042143, "results": "97", "hashOfConfig": "69"}, {"size": 791, "mtime": 1750495042144, "results": "98", "hashOfConfig": "69"}, {"size": 724, "mtime": 1750495042144, "results": "99", "hashOfConfig": "69"}, {"size": 1244, "mtime": 1750495042144, "results": "100", "hashOfConfig": "69"}, {"size": 791, "mtime": 1750495042145, "results": "101", "hashOfConfig": "69"}, {"size": 5629, "mtime": 1750495042146, "results": "102", "hashOfConfig": "69"}, {"size": 770, "mtime": 1750495042145, "results": "103", "hashOfConfig": "69"}, {"size": 261, "mtime": 1750495042146, "results": "104", "hashOfConfig": "69"}, {"size": 1091, "mtime": 1750495042146, "results": "105", "hashOfConfig": "69"}, {"size": 894, "mtime": 1750495042146, "results": "106", "hashOfConfig": "69"}, {"size": 1153, "mtime": 1750495042146, "results": "107", "hashOfConfig": "69"}, {"size": 1897, "mtime": 1750495042146, "results": "108", "hashOfConfig": "69"}, {"size": 689, "mtime": 1750495042146, "results": "109", "hashOfConfig": "69"}, {"size": 4859, "mtime": 1750495042147, "results": "110", "hashOfConfig": "69"}, {"size": 786, "mtime": 1750495042147, "results": "111", "hashOfConfig": "69"}, {"size": 1159, "mtime": 1750495042147, "results": "112", "hashOfConfig": "69"}, {"size": 3948, "mtime": 1750495042147, "results": "113", "hashOfConfig": "69"}, {"size": 1999, "mtime": 1750499917421, "results": "114", "hashOfConfig": "69"}, {"size": 1428, "mtime": 1750495042148, "results": "115", "hashOfConfig": "69"}, {"size": 1136, "mtime": 1750495042148, "results": "116", "hashOfConfig": "69"}, {"size": 16270, "mtime": 1750496200653, "results": "117", "hashOfConfig": "69"}, {"size": 13639, "mtime": 1750496102027, "results": "118", "hashOfConfig": "69"}, {"size": 310, "mtime": 1750495042149, "results": "119", "hashOfConfig": "69"}, {"size": 16199, "mtime": 1750496111182, "results": "120", "hashOfConfig": "69"}, {"size": 8109, "mtime": 1750496392266, "results": "121", "hashOfConfig": "69"}, {"size": 1668, "mtime": 1750495042149, "results": "122", "hashOfConfig": "69"}, {"size": 657, "mtime": 1750495042149, "results": "123", "hashOfConfig": "69"}, {"size": 386, "mtime": 1750495042149, "results": "124", "hashOfConfig": "69"}, {"size": 4853, "mtime": 1750495042149, "results": "125", "hashOfConfig": "69"}, {"size": 1136, "mtime": 1750495042149, "results": "126", "hashOfConfig": "69"}, {"size": 5556, "mtime": 1750495042150, "results": "127", "hashOfConfig": "69"}, {"size": 1817, "mtime": 1750495042150, "results": "128", "hashOfConfig": "69"}, {"size": 7935, "mtime": 1750495042150, "results": "129", "hashOfConfig": "69"}, {"size": 3863, "mtime": 1750495042150, "results": "130", "hashOfConfig": "69"}, {"size": 19881, "mtime": 1750496265611, "results": "131", "hashOfConfig": "69"}, {"size": 4763, "mtime": 1750499904096, "results": "132", "hashOfConfig": "69"}, {"size": 4361, "mtime": 1750495159242, "results": "133", "hashOfConfig": "69"}, {"size": 1386, "mtime": 1750495042151, "results": "134", "hashOfConfig": "69"}, {"size": 3712, "mtime": 1750499950319, "results": "135", "hashOfConfig": "69"}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "hb3iq5", {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\app\\api\\analytics\\daily-summary\\route.ts", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\app\\api\\analytics\\process\\route.ts", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\app\\api\\component-source\\route.ts", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\app\\api\\fetch-external\\route.ts", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\app\\api\\settings\\route.ts", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\app\\layout.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\app\\page.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ai-presets-dialog.tsx", ["337", "338", "339"], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ai-presets-selector.tsx", ["340", "341", "342"], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\dashboard\\productivity-overview.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\focus-analytics-dashboard.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\playground-card.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ready-to-use-examples\\health-status.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ready-to-use-examples\\last-audio-transcription.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ready-to-use-examples\\last-ocr-image.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ready-to-use-examples\\last-ui-record.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ready-to-use-examples\\move-mouse-click.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ready-to-use-examples\\open-app-get-text.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ready-to-use-examples\\realtime-audio.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ready-to-use-examples\\realtime-screen.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\accordion.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\badge.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\button.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\card.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\checkbox.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\codeblock.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\command.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\dialog.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\icons.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\input.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\label.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\popover.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\progress.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\select.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\separator.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\skeleton.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\slider.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\sonner.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\switch.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\tabs.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\textarea.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\toast.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\toaster.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\tooltip.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\use-toast.ts", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\actions\\get-screenpipe-app-settings.ts", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\actions\\update-pipe-config.ts", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\actions\\video-actions.ts", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\ai-analysis.ts", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\analytics-engine.ts", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\client-only.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\coaching-system.ts", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\demo-data.ts", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\hooks\\generic-settings.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\hooks\\use-copy-to-clipboard.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\hooks\\use-debounce.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\hooks\\use-health-check.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\hooks\\use-pipe-settings.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\hooks\\use-settings.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\hooks\\use-sql-autocomplete.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\html-content-parser.ts", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\ollama-models-list.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\pattern-recognition.ts", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\settings-provider.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\types.ts", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\utils.ts", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\screenpipe-status.tsx", ["343"], [], {"ruleId": "344", "severity": 1, "message": "345", "line": 275, "column": 6, "nodeType": "346", "endLine": 275, "endColumn": 59, "suggestions": "347"}, {"ruleId": "344", "severity": 1, "message": "348", "line": 815, "column": 6, "nodeType": "346", "endLine": 815, "endColumn": 50, "suggestions": "349"}, {"ruleId": "344", "severity": 1, "message": "350", "line": 842, "column": 6, "nodeType": "346", "endLine": 842, "endColumn": 60, "suggestions": "351"}, {"ruleId": "344", "severity": 1, "message": "345", "line": 276, "column": 6, "nodeType": "346", "endLine": 276, "endColumn": 59, "suggestions": "352"}, {"ruleId": "344", "severity": 1, "message": "348", "line": 814, "column": 6, "nodeType": "346", "endLine": 814, "endColumn": 50, "suggestions": "353"}, {"ruleId": "344", "severity": 1, "message": "354", "line": 841, "column": 6, "nodeType": "346", "endLine": 841, "endColumn": 53, "suggestions": "355"}, {"ruleId": "356", "severity": 2, "message": "357", "line": 102, "column": 28, "nodeType": "358", "messageId": "359", "suggestions": "360"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'settings?.user?.token'. Either include it or remove the dependency array.", "ArrayExpression", ["361"], "React Hook useMemo has missing dependencies: 'aiKey' and 'pipeSettings'. Either include them or remove the dependency array.", ["362"], "React Hook useEffect has a missing dependency: 'shortcutKey'. Either include it or remove the dependency array.", ["363"], ["364"], ["365"], "React Hook useEffect has missing dependencies: 'aiKey' and 'shortcutKey'. Either include them or remove the dependency array.", ["366"], "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["367", "368", "369", "370"], {"desc": "371", "fix": "372"}, {"desc": "373", "fix": "374"}, {"desc": "375", "fix": "376"}, {"desc": "371", "fix": "377"}, {"desc": "373", "fix": "378"}, {"desc": "379", "fix": "380"}, {"messageId": "381", "data": "382", "fix": "383", "desc": "384"}, {"messageId": "381", "data": "385", "fix": "386", "desc": "387"}, {"messageId": "381", "data": "388", "fix": "389", "desc": "390"}, {"messageId": "381", "data": "391", "fix": "392", "desc": "393"}, "Update the dependencies array to be: [selectedProvider, formData.apiKey, formData.baseUrl, settings?.user?.token]", {"range": "394", "text": "395"}, "Update the dependencies array to be: [settings?.aiPresets, pipeSettings, aiKey]", {"range": "396", "text": "397"}, "Update the dependencies array to be: [aiPresets, selectedPreset, updatePipeSettings, aiKey, shortcutKey]", {"range": "398", "text": "399"}, {"range": "400", "text": "395"}, {"range": "401", "text": "397"}, "Update the dependencies array to be: [aiKey, aiPresets, selectedPreset, shortcutKey, updatePipeSettings]", {"range": "402", "text": "403"}, "replaceWithAlt", {"alt": "404"}, {"range": "405", "text": "406"}, "Replace with `&apos;`.", {"alt": "407"}, {"range": "408", "text": "409"}, "Replace with `&lsquo;`.", {"alt": "410"}, {"range": "411", "text": "412"}, "Replace with `&#39;`.", {"alt": "413"}, {"range": "414", "text": "415"}, "Replace with `&rsquo;`.", [8444, 8497], "[selected<PERSON><PERSON><PERSON>, formData.apiKey, formData.baseUrl, settings?.user?.token]", [26450, 26494], "[settings?.aiPresets, pipeSettings, aiKey]", [27327, 27381], "[aiPresets, selectedPreset, updatePipeSettings, aiKey, shortcutKey]", [8517, 8570], [26467, 26511], [27341, 27388], "[aiKey, aiPresets, selectedPreset, shortcutKey, updatePipeSettings]", "&apos;", [3552, 3584], "Ensure it&apos;s running on port 3030", "&lsquo;", [3552, 3584], "Ensure it&lsquo;s running on port 3030", "&#39;", [3552, 3584], "Ensure it&#39;s running on port 3030", "&rsquo;", [3552, 3584], "Ensure it&rsquo;s running on port 3030"]