[{"G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\app\\api\\analytics\\daily-summary\\route.ts": "1", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\app\\api\\analytics\\process\\route.ts": "2", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\app\\api\\component-source\\route.ts": "3", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\app\\api\\fetch-external\\route.ts": "4", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\app\\api\\settings\\route.ts": "5", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\app\\layout.tsx": "6", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\app\\page.tsx": "7", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ai-presets-dialog.tsx": "8", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ai-presets-selector.tsx": "9", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\dashboard\\productivity-overview.tsx": "10", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\focus-analytics-dashboard.tsx": "11", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\playground-card.tsx": "12", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ready-to-use-examples\\health-status.tsx": "13", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ready-to-use-examples\\last-audio-transcription.tsx": "14", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ready-to-use-examples\\last-ocr-image.tsx": "15", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ready-to-use-examples\\last-ui-record.tsx": "16", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ready-to-use-examples\\move-mouse-click.tsx": "17", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ready-to-use-examples\\open-app-get-text.tsx": "18", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ready-to-use-examples\\realtime-audio.tsx": "19", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ready-to-use-examples\\realtime-screen.tsx": "20", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\accordion.tsx": "21", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\badge.tsx": "22", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\button.tsx": "23", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\card.tsx": "24", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\checkbox.tsx": "25", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\codeblock.tsx": "26", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\command.tsx": "27", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\dialog.tsx": "28", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\icons.tsx": "29", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\input.tsx": "30", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\label.tsx": "31", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\popover.tsx": "32", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\progress.tsx": "33", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\select.tsx": "34", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\separator.tsx": "35", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\skeleton.tsx": "36", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\slider.tsx": "37", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\sonner.tsx": "38", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\switch.tsx": "39", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\tabs.tsx": "40", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\textarea.tsx": "41", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\toast.tsx": "42", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\toaster.tsx": "43", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\tooltip.tsx": "44", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\use-toast.ts": "45", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\actions\\get-screenpipe-app-settings.ts": "46", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\actions\\update-pipe-config.ts": "47", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\actions\\video-actions.ts": "48", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\ai-analysis.ts": "49", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\analytics-engine.ts": "50", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\client-only.tsx": "51", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\coaching-system.ts": "52", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\hooks\\generic-settings.tsx": "53", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\hooks\\use-copy-to-clipboard.tsx": "54", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\hooks\\use-debounce.tsx": "55", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\hooks\\use-health-check.tsx": "56", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\hooks\\use-pipe-settings.tsx": "57", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\hooks\\use-settings.tsx": "58", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\hooks\\use-sql-autocomplete.tsx": "59", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\html-content-parser.ts": "60", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\ollama-models-list.tsx": "61", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\pattern-recognition.ts": "62", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\settings-provider.tsx": "63", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\types.ts": "64", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\utils.ts": "65"}, {"size": 5130, "mtime": 1750495717551, "results": "66", "hashOfConfig": "67"}, {"size": 3286, "mtime": 1750495691745, "results": "68", "hashOfConfig": "67"}, {"size": 2216, "mtime": 1750495042133, "results": "69", "hashOfConfig": "67"}, {"size": 2154, "mtime": 1750495042134, "results": "70", "hashOfConfig": "67"}, {"size": 3610, "mtime": 1750495042134, "results": "71", "hashOfConfig": "67"}, {"size": 648, "mtime": 1750495042135, "results": "72", "hashOfConfig": "67"}, {"size": 650, "mtime": 1750495498420, "results": "73", "hashOfConfig": "67"}, {"size": 44832, "mtime": 1750495042138, "results": "74", "hashOfConfig": "67"}, {"size": 45095, "mtime": 1750495042138, "results": "75", "hashOfConfig": "67"}, {"size": 14459, "mtime": 1750495624785, "results": "76", "hashOfConfig": "67"}, {"size": 13104, "mtime": 1750495653828, "results": "77", "hashOfConfig": "67"}, {"size": 20367, "mtime": 1750495042139, "results": "78", "hashOfConfig": "67"}, {"size": 8318, "mtime": 1750495042140, "results": "79", "hashOfConfig": "67"}, {"size": 4642, "mtime": 1750495042140, "results": "80", "hashOfConfig": "67"}, {"size": 4218, "mtime": 1750495042140, "results": "81", "hashOfConfig": "67"}, {"size": 3807, "mtime": 1750495042140, "results": "82", "hashOfConfig": "67"}, {"size": 670, "mtime": 1750495042140, "results": "83", "hashOfConfig": "67"}, {"size": 1895, "mtime": 1750495042141, "results": "84", "hashOfConfig": "67"}, {"size": 7910, "mtime": 1750495042141, "results": "85", "hashOfConfig": "67"}, {"size": 7165, "mtime": 1750495042141, "results": "86", "hashOfConfig": "67"}, {"size": 1991, "mtime": 1750495042142, "results": "87", "hashOfConfig": "67"}, {"size": 1128, "mtime": 1750495042142, "results": "88", "hashOfConfig": "67"}, {"size": 1901, "mtime": 1750495042142, "results": "89", "hashOfConfig": "67"}, {"size": 1858, "mtime": 1750495042142, "results": "90", "hashOfConfig": "67"}, {"size": 1070, "mtime": 1750495042142, "results": "91", "hashOfConfig": "67"}, {"size": 4275, "mtime": 1750495042142, "results": "92", "hashOfConfig": "67"}, {"size": 4926, "mtime": 1750495042143, "results": "93", "hashOfConfig": "67"}, {"size": 3849, "mtime": 1750495042143, "results": "94", "hashOfConfig": "67"}, {"size": 38653, "mtime": 1750495042143, "results": "95", "hashOfConfig": "67"}, {"size": 791, "mtime": 1750495042144, "results": "96", "hashOfConfig": "67"}, {"size": 724, "mtime": 1750495042144, "results": "97", "hashOfConfig": "67"}, {"size": 1244, "mtime": 1750495042144, "results": "98", "hashOfConfig": "67"}, {"size": 791, "mtime": 1750495042145, "results": "99", "hashOfConfig": "67"}, {"size": 5629, "mtime": 1750495042146, "results": "100", "hashOfConfig": "67"}, {"size": 770, "mtime": 1750495042145, "results": "101", "hashOfConfig": "67"}, {"size": 261, "mtime": 1750495042146, "results": "102", "hashOfConfig": "67"}, {"size": 1091, "mtime": 1750495042146, "results": "103", "hashOfConfig": "67"}, {"size": 894, "mtime": 1750495042146, "results": "104", "hashOfConfig": "67"}, {"size": 1153, "mtime": 1750495042146, "results": "105", "hashOfConfig": "67"}, {"size": 1897, "mtime": 1750495042146, "results": "106", "hashOfConfig": "67"}, {"size": 689, "mtime": 1750495042146, "results": "107", "hashOfConfig": "67"}, {"size": 4859, "mtime": 1750495042147, "results": "108", "hashOfConfig": "67"}, {"size": 786, "mtime": 1750495042147, "results": "109", "hashOfConfig": "67"}, {"size": 1159, "mtime": 1750495042147, "results": "110", "hashOfConfig": "67"}, {"size": 3948, "mtime": 1750495042147, "results": "111", "hashOfConfig": "67"}, {"size": 374, "mtime": 1750495042148, "results": "112", "hashOfConfig": "67"}, {"size": 1428, "mtime": 1750495042148, "results": "113", "hashOfConfig": "67"}, {"size": 1136, "mtime": 1750495042148, "results": "114", "hashOfConfig": "67"}, {"size": 16236, "mtime": 1750495310371, "results": "115", "hashOfConfig": "67"}, {"size": 13900, "mtime": 1750495235898, "results": "116", "hashOfConfig": "67"}, {"size": 310, "mtime": 1750495042149, "results": "117", "hashOfConfig": "67"}, {"size": 16201, "mtime": 1750495377623, "results": "118", "hashOfConfig": "67"}, {"size": 1668, "mtime": 1750495042149, "results": "119", "hashOfConfig": "67"}, {"size": 657, "mtime": 1750495042149, "results": "120", "hashOfConfig": "67"}, {"size": 386, "mtime": 1750495042149, "results": "121", "hashOfConfig": "67"}, {"size": 4853, "mtime": 1750495042149, "results": "122", "hashOfConfig": "67"}, {"size": 1136, "mtime": 1750495042149, "results": "123", "hashOfConfig": "67"}, {"size": 5556, "mtime": 1750495042150, "results": "124", "hashOfConfig": "67"}, {"size": 1817, "mtime": 1750495042150, "results": "125", "hashOfConfig": "67"}, {"size": 7935, "mtime": 1750495042150, "results": "126", "hashOfConfig": "67"}, {"size": 3863, "mtime": 1750495042150, "results": "127", "hashOfConfig": "67"}, {"size": 19538, "mtime": 1750495471265, "results": "128", "hashOfConfig": "67"}, {"size": 966, "mtime": 1750495042150, "results": "129", "hashOfConfig": "67"}, {"size": 4361, "mtime": 1750495159242, "results": "130", "hashOfConfig": "67"}, {"size": 1386, "mtime": 1750495042151, "results": "131", "hashOfConfig": "67"}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "hb3iq5", {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\app\\api\\analytics\\daily-summary\\route.ts", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\app\\api\\analytics\\process\\route.ts", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\app\\api\\component-source\\route.ts", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\app\\api\\fetch-external\\route.ts", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\app\\api\\settings\\route.ts", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\app\\layout.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\app\\page.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ai-presets-dialog.tsx", ["327", "328", "329"], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ai-presets-selector.tsx", ["330", "331", "332"], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\dashboard\\productivity-overview.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\focus-analytics-dashboard.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\playground-card.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ready-to-use-examples\\health-status.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ready-to-use-examples\\last-audio-transcription.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ready-to-use-examples\\last-ocr-image.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ready-to-use-examples\\last-ui-record.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ready-to-use-examples\\move-mouse-click.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ready-to-use-examples\\open-app-get-text.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ready-to-use-examples\\realtime-audio.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ready-to-use-examples\\realtime-screen.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\accordion.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\badge.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\button.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\card.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\checkbox.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\codeblock.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\command.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\dialog.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\icons.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\input.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\label.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\popover.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\progress.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\select.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\separator.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\skeleton.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\slider.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\sonner.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\switch.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\tabs.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\textarea.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\toast.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\toaster.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\tooltip.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\use-toast.ts", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\actions\\get-screenpipe-app-settings.ts", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\actions\\update-pipe-config.ts", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\actions\\video-actions.ts", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\ai-analysis.ts", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\analytics-engine.ts", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\client-only.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\coaching-system.ts", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\hooks\\generic-settings.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\hooks\\use-copy-to-clipboard.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\hooks\\use-debounce.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\hooks\\use-health-check.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\hooks\\use-pipe-settings.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\hooks\\use-settings.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\hooks\\use-sql-autocomplete.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\html-content-parser.ts", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\ollama-models-list.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\pattern-recognition.ts", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\settings-provider.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\types.ts", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\utils.ts", [], [], {"ruleId": "333", "severity": 1, "message": "334", "line": 275, "column": 6, "nodeType": "335", "endLine": 275, "endColumn": 59, "suggestions": "336"}, {"ruleId": "333", "severity": 1, "message": "337", "line": 815, "column": 6, "nodeType": "335", "endLine": 815, "endColumn": 50, "suggestions": "338"}, {"ruleId": "333", "severity": 1, "message": "339", "line": 842, "column": 6, "nodeType": "335", "endLine": 842, "endColumn": 60, "suggestions": "340"}, {"ruleId": "333", "severity": 1, "message": "334", "line": 276, "column": 6, "nodeType": "335", "endLine": 276, "endColumn": 59, "suggestions": "341"}, {"ruleId": "333", "severity": 1, "message": "337", "line": 814, "column": 6, "nodeType": "335", "endLine": 814, "endColumn": 50, "suggestions": "342"}, {"ruleId": "333", "severity": 1, "message": "343", "line": 841, "column": 6, "nodeType": "335", "endLine": 841, "endColumn": 53, "suggestions": "344"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'settings?.user?.token'. Either include it or remove the dependency array.", "ArrayExpression", ["345"], "React Hook useMemo has missing dependencies: 'aiKey' and 'pipeSettings'. Either include them or remove the dependency array.", ["346"], "React Hook useEffect has a missing dependency: 'shortcutKey'. Either include it or remove the dependency array.", ["347"], ["348"], ["349"], "React Hook useEffect has missing dependencies: 'aiKey' and 'shortcutKey'. Either include them or remove the dependency array.", ["350"], {"desc": "351", "fix": "352"}, {"desc": "353", "fix": "354"}, {"desc": "355", "fix": "356"}, {"desc": "351", "fix": "357"}, {"desc": "353", "fix": "358"}, {"desc": "359", "fix": "360"}, "Update the dependencies array to be: [selectedProvider, formData.apiKey, formData.baseUrl, settings?.user?.token]", {"range": "361", "text": "362"}, "Update the dependencies array to be: [settings?.aiPresets, pipeSettings, aiKey]", {"range": "363", "text": "364"}, "Update the dependencies array to be: [aiPresets, selectedPreset, updatePipeSettings, aiKey, shortcutKey]", {"range": "365", "text": "366"}, {"range": "367", "text": "362"}, {"range": "368", "text": "364"}, "Update the dependencies array to be: [aiKey, aiPresets, selectedPreset, shortcutKey, updatePipeSettings]", {"range": "369", "text": "370"}, [8444, 8497], "[selected<PERSON><PERSON><PERSON>, formData.apiKey, formData.baseUrl, settings?.user?.token]", [26450, 26494], "[settings?.aiPresets, pipeSettings, aiKey]", [27327, 27381], "[aiPresets, selectedPreset, updatePipeSettings, aiKey, shortcutKey]", [8517, 8570], [26467, 26511], [27341, 27388], "[aiKey, aiPresets, selectedPreset, shortcutKey, updatePipeSettings]"]