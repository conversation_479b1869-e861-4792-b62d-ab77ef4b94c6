[{"G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\app\\api\\analytics\\daily-summary\\route.ts": "1", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\app\\api\\analytics\\process\\route.ts": "2", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\app\\api\\component-source\\route.ts": "3", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\app\\api\\fetch-external\\route.ts": "4", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\app\\api\\settings\\route.ts": "5", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\app\\layout.tsx": "6", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\app\\page.tsx": "7", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ai-presets-dialog.tsx": "8", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ai-presets-selector.tsx": "9", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\dashboard\\productivity-overview.tsx": "10", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\focus-analytics-dashboard.tsx": "11", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\playground-card.tsx": "12", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ready-to-use-examples\\health-status.tsx": "13", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ready-to-use-examples\\last-audio-transcription.tsx": "14", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ready-to-use-examples\\last-ocr-image.tsx": "15", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ready-to-use-examples\\last-ui-record.tsx": "16", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ready-to-use-examples\\move-mouse-click.tsx": "17", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ready-to-use-examples\\open-app-get-text.tsx": "18", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ready-to-use-examples\\realtime-audio.tsx": "19", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ready-to-use-examples\\realtime-screen.tsx": "20", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\accordion.tsx": "21", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\badge.tsx": "22", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\button.tsx": "23", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\card.tsx": "24", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\checkbox.tsx": "25", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\codeblock.tsx": "26", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\command.tsx": "27", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\dialog.tsx": "28", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\icons.tsx": "29", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\input.tsx": "30", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\label.tsx": "31", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\popover.tsx": "32", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\progress.tsx": "33", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\select.tsx": "34", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\separator.tsx": "35", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\skeleton.tsx": "36", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\slider.tsx": "37", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\sonner.tsx": "38", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\switch.tsx": "39", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\tabs.tsx": "40", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\textarea.tsx": "41", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\toast.tsx": "42", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\toaster.tsx": "43", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\tooltip.tsx": "44", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\use-toast.ts": "45", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\actions\\get-screenpipe-app-settings.ts": "46", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\actions\\update-pipe-config.ts": "47", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\actions\\video-actions.ts": "48", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\ai-analysis.ts": "49", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\analytics-engine.ts": "50", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\client-only.tsx": "51", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\coaching-system.ts": "52", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\hooks\\generic-settings.tsx": "53", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\hooks\\use-copy-to-clipboard.tsx": "54", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\hooks\\use-debounce.tsx": "55", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\hooks\\use-health-check.tsx": "56", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\hooks\\use-pipe-settings.tsx": "57", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\hooks\\use-settings.tsx": "58", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\hooks\\use-sql-autocomplete.tsx": "59", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\html-content-parser.ts": "60", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\ollama-models-list.tsx": "61", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\pattern-recognition.ts": "62", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\settings-provider.tsx": "63", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\types.ts": "64", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\utils.ts": "65", "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\demo-data.ts": "66"}, {"size": 5130, "mtime": 1750495717551, "results": "67", "hashOfConfig": "68"}, {"size": 3286, "mtime": 1750495691745, "results": "69", "hashOfConfig": "68"}, {"size": 2216, "mtime": 1750495042133, "results": "70", "hashOfConfig": "68"}, {"size": 2154, "mtime": 1750495042134, "results": "71", "hashOfConfig": "68"}, {"size": 3610, "mtime": 1750495042134, "results": "72", "hashOfConfig": "68"}, {"size": 648, "mtime": 1750495042135, "results": "73", "hashOfConfig": "68"}, {"size": 650, "mtime": 1750495498420, "results": "74", "hashOfConfig": "68"}, {"size": 44832, "mtime": 1750495042138, "results": "75", "hashOfConfig": "68"}, {"size": 45095, "mtime": 1750495042138, "results": "76", "hashOfConfig": "68"}, {"size": 14459, "mtime": 1750495624785, "results": "77", "hashOfConfig": "68"}, {"size": 27218, "mtime": 1750496547056, "results": "78", "hashOfConfig": "68"}, {"size": 20367, "mtime": 1750495042139, "results": "79", "hashOfConfig": "68"}, {"size": 8318, "mtime": 1750495042140, "results": "80", "hashOfConfig": "68"}, {"size": 4642, "mtime": 1750495042140, "results": "81", "hashOfConfig": "68"}, {"size": 4218, "mtime": 1750495042140, "results": "82", "hashOfConfig": "68"}, {"size": 3807, "mtime": 1750495042140, "results": "83", "hashOfConfig": "68"}, {"size": 670, "mtime": 1750495042140, "results": "84", "hashOfConfig": "68"}, {"size": 1895, "mtime": 1750495042141, "results": "85", "hashOfConfig": "68"}, {"size": 7910, "mtime": 1750495042141, "results": "86", "hashOfConfig": "68"}, {"size": 7165, "mtime": 1750495042141, "results": "87", "hashOfConfig": "68"}, {"size": 1991, "mtime": 1750495042142, "results": "88", "hashOfConfig": "68"}, {"size": 1128, "mtime": 1750495042142, "results": "89", "hashOfConfig": "68"}, {"size": 1901, "mtime": 1750495042142, "results": "90", "hashOfConfig": "68"}, {"size": 1858, "mtime": 1750495042142, "results": "91", "hashOfConfig": "68"}, {"size": 1070, "mtime": 1750495042142, "results": "92", "hashOfConfig": "68"}, {"size": 4275, "mtime": 1750495042142, "results": "93", "hashOfConfig": "68"}, {"size": 4926, "mtime": 1750495042143, "results": "94", "hashOfConfig": "68"}, {"size": 3849, "mtime": 1750495042143, "results": "95", "hashOfConfig": "68"}, {"size": 38653, "mtime": 1750495042143, "results": "96", "hashOfConfig": "68"}, {"size": 791, "mtime": 1750495042144, "results": "97", "hashOfConfig": "68"}, {"size": 724, "mtime": 1750495042144, "results": "98", "hashOfConfig": "68"}, {"size": 1244, "mtime": 1750495042144, "results": "99", "hashOfConfig": "68"}, {"size": 791, "mtime": 1750495042145, "results": "100", "hashOfConfig": "68"}, {"size": 5629, "mtime": 1750495042146, "results": "101", "hashOfConfig": "68"}, {"size": 770, "mtime": 1750495042145, "results": "102", "hashOfConfig": "68"}, {"size": 261, "mtime": 1750495042146, "results": "103", "hashOfConfig": "68"}, {"size": 1091, "mtime": 1750495042146, "results": "104", "hashOfConfig": "68"}, {"size": 894, "mtime": 1750495042146, "results": "105", "hashOfConfig": "68"}, {"size": 1153, "mtime": 1750495042146, "results": "106", "hashOfConfig": "68"}, {"size": 1897, "mtime": 1750495042146, "results": "107", "hashOfConfig": "68"}, {"size": 689, "mtime": 1750495042146, "results": "108", "hashOfConfig": "68"}, {"size": 4859, "mtime": 1750495042147, "results": "109", "hashOfConfig": "68"}, {"size": 786, "mtime": 1750495042147, "results": "110", "hashOfConfig": "68"}, {"size": 1159, "mtime": 1750495042147, "results": "111", "hashOfConfig": "68"}, {"size": 3948, "mtime": 1750495042147, "results": "112", "hashOfConfig": "68"}, {"size": 374, "mtime": 1750495042148, "results": "113", "hashOfConfig": "68"}, {"size": 1428, "mtime": 1750495042148, "results": "114", "hashOfConfig": "68"}, {"size": 1136, "mtime": 1750495042148, "results": "115", "hashOfConfig": "68"}, {"size": 16270, "mtime": 1750496200653, "results": "116", "hashOfConfig": "68"}, {"size": 13639, "mtime": 1750496102027, "results": "117", "hashOfConfig": "68"}, {"size": 310, "mtime": 1750495042149, "results": "118", "hashOfConfig": "68"}, {"size": 16199, "mtime": 1750496111182, "results": "119", "hashOfConfig": "68"}, {"size": 1668, "mtime": 1750495042149, "results": "120", "hashOfConfig": "68"}, {"size": 657, "mtime": 1750495042149, "results": "121", "hashOfConfig": "68"}, {"size": 386, "mtime": 1750495042149, "results": "122", "hashOfConfig": "68"}, {"size": 4853, "mtime": 1750495042149, "results": "123", "hashOfConfig": "68"}, {"size": 1136, "mtime": 1750495042149, "results": "124", "hashOfConfig": "68"}, {"size": 5556, "mtime": 1750495042150, "results": "125", "hashOfConfig": "68"}, {"size": 1817, "mtime": 1750495042150, "results": "126", "hashOfConfig": "68"}, {"size": 7935, "mtime": 1750495042150, "results": "127", "hashOfConfig": "68"}, {"size": 3863, "mtime": 1750495042150, "results": "128", "hashOfConfig": "68"}, {"size": 19881, "mtime": 1750496265611, "results": "129", "hashOfConfig": "68"}, {"size": 966, "mtime": 1750495042150, "results": "130", "hashOfConfig": "68"}, {"size": 4361, "mtime": 1750495159242, "results": "131", "hashOfConfig": "68"}, {"size": 1386, "mtime": 1750495042151, "results": "132", "hashOfConfig": "68"}, {"size": 8109, "mtime": 1750496392266, "results": "133", "hashOfConfig": "68"}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "hb3iq5", {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\app\\api\\analytics\\daily-summary\\route.ts", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\app\\api\\analytics\\process\\route.ts", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\app\\api\\component-source\\route.ts", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\app\\api\\fetch-external\\route.ts", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\app\\api\\settings\\route.ts", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\app\\layout.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\app\\page.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ai-presets-dialog.tsx", ["332", "333", "334"], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ai-presets-selector.tsx", ["335", "336", "337"], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\dashboard\\productivity-overview.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\focus-analytics-dashboard.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\playground-card.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ready-to-use-examples\\health-status.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ready-to-use-examples\\last-audio-transcription.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ready-to-use-examples\\last-ocr-image.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ready-to-use-examples\\last-ui-record.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ready-to-use-examples\\move-mouse-click.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ready-to-use-examples\\open-app-get-text.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ready-to-use-examples\\realtime-audio.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ready-to-use-examples\\realtime-screen.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\accordion.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\badge.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\button.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\card.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\checkbox.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\codeblock.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\command.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\dialog.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\icons.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\input.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\label.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\popover.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\progress.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\select.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\separator.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\skeleton.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\slider.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\sonner.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\switch.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\tabs.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\textarea.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\toast.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\toaster.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\tooltip.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\components\\ui\\use-toast.ts", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\actions\\get-screenpipe-app-settings.ts", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\actions\\update-pipe-config.ts", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\actions\\video-actions.ts", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\ai-analysis.ts", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\analytics-engine.ts", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\client-only.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\coaching-system.ts", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\hooks\\generic-settings.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\hooks\\use-copy-to-clipboard.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\hooks\\use-debounce.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\hooks\\use-health-check.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\hooks\\use-pipe-settings.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\hooks\\use-settings.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\hooks\\use-sql-autocomplete.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\html-content-parser.ts", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\ollama-models-list.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\pattern-recognition.ts", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\settings-provider.tsx", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\types.ts", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\utils.ts", [], [], "G:\\projects\\screenpipe\\screenpipe\\pipes\\focus-analytics-coach\\lib\\demo-data.ts", [], [], {"ruleId": "338", "severity": 1, "message": "339", "line": 275, "column": 6, "nodeType": "340", "endLine": 275, "endColumn": 59, "suggestions": "341"}, {"ruleId": "338", "severity": 1, "message": "342", "line": 815, "column": 6, "nodeType": "340", "endLine": 815, "endColumn": 50, "suggestions": "343"}, {"ruleId": "338", "severity": 1, "message": "344", "line": 842, "column": 6, "nodeType": "340", "endLine": 842, "endColumn": 60, "suggestions": "345"}, {"ruleId": "338", "severity": 1, "message": "339", "line": 276, "column": 6, "nodeType": "340", "endLine": 276, "endColumn": 59, "suggestions": "346"}, {"ruleId": "338", "severity": 1, "message": "342", "line": 814, "column": 6, "nodeType": "340", "endLine": 814, "endColumn": 50, "suggestions": "347"}, {"ruleId": "338", "severity": 1, "message": "348", "line": 841, "column": 6, "nodeType": "340", "endLine": 841, "endColumn": 53, "suggestions": "349"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'settings?.user?.token'. Either include it or remove the dependency array.", "ArrayExpression", ["350"], "React Hook useMemo has missing dependencies: 'aiKey' and 'pipeSettings'. Either include them or remove the dependency array.", ["351"], "React Hook useEffect has a missing dependency: 'shortcutKey'. Either include it or remove the dependency array.", ["352"], ["353"], ["354"], "React Hook useEffect has missing dependencies: 'aiKey' and 'shortcutKey'. Either include them or remove the dependency array.", ["355"], {"desc": "356", "fix": "357"}, {"desc": "358", "fix": "359"}, {"desc": "360", "fix": "361"}, {"desc": "356", "fix": "362"}, {"desc": "358", "fix": "363"}, {"desc": "364", "fix": "365"}, "Update the dependencies array to be: [selectedProvider, formData.apiKey, formData.baseUrl, settings?.user?.token]", {"range": "366", "text": "367"}, "Update the dependencies array to be: [settings?.aiPresets, pipeSettings, aiKey]", {"range": "368", "text": "369"}, "Update the dependencies array to be: [aiPresets, selectedPreset, updatePipeSettings, aiKey, shortcutKey]", {"range": "370", "text": "371"}, {"range": "372", "text": "367"}, {"range": "373", "text": "369"}, "Update the dependencies array to be: [aiKey, aiPresets, selectedPreset, shortcutKey, updatePipeSettings]", {"range": "374", "text": "375"}, [8444, 8497], "[selected<PERSON><PERSON><PERSON>, formData.apiKey, formData.baseUrl, settings?.user?.token]", [26450, 26494], "[settings?.aiPresets, pipeSettings, aiKey]", [27327, 27381], "[aiPresets, selectedPreset, updatePipeSettings, aiKey, shortcutKey]", [8517, 8570], [26467, 26511], [27341, 27388], "[aiKey, aiPresets, selectedPreset, shortcutKey, updatePipeSettings]"]