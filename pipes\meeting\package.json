{"name": "meeting", "version": "0.1.5", "private": true, "repository": {"type": "git", "url": "git+https://github.com/m13v/screen-pipe/tree/main/pipes/meeting.git"}, "scripts": {"dev": "next dev", "build": "next build --no-lint", "start": "next start", "lint": "next lint"}, "dependencies": {"@discordjs/rest": "^2.4.3", "@discordjs/voice": "^0.18.0", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-dialog": "^1.1.3", "@radix-ui/react-hover-card": "^1.1.5", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.3", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-select": "^2.1.3", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.5", "@radix-ui/react-tooltip": "^1.1.5", "@screenpipe/browser": "^0.1.31", "@screenpipe/js": "1.0.7", "@tauri-apps/api": "^2.2.0", "@tiptap/extension-color": "^2.2.4", "@tiptap/extension-list-item": "^2.2.4", "@tiptap/extension-text-style": "^2.2.4", "@tiptap/pm": "^2.11.3", "@tiptap/react": "^2.2.4", "@tiptap/starter-kit": "^2.2.4", "@types/lodash": "^4.17.13", "@types/react-syntax-highlighter": "^15.5.13", "@vercel/analytics": "^1.4.1", "bufferutil": "^4.0.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.0", "date-fns": "^3.3.1", "diff": "^7.0.0", "discord.js": "^14.18.0", "erlpack": "^0.1.4", "framer-motion": "^12.0.11", "localforage": "^1.10.0", "lodash": "^4.17.21", "lucide-react": "^0.468.0", "next": "15.1.0", "openai": "^4.76.3", "p-throttle": "^7.0.0", "posthog-js": "^1.217.1", "react": "^18.2.0", "react-day-picker": "8.10.1", "react-dom": "^18.2.0", "react-error-boundary": "^5.0.0", "react-markdown": "^9.0.1", "react-split": "^2.0.14", "react-syntax-highlighter": "^15.6.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "use-sound": "^4.0.3", "utf-8-validate": "^6.0.5", "zlib-sync": "^0.1.9"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/diff": "^7.0.1", "@types/next": "^9.0.0", "@types/node": "^22.10.2", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.4.20", "eslint": "^9", "eslint-config-next": "15.1.0", "postcss": "^8.4.49", "tailwindcss": "^3.4.16", "typescript": "^5"}}