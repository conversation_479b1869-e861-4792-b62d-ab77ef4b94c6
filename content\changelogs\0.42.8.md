### **New Features:**
- **Added purge button:** Introduced a new purge button for enhanced control over application state.
- **Custom port for MCP server:** Implemented the ability to set a custom port for the MCP server.

### **Improvements:**
- **Updated build instructions:** Revised the build from source instructions to include Xcode for better clarity.
- **Improved search URL handling:** Fixed broken search URL functionality for improved user experience.
- **Enhanced window capturing:** Added functionality to capture windows that do not have a PID.

### **Fixes:**
- **Fixed app name window name fetch:** Resolved the issue with app name fetching from the frame instead of OCR text.
- **Fixed AI preset onboarding issue:** Corrected the AI preset functionality that was not working during onboarding.
- **Resolved OpenAI model display issue:** Fixed the issue of the OpenAI model not showing.
- **Fixed CLI functionality on Windows:** Addressed the issue that affected Screenpipe CLI on Windows 10 and 11.
- **Resolved disk usage display issue:** Fixed a problem with disk usage reporting.
- **Restored confirmation dialog:** Reintroduced the confirmation dialog for resetting the pipe that was removed in a previous update.
- **Fixed native look on Windows:** Corrected issues to ensure the application has a native look on Windows.
- **Fixed `test-linux` in GitHub Codespaces:** Resolved issues so that `test-linux` now works correctly on GitHub Codespaces.

#### **Full Changelog:** [ed897..74c61](https://github.com/mediar-ai/screenpipe/compare/ed897..74c61)

