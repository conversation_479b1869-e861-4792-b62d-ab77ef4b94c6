Based on your commits, here's the updated changelog:

### **New Features:**
- **Automatically pause vision capture on sleep:** Implemented a feature that automatically pauses vision capture when the computer goes to sleep and resumes when waking up.

### **Improvements:**
- **Bumped search enhancements:** Improved search capabilities for a better user experience.

### **Fixes:**
- **Fixed search filter generator:** Resolved an issue where the search filter generator was not functioning properly.
- **Fixed AI search functionality:** Addressed a bug that caused the AI-based search not to work correctly.
- **Fixed CLI command on Windows:** Corrected functionality of CLI command specific to Windows.
- **Fixed longevity issues:** Resolved various longevity-related problems to improve overall stability.

This changelog should provide clear value to your users based on the identified commits.

#### **Full Changelog:** [848d6..52ed8](https://github.com/mediar-ai/screenpipe/compare/848d6..52ed8)

