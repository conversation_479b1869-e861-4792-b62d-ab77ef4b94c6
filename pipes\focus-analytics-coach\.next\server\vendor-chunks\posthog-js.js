"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/posthog-js";
exports.ids = ["vendor-chunks/posthog-js"];
exports.modules = {

/***/ "(rsc)/./node_modules/posthog-js/dist/module.js":
/*!************************************************!*\
  !*** ./node_modules/posthog-js/dist/module.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   COPY_AUTOCAPTURE_EVENT: () => (/* binding */ v),\n/* harmony export */   Compression: () => (/* binding */ e),\n/* harmony export */   PostHog: () => (/* binding */ mo),\n/* harmony export */   SurveyQuestionBranchingType: () => (/* binding */ ms),\n/* harmony export */   SurveyQuestionType: () => (/* binding */ fs),\n/* harmony export */   SurveyType: () => (/* binding */ gs),\n/* harmony export */   \"default\": () => (/* binding */ yo),\n/* harmony export */   knownUnsafeEditableEvent: () => (/* binding */ g),\n/* harmony export */   posthog: () => (/* binding */ yo),\n/* harmony export */   severityLevels: () => (/* binding */ f)\n/* harmony export */ });\nvar e,t=\"undefined\"!=typeof window?window:void 0,i=\"undefined\"!=typeof globalThis?globalThis:t,r=Array.prototype,n=r.forEach,s=r.indexOf,o=null==i?void 0:i.navigator,a=null==i?void 0:i.document,l=null==i?void 0:i.location,u=null==i?void 0:i.fetch,c=null!=i&&i.XMLHttpRequest&&\"withCredentials\"in new i.XMLHttpRequest?i.XMLHttpRequest:void 0,d=null==i?void 0:i.AbortController,h=null==o?void 0:o.userAgent,_=null!=t?t:{},p={DEBUG:!1,LIB_VERSION:\"1.207.2\"},v=\"$copy_autocapture\",g=[\"$snapshot\",\"$pageview\",\"$pageleave\",\"$set\",\"survey dismissed\",\"survey sent\",\"survey shown\",\"$identify\",\"$groupidentify\",\"$create_alias\",\"$$client_ingestion_warning\",\"$web_experiment_applied\",\"$feature_enrollment_update\",\"$feature_flag_called\"];!function(e){e.GZipJS=\"gzip-js\",e.Base64=\"base64\"}(e||(e={}));var f=[\"fatal\",\"error\",\"warning\",\"log\",\"info\",\"debug\"];function m(e,t){return-1!==e.indexOf(t)}var b=function(e){return e.replace(/^[\\s\\uFEFF\\xA0]+|[\\s\\uFEFF\\xA0]+$/g,\"\")},y=function(e){return e.replace(/^\\$/,\"\")};var w=Array.isArray,S=Object.prototype,E=S.hasOwnProperty,k=S.toString,x=w||function(e){return\"[object Array]\"===k.call(e)},I=e=>\"function\"==typeof e,C=e=>e===Object(e)&&!x(e),P=e=>{if(C(e)){for(var t in e)if(E.call(e,t))return!1;return!0}return!1},F=e=>void 0===e,R=e=>\"[object String]\"==k.call(e),T=e=>R(e)&&0===e.trim().length,$=e=>null===e,O=e=>F(e)||$(e),M=e=>\"[object Number]\"==k.call(e),L=e=>\"[object Boolean]\"===k.call(e),A=e=>e instanceof FormData,D=e=>m(g,e),N=e=>{var i={_log:function(i){if(t&&(p.DEBUG||_.POSTHOG_DEBUG)&&!F(t.console)&&t.console){for(var r=(\"__rrweb_original__\"in t.console[i]?t.console[i].__rrweb_original__:t.console[i]),n=arguments.length,s=new Array(n>1?n-1:0),o=1;o<n;o++)s[o-1]=arguments[o];r(e,...s)}},info:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];i._log(\"log\",...t)},warn:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];i._log(\"warn\",...t)},error:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];i._log(\"error\",...t)},critical:function(){for(var t=arguments.length,i=new Array(t),r=0;r<t;r++)i[r]=arguments[r];console.error(e,...i)},uninitializedWarning:e=>{i.error(\"You must initialize PostHog before calling \".concat(e))},createLogger:t=>N(\"\".concat(e,\" \").concat(t))};return i},q=N(\"[PostHog.js]\"),B=q.createLogger,H=B(\"[ExternalScriptsLoader]\"),U=(e,t,i)=>{if(e.config.disable_external_dependency_loading)return H.warn(\"\".concat(t,\" was requested but loading of external scripts is disabled.\")),i(\"Loading of external scripts is disabled\");var r=()=>{if(!a)return i(\"document not found\");var r=a.createElement(\"script\");if(r.type=\"text/javascript\",r.crossOrigin=\"anonymous\",r.src=t,r.onload=e=>i(void 0,e),r.onerror=e=>i(e),e.config.prepare_external_dependency_script&&(r=e.config.prepare_external_dependency_script(r)),!r)return i(\"prepare_external_dependency_script returned null\");var n,s=a.querySelectorAll(\"body > script\");s.length>0?null===(n=s[0].parentNode)||void 0===n||n.insertBefore(r,s[0]):a.body.appendChild(r)};null!=a&&a.body?r():null==a||a.addEventListener(\"DOMContentLoaded\",r)};function z(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),i.push.apply(i,r)}return i}function j(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?z(Object(i),!0).forEach((function(t){W(e,t,i[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):z(Object(i)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}))}return e}function W(e,t,i){return t in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}function V(e,t){if(null==e)return{};var i,r,n=function(e,t){if(null==e)return{};var i,r,n={},s=Object.keys(e);for(r=0;r<s.length;r++)i=s[r],t.indexOf(i)>=0||(n[i]=e[i]);return n}(e,t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(r=0;r<s.length;r++)i=s[r],t.indexOf(i)>=0||Object.prototype.propertyIsEnumerable.call(e,i)&&(n[i]=e[i])}return n}_.__PosthogExtensions__=_.__PosthogExtensions__||{},_.__PosthogExtensions__.loadExternalDependency=(e,t,i)=>{var r=\"/static/\".concat(t,\".js\")+\"?v=\".concat(e.version);if(\"remote-config\"===t&&(r=\"/array/\".concat(e.config.token,\"/config.js\")),\"toolbar\"===t){var n=3e5,s=Math.floor(Date.now()/n)*n;r=\"\".concat(r,\"&t=\").concat(s)}var o=e.requestRouter.endpointFor(\"assets\",r);U(e,o,i)},_.__PosthogExtensions__.loadSiteApp=(e,t,i)=>{var r=e.requestRouter.endpointFor(\"api\",t);U(e,r,i)};var G={};function J(e,t,i){if(x(e))if(n&&e.forEach===n)e.forEach(t,i);else if(\"length\"in e&&e.length===+e.length)for(var r=0,s=e.length;r<s;r++)if(r in e&&t.call(i,e[r],r)===G)return}function Y(e,t,i){if(!O(e)){if(x(e))return J(e,t,i);if(A(e)){for(var r of e.entries())if(t.call(i,r[1],r[0])===G)return}else for(var n in e)if(E.call(e,n)&&t.call(i,e[n],n)===G)return}}var K=function(e){for(var t=arguments.length,i=new Array(t>1?t-1:0),r=1;r<t;r++)i[r-1]=arguments[r];return J(i,(function(t){for(var i in t)void 0!==t[i]&&(e[i]=t[i])})),e},X=function(e){for(var t=arguments.length,i=new Array(t>1?t-1:0),r=1;r<t;r++)i[r-1]=arguments[r];return J(i,(function(t){J(t,(function(t){e.push(t)}))})),e};function Q(e){for(var t=Object.keys(e),i=t.length,r=new Array(i);i--;)r[i]=[t[i],e[t[i]]];return r}var Z=function(e){try{return e()}catch(e){return}},ee=function(e){return function(){try{for(var t=arguments.length,i=new Array(t),r=0;r<t;r++)i[r]=arguments[r];return e.apply(this,i)}catch(e){q.critical(\"Implementation error. Please turn on debug mode and open a ticket on https://app.posthog.com/home#panel=support%3Asupport%3A.\"),q.critical(e)}}},te=function(e){var t={};return Y(e,(function(e,i){R(e)&&e.length>0&&(t[i]=e)})),t};function ie(e,t){return i=e,r=e=>R(e)&&!$(t)?e.slice(0,t):e,n=new Set,function e(t,i){return t!==Object(t)?r?r(t,i):t:n.has(t)?void 0:(n.add(t),x(t)?(s=[],J(t,(t=>{s.push(e(t))}))):(s={},Y(t,((t,i)=>{n.has(t)||(s[i]=e(t,i))}))),s);var s}(i);var i,r,n}var re=function(){function e(t){return t&&(t.preventDefault=e.preventDefault,t.stopPropagation=e.stopPropagation),t}return e.preventDefault=function(){this.returnValue=!1},e.stopPropagation=function(){this.cancelBubble=!0},function(i,r,n,s,o){if(i)if(i.addEventListener&&!s)i.addEventListener(r,n,!!o);else{var a=\"on\"+r,l=i[a];i[a]=function(i,r,n){return function(s){if(s=s||e(null==t?void 0:t.event)){var o,a=!0;I(n)&&(o=n(s));var l=r.call(i,s);return!1!==o&&!1!==l||(a=!1),a}}}(i,n,l)}else q.error(\"No valid element provided to register_event\")}}();function ne(e,t){for(var i=0;i<e.length;i++)if(t(e[i]))return e[i]}var se=\"$people_distinct_id\",oe=\"__alias\",ae=\"__timers\",le=\"$autocapture_disabled_server_side\",ue=\"$heatmaps_enabled_server_side\",ce=\"$exception_capture_enabled_server_side\",de=\"$web_vitals_enabled_server_side\",he=\"$dead_clicks_enabled_server_side\",_e=\"$web_vitals_allowed_metrics\",pe=\"$session_recording_enabled_server_side\",ve=\"$console_log_recording_enabled_server_side\",ge=\"$session_recording_network_payload_capture\",fe=\"$session_recording_canvas_recording\",me=\"$replay_sample_rate\",be=\"$replay_minimum_duration\",ye=\"$replay_script_config\",we=\"$sesid\",Se=\"$session_is_sampled\",Ee=\"$session_recording_url_trigger_activated_session\",ke=\"$session_recording_event_trigger_activated_session\",xe=\"$enabled_feature_flags\",Ie=\"$early_access_features\",Ce=\"$stored_person_properties\",Pe=\"$stored_group_properties\",Fe=\"$surveys\",Re=\"$surveys_activated\",Te=\"$flag_call_reported\",$e=\"$user_state\",Oe=\"$client_session_props\",Me=\"$capture_rate_limit\",Le=\"$initial_campaign_params\",Ae=\"$initial_referrer_info\",De=\"$initial_person_info\",Ne=\"$epp\",qe=\"__POSTHOG_TOOLBAR__\",Be=\"$posthog_cookieless\",He=[se,oe,\"__cmpns\",ae,pe,ue,we,xe,$e,Ie,Pe,Ce,Fe,Te,Oe,Me,Le,Ae,Ne],Ue=B(\"[FeatureFlags]\"),ze=\"$active_feature_flags\",je=\"$override_feature_flags\",We=\"$feature_flag_payloads\",Ve=e=>{var t={};for(var[i,r]of Q(e||{}))r&&(t[i]=r);return t};class Ge{constructor(e){W(this,\"_override_warning\",!1),W(this,\"_hasLoadedFlags\",!1),W(this,\"_requestInFlight\",!1),W(this,\"_reloadingDisabled\",!1),W(this,\"_additionalReloadRequested\",!1),W(this,\"_decideCalled\",!1),W(this,\"_flagsLoadedFromRemote\",!1),this.instance=e,this.featureFlagEventHandlers=[]}decide(){if(this.instance.config.__preview_remote_config)this._decideCalled=!0;else{var e=!this._reloadDebouncer&&(this.instance.config.advanced_disable_feature_flags||this.instance.config.advanced_disable_feature_flags_on_first_load);this._callDecideEndpoint({disableFlags:e})}}get hasLoadedFlags(){return this._hasLoadedFlags}getFlags(){return Object.keys(this.getFlagVariants())}getFlagVariants(){var e=this.instance.get_property(xe),t=this.instance.get_property(je);if(!t)return e||{};for(var i=K({},e),r=Object.keys(t),n=0;n<r.length;n++)i[r[n]]=t[r[n]];return this._override_warning||(Ue.warn(\" Overriding feature flags!\",{enabledFlags:e,overriddenFlags:t,finalFlags:i}),this._override_warning=!0),i}getFlagPayloads(){return this.instance.get_property(We)||{}}reloadFeatureFlags(){this._reloadingDisabled||this.instance.config.advanced_disable_feature_flags||this._reloadDebouncer||(this._reloadDebouncer=setTimeout((()=>{this._callDecideEndpoint()}),5))}clearDebouncer(){clearTimeout(this._reloadDebouncer),this._reloadDebouncer=void 0}ensureFlagsLoaded(){this._hasLoadedFlags||this._requestInFlight||this._reloadDebouncer||this.reloadFeatureFlags()}setAnonymousDistinctId(e){this.$anon_distinct_id=e}setReloadingPaused(e){this._reloadingDisabled=e}_callDecideEndpoint(t){if(this.clearDebouncer(),!this.instance.config.advanced_disable_decide)if(this._requestInFlight)this._additionalReloadRequested=!0;else{var i={token:this.instance.config.token,distinct_id:this.instance.get_distinct_id(),groups:this.instance.getGroups(),$anon_distinct_id:this.$anon_distinct_id,person_properties:this.instance.get_property(Ce),group_properties:this.instance.get_property(Pe)};(null!=t&&t.disableFlags||this.instance.config.advanced_disable_feature_flags)&&(i.disable_flags=!0),this._requestInFlight=!0,this.instance._send_request({method:\"POST\",url:this.instance.requestRouter.endpointFor(\"api\",\"/decide/?v=3\"),data:i,compression:this.instance.config.disable_compression?void 0:e.Base64,timeout:this.instance.config.feature_flag_request_timeout_ms,callback:e=>{var t,r,n=!0;(200===e.statusCode&&(this.$anon_distinct_id=void 0,n=!1),this._requestInFlight=!1,this._decideCalled)||(this._decideCalled=!0,this.instance._onRemoteConfig(null!==(r=e.json)&&void 0!==r?r:{}));i.disable_flags||(this._flagsLoadedFromRemote=!n,this.receivedFeatureFlags(null!==(t=e.json)&&void 0!==t?t:{},n),this._additionalReloadRequested&&(this._additionalReloadRequested=!1,this._callDecideEndpoint()))}})}}getFeatureFlag(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(this._hasLoadedFlags||this.getFlags()&&this.getFlags().length>0){var i,r,n,s,o,a=this.getFlagVariants()[e],l=\"\".concat(a),u=this.instance.get_property(Te)||{};if(t.send_event||!(\"send_event\"in t))if(!(e in u)||!u[e].includes(l))x(u[e])?u[e].push(l):u[e]=[l],null===(i=this.instance.persistence)||void 0===i||i.register({[Te]:u}),this.instance.capture(\"$feature_flag_called\",{$feature_flag:e,$feature_flag_response:a,$feature_flag_payload:this.getFeatureFlagPayload(e)||null,$feature_flag_bootstrapped_response:(null===(r=this.instance.config.bootstrap)||void 0===r||null===(n=r.featureFlags)||void 0===n?void 0:n[e])||null,$feature_flag_bootstrapped_payload:(null===(s=this.instance.config.bootstrap)||void 0===s||null===(o=s.featureFlagPayloads)||void 0===o?void 0:o[e])||null,$used_bootstrap_value:!this._flagsLoadedFromRemote});return a}Ue.warn('getFeatureFlag for key \"'+e+\"\\\" failed. Feature flags didn't load in time.\")}getFeatureFlagPayload(e){return this.getFlagPayloads()[e]}isFeatureEnabled(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(this._hasLoadedFlags||this.getFlags()&&this.getFlags().length>0)return!!this.getFeatureFlag(e,t);Ue.warn('isFeatureEnabled for key \"'+e+\"\\\" failed. Feature flags didn't load in time.\")}addFeatureFlagsHandler(e){this.featureFlagEventHandlers.push(e)}removeFeatureFlagsHandler(e){this.featureFlagEventHandlers=this.featureFlagEventHandlers.filter((t=>t!==e))}receivedFeatureFlags(e,t){if(this.instance.persistence){this._hasLoadedFlags=!0;var i=this.getFlagVariants(),r=this.getFlagPayloads();!function(e,t){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},n=e.featureFlags,s=e.featureFlagPayloads;if(n)if(x(n)){var o={};if(n)for(var a=0;a<n.length;a++)o[n[a]]=!0;t&&t.register({[ze]:n,[xe]:o})}else{var l=n,u=s;e.errorsWhileComputingFlags&&(l=j(j({},i),l),u=j(j({},r),u)),t&&t.register({[ze]:Object.keys(Ve(l)),[xe]:l||{},[We]:u||{}})}}(e,this.instance.persistence,i,r),this._fireFeatureFlagsCallbacks(t)}}override(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!this.instance.__loaded||!this.instance.persistence)return Ue.uninitializedWarning(\"posthog.feature_flags.override\");if(this._override_warning=t,!1===e)this.instance.persistence.unregister(je);else if(x(e)){for(var i={},r=0;r<e.length;r++)i[e[r]]=!0;this.instance.persistence.register({[je]:i})}else this.instance.persistence.register({[je]:e})}onFeatureFlags(e){if(this.addFeatureFlagsHandler(e),this._hasLoadedFlags){var{flags:t,flagVariants:i}=this._prepareFeatureFlagsForCallbacks();e(t,i)}return()=>this.removeFeatureFlagsHandler(e)}updateEarlyAccessFeatureEnrollment(e,t){var i,r=(this.instance.get_property(Ie)||[]).find((t=>t.flagKey===e)),n={[\"$feature_enrollment/\".concat(e)]:t},s={$feature_flag:e,$feature_enrollment:t,$set:n};r&&(s.$early_access_feature_name=r.name),this.instance.capture(\"$feature_enrollment_update\",s),this.setPersonPropertiesForFlags(n,!1);var o=j(j({},this.getFlagVariants()),{},{[e]:t});null===(i=this.instance.persistence)||void 0===i||i.register({[ze]:Object.keys(Ve(o)),[xe]:o}),this._fireFeatureFlagsCallbacks()}getEarlyAccessFeatures(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=this.instance.get_property(Ie);if(i&&!t)return e(i);this.instance._send_request({url:this.instance.requestRouter.endpointFor(\"api\",\"/api/early_access_features/?token=\".concat(this.instance.config.token)),method:\"GET\",callback:t=>{var i;if(t.json){var r=t.json.earlyAccessFeatures;return null===(i=this.instance.persistence)||void 0===i||i.register({[Ie]:r}),e(r)}}})}_prepareFeatureFlagsForCallbacks(){var e=this.getFlags(),t=this.getFlagVariants();return{flags:e.filter((e=>t[e])),flagVariants:Object.keys(t).filter((e=>t[e])).reduce(((e,i)=>(e[i]=t[i],e)),{})}}_fireFeatureFlagsCallbacks(e){var{flags:t,flagVariants:i}=this._prepareFeatureFlagsForCallbacks();this.featureFlagEventHandlers.forEach((r=>r(t,i,{errorsLoading:e})))}setPersonPropertiesForFlags(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],i=this.instance.get_property(Ce)||{};this.instance.register({[Ce]:j(j({},i),e)}),t&&this.instance.reloadFeatureFlags()}resetPersonPropertiesForFlags(){this.instance.unregister(Ce)}setGroupPropertiesForFlags(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],i=this.instance.get_property(Pe)||{};0!==Object.keys(i).length&&Object.keys(i).forEach((t=>{i[t]=j(j({},i[t]),e[t]),delete e[t]})),this.instance.register({[Pe]:j(j({},i),e)}),t&&this.instance.reloadFeatureFlags()}resetGroupPropertiesForFlags(e){if(e){var t=this.instance.get_property(Pe)||{};this.instance.register({[Pe]:j(j({},t),{},{[e]:{}})})}else this.instance.unregister(Pe)}}Math.trunc||(Math.trunc=function(e){return e<0?Math.ceil(e):Math.floor(e)}),Number.isInteger||(Number.isInteger=function(e){return M(e)&&isFinite(e)&&Math.floor(e)===e});var Je=\"0123456789abcdef\";class Ye{constructor(e){if(this.bytes=e,16!==e.length)throw new TypeError(\"not 128-bit length\")}static fromFieldsV7(e,t,i,r){if(!Number.isInteger(e)||!Number.isInteger(t)||!Number.isInteger(i)||!Number.isInteger(r)||e<0||t<0||i<0||r<0||e>0xffffffffffff||t>4095||i>1073741823||r>4294967295)throw new RangeError(\"invalid field value\");var n=new Uint8Array(16);return n[0]=e/Math.pow(2,40),n[1]=e/Math.pow(2,32),n[2]=e/Math.pow(2,24),n[3]=e/Math.pow(2,16),n[4]=e/Math.pow(2,8),n[5]=e,n[6]=112|t>>>8,n[7]=t,n[8]=128|i>>>24,n[9]=i>>>16,n[10]=i>>>8,n[11]=i,n[12]=r>>>24,n[13]=r>>>16,n[14]=r>>>8,n[15]=r,new Ye(n)}toString(){for(var e=\"\",t=0;t<this.bytes.length;t++)e=e+Je.charAt(this.bytes[t]>>>4)+Je.charAt(15&this.bytes[t]),3!==t&&5!==t&&7!==t&&9!==t||(e+=\"-\");if(36!==e.length)throw new Error(\"Invalid UUIDv7 was generated\");return e}clone(){return new Ye(this.bytes.slice(0))}equals(e){return 0===this.compareTo(e)}compareTo(e){for(var t=0;t<16;t++){var i=this.bytes[t]-e.bytes[t];if(0!==i)return Math.sign(i)}return 0}}class Ke{constructor(){W(this,\"timestamp\",0),W(this,\"counter\",0),W(this,\"random\",new Ze)}generate(){var e=this.generateOrAbort();if(F(e)){this.timestamp=0;var t=this.generateOrAbort();if(F(t))throw new Error(\"Could not generate UUID after timestamp reset\");return t}return e}generateOrAbort(){var e=Date.now();if(e>this.timestamp)this.timestamp=e,this.resetCounter();else{if(!(e+1e4>this.timestamp))return;this.counter++,this.counter>4398046511103&&(this.timestamp++,this.resetCounter())}return Ye.fromFieldsV7(this.timestamp,Math.trunc(this.counter/Math.pow(2,30)),this.counter&Math.pow(2,30)-1,this.random.nextUint32())}resetCounter(){this.counter=1024*this.random.nextUint32()+(1023&this.random.nextUint32())}}var Xe,Qe=e=>{if(\"undefined\"!=typeof UUIDV7_DENY_WEAK_RNG&&UUIDV7_DENY_WEAK_RNG)throw new Error(\"no cryptographically strong RNG available\");for(var t=0;t<e.length;t++)e[t]=65536*Math.trunc(65536*Math.random())+Math.trunc(65536*Math.random());return e};t&&!F(t.crypto)&&crypto.getRandomValues&&(Qe=e=>crypto.getRandomValues(e));class Ze{constructor(){W(this,\"buffer\",new Uint32Array(8)),W(this,\"cursor\",1/0)}nextUint32(){return this.cursor>=this.buffer.length&&(Qe(this.buffer),this.cursor=0),this.buffer[this.cursor++]}}var et=()=>tt().toString(),tt=()=>(Xe||(Xe=new Ke)).generate(),it=\"Thu, 01 Jan 1970 00:00:00 GMT\",rt=\"\";var nt=/[a-z0-9][a-z0-9-]+\\.[a-z]{2,}$/i;function st(e,t){if(t){var i=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a;if(rt)return rt;if(!t)return\"\";if([\"localhost\",\"127.0.0.1\"].includes(e))return\"\";for(var i=e.split(\".\"),r=Math.min(i.length,8),n=\"dmn_chk_\"+et(),s=new RegExp(\"(^|;)\\\\s*\"+n+\"=1\");!rt&&r--;){var o=i.slice(r).join(\".\"),l=n+\"=1;domain=.\"+o;t.cookie=l,s.test(t.cookie)&&(t.cookie=l+\";expires=\"+it,rt=o)}return rt}(e);if(!i){var r=(e=>{var t=e.match(nt);return t?t[0]:\"\"})(e);r!==i&&q.info(\"Warning: cookie subdomain discovery mismatch\",r,i),i=r}return i?\"; domain=.\"+i:\"\"}return\"\"}var ot={is_supported:()=>!!a,error:function(e){q.error(\"cookieStore error: \"+e)},get:function(e){if(a){try{for(var t=e+\"=\",i=a.cookie.split(\";\").filter((e=>e.length)),r=0;r<i.length;r++){for(var n=i[r];\" \"==n.charAt(0);)n=n.substring(1,n.length);if(0===n.indexOf(t))return decodeURIComponent(n.substring(t.length,n.length))}}catch(e){}return null}},parse:function(e){var t;try{t=JSON.parse(ot.get(e))||{}}catch(e){}return t},set:function(e,t,i,r,n){if(a)try{var s=\"\",o=\"\",l=st(a.location.hostname,r);if(i){var u=new Date;u.setTime(u.getTime()+24*i*60*60*1e3),s=\"; expires=\"+u.toUTCString()}n&&(o=\"; secure\");var c=e+\"=\"+encodeURIComponent(JSON.stringify(t))+s+\"; SameSite=Lax; path=/\"+l+o;return c.length>3686.4&&q.warn(\"cookieStore warning: large cookie, len=\"+c.length),a.cookie=c,c}catch(e){return}},remove:function(e,t){try{ot.set(e,\"\",-1,t)}catch(e){return}}},at=null,lt={is_supported:function(){if(!$(at))return at;var e=!0;if(F(t))e=!1;else try{var i=\"__mplssupport__\";lt.set(i,\"xyz\"),'\"xyz\"'!==lt.get(i)&&(e=!1),lt.remove(i)}catch(t){e=!1}return e||q.error(\"localStorage unsupported; falling back to cookie store\"),at=e,e},error:function(e){q.error(\"localStorage error: \"+e)},get:function(e){try{return null==t?void 0:t.localStorage.getItem(e)}catch(e){lt.error(e)}return null},parse:function(e){try{return JSON.parse(lt.get(e))||{}}catch(e){}return null},set:function(e,i){try{null==t||t.localStorage.setItem(e,JSON.stringify(i))}catch(e){lt.error(e)}},remove:function(e){try{null==t||t.localStorage.removeItem(e)}catch(e){lt.error(e)}}},ut=[\"distinct_id\",we,Se,Ne,De],ct=j(j({},lt),{},{parse:function(e){try{var t={};try{t=ot.parse(e)||{}}catch(e){}var i=K(t,JSON.parse(lt.get(e)||\"{}\"));return lt.set(e,i),i}catch(e){}return null},set:function(e,t,i,r,n,s){try{lt.set(e,t,void 0,void 0,s);var o={};ut.forEach((e=>{t[e]&&(o[e]=t[e])})),Object.keys(o).length&&ot.set(e,o,i,r,n,s)}catch(e){lt.error(e)}},remove:function(e,i){try{null==t||t.localStorage.removeItem(e),ot.remove(e,i)}catch(e){lt.error(e)}}}),dt={},ht={is_supported:function(){return!0},error:function(e){q.error(\"memoryStorage error: \"+e)},get:function(e){return dt[e]||null},parse:function(e){return dt[e]||null},set:function(e,t){dt[e]=t},remove:function(e){delete dt[e]}},_t=null,pt={is_supported:function(){if(!$(_t))return _t;if(_t=!0,F(t))_t=!1;else try{var e=\"__support__\";pt.set(e,\"xyz\"),'\"xyz\"'!==pt.get(e)&&(_t=!1),pt.remove(e)}catch(e){_t=!1}return _t},error:function(e){q.error(\"sessionStorage error: \",e)},get:function(e){try{return null==t?void 0:t.sessionStorage.getItem(e)}catch(e){pt.error(e)}return null},parse:function(e){try{return JSON.parse(pt.get(e))||null}catch(e){}return null},set:function(e,i){try{null==t||t.sessionStorage.setItem(e,JSON.stringify(i))}catch(e){pt.error(e)}},remove:function(e){try{null==t||t.sessionStorage.removeItem(e)}catch(e){pt.error(e)}}},vt=[\"localhost\",\"127.0.0.1\"],gt=e=>{var t=null==a?void 0:a.createElement(\"a\");return F(t)?null:(t.href=e,t)},ft=function(e,t){return!!function(e){try{new RegExp(e)}catch(e){return!1}return!0}(t)&&new RegExp(t).test(e)},mt=function(e){var t,i,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:\"&\",n=[];return Y(e,(function(e,r){F(e)||F(r)||\"undefined\"===r||(t=encodeURIComponent((e=>e instanceof File)(e)?e.name:e.toString()),i=encodeURIComponent(r),n[n.length]=i+\"=\"+t)})),n.join(r)},bt=function(e,t){for(var i,r=((e.split(\"#\")[0]||\"\").split(\"?\")[1]||\"\").split(\"&\"),n=0;n<r.length;n++){var s=r[n].split(\"=\");if(s[0]===t){i=s;break}}if(!x(i)||i.length<2)return\"\";var o=i[1];try{o=decodeURIComponent(o)}catch(e){q.error(\"Skipping decoding for malformed query param: \"+o)}return o.replace(/\\+/g,\" \")},yt=function(e,t,i){if(!e||!t||!t.length)return e;for(var r=e.split(\"#\"),n=r[0]||\"\",s=r[1],o=n.split(\"?\"),a=o[1],l=o[0],u=(a||\"\").split(\"&\"),c=[],d=0;d<u.length;d++){var h=u[d].split(\"=\");x(h)&&(t.includes(h[0])?c.push(h[0]+\"=\"+i):c.push(u[d]))}var _=l;return null!=a&&(_+=\"?\"+c.join(\"&\")),null!=s&&(_+=\"#\"+s),_},wt=function(e,t){var i=e.match(new RegExp(t+\"=([^&]*)\"));return i?i[1]:null},St=\"Mobile\",Et=\"iOS\",kt=\"Android\",xt=\"Tablet\",It=kt+\" \"+xt,Ct=\"iPad\",Pt=\"Apple\",Ft=Pt+\" Watch\",Rt=\"Safari\",Tt=\"BlackBerry\",$t=\"Samsung\",Ot=$t+\"Browser\",Mt=$t+\" Internet\",Lt=\"Chrome\",At=Lt+\" OS\",Dt=Lt+\" \"+Et,Nt=\"Internet Explorer\",qt=Nt+\" \"+St,Bt=\"Opera\",Ht=Bt+\" Mini\",Ut=\"Edge\",zt=\"Microsoft \"+Ut,jt=\"Firefox\",Wt=jt+\" \"+Et,Vt=\"Nintendo\",Gt=\"PlayStation\",Jt=\"Xbox\",Yt=kt+\" \"+St,Kt=St+\" \"+Rt,Xt=\"Windows\",Qt=Xt+\" Phone\",Zt=\"Nokia\",ei=\"Ouya\",ti=\"Generic\",ii=ti+\" \"+St.toLowerCase(),ri=ti+\" \"+xt.toLowerCase(),ni=\"Konqueror\",si=\"(\\\\d+(\\\\.\\\\d+)?)\",oi=new RegExp(\"Version/\"+si),ai=new RegExp(Jt,\"i\"),li=new RegExp(Gt+\" \\\\w+\",\"i\"),ui=new RegExp(Vt+\" \\\\w+\",\"i\"),ci=new RegExp(Tt+\"|PlayBook|BB10\",\"i\"),di={\"NT3.51\":\"NT 3.11\",\"NT4.0\":\"NT 4.0\",\"5.0\":\"2000\",5.1:\"XP\",5.2:\"XP\",\"6.0\":\"Vista\",6.1:\"7\",6.2:\"8\",6.3:\"8.1\",6.4:\"10\",\"10.0\":\"10\"};var hi=(e,t)=>t&&m(t,Pt)||function(e){return m(e,Rt)&&!m(e,Lt)&&!m(e,kt)}(e),_i=function(e,t){return t=t||\"\",m(e,\" OPR/\")&&m(e,\"Mini\")?Ht:m(e,\" OPR/\")?Bt:ci.test(e)?Tt:m(e,\"IE\"+St)||m(e,\"WPDesktop\")?qt:m(e,Ot)?Mt:m(e,Ut)||m(e,\"Edg/\")?zt:m(e,\"FBIOS\")?\"Facebook \"+St:m(e,\"UCWEB\")||m(e,\"UCBrowser\")?\"UC Browser\":m(e,\"CriOS\")?Dt:m(e,\"CrMo\")||m(e,Lt)?Lt:m(e,kt)&&m(e,Rt)?Yt:m(e,\"FxiOS\")?Wt:m(e.toLowerCase(),ni.toLowerCase())?ni:hi(e,t)?m(e,St)?Kt:Rt:m(e,jt)?jt:m(e,\"MSIE\")||m(e,\"Trident/\")?Nt:m(e,\"Gecko\")?jt:\"\"},pi={[qt]:[new RegExp(\"rv:\"+si)],[zt]:[new RegExp(Ut+\"?\\\\/\"+si)],[Lt]:[new RegExp(\"(\"+Lt+\"|CrMo)\\\\/\"+si)],[Dt]:[new RegExp(\"CriOS\\\\/\"+si)],\"UC Browser\":[new RegExp(\"(UCBrowser|UCWEB)\\\\/\"+si)],[Rt]:[oi],[Kt]:[oi],[Bt]:[new RegExp(\"(Opera|OPR)\\\\/\"+si)],[jt]:[new RegExp(jt+\"\\\\/\"+si)],[Wt]:[new RegExp(\"FxiOS\\\\/\"+si)],[ni]:[new RegExp(\"Konqueror[:/]?\"+si,\"i\")],[Tt]:[new RegExp(Tt+\" \"+si),oi],[Yt]:[new RegExp(\"android\\\\s\"+si,\"i\")],[Mt]:[new RegExp(Ot+\"\\\\/\"+si)],[Nt]:[new RegExp(\"(rv:|MSIE )\"+si)],Mozilla:[new RegExp(\"rv:\"+si)]},vi=[[new RegExp(Jt+\"; \"+Jt+\" (.*?)[);]\",\"i\"),e=>[Jt,e&&e[1]||\"\"]],[new RegExp(Vt,\"i\"),[Vt,\"\"]],[new RegExp(Gt,\"i\"),[Gt,\"\"]],[ci,[Tt,\"\"]],[new RegExp(Xt,\"i\"),(e,t)=>{if(/Phone/.test(t)||/WPDesktop/.test(t))return[Qt,\"\"];if(new RegExp(St).test(t)&&!/IEMobile\\b/.test(t))return[Xt+\" \"+St,\"\"];var i=/Windows NT ([0-9.]+)/i.exec(t);if(i&&i[1]){var r=i[1],n=di[r]||\"\";return/arm/i.test(t)&&(n=\"RT\"),[Xt,n]}return[Xt,\"\"]}],[/((iPhone|iPad|iPod).*?OS (\\d+)_(\\d+)_?(\\d+)?|iPhone)/,e=>{if(e&&e[3]){var t=[e[3],e[4],e[5]||\"0\"];return[Et,t.join(\".\")]}return[Et,\"\"]}],[/(watch.*\\/(\\d+\\.\\d+\\.\\d+)|watch os,(\\d+\\.\\d+),)/i,e=>{var t=\"\";return e&&e.length>=3&&(t=F(e[2])?e[3]:e[2]),[\"watchOS\",t]}],[new RegExp(\"(\"+kt+\" (\\\\d+)\\\\.(\\\\d+)\\\\.?(\\\\d+)?|\"+kt+\")\",\"i\"),e=>{if(e&&e[2]){var t=[e[2],e[3],e[4]||\"0\"];return[kt,t.join(\".\")]}return[kt,\"\"]}],[/Mac OS X (\\d+)[_.](\\d+)[_.]?(\\d+)?/i,e=>{var t=[\"Mac OS X\",\"\"];if(e&&e[1]){var i=[e[1],e[2],e[3]||\"0\"];t[1]=i.join(\".\")}return t}],[/Mac/i,[\"Mac OS X\",\"\"]],[/CrOS/,[At,\"\"]],[/Linux|debian/i,[\"Linux\",\"\"]]],gi=function(e){return ui.test(e)?Vt:li.test(e)?Gt:ai.test(e)?Jt:new RegExp(ei,\"i\").test(e)?ei:new RegExp(\"(\"+Qt+\"|WPDesktop)\",\"i\").test(e)?Qt:/iPad/.test(e)?Ct:/iPod/.test(e)?\"iPod Touch\":/iPhone/.test(e)?\"iPhone\":/(watch)(?: ?os[,/]|\\d,\\d\\/)[\\d.]+/i.test(e)?Ft:ci.test(e)?Tt:/(kobo)\\s(ereader|touch)/i.test(e)?\"Kobo\":new RegExp(Zt,\"i\").test(e)?Zt:/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\\))/i.test(e)||/(kf[a-z]+)( bui|\\)).+silk\\//i.test(e)?\"Kindle Fire\":/(Android|ZTE)/i.test(e)?!new RegExp(St).test(e)||/(9138B|TB782B|Nexus [97]|pixel c|HUAWEISHT|BTV|noble nook|smart ultra 6)/i.test(e)?/pixel[\\daxl ]{1,6}/i.test(e)&&!/pixel c/i.test(e)||/(huaweimed-al00|tah-|APA|SM-G92|i980|zte|U304AA)/i.test(e)||/lmy47v/i.test(e)&&!/QTAQZ3/i.test(e)?kt:It:kt:new RegExp(\"(pda|\"+St+\")\",\"i\").test(e)?ii:new RegExp(xt,\"i\").test(e)&&!new RegExp(xt+\" pc\",\"i\").test(e)?ri:\"\"},fi=\"https?://(.*)\",mi=[\"gclid\",\"gclsrc\",\"dclid\",\"gbraid\",\"wbraid\",\"fbclid\",\"msclkid\",\"twclid\",\"li_fat_id\",\"igshid\",\"ttclid\",\"rdt_cid\",\"irclid\",\"_kx\"],bi=X([\"utm_source\",\"utm_medium\",\"utm_campaign\",\"utm_content\",\"utm_term\",\"gad_source\",\"mc_cid\"],mi),yi=\"<masked>\",wi={campaignParams:function(){var{customTrackedParams:e,maskPersonalDataProperties:t,customPersonalDataProperties:i}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!a)return{};var r=t?X([],mi,i||[]):[];return this._campaignParamsFromUrl(yt(a.URL,r,yi),e)},_campaignParamsFromUrl:function(e,t){var i=bi.concat(t||[]),r={};return Y(i,(function(t){var i=bt(e,t);r[t]=i||null})),r},_searchEngine:function(e){return e?0===e.search(fi+\"google.([^/?]*)\")?\"google\":0===e.search(fi+\"bing.com\")?\"bing\":0===e.search(fi+\"yahoo.com\")?\"yahoo\":0===e.search(fi+\"duckduckgo.com\")?\"duckduckgo\":null:null},_searchInfoFromReferrer:function(e){var t=wi._searchEngine(e),i=\"yahoo\"!=t?\"q\":\"p\",r={};if(!$(t)){r.$search_engine=t;var n=a?bt(a.referrer,i):\"\";n.length&&(r.ph_keyword=n)}return r},searchInfo:function(){var e=null==a?void 0:a.referrer;return e?this._searchInfoFromReferrer(e):{}},browser:_i,browserVersion:function(e,t){var i=_i(e,t),r=pi[i];if(F(r))return null;for(var n=0;n<r.length;n++){var s=r[n],o=e.match(s);if(o)return parseFloat(o[o.length-2])}return null},browserLanguage:function(){return navigator.language||navigator.userLanguage},browserLanguagePrefix:function(){var e=this.browserLanguage();return\"string\"==typeof e?e.split(\"-\")[0]:void 0},os:function(e){for(var t=0;t<vi.length;t++){var[i,r]=vi[t],n=i.exec(e),s=n&&(I(r)?r(n,e):r);if(s)return s}return[\"\",\"\"]},device:gi,deviceType:function(e){var t=gi(e);return t===Ct||t===It||\"Kobo\"===t||\"Kindle Fire\"===t||t===ri?xt:t===Vt||t===Jt||t===Gt||t===ei?\"Console\":t===Ft?\"Wearable\":t?St:\"Desktop\"},referrer:function(){return(null==a?void 0:a.referrer)||\"$direct\"},referringDomain:function(){var e;return null!=a&&a.referrer&&(null===(e=gt(a.referrer))||void 0===e?void 0:e.host)||\"$direct\"},referrerInfo:function(){return{$referrer:this.referrer(),$referring_domain:this.referringDomain()}},initialPersonInfo:function(){return{r:this.referrer().substring(0,1e3),u:null==l?void 0:l.href.substring(0,1e3)}},initialPersonPropsFromInfo:function(e){var t,{r:i,u:r}=e,n={$initial_referrer:i,$initial_referring_domain:null==i?void 0:\"$direct\"==i?\"$direct\":null===(t=gt(i))||void 0===t?void 0:t.host};if(r){n.$initial_current_url=r;var s=gt(r);n.$initial_host=null==s?void 0:s.host,n.$initial_pathname=null==s?void 0:s.pathname,Y(this._campaignParamsFromUrl(r),(function(e,t){n[\"$initial_\"+y(t)]=e}))}i&&Y(this._searchInfoFromReferrer(i),(function(e,t){n[\"$initial_\"+y(t)]=e}));return n},timezone:function(){try{return Intl.DateTimeFormat().resolvedOptions().timeZone}catch(e){return}},timezoneOffset:function(){try{return(new Date).getTimezoneOffset()}catch(e){return}},properties:function(){var{maskPersonalDataProperties:e,customPersonalDataProperties:i}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!h)return{};var r=e?X([],mi,i||[]):[],[n,s]=wi.os(h);return K(te({$os:n,$os_version:s,$browser:wi.browser(h,navigator.vendor),$device:wi.device(h),$device_type:wi.deviceType(h),$timezone:wi.timezone(),$timezone_offset:wi.timezoneOffset()}),{$current_url:yt(null==l?void 0:l.href,r,yi),$host:null==l?void 0:l.host,$pathname:null==l?void 0:l.pathname,$raw_user_agent:h.length>1e3?h.substring(0,997)+\"...\":h,$browser_version:wi.browserVersion(h,navigator.vendor),$browser_language:wi.browserLanguage(),$browser_language_prefix:wi.browserLanguagePrefix(),$screen_height:null==t?void 0:t.screen.height,$screen_width:null==t?void 0:t.screen.width,$viewport_height:null==t?void 0:t.innerHeight,$viewport_width:null==t?void 0:t.innerWidth,$lib:\"web\",$lib_version:p.LIB_VERSION,$insert_id:Math.random().toString(36).substring(2,10)+Math.random().toString(36).substring(2,10),$time:Date.now()/1e3})},people_properties:function(){if(!h)return{};var[e,t]=wi.os(h);return K(te({$os:e,$os_version:t,$browser:wi.browser(h,navigator.vendor)}),{$browser_version:wi.browserVersion(h,navigator.vendor)})}},Si=[\"cookie\",\"localstorage\",\"localstorage+cookie\",\"sessionstorage\",\"memory\"];class Ei{constructor(e){this.config=e,this.props={},this.campaign_params_saved=!1,this.name=(e=>{var t=\"\";return e.token&&(t=e.token.replace(/\\+/g,\"PL\").replace(/\\//g,\"SL\").replace(/=/g,\"EQ\")),e.persistence_name?\"ph_\"+e.persistence_name:\"ph_\"+t+\"_posthog\"})(e),this.storage=this.buildStorage(e),this.load(),e.debug&&q.info(\"Persistence loaded\",e.persistence,j({},this.props)),this.update_config(e,e),this.save()}buildStorage(e){-1===Si.indexOf(e.persistence.toLowerCase())&&(q.critical(\"Unknown persistence type \"+e.persistence+\"; falling back to localStorage+cookie\"),e.persistence=\"localStorage+cookie\");var t=e.persistence.toLowerCase();return\"localstorage\"===t&&lt.is_supported()?lt:\"localstorage+cookie\"===t&&ct.is_supported()?ct:\"sessionstorage\"===t&&pt.is_supported()?pt:\"memory\"===t?ht:\"cookie\"===t?ot:ct.is_supported()?ct:ot}properties(){var e={};return Y(this.props,(function(t,i){if(i===xe&&C(t))for(var r=Object.keys(t),n=0;n<r.length;n++)e[\"$feature/\".concat(r[n])]=t[r[n]];else a=i,l=!1,($(o=He)?l:s&&o.indexOf===s?-1!=o.indexOf(a):(Y(o,(function(e){if(l||(l=e===a))return G})),l))||(e[i]=t);var o,a,l})),e}load(){if(!this.disabled){var e=this.storage.parse(this.name);e&&(this.props=K({},e))}}save(){this.disabled||this.storage.set(this.name,this.props,this.expire_days,this.cross_subdomain,this.secure,this.config.debug)}remove(){this.storage.remove(this.name,!1),this.storage.remove(this.name,!0)}clear(){this.remove(),this.props={}}register_once(e,t,i){if(C(e)){F(t)&&(t=\"None\"),this.expire_days=F(i)?this.default_expiry:i;var r=!1;if(Y(e,((e,i)=>{this.props.hasOwnProperty(i)&&this.props[i]!==t||(this.props[i]=e,r=!0)})),r)return this.save(),!0}return!1}register(e,t){if(C(e)){this.expire_days=F(t)?this.default_expiry:t;var i=!1;if(Y(e,((t,r)=>{e.hasOwnProperty(r)&&this.props[r]!==t&&(this.props[r]=t,i=!0)})),i)return this.save(),!0}return!1}unregister(e){e in this.props&&(delete this.props[e],this.save())}update_campaign_params(){if(!this.campaign_params_saved){var e=wi.campaignParams({customTrackedParams:this.config.custom_campaign_params,maskPersonalDataProperties:this.config.mask_personal_data_properties,customPersonalDataProperties:this.config.custom_personal_data_properties});P(te(e))||this.register(e),this.campaign_params_saved=!0}}update_search_keyword(){this.register(wi.searchInfo())}update_referrer_info(){this.register_once(wi.referrerInfo(),void 0)}set_initial_person_info(){this.props[Le]||this.props[Ae]||this.register_once({[De]:wi.initialPersonInfo()},void 0)}get_referrer_info(){return te({$referrer:this.props.$referrer,$referring_domain:this.props.$referring_domain})}get_initial_props(){var e={};Y([Ae,Le],(t=>{var i=this.props[t];i&&Y(i,(function(t,i){e[\"$initial_\"+y(i)]=t}))}));var t=this.props[De];if(t){var i=wi.initialPersonPropsFromInfo(t);K(e,i)}return e}safe_merge(e){return Y(this.props,(function(t,i){i in e||(e[i]=t)})),e}update_config(e,t){if(this.default_expiry=this.expire_days=e.cookie_expiration,this.set_disabled(e.disable_persistence),this.set_cross_subdomain(e.cross_subdomain_cookie),this.set_secure(e.secure_cookie),e.persistence!==t.persistence){var i=this.buildStorage(e),r=this.props;this.clear(),this.storage=i,this.props=r,this.save()}}set_disabled(e){this.disabled=e,this.disabled?this.remove():this.save()}set_cross_subdomain(e){e!==this.cross_subdomain&&(this.cross_subdomain=e,this.remove(),this.save())}get_cross_subdomain(){return!!this.cross_subdomain}set_secure(e){e!==this.secure&&(this.secure=e,this.remove(),this.save())}set_event_timer(e,t){var i=this.props[ae]||{};i[e]=t,this.props[ae]=i,this.save()}remove_event_timer(e){var t=(this.props[ae]||{})[e];return F(t)||(delete this.props[ae][e],this.save()),t}get_property(e){return this.props[e]}set_property(e,t){this.props[e]=t,this.save()}}function ki(e){var t,i;return(null===(t=JSON.stringify(e,(i=[],function(e,t){if(C(t)){for(;i.length>0&&i[i.length-1]!==this;)i.pop();return i.includes(t)?\"[Circular]\":(i.push(t),t)}return t})))||void 0===t?void 0:t.length)||0}function xi(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6606028.8;if(e.size>=t&&e.data.length>1){var i=Math.floor(e.data.length/2),r=e.data.slice(0,i),n=e.data.slice(i);return[xi({size:ki(r),data:r,sessionId:e.sessionId,windowId:e.windowId}),xi({size:ki(n),data:n,sessionId:e.sessionId,windowId:e.windowId})].flatMap((e=>e))}return[e]}var Ii=(e=>(e[e.DomContentLoaded=0]=\"DomContentLoaded\",e[e.Load=1]=\"Load\",e[e.FullSnapshot=2]=\"FullSnapshot\",e[e.IncrementalSnapshot=3]=\"IncrementalSnapshot\",e[e.Meta=4]=\"Meta\",e[e.Custom=5]=\"Custom\",e[e.Plugin=6]=\"Plugin\",e))(Ii||{}),Ci=(e=>(e[e.Mutation=0]=\"Mutation\",e[e.MouseMove=1]=\"MouseMove\",e[e.MouseInteraction=2]=\"MouseInteraction\",e[e.Scroll=3]=\"Scroll\",e[e.ViewportResize=4]=\"ViewportResize\",e[e.Input=5]=\"Input\",e[e.TouchMove=6]=\"TouchMove\",e[e.MediaInteraction=7]=\"MediaInteraction\",e[e.StyleSheetRule=8]=\"StyleSheetRule\",e[e.CanvasMutation=9]=\"CanvasMutation\",e[e.Font=10]=\"Font\",e[e.Log=11]=\"Log\",e[e.Drag=12]=\"Drag\",e[e.StyleDeclaration=13]=\"StyleDeclaration\",e[e.Selection=14]=\"Selection\",e[e.AdoptedStyleSheet=15]=\"AdoptedStyleSheet\",e[e.CustomElement=16]=\"CustomElement\",e))(Ci||{});function Pi(e){var t;return e instanceof Element&&(e.id===qe||!(null===(t=e.closest)||void 0===t||!t.call(e,\".toolbar-global-fade-container\")))}function Fi(e){return!!e&&1===e.nodeType}function Ri(e,t){return!!e&&!!e.tagName&&e.tagName.toLowerCase()===t.toLowerCase()}function Ti(e){return!!e&&3===e.nodeType}function $i(e){return!!e&&11===e.nodeType}function Oi(e){return e?b(e).split(/\\s+/):[]}function Mi(e){var i=null==t?void 0:t.location.href;return!!(i&&e&&e.some((e=>i.match(e))))}function Li(e){var t=\"\";switch(typeof e.className){case\"string\":t=e.className;break;case\"object\":t=(e.className&&\"baseVal\"in e.className?e.className.baseVal:null)||e.getAttribute(\"class\")||\"\";break;default:t=\"\"}return Oi(t)}function Ai(e){return O(e)?null:b(e).split(/(\\s+)/).filter((e=>Ki(e))).join(\"\").replace(/[\\r\\n]/g,\" \").replace(/[ ]+/g,\" \").substring(0,255)}function Di(e){var t=\"\";return Ui(e)&&!zi(e)&&e.childNodes&&e.childNodes.length&&Y(e.childNodes,(function(e){var i;Ti(e)&&e.textContent&&(t+=null!==(i=Ai(e.textContent))&&void 0!==i?i:\"\")})),b(t)}function Ni(e){return F(e.target)?e.srcElement||null:null!==(t=e.target)&&void 0!==t&&t.shadowRoot?e.composedPath()[0]||null:e.target||null;var t}var qi=[\"a\",\"button\",\"form\",\"input\",\"select\",\"textarea\",\"label\"];function Bi(e){var t=e.parentNode;return!(!t||!Fi(t))&&t}function Hi(e,i){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0,n=arguments.length>3?arguments[3]:void 0,s=arguments.length>4?arguments[4]:void 0;if(!t||!e||Ri(e,\"html\")||!Fi(e))return!1;if(null!=r&&r.url_allowlist&&!Mi(r.url_allowlist))return!1;if(null!=r&&r.url_ignorelist&&Mi(r.url_ignorelist))return!1;if(null!=r&&r.dom_event_allowlist){var o=r.dom_event_allowlist;if(o&&!o.some((e=>i.type===e)))return!1}for(var a=!1,l=[e],u=!0,c=e;c.parentNode&&!Ri(c,\"body\");)if($i(c.parentNode))l.push(c.parentNode.host),c=c.parentNode.host;else{if(!(u=Bi(c)))break;if(n||qi.indexOf(u.tagName.toLowerCase())>-1)a=!0;else{var d=t.getComputedStyle(u);d&&\"pointer\"===d.getPropertyValue(\"cursor\")&&(a=!0)}l.push(u),c=u}if(!function(e,t){var i=null==t?void 0:t.element_allowlist;if(F(i))return!0;var r=function(e){if(i.some((t=>e.tagName.toLowerCase()===t)))return{v:!0}};for(var n of e){var s=r(n);if(\"object\"==typeof s)return s.v}return!1}(l,r))return!1;if(!function(e,t){var i=null==t?void 0:t.css_selector_allowlist;if(F(i))return!0;var r=function(e){if(i.some((t=>e.matches(t))))return{v:!0}};for(var n of e){var s=r(n);if(\"object\"==typeof s)return s.v}return!1}(l,r))return!1;var h=t.getComputedStyle(e);if(h&&\"pointer\"===h.getPropertyValue(\"cursor\")&&\"click\"===i.type)return!0;var _=e.tagName.toLowerCase();switch(_){case\"html\":return!1;case\"form\":return(s||[\"submit\"]).indexOf(i.type)>=0;case\"input\":case\"select\":case\"textarea\":return(s||[\"change\",\"click\"]).indexOf(i.type)>=0;default:return a?(s||[\"click\"]).indexOf(i.type)>=0:(s||[\"click\"]).indexOf(i.type)>=0&&(qi.indexOf(_)>-1||\"true\"===e.getAttribute(\"contenteditable\"))}}function Ui(e){for(var t=e;t.parentNode&&!Ri(t,\"body\");t=t.parentNode){var i=Li(t);if(m(i,\"ph-sensitive\")||m(i,\"ph-no-capture\"))return!1}if(m(Li(e),\"ph-include\"))return!0;var r=e.type||\"\";if(R(r))switch(r.toLowerCase()){case\"hidden\":case\"password\":return!1}var n=e.name||e.id||\"\";if(R(n)){if(/^cc|cardnum|ccnum|creditcard|csc|cvc|cvv|exp|pass|pwd|routing|seccode|securitycode|securitynum|socialsec|socsec|ssn/i.test(n.replace(/[^a-zA-Z0-9]/g,\"\")))return!1}return!0}function zi(e){return!!(Ri(e,\"input\")&&![\"button\",\"checkbox\",\"submit\",\"reset\"].includes(e.type)||Ri(e,\"select\")||Ri(e,\"textarea\")||\"true\"===e.getAttribute(\"contenteditable\"))}var ji=\"(4[0-9]{12}(?:[0-9]{3})?)|(5[1-5][0-9]{14})|(6(?:011|5[0-9]{2})[0-9]{12})|(3[47][0-9]{13})|(3(?:0[0-5]|[68][0-9])[0-9]{11})|((?:2131|1800|35[0-9]{3})[0-9]{11})\",Wi=new RegExp(\"^(?:\".concat(ji,\")$\")),Vi=new RegExp(ji),Gi=\"\\\\d{3}-?\\\\d{2}-?\\\\d{4}\",Ji=new RegExp(\"^(\".concat(Gi,\")$\")),Yi=new RegExp(\"(\".concat(Gi,\")\"));function Ki(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(O(e))return!1;if(R(e)){if(e=b(e),(t?Wi:Vi).test((e||\"\").replace(/[- ]/g,\"\")))return!1;if((t?Ji:Yi).test(e))return!1}return!0}function Xi(e){var t=Di(e);return Ki(t=\"\".concat(t,\" \").concat(Qi(e)).trim())?t:\"\"}function Qi(e){var t=\"\";return e&&e.childNodes&&e.childNodes.length&&Y(e.childNodes,(function(e){var i;if(e&&\"span\"===(null===(i=e.tagName)||void 0===i?void 0:i.toLowerCase()))try{var r=Di(e);t=\"\".concat(t,\" \").concat(r).trim(),e.childNodes&&e.childNodes.length&&(t=\"\".concat(t,\" \").concat(Qi(e)).trim())}catch(e){q.error(\"[AutoCapture]\",e)}})),t}function Zi(e){return function(e){var t=e.map((e=>{var t,i,r=\"\";if(e.tag_name&&(r+=e.tag_name),e.attr_class)for(var n of(e.attr_class.sort(),e.attr_class))r+=\".\".concat(n.replace(/\"/g,\"\"));var s=j(j(j(j({},e.text?{text:e.text}:{}),{},{\"nth-child\":null!==(t=e.nth_child)&&void 0!==t?t:0,\"nth-of-type\":null!==(i=e.nth_of_type)&&void 0!==i?i:0},e.href?{href:e.href}:{}),e.attr_id?{attr_id:e.attr_id}:{}),e.attributes),o={};return Q(s).sort(((e,t)=>{var[i]=e,[r]=t;return i.localeCompare(r)})).forEach((e=>{var[t,i]=e;return o[er(t.toString())]=er(i.toString())})),r+=\":\",r+=Q(s).map((e=>{var[t,i]=e;return\"\".concat(t,'=\"').concat(i,'\"')})).join(\"\")}));return t.join(\";\")}(function(e){return e.map((e=>{var t,i,r={text:null===(t=e.$el_text)||void 0===t?void 0:t.slice(0,400),tag_name:e.tag_name,href:null===(i=e.attr__href)||void 0===i?void 0:i.slice(0,2048),attr_class:tr(e),attr_id:e.attr__id,nth_child:e.nth_child,nth_of_type:e.nth_of_type,attributes:{}};return Q(e).filter((e=>{var[t]=e;return 0===t.indexOf(\"attr__\")})).forEach((e=>{var[t,i]=e;return r.attributes[t]=i})),r}))}(e))}function er(e){return e.replace(/\"|\\\\\"/g,'\\\\\"')}function tr(e){var t=e.attr__class;return t?x(t)?t:Oi(t):void 0}var ir=\"[SessionRecording]\",rr=\"redacted\",nr={initiatorTypes:[\"audio\",\"beacon\",\"body\",\"css\",\"early-hint\",\"embed\",\"fetch\",\"frame\",\"iframe\",\"icon\",\"image\",\"img\",\"input\",\"link\",\"navigation\",\"object\",\"ping\",\"script\",\"track\",\"video\",\"xmlhttprequest\"],maskRequestFn:e=>e,recordHeaders:!1,recordBody:!1,recordInitialRequests:!1,recordPerformance:!1,performanceEntryTypeToObserve:[\"first-input\",\"navigation\",\"paint\",\"resource\"],payloadSizeLimitBytes:1e6,payloadHostDenyList:[\".lr-ingest.io\",\".ingest.sentry.io\",\".clarity.ms\",\"analytics.google.com\"]},sr=[\"authorization\",\"x-forwarded-for\",\"authorization\",\"cookie\",\"set-cookie\",\"x-api-key\",\"x-real-ip\",\"remote-addr\",\"forwarded\",\"proxy-authorization\",\"x-csrf-token\",\"x-csrftoken\",\"x-xsrf-token\"],or=[\"password\",\"secret\",\"passwd\",\"api_key\",\"apikey\",\"auth\",\"credentials\",\"mysql_pwd\",\"privatekey\",\"private_key\",\"token\"],ar=[\"/s/\",\"/e/\",\"/i/\"];function lr(e,t,i,r){if(O(e))return e;var n=(null==t?void 0:t[\"content-length\"])||function(e){return new Blob([e]).size}(e);return R(n)&&(n=parseInt(n)),n>i?ir+\" \".concat(r,\" body too large to record (\").concat(n,\" bytes)\"):e}function ur(e,t){if(O(e))return e;var i=e;return Ki(i,!1)||(i=ir+\" \"+t+\" body \"+rr),Y(or,(e=>{var r,n;null!==(r=i)&&void 0!==r&&r.length&&-1!==(null===(n=i)||void 0===n?void 0:n.indexOf(e))&&(i=ir+\" \"+t+\" body \"+rr+\" as might contain: \"+e)})),i}var cr=(e,t)=>{var i,r,n,s={payloadSizeLimitBytes:nr.payloadSizeLimitBytes,performanceEntryTypeToObserve:[...nr.performanceEntryTypeToObserve],payloadHostDenyList:[...t.payloadHostDenyList||[],...nr.payloadHostDenyList]},o=!1!==e.session_recording.recordHeaders&&t.recordHeaders,a=!1!==e.session_recording.recordBody&&t.recordBody,l=!1!==e.capture_performance&&t.recordPerformance,u=(i=s,n=Math.min(1e6,null!==(r=i.payloadSizeLimitBytes)&&void 0!==r?r:1e6),e=>(null!=e&&e.requestBody&&(e.requestBody=lr(e.requestBody,e.requestHeaders,n,\"Request\")),null!=e&&e.responseBody&&(e.responseBody=lr(e.responseBody,e.responseHeaders,n,\"Response\")),e)),c=t=>{return u(((e,t)=>{var i,r=gt(e.name),n=0===t.indexOf(\"http\")?null===(i=gt(t))||void 0===i?void 0:i.pathname:t;\"/\"===n&&(n=\"\");var s=null==r?void 0:r.pathname.replace(n||\"\",\"\");if(!(r&&s&&ar.some((e=>0===s.indexOf(e)))))return e})((r=(i=t).requestHeaders,O(r)||Y(Object.keys(null!=r?r:{}),(e=>{sr.includes(e.toLowerCase())&&(r[e]=rr)})),i),e.api_host));var i,r},d=I(e.session_recording.maskNetworkRequestFn);return d&&I(e.session_recording.maskCapturedNetworkRequestFn)&&q.warn(\"Both `maskNetworkRequestFn` and `maskCapturedNetworkRequestFn` are defined. `maskNetworkRequestFn` will be ignored.\"),d&&(e.session_recording.maskCapturedNetworkRequestFn=t=>{var i=e.session_recording.maskNetworkRequestFn({url:t.name});return j(j({},t),{},{name:null==i?void 0:i.url})}),s.maskRequestFn=I(e.session_recording.maskCapturedNetworkRequestFn)?t=>{var i,r,n,s=c(t);return s&&null!==(i=null===(r=(n=e.session_recording).maskCapturedNetworkRequestFn)||void 0===r?void 0:r.call(n,s))&&void 0!==i?i:void 0}:e=>function(e){if(!F(e))return e.requestBody=ur(e.requestBody,\"Request\"),e.responseBody=ur(e.responseBody,\"Response\"),e}(c(e)),j(j(j({},nr),s),{},{recordHeaders:o,recordBody:a,recordPerformance:l,recordInitialRequests:l})};function dr(e,t,i,r,n){return t>i&&(q.warn(\"min cannot be greater than max.\"),t=i),M(e)?e>i?(r&&q.warn(r+\" cannot be  greater than max: \"+i+\". Using max value instead.\"),i):e<t?(r&&q.warn(r+\" cannot be less than min: \"+t+\". Using min value instead.\"),t):e:(r&&q.warn(r+\" must be a number. using max or fallback. max: \"+i+\", fallback: \"+n),dr(n||i,t,i,r))}class hr{constructor(e){var t,i,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};W(this,\"bucketSize\",100),W(this,\"refillRate\",10),W(this,\"mutationBuckets\",{}),W(this,\"loggedTracker\",{}),W(this,\"refillBuckets\",(()=>{Object.keys(this.mutationBuckets).forEach((e=>{this.mutationBuckets[e]=this.mutationBuckets[e]+this.refillRate,this.mutationBuckets[e]>=this.bucketSize&&delete this.mutationBuckets[e]}))})),W(this,\"getNodeOrRelevantParent\",(e=>{var t=this.rrweb.mirror.getNode(e);if(\"svg\"!==(null==t?void 0:t.nodeName)&&t instanceof Element){var i=t.closest(\"svg\");if(i)return[this.rrweb.mirror.getId(i),i]}return[e,t]})),W(this,\"numberOfChanges\",(e=>{var t,i,r,n,s,o,a,l;return(null!==(t=null===(i=e.removes)||void 0===i?void 0:i.length)&&void 0!==t?t:0)+(null!==(r=null===(n=e.attributes)||void 0===n?void 0:n.length)&&void 0!==r?r:0)+(null!==(s=null===(o=e.texts)||void 0===o?void 0:o.length)&&void 0!==s?s:0)+(null!==(a=null===(l=e.adds)||void 0===l?void 0:l.length)&&void 0!==a?a:0)})),W(this,\"throttleMutations\",(e=>{if(3!==e.type||0!==e.data.source)return e;var t=e.data,i=this.numberOfChanges(t);t.attributes&&(t.attributes=t.attributes.filter((e=>{var t,i,r,[n,s]=this.getNodeOrRelevantParent(e.id);if(0===this.mutationBuckets[n])return!1;(this.mutationBuckets[n]=null!==(t=this.mutationBuckets[n])&&void 0!==t?t:this.bucketSize,this.mutationBuckets[n]=Math.max(this.mutationBuckets[n]-1,0),0===this.mutationBuckets[n])&&(this.loggedTracker[n]||(this.loggedTracker[n]=!0,null===(i=(r=this.options).onBlockedNode)||void 0===i||i.call(r,n,s)));return e})));var r=this.numberOfChanges(t);return 0!==r||i===r?e:void 0})),this.rrweb=e,this.options=r,this.refillRate=dr(null!==(t=this.options.refillRate)&&void 0!==t?t:this.refillRate,0,100,\"mutation throttling refill rate\"),this.bucketSize=dr(null!==(i=this.options.bucketSize)&&void 0!==i?i:this.bucketSize,0,100,\"mutation throttling bucket size\"),setInterval((()=>{this.refillBuckets()}),1e3)}}var _r=Uint8Array,pr=Uint16Array,vr=Uint32Array,gr=new _r([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),fr=new _r([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),mr=new _r([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),br=function(e,t){for(var i=new pr(31),r=0;r<31;++r)i[r]=t+=1<<e[r-1];var n=new vr(i[30]);for(r=1;r<30;++r)for(var s=i[r];s<i[r+1];++s)n[s]=s-i[r]<<5|r;return[i,n]},yr=br(gr,2),wr=yr[0],Sr=yr[1];wr[28]=258,Sr[258]=28;for(var Er=br(fr,0)[1],kr=new pr(32768),xr=0;xr<32768;++xr){var Ir=(43690&xr)>>>1|(21845&xr)<<1;Ir=(61680&(Ir=(52428&Ir)>>>2|(13107&Ir)<<2))>>>4|(3855&Ir)<<4,kr[xr]=((65280&Ir)>>>8|(255&Ir)<<8)>>>1}var Cr=function(e,t,i){for(var r=e.length,n=0,s=new pr(t);n<r;++n)++s[e[n]-1];var o,a=new pr(t);for(n=0;n<t;++n)a[n]=a[n-1]+s[n-1]<<1;if(i){o=new pr(1<<t);var l=15-t;for(n=0;n<r;++n)if(e[n])for(var u=n<<4|e[n],c=t-e[n],d=a[e[n]-1]++<<c,h=d|(1<<c)-1;d<=h;++d)o[kr[d]>>>l]=u}else for(o=new pr(r),n=0;n<r;++n)o[n]=kr[a[e[n]-1]++]>>>15-e[n];return o},Pr=new _r(288);for(xr=0;xr<144;++xr)Pr[xr]=8;for(xr=144;xr<256;++xr)Pr[xr]=9;for(xr=256;xr<280;++xr)Pr[xr]=7;for(xr=280;xr<288;++xr)Pr[xr]=8;var Fr=new _r(32);for(xr=0;xr<32;++xr)Fr[xr]=5;var Rr=Cr(Pr,9,0),Tr=Cr(Fr,5,0),$r=function(e){return(e/8>>0)+(7&e&&1)},Or=function(e,t,i){(null==i||i>e.length)&&(i=e.length);var r=new(e instanceof pr?pr:e instanceof vr?vr:_r)(i-t);return r.set(e.subarray(t,i)),r},Mr=function(e,t,i){i<<=7&t;var r=t/8>>0;e[r]|=i,e[r+1]|=i>>>8},Lr=function(e,t,i){i<<=7&t;var r=t/8>>0;e[r]|=i,e[r+1]|=i>>>8,e[r+2]|=i>>>16},Ar=function(e,t){for(var i=[],r=0;r<e.length;++r)e[r]&&i.push({s:r,f:e[r]});var n=i.length,s=i.slice();if(!n)return[new _r(0),0];if(1==n){var o=new _r(i[0].s+1);return o[i[0].s]=1,[o,1]}i.sort((function(e,t){return e.f-t.f})),i.push({s:-1,f:25001});var a=i[0],l=i[1],u=0,c=1,d=2;for(i[0]={s:-1,f:a.f+l.f,l:a,r:l};c!=n-1;)a=i[i[u].f<i[d].f?u++:d++],l=i[u!=c&&i[u].f<i[d].f?u++:d++],i[c++]={s:-1,f:a.f+l.f,l:a,r:l};var h=s[0].s;for(r=1;r<n;++r)s[r].s>h&&(h=s[r].s);var _=new pr(h+1),p=Dr(i[c-1],_,0);if(p>t){r=0;var v=0,g=p-t,f=1<<g;for(s.sort((function(e,t){return _[t.s]-_[e.s]||e.f-t.f}));r<n;++r){var m=s[r].s;if(!(_[m]>t))break;v+=f-(1<<p-_[m]),_[m]=t}for(v>>>=g;v>0;){var b=s[r].s;_[b]<t?v-=1<<t-_[b]++-1:++r}for(;r>=0&&v;--r){var y=s[r].s;_[y]==t&&(--_[y],++v)}p=t}return[new _r(_),p]},Dr=function(e,t,i){return-1==e.s?Math.max(Dr(e.l,t,i+1),Dr(e.r,t,i+1)):t[e.s]=i},Nr=function(e){for(var t=e.length;t&&!e[--t];);for(var i=new pr(++t),r=0,n=e[0],s=1,o=function(e){i[r++]=e},a=1;a<=t;++a)if(e[a]==n&&a!=t)++s;else{if(!n&&s>2){for(;s>138;s-=138)o(32754);s>2&&(o(s>10?s-11<<5|28690:s-3<<5|12305),s=0)}else if(s>3){for(o(n),--s;s>6;s-=6)o(8304);s>2&&(o(s-3<<5|8208),s=0)}for(;s--;)o(n);s=1,n=e[a]}return[i.subarray(0,r),t]},qr=function(e,t){for(var i=0,r=0;r<t.length;++r)i+=e[r]*t[r];return i},Br=function(e,t,i){var r=i.length,n=$r(t+2);e[n]=255&r,e[n+1]=r>>>8,e[n+2]=255^e[n],e[n+3]=255^e[n+1];for(var s=0;s<r;++s)e[n+s+4]=i[s];return 8*(n+4+r)},Hr=function(e,t,i,r,n,s,o,a,l,u,c){Mr(t,c++,i),++n[256];for(var d=Ar(n,15),h=d[0],_=d[1],p=Ar(s,15),v=p[0],g=p[1],f=Nr(h),m=f[0],b=f[1],y=Nr(v),w=y[0],S=y[1],E=new pr(19),k=0;k<m.length;++k)E[31&m[k]]++;for(k=0;k<w.length;++k)E[31&w[k]]++;for(var x=Ar(E,7),I=x[0],C=x[1],P=19;P>4&&!I[mr[P-1]];--P);var F,R,T,$,O=u+5<<3,M=qr(n,Pr)+qr(s,Fr)+o,L=qr(n,h)+qr(s,v)+o+14+3*P+qr(E,I)+(2*E[16]+3*E[17]+7*E[18]);if(O<=M&&O<=L)return Br(t,c,e.subarray(l,l+u));if(Mr(t,c,1+(L<M)),c+=2,L<M){F=Cr(h,_,0),R=h,T=Cr(v,g,0),$=v;var A=Cr(I,C,0);Mr(t,c,b-257),Mr(t,c+5,S-1),Mr(t,c+10,P-4),c+=14;for(k=0;k<P;++k)Mr(t,c+3*k,I[mr[k]]);c+=3*P;for(var D=[m,w],N=0;N<2;++N){var q=D[N];for(k=0;k<q.length;++k){var B=31&q[k];Mr(t,c,A[B]),c+=I[B],B>15&&(Mr(t,c,q[k]>>>5&127),c+=q[k]>>>12)}}}else F=Rr,R=Pr,T=Tr,$=Fr;for(k=0;k<a;++k)if(r[k]>255){B=r[k]>>>18&31;Lr(t,c,F[B+257]),c+=R[B+257],B>7&&(Mr(t,c,r[k]>>>23&31),c+=gr[B]);var H=31&r[k];Lr(t,c,T[H]),c+=$[H],H>3&&(Lr(t,c,r[k]>>>5&8191),c+=fr[H])}else Lr(t,c,F[r[k]]),c+=R[r[k]];return Lr(t,c,F[256]),c+R[256]},Ur=new vr([65540,131080,131088,131104,262176,1048704,1048832,2114560,2117632]),zr=function(){for(var e=new vr(256),t=0;t<256;++t){for(var i=t,r=9;--r;)i=(1&i&&3988292384)^i>>>1;e[t]=i}return e}(),jr=function(){var e=4294967295;return{p:function(t){for(var i=e,r=0;r<t.length;++r)i=zr[255&i^t[r]]^i>>>8;e=i},d:function(){return 4294967295^e}}},Wr=function(e,t,i,r,n){return function(e,t,i,r,n,s){var o=e.length,a=new _r(r+o+5*(1+Math.floor(o/7e3))+n),l=a.subarray(r,a.length-n),u=0;if(!t||o<8)for(var c=0;c<=o;c+=65535){var d=c+65535;d<o?u=Br(l,u,e.subarray(c,d)):(l[c]=s,u=Br(l,u,e.subarray(c,o)))}else{for(var h=Ur[t-1],_=h>>>13,p=8191&h,v=(1<<i)-1,g=new pr(32768),f=new pr(v+1),m=Math.ceil(i/3),b=2*m,y=function(t){return(e[t]^e[t+1]<<m^e[t+2]<<b)&v},w=new vr(25e3),S=new pr(288),E=new pr(32),k=0,x=0,I=(c=0,0),C=0,P=0;c<o;++c){var F=y(c),R=32767&c,T=f[F];if(g[R]=T,f[F]=R,C<=c){var $=o-c;if((k>7e3||I>24576)&&$>423){u=Hr(e,l,0,w,S,E,x,I,P,c-P,u),I=k=x=0,P=c;for(var O=0;O<286;++O)S[O]=0;for(O=0;O<30;++O)E[O]=0}var M=2,L=0,A=p,D=R-T&32767;if($>2&&F==y(c-D))for(var N=Math.min(_,$)-1,q=Math.min(32767,c),B=Math.min(258,$);D<=q&&--A&&R!=T;){if(e[c+M]==e[c+M-D]){for(var H=0;H<B&&e[c+H]==e[c+H-D];++H);if(H>M){if(M=H,L=D,H>N)break;var U=Math.min(D,H-2),z=0;for(O=0;O<U;++O){var j=c-D+O+32768&32767,W=j-g[j]+32768&32767;W>z&&(z=W,T=j)}}}D+=(R=T)-(T=g[R])+32768&32767}if(L){w[I++]=268435456|Sr[M]<<18|Er[L];var V=31&Sr[M],G=31&Er[L];x+=gr[V]+fr[G],++S[257+V],++E[G],C=c+M,++k}else w[I++]=e[c],++S[e[c]]}}u=Hr(e,l,s,w,S,E,x,I,P,c-P,u)}return Or(a,0,r+$r(u)+n)}(e,null==t.level?6:t.level,null==t.mem?Math.ceil(1.5*Math.max(8,Math.min(13,Math.log(e.length)))):12+t.mem,i,r,!n)},Vr=function(e,t,i){for(;i;++t)e[t]=i,i>>>=8},Gr=function(e,t){var i=t.filename;if(e[0]=31,e[1]=139,e[2]=8,e[8]=t.level<2?4:9==t.level?2:0,e[9]=3,0!=t.mtime&&Vr(e,4,Math.floor(new Date(t.mtime||Date.now())/1e3)),i){e[3]=8;for(var r=0;r<=i.length;++r)e[r+10]=i.charCodeAt(r)}},Jr=function(e){return 10+(e.filename&&e.filename.length+1||0)};function Yr(e,t){void 0===t&&(t={});var i=jr(),r=e.length;i.p(e);var n=Wr(e,t,Jr(t),8),s=n.length;return Gr(n,t),Vr(n,s-8,i.d()),Vr(n,s-4,r),n}function Kr(e,t){var i=e.length;if(\"undefined\"!=typeof TextEncoder)return(new TextEncoder).encode(e);for(var r=new _r(e.length+(e.length>>>1)),n=0,s=function(e){r[n++]=e},o=0;o<i;++o){if(n+5>r.length){var a=new _r(n+8+(i-o<<1));a.set(r),r=a}var l=e.charCodeAt(o);l<128||t?s(l):l<2048?(s(192|l>>>6),s(128|63&l)):l>55295&&l<57344?(s(240|(l=65536+(1047552&l)|1023&e.charCodeAt(++o))>>>18),s(128|l>>>12&63),s(128|l>>>6&63),s(128|63&l)):(s(224|l>>>12),s(128|l>>>6&63),s(128|63&l))}return Or(r,0,n)}var Xr=\"[SessionRecording]\",Qr=B(Xr),Zr=3e5,en=[Ci.MouseMove,Ci.MouseInteraction,Ci.Scroll,Ci.ViewportResize,Ci.Input,Ci.TouchMove,Ci.MediaInteraction,Ci.Drag],tn=e=>({rrwebMethod:e,enqueuedAt:Date.now(),attempt:1});function rn(e){return function(e,t){for(var i=\"\",r=0;r<e.length;){var n=e[r++];n<128||t?i+=String.fromCharCode(n):n<224?i+=String.fromCharCode((31&n)<<6|63&e[r++]):n<240?i+=String.fromCharCode((15&n)<<12|(63&e[r++])<<6|63&e[r++]):(n=((15&n)<<18|(63&e[r++])<<12|(63&e[r++])<<6|63&e[r++])-65536,i+=String.fromCharCode(55296|n>>10,56320|1023&n))}return i}(Yr(Kr(JSON.stringify(e))),!0)}function nn(e){return e.type===Ii.Custom&&\"sessionIdle\"===e.data.tag}function sn(e,t){return t.some((t=>\"regex\"===t.matching&&new RegExp(t.url).test(e)))}class on{get sessionIdleThresholdMilliseconds(){return this.instance.config.session_recording.session_idle_threshold_ms||3e5}get rrwebRecord(){var e,t;return null==_||null===(e=_.__PosthogExtensions__)||void 0===e||null===(t=e.rrweb)||void 0===t?void 0:t.record}get started(){return this._captureStarted}get sessionManager(){if(!this.instance.sessionManager)throw new Error(Xr+\" must be started with a valid sessionManager.\");return this.instance.sessionManager}get fullSnapshotIntervalMillis(){var e,t;return\"trigger_pending\"===this.triggerStatus?6e4:null!==(e=null===(t=this.instance.config.session_recording)||void 0===t?void 0:t.full_snapshot_interval_millis)&&void 0!==e?e:Zr}get isSampled(){var e=this.instance.get_property(Se);return L(e)?e:null}get sessionDuration(){var e,t,i=null===(e=this.buffer)||void 0===e?void 0:e.data[(null===(t=this.buffer)||void 0===t?void 0:t.data.length)-1],{sessionStartTimestamp:r}=this.sessionManager.checkAndGetSessionAndWindowId(!0);return i?i.timestamp-r:null}get isRecordingEnabled(){var e=!!this.instance.get_property(pe),i=!this.instance.config.disable_session_recording;return t&&e&&i}get isConsoleLogCaptureEnabled(){var e=!!this.instance.get_property(ve),t=this.instance.config.enable_recording_console_log;return null!=t?t:e}get canvasRecording(){var e,t,i,r,n,s,o=this.instance.config.session_recording.captureCanvas,a=this.instance.get_property(fe),l=null!==(e=null!==(t=null==o?void 0:o.recordCanvas)&&void 0!==t?t:null==a?void 0:a.enabled)&&void 0!==e&&e,u=null!==(i=null!==(r=null==o?void 0:o.canvasFps)&&void 0!==r?r:null==a?void 0:a.fps)&&void 0!==i?i:0,c=null!==(n=null!==(s=null==o?void 0:o.canvasQuality)&&void 0!==s?s:null==a?void 0:a.quality)&&void 0!==n?n:0;return{enabled:l,fps:dr(u,0,12,\"canvas recording fps\"),quality:dr(c,0,1,\"canvas recording quality\")}}get networkPayloadCapture(){var e,t,i=this.instance.get_property(ge),r={recordHeaders:null===(e=this.instance.config.session_recording)||void 0===e?void 0:e.recordHeaders,recordBody:null===(t=this.instance.config.session_recording)||void 0===t?void 0:t.recordBody},n=(null==r?void 0:r.recordHeaders)||(null==i?void 0:i.recordHeaders),s=(null==r?void 0:r.recordBody)||(null==i?void 0:i.recordBody),o=C(this.instance.config.capture_performance)?this.instance.config.capture_performance.network_timing:this.instance.config.capture_performance,a=!!(L(o)?o:null==i?void 0:i.capturePerformance);return n||s||a?{recordHeaders:n,recordBody:s,recordPerformance:a}:void 0}get sampleRate(){var e=this.instance.get_property(me);return M(e)?e:null}get minimumDuration(){var e=this.instance.get_property(be);return M(e)?e:null}get status(){return this.receivedDecide?this.isRecordingEnabled?this._urlBlocked?\"paused\":O(this._linkedFlag)||this._linkedFlagSeen?\"trigger_pending\"===this.triggerStatus?\"buffering\":L(this.isSampled)?this.isSampled?\"sampled\":\"disabled\":\"active\":\"buffering\":\"disabled\":\"buffering\"}get urlTriggerStatus(){var e;return 0===this._urlTriggers.length?\"trigger_disabled\":(null===(e=this.instance)||void 0===e?void 0:e.get_property(Ee))===this.sessionId?\"trigger_activated\":\"trigger_pending\"}get eventTriggerStatus(){var e;return 0===this._eventTriggers.length?\"trigger_disabled\":(null===(e=this.instance)||void 0===e?void 0:e.get_property(ke))===this.sessionId?\"trigger_activated\":\"trigger_pending\"}get triggerStatus(){var e=\"trigger_activated\"===this.eventTriggerStatus||\"trigger_activated\"===this.urlTriggerStatus,t=\"trigger_pending\"===this.eventTriggerStatus||\"trigger_pending\"===this.urlTriggerStatus;return e?\"trigger_activated\":t?\"trigger_pending\":\"trigger_disabled\"}constructor(e){if(W(this,\"queuedRRWebEvents\",[]),W(this,\"isIdle\",!1),W(this,\"_linkedFlagSeen\",!1),W(this,\"_lastActivityTimestamp\",Date.now()),W(this,\"_linkedFlag\",null),W(this,\"_removePageViewCaptureHook\",void 0),W(this,\"_onSessionIdListener\",void 0),W(this,\"_persistDecideOnSessionListener\",void 0),W(this,\"_samplingSessionListener\",void 0),W(this,\"_urlTriggers\",[]),W(this,\"_urlBlocklist\",[]),W(this,\"_urlBlocked\",!1),W(this,\"_eventTriggers\",[]),W(this,\"_removeEventTriggerCaptureHook\",void 0),W(this,\"_forceAllowLocalhostNetworkCapture\",!1),W(this,\"_onBeforeUnload\",(()=>{this._flushBuffer()})),W(this,\"_onOffline\",(()=>{this._tryAddCustomEvent(\"browser offline\",{})})),W(this,\"_onOnline\",(()=>{this._tryAddCustomEvent(\"browser online\",{})})),W(this,\"_onVisibilityChange\",(()=>{if(null!=a&&a.visibilityState){var e=\"window \"+a.visibilityState;this._tryAddCustomEvent(e,{})}})),this.instance=e,this._captureStarted=!1,this._endpoint=\"/s/\",this.stopRrweb=void 0,this.receivedDecide=!1,!this.instance.sessionManager)throw Qr.error(\"started without valid sessionManager\"),new Error(Xr+\" started without valid sessionManager. This is a bug.\");if(this.instance.config.__preview_experimental_cookieless_mode)throw new Error(Xr+\" cannot be used with __preview_experimental_cookieless_mode.\");var{sessionId:t,windowId:i}=this.sessionManager.checkAndGetSessionAndWindowId();this.sessionId=t,this.windowId=i,this.buffer=this.clearBuffer(),this.sessionIdleThresholdMilliseconds>=this.sessionManager.sessionTimeoutMs&&Qr.warn(\"session_idle_threshold_ms (\".concat(this.sessionIdleThresholdMilliseconds,\") is greater than the session timeout (\").concat(this.sessionManager.sessionTimeoutMs,\"). Session will never be detected as idle\"))}startIfEnabledOrStop(e){this.isRecordingEnabled?(this._startCapture(e),null==t||t.addEventListener(\"beforeunload\",this._onBeforeUnload),null==t||t.addEventListener(\"offline\",this._onOffline),null==t||t.addEventListener(\"online\",this._onOnline),null==t||t.addEventListener(\"visibilitychange\",this._onVisibilityChange),this._setupSampling(),this._addEventTriggerListener(),O(this._removePageViewCaptureHook)&&(this._removePageViewCaptureHook=this.instance.on(\"eventCaptured\",(e=>{try{if(\"$pageview\"===e.event){var t=null!=e&&e.properties.$current_url?this._maskUrl(null==e?void 0:e.properties.$current_url):\"\";if(!t)return;this._tryAddCustomEvent(\"$pageview\",{href:t})}}catch(e){Qr.error(\"Could not add $pageview to rrweb session\",e)}}))),this._onSessionIdListener||(this._onSessionIdListener=this.sessionManager.onSessionId(((e,t,i)=>{var r,n,s,o;i&&(this._tryAddCustomEvent(\"$session_id_change\",{sessionId:e,windowId:t,changeReason:i}),null===(r=this.instance)||void 0===r||null===(n=r.persistence)||void 0===n||n.unregister(ke),null===(s=this.instance)||void 0===s||null===(o=s.persistence)||void 0===o||o.unregister(Ee))})))):this.stopRecording()}stopRecording(){var e,i,r,n;this._captureStarted&&this.stopRrweb&&(this.stopRrweb(),this.stopRrweb=void 0,this._captureStarted=!1,null==t||t.removeEventListener(\"beforeunload\",this._onBeforeUnload),null==t||t.removeEventListener(\"offline\",this._onOffline),null==t||t.removeEventListener(\"online\",this._onOnline),null==t||t.removeEventListener(\"visibilitychange\",this._onVisibilityChange),this.clearBuffer(),clearInterval(this._fullSnapshotTimer),null===(e=this._removePageViewCaptureHook)||void 0===e||e.call(this),this._removePageViewCaptureHook=void 0,null===(i=this._removeEventTriggerCaptureHook)||void 0===i||i.call(this),this._removeEventTriggerCaptureHook=void 0,null===(r=this._onSessionIdListener)||void 0===r||r.call(this),this._onSessionIdListener=void 0,null===(n=this._samplingSessionListener)||void 0===n||n.call(this),this._samplingSessionListener=void 0,Qr.info(\"stopped\"))}makeSamplingDecision(e){var t,i=this.sessionId!==e,r=this.sampleRate;if(M(r)){var n,s=this.isSampled,o=i||!L(s);if(o)n=Math.random()<r;else n=s;o&&(n?this._reportStarted(\"sampled\"):Qr.warn(\"Sample rate (\".concat(r,\") has determined that this sessionId (\").concat(e,\") will not be sent to the server.\")),this._tryAddCustomEvent(\"samplingDecisionMade\",{sampleRate:r,isSampled:n})),null===(t=this.instance.persistence)||void 0===t||t.register({[Se]:n})}else{var a;null===(a=this.instance.persistence)||void 0===a||a.register({[Se]:null})}}onRemoteConfig(e){var t,i,r,n,s,o;(this._tryAddCustomEvent(\"$remote_config_received\",e),this._persistRemoteConfig(e),this._linkedFlag=(null===(t=e.sessionRecording)||void 0===t?void 0:t.linkedFlag)||null,null!==(i=e.sessionRecording)&&void 0!==i&&i.endpoint)&&(this._endpoint=null===(o=e.sessionRecording)||void 0===o?void 0:o.endpoint);if(this._setupSampling(),!O(this._linkedFlag)&&!this._linkedFlagSeen){var a=R(this._linkedFlag)?this._linkedFlag:this._linkedFlag.flag,l=R(this._linkedFlag)?null:this._linkedFlag.variant;this.instance.onFeatureFlags(((e,t)=>{var i=C(t)&&a in t,r=l?t[a]===l:i;r&&this._reportStarted(\"linked_flag_matched\",{linkedFlag:a,linkedVariant:l}),this._linkedFlagSeen=r}))}null!==(r=e.sessionRecording)&&void 0!==r&&r.urlTriggers&&(this._urlTriggers=e.sessionRecording.urlTriggers),null!==(n=e.sessionRecording)&&void 0!==n&&n.urlBlocklist&&(this._urlBlocklist=e.sessionRecording.urlBlocklist),null!==(s=e.sessionRecording)&&void 0!==s&&s.eventTriggers&&(this._eventTriggers=e.sessionRecording.eventTriggers),this.receivedDecide=!0,this.startIfEnabledOrStop()}_setupSampling(){M(this.sampleRate)&&O(this._samplingSessionListener)&&(this._samplingSessionListener=this.sessionManager.onSessionId((e=>{this.makeSamplingDecision(e)})))}_persistRemoteConfig(e){if(this.instance.persistence){var t,i=this.instance.persistence,r=()=>{var t,r,n,s,o,a,l,u,c=null===(t=e.sessionRecording)||void 0===t?void 0:t.sampleRate,d=O(c)?null:parseFloat(c),h=null===(r=e.sessionRecording)||void 0===r?void 0:r.minimumDurationMilliseconds;i.register({[pe]:!!e.sessionRecording,[ve]:null===(n=e.sessionRecording)||void 0===n?void 0:n.consoleLogRecordingEnabled,[ge]:j({capturePerformance:e.capturePerformance},null===(s=e.sessionRecording)||void 0===s?void 0:s.networkPayloadCapture),[fe]:{enabled:null===(o=e.sessionRecording)||void 0===o?void 0:o.recordCanvas,fps:null===(a=e.sessionRecording)||void 0===a?void 0:a.canvasFps,quality:null===(l=e.sessionRecording)||void 0===l?void 0:l.canvasQuality},[me]:d,[be]:F(h)?null:h,[ye]:null===(u=e.sessionRecording)||void 0===u?void 0:u.scriptConfig})};r(),null===(t=this._persistDecideOnSessionListener)||void 0===t||t.call(this),this._persistDecideOnSessionListener=this.sessionManager.onSessionId(r)}}log(e){var t,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:\"log\";null===(t=this.instance.sessionRecording)||void 0===t||t.onRRwebEmit({type:6,data:{plugin:\"rrweb/console@1\",payload:{level:i,trace:[],payload:[JSON.stringify(e)]}},timestamp:Date.now()})}_startCapture(e){if(!F(Object.assign)&&!F(Array.from)&&!(this._captureStarted||this.instance.config.disable_session_recording||this.instance.consent.isOptedOut())){var t,i;if(this._captureStarted=!0,this.sessionManager.checkAndGetSessionAndWindowId(),this.rrwebRecord)this._onScriptLoaded();else null===(t=_.__PosthogExtensions__)||void 0===t||null===(i=t.loadExternalDependency)||void 0===i||i.call(t,this.instance,this.scriptName,(e=>{if(e)return Qr.error(\"could not load recorder\",e);this._onScriptLoaded()}));Qr.info(\"starting\"),\"active\"===this.status&&this._reportStarted(e||\"recording_initialized\")}}get scriptName(){var e,t,i;return(null===(e=this.instance)||void 0===e||null===(t=e.persistence)||void 0===t||null===(i=t.get_property(ye))||void 0===i?void 0:i.script)||\"recorder\"}isInteractiveEvent(e){var t;return 3===e.type&&-1!==en.indexOf(null===(t=e.data)||void 0===t?void 0:t.source)}_updateWindowAndSessionIds(e){var t=this.isInteractiveEvent(e);t||this.isIdle||e.timestamp-this._lastActivityTimestamp>this.sessionIdleThresholdMilliseconds&&(this.isIdle=!0,clearInterval(this._fullSnapshotTimer),this._tryAddCustomEvent(\"sessionIdle\",{eventTimestamp:e.timestamp,lastActivityTimestamp:this._lastActivityTimestamp,threshold:this.sessionIdleThresholdMilliseconds,bufferLength:this.buffer.data.length,bufferSize:this.buffer.size}),this._flushBuffer());var i=!1;if(t&&(this._lastActivityTimestamp=e.timestamp,this.isIdle&&(this.isIdle=!1,this._tryAddCustomEvent(\"sessionNoLongerIdle\",{reason:\"user activity\",type:e.type}),i=!0)),!this.isIdle){var{windowId:r,sessionId:n}=this.sessionManager.checkAndGetSessionAndWindowId(!t,e.timestamp),s=this.sessionId!==n,o=this.windowId!==r;this.windowId=r,this.sessionId=n,s||o?(this.stopRecording(),this.startIfEnabledOrStop(\"session_id_changed\")):i&&this._scheduleFullSnapshot()}}_tryRRWebMethod(e){try{return e.rrwebMethod(),!0}catch(t){return this.queuedRRWebEvents.length<10?this.queuedRRWebEvents.push({enqueuedAt:e.enqueuedAt||Date.now(),attempt:e.attempt++,rrwebMethod:e.rrwebMethod}):Qr.warn(\"could not emit queued rrweb event.\",t,e),!1}}_tryAddCustomEvent(e,t){return this._tryRRWebMethod(tn((()=>this.rrwebRecord.addCustomEvent(e,t))))}_tryTakeFullSnapshot(){return this._tryRRWebMethod(tn((()=>this.rrwebRecord.takeFullSnapshot())))}_onScriptLoaded(){var e,t={blockClass:\"ph-no-capture\",blockSelector:void 0,ignoreClass:\"ph-ignore-input\",maskTextClass:\"ph-mask\",maskTextSelector:void 0,maskTextFn:void 0,maskAllInputs:!0,maskInputOptions:{password:!0},maskInputFn:void 0,slimDOMOptions:{},collectFonts:!1,inlineStylesheet:!0,recordCrossOriginIframes:!1},i=this.instance.config.session_recording;for(var[r,n]of Object.entries(i||{}))r in t&&(\"maskInputOptions\"===r?t.maskInputOptions=j({password:!0},n):t[r]=n);if(this.canvasRecording&&this.canvasRecording.enabled&&(t.recordCanvas=!0,t.sampling={canvas:this.canvasRecording.fps},t.dataURLOptions={type:\"image/webp\",quality:this.canvasRecording.quality}),this.rrwebRecord){this.mutationRateLimiter=null!==(e=this.mutationRateLimiter)&&void 0!==e?e:new hr(this.rrwebRecord,{refillRate:this.instance.config.session_recording.__mutationRateLimiterRefillRate,bucketSize:this.instance.config.session_recording.__mutationRateLimiterBucketSize,onBlockedNode:(e,t)=>{var i=\"Too many mutations on node '\".concat(e,\"'. Rate limiting. This could be due to SVG animations or something similar\");Qr.info(i,{node:t}),this.log(Xr+\" \"+i,\"warn\")}});var s=this._gatherRRWebPlugins();this.stopRrweb=this.rrwebRecord(j({emit:e=>{this.onRRwebEmit(e)},plugins:s},t)),this._lastActivityTimestamp=Date.now(),this.isIdle=!1,this._tryAddCustomEvent(\"$session_options\",{sessionRecordingOptions:t,activePlugins:s.map((e=>null==e?void 0:e.name))}),this._tryAddCustomEvent(\"$posthog_config\",{config:this.instance.config})}else Qr.error(\"onScriptLoaded was called but rrwebRecord is not available. This indicates something has gone wrong.\")}_scheduleFullSnapshot(){if(this._fullSnapshotTimer&&clearInterval(this._fullSnapshotTimer),!this.isIdle){var e=this.fullSnapshotIntervalMillis;e&&(this._fullSnapshotTimer=setInterval((()=>{this._tryTakeFullSnapshot()}),e))}}_gatherRRWebPlugins(){var e,t,i,r,n=[],s=null===(e=_.__PosthogExtensions__)||void 0===e||null===(t=e.rrwebPlugins)||void 0===t?void 0:t.getRecordConsolePlugin;s&&this.isConsoleLogCaptureEnabled&&n.push(s());var o=null===(i=_.__PosthogExtensions__)||void 0===i||null===(r=i.rrwebPlugins)||void 0===r?void 0:r.getRecordNetworkPlugin;this.networkPayloadCapture&&I(o)&&(!vt.includes(location.hostname)||this._forceAllowLocalhostNetworkCapture?n.push(o(cr(this.instance.config,this.networkPayloadCapture))):Qr.info(\"NetworkCapture not started because we are on localhost.\"));return n}onRRwebEmit(e){var t;if(this._processQueuedEvents(),e&&C(e)){if(e.type===Ii.Meta){var i=this._maskUrl(e.data.href);if(this._lastHref=i,!i)return;e.data.href=i}else this._pageViewFallBack();if(this._checkUrlTriggerConditions(),\"paused\"!==this.status||function(e){return e.type===Ii.Custom&&\"recording paused\"===e.data.tag}(e)){e.type===Ii.FullSnapshot&&this._scheduleFullSnapshot(),e.type===Ii.FullSnapshot&&\"trigger_pending\"===this.triggerStatus&&this.clearBuffer();var r=this.mutationRateLimiter?this.mutationRateLimiter.throttleMutations(e):e;if(r){var n=function(e){var t=e;if(t&&C(t)&&6===t.type&&C(t.data)&&\"rrweb/console@1\"===t.data.plugin){t.data.payload.payload.length>10&&(t.data.payload.payload=t.data.payload.payload.slice(0,10),t.data.payload.payload.push(\"...[truncated]\"));for(var i=[],r=0;r<t.data.payload.payload.length;r++)t.data.payload.payload[r]&&t.data.payload.payload[r].length>2e3?i.push(t.data.payload.payload[r].slice(0,2e3)+\"...[truncated]\"):i.push(t.data.payload.payload[r]);return t.data.payload.payload=i,e}return e}(r);if(this._updateWindowAndSessionIds(n),!this.isIdle||nn(n)){if(nn(n)){var s=n.data.payload;if(s){var o=s.lastActivityTimestamp,a=s.threshold;n.timestamp=o+a}}var l=null===(t=this.instance.config.session_recording.compress_events)||void 0===t||t?function(e){if(ki(e)<1024)return e;try{if(e.type===Ii.FullSnapshot)return j(j({},e),{},{data:rn(e.data),cv:\"2024-10\"});if(e.type===Ii.IncrementalSnapshot&&e.data.source===Ci.Mutation)return j(j({},e),{},{cv:\"2024-10\",data:j(j({},e.data),{},{texts:rn(e.data.texts),attributes:rn(e.data.attributes),removes:rn(e.data.removes),adds:rn(e.data.adds)})});if(e.type===Ii.IncrementalSnapshot&&e.data.source===Ci.StyleSheetRule)return j(j({},e),{},{cv:\"2024-10\",data:j(j({},e.data),{},{adds:rn(e.data.adds),removes:rn(e.data.removes)})})}catch(e){Qr.error(\"could not compress event - will use uncompressed event\",e)}return e}(n):n,u={$snapshot_bytes:ki(l),$snapshot_data:l,$session_id:this.sessionId,$window_id:this.windowId};\"disabled\"!==this.status?this._captureSnapshotBuffered(u):this.clearBuffer()}}}}}_pageViewFallBack(){if(!this.instance.config.capture_pageview&&t){var e=this._maskUrl(t.location.href);this._lastHref!==e&&(this._tryAddCustomEvent(\"$url_changed\",{href:e}),this._lastHref=e)}}_processQueuedEvents(){if(this.queuedRRWebEvents.length){var e=[...this.queuedRRWebEvents];this.queuedRRWebEvents=[],e.forEach((e=>{Date.now()-e.enqueuedAt<=2e3&&this._tryRRWebMethod(e)}))}}_maskUrl(e){var t=this.instance.config.session_recording;if(t.maskNetworkRequestFn){var i,r={url:e};return null===(i=r=t.maskNetworkRequestFn(r))||void 0===i?void 0:i.url}return e}clearBuffer(){return this.buffer={size:0,data:[],sessionId:this.sessionId,windowId:this.windowId},this.buffer}_flushBuffer(){this.flushBufferTimer&&(clearTimeout(this.flushBufferTimer),this.flushBufferTimer=void 0);var e=this.minimumDuration,t=this.sessionDuration,i=M(t)&&t>=0,r=M(e)&&i&&t<e;if(\"buffering\"===this.status||\"paused\"===this.status||r)return this.flushBufferTimer=setTimeout((()=>{this._flushBuffer()}),2e3),this.buffer;this.buffer.data.length>0&&xi(this.buffer).forEach((e=>{this._captureSnapshot({$snapshot_bytes:e.size,$snapshot_data:e.data,$session_id:e.sessionId,$window_id:e.windowId,$lib:\"web\",$lib_version:p.LIB_VERSION})}));return this.clearBuffer()}_captureSnapshotBuffered(e){var t,i=2+((null===(t=this.buffer)||void 0===t?void 0:t.data.length)||0);!this.isIdle&&(this.buffer.size+e.$snapshot_bytes+i>943718.4||this.buffer.sessionId!==this.sessionId)&&(this.buffer=this._flushBuffer()),this.buffer.size+=e.$snapshot_bytes,this.buffer.data.push(e.$snapshot_data),this.flushBufferTimer||this.isIdle||(this.flushBufferTimer=setTimeout((()=>{this._flushBuffer()}),2e3))}_captureSnapshot(e){this.instance.capture(\"$snapshot\",e,{_url:this.instance.requestRouter.endpointFor(\"api\",this._endpoint),_noTruncate:!0,_batchKey:\"recordings\",skip_client_rate_limiting:!0})}_checkUrlTriggerConditions(){if(void 0!==t&&t.location.href){var e=t.location.href,i=\"paused\"===this.status,r=sn(e,this._urlBlocklist);r&&!i?this._pauseRecording():!r&&i&&this._resumeRecording(),sn(e,this._urlTriggers)&&this._activateTrigger(\"url\")}}_activateTrigger(e){var t,i;\"trigger_pending\"===this.triggerStatus&&(null===(t=this.instance)||void 0===t||null===(i=t.persistence)||void 0===i||i.register({[\"url\"===e?Ee:ke]:this.sessionId}),this._flushBuffer(),this._reportStarted(e+\"_trigger_matched\"))}_pauseRecording(){\"paused\"!==this.status&&(this._urlBlocked=!0,clearInterval(this._fullSnapshotTimer),Qr.info(\"recording paused due to URL blocker\"),this._tryAddCustomEvent(\"recording paused\",{reason:\"url blocker\"}))}_resumeRecording(){\"paused\"===this.status&&(this._urlBlocked=!1,this._tryTakeFullSnapshot(),this._scheduleFullSnapshot(),this._tryAddCustomEvent(\"recording resumed\",{reason:\"left blocked url\"}),Qr.info(\"recording resumed\"))}_addEventTriggerListener(){0!==this._eventTriggers.length&&O(this._removeEventTriggerCaptureHook)&&(this._removeEventTriggerCaptureHook=this.instance.on(\"eventCaptured\",(e=>{try{this._eventTriggers.includes(e.event)&&this._activateTrigger(\"event\")}catch(e){Qr.error(\"Could not activate event trigger\",e)}})))}overrideLinkedFlag(){this._linkedFlagSeen=!0,this._tryTakeFullSnapshot(),this._reportStarted(\"linked_flag_overridden\")}overrideSampling(){var e;null===(e=this.instance.persistence)||void 0===e||e.register({[Se]:!0}),this._tryTakeFullSnapshot(),this._reportStarted(\"sampling_overridden\")}overrideTrigger(e){this._activateTrigger(e)}_reportStarted(e,t){this.instance.register_for_session({$session_recording_start_reason:e}),Qr.info(e.replace(\"_\",\" \"),t),m([\"recording_initialized\",\"session_id_changed\"],e)||this._tryAddCustomEvent(e,t)}}var an=B(\"[RemoteConfig]\");class ln{constructor(e){this.instance=e}get remoteConfig(){var e,t;return null===(e=_._POSTHOG_REMOTE_CONFIG)||void 0===e||null===(t=e[this.instance.config.token])||void 0===t?void 0:t.config}_loadRemoteConfigJs(e){var t,i,r;null!==(t=_.__PosthogExtensions__)&&void 0!==t&&t.loadExternalDependency?null===(i=_.__PosthogExtensions__)||void 0===i||null===(r=i.loadExternalDependency)||void 0===r||r.call(i,this.instance,\"remote-config\",(()=>e(this.remoteConfig))):(an.error(\"PostHog Extensions not found. Cannot load remote config.\"),e())}_loadRemoteConfigJSON(e){this.instance._send_request({method:\"GET\",url:this.instance.requestRouter.endpointFor(\"assets\",\"/array/\".concat(this.instance.config.token,\"/config\")),callback:t=>{e(t.json)}})}load(){try{if(this.remoteConfig)return an.info(\"Using preloaded remote config\",this.remoteConfig),void this.onRemoteConfig(this.remoteConfig);if(this.instance.config.advanced_disable_decide)return void an.warn(\"Remote config is disabled. Falling back to local config.\");this._loadRemoteConfigJs((e=>{if(!e)return an.info(\"No config found after loading remote JS config. Falling back to JSON.\"),void this._loadRemoteConfigJSON((e=>{this.onRemoteConfig(e)}));this.onRemoteConfig(e)}))}catch(e){an.error(\"Error loading remote config\",e)}}onRemoteConfig(e){e?this.instance.config.__preview_remote_config?(this.instance._onRemoteConfig(e),!1!==e.hasFeatureFlags&&this.instance.featureFlags.ensureFlagsLoaded()):an.info(\"__preview_remote_config is disabled. Logging config instead\",e):an.error(\"Failed to fetch remote config from PostHog.\")}}var un,cn=null!=t&&t.location?wt(t.location.hash,\"__posthog\")||wt(location.hash,\"state\"):null,dn=\"_postHogToolbarParams\",hn=B(\"[Toolbar]\");!function(e){e[e.UNINITIALIZED=0]=\"UNINITIALIZED\",e[e.LOADING=1]=\"LOADING\",e[e.LOADED=2]=\"LOADED\"}(un||(un={}));class _n{constructor(e){this.instance=e}setToolbarState(e){_.ph_toolbar_state=e}getToolbarState(){var e;return null!==(e=_.ph_toolbar_state)&&void 0!==e?e:un.UNINITIALIZED}maybeLoadToolbar(){var e,i,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:void 0,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0;if(!t||!a)return!1;r=null!==(e=r)&&void 0!==e?e:t.location,s=null!==(i=s)&&void 0!==i?i:t.history;try{if(!n){try{t.localStorage.setItem(\"test\",\"test\"),t.localStorage.removeItem(\"test\")}catch(e){return!1}n=null==t?void 0:t.localStorage}var o,l=cn||wt(r.hash,\"__posthog\")||wt(r.hash,\"state\"),u=l?Z((()=>JSON.parse(atob(decodeURIComponent(l)))))||Z((()=>JSON.parse(decodeURIComponent(l)))):null;return u&&\"ph_authorize\"===u.action?((o=u).source=\"url\",o&&Object.keys(o).length>0&&(u.desiredHash?r.hash=u.desiredHash:s?s.replaceState(s.state,\"\",r.pathname+r.search):r.hash=\"\")):((o=JSON.parse(n.getItem(dn)||\"{}\")).source=\"localstorage\",delete o.userIntent),!(!o.token||this.instance.config.token!==o.token)&&(this.loadToolbar(o),!0)}catch(e){return!1}}_callLoadToolbar(e){var t=_.ph_load_toolbar||_.ph_load_editor;!O(t)&&I(t)?t(e,this.instance):hn.warn(\"No toolbar load function found\")}loadToolbar(e){var i=!(null==a||!a.getElementById(qe));if(!t||i)return!1;var r=\"custom\"===this.instance.requestRouter.region&&this.instance.config.advanced_disable_toolbar_metrics,n=j(j({token:this.instance.config.token},e),{},{apiURL:this.instance.requestRouter.endpointFor(\"ui\")},r?{instrument:!1}:{});if(t.localStorage.setItem(dn,JSON.stringify(j(j({},n),{},{source:void 0}))),this.getToolbarState()===un.LOADED)this._callLoadToolbar(n);else if(this.getToolbarState()===un.UNINITIALIZED){var s,o;this.setToolbarState(un.LOADING),null===(s=_.__PosthogExtensions__)||void 0===s||null===(o=s.loadExternalDependency)||void 0===o||o.call(s,this.instance,\"toolbar\",(e=>{if(e)return hn.error(\"[Toolbar] Failed to load\",e),void this.setToolbarState(un.UNINITIALIZED);this.setToolbarState(un.LOADED),this._callLoadToolbar(n)})),re(t,\"turbolinks:load\",(()=>{this.setToolbarState(un.UNINITIALIZED),this.loadToolbar(n)}))}return!0}_loadEditor(e){return this.loadToolbar(e)}maybeLoadEditor(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:void 0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0;return this.maybeLoadToolbar(e,t,i)}}class pn{constructor(e){W(this,\"isPaused\",!0),W(this,\"queue\",[]),W(this,\"flushTimeoutMs\",3e3),this.sendRequest=e}enqueue(e){this.queue.push(e),this.flushTimeout||this.setFlushTimeout()}unload(){this.clearFlushTimeout();var e=this.queue.length>0?this.formatQueue():{},t=Object.values(e),i=[...t.filter((e=>0===e.url.indexOf(\"/e\"))),...t.filter((e=>0!==e.url.indexOf(\"/e\")))];i.map((e=>{this.sendRequest(j(j({},e),{},{transport:\"sendBeacon\"}))}))}enable(){this.isPaused=!1,this.setFlushTimeout()}setFlushTimeout(){var e=this;this.isPaused||(this.flushTimeout=setTimeout((()=>{if(this.clearFlushTimeout(),this.queue.length>0){var t=this.formatQueue(),i=function(i){var r=t[i],n=(new Date).getTime();r.data&&x(r.data)&&Y(r.data,(e=>{e.offset=Math.abs(e.timestamp-n),delete e.timestamp})),e.sendRequest(r)};for(var r in t)i(r)}}),this.flushTimeoutMs))}clearFlushTimeout(){clearTimeout(this.flushTimeout),this.flushTimeout=void 0}formatQueue(){var e={};return Y(this.queue,(t=>{var i,r=t,n=(r?r.batchKey:null)||r.url;F(e[n])&&(e[n]=j(j({},r),{},{data:[]})),null===(i=e[n].data)||void 0===i||i.push(r.data)})),this.queue=[],e}}var vn=function(e){var t,i,r,n,s=\"\";for(t=i=0,r=(e=(e+\"\").replace(/\\r\\n/g,\"\\n\").replace(/\\r/g,\"\\n\")).length,n=0;n<r;n++){var o=e.charCodeAt(n),a=null;o<128?i++:a=o>127&&o<2048?String.fromCharCode(o>>6|192,63&o|128):String.fromCharCode(o>>12|224,o>>6&63|128,63&o|128),$(a)||(i>t&&(s+=e.substring(t,i)),s+=a,t=i=n+1)}return i>t&&(s+=e.substring(t,e.length)),s},gn=!!c||!!u,fn=\"text/plain\",mn=(e,t)=>{var[i,r]=e.split(\"?\"),n=j({},t);null==r||r.split(\"&\").forEach((e=>{var[t]=e.split(\"=\");delete n[t]}));var s=mt(n);return s=s?(r?r+\"&\":\"\")+s:r,\"\".concat(i,\"?\").concat(s)},bn=(e,t)=>JSON.stringify(e,((e,t)=>\"bigint\"==typeof t?t.toString():t),t),yn=t=>{var{data:i,compression:r}=t;if(i){if(r===e.GZipJS){var n=Yr(Kr(bn(i)),{mtime:0}),s=new Blob([n],{type:fn});return{contentType:fn,body:s,estimatedSize:s.size}}if(r===e.Base64){var o=function(e){var t,i,r,n,s,o=\"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=\",a=0,l=0,u=\"\",c=[];if(!e)return e;e=vn(e);do{t=(s=e.charCodeAt(a++)<<16|e.charCodeAt(a++)<<8|e.charCodeAt(a++))>>18&63,i=s>>12&63,r=s>>6&63,n=63&s,c[l++]=o.charAt(t)+o.charAt(i)+o.charAt(r)+o.charAt(n)}while(a<e.length);switch(u=c.join(\"\"),e.length%3){case 1:u=u.slice(0,-2)+\"==\";break;case 2:u=u.slice(0,-1)+\"=\"}return u}(bn(i)),a=(e=>\"data=\"+encodeURIComponent(\"string\"==typeof e?e:bn(e)))(o);return{contentType:\"application/x-www-form-urlencoded\",body:a,estimatedSize:new Blob([a]).size}}var l=bn(i);return{contentType:\"application/json\",body:l,estimatedSize:new Blob([l]).size}}},wn=[];u&&wn.push({transport:\"fetch\",method:e=>{var t,i,{contentType:r,body:n,estimatedSize:s}=null!==(t=yn(e))&&void 0!==t?t:{},o=new Headers;Y(e.headers,(function(e,t){o.append(t,e)})),r&&o.append(\"Content-Type\",r);var a=e.url,l=null;if(d){var c=new d;l={signal:c.signal,timeout:setTimeout((()=>c.abort()),e.timeout)}}u(a,j({method:(null==e?void 0:e.method)||\"GET\",headers:o,keepalive:\"POST\"===e.method&&(s||0)<52428.8,body:n,signal:null===(i=l)||void 0===i?void 0:i.signal},e.fetchOptions)).then((t=>t.text().then((i=>{var r,n={statusCode:t.status,text:i};if(200===t.status)try{n.json=JSON.parse(i)}catch(e){q.error(e)}null===(r=e.callback)||void 0===r||r.call(e,n)})))).catch((t=>{var i;q.error(t),null===(i=e.callback)||void 0===i||i.call(e,{statusCode:0,text:t})})).finally((()=>l?clearTimeout(l.timeout):null))}}),c&&wn.push({transport:\"XHR\",method:e=>{var t,i=new c;i.open(e.method||\"GET\",e.url,!0);var{contentType:r,body:n}=null!==(t=yn(e))&&void 0!==t?t:{};Y(e.headers,(function(e,t){i.setRequestHeader(t,e)})),r&&i.setRequestHeader(\"Content-Type\",r),e.timeout&&(i.timeout=e.timeout),i.withCredentials=!0,i.onreadystatechange=()=>{if(4===i.readyState){var t,r={statusCode:i.status,text:i.responseText};if(200===i.status)try{r.json=JSON.parse(i.responseText)}catch(e){}null===(t=e.callback)||void 0===t||t.call(e,r)}},i.send(n)}}),null!=o&&o.sendBeacon&&wn.push({transport:\"sendBeacon\",method:e=>{var t=mn(e.url,{beacon:\"1\"});try{var i,{contentType:r,body:n}=null!==(i=yn(e))&&void 0!==i?i:{},s=\"string\"==typeof n?new Blob([n],{type:r}):n;o.sendBeacon(t,s)}catch(e){}}});var Sn=[\"retriesPerformedSoFar\"];class En{constructor(e){W(this,\"isPolling\",!1),W(this,\"pollIntervalMs\",3e3),W(this,\"queue\",[]),this.instance=e,this.queue=[],this.areWeOnline=!0,!F(t)&&\"onLine\"in t.navigator&&(this.areWeOnline=t.navigator.onLine,t.addEventListener(\"online\",(()=>{this.areWeOnline=!0,this.flush()})),t.addEventListener(\"offline\",(()=>{this.areWeOnline=!1})))}retriableRequest(e){var{retriesPerformedSoFar:t}=e,i=V(e,Sn);M(t)&&t>0&&(i.url=mn(i.url,{retry_count:t})),this.instance._send_request(j(j({},i),{},{callback:e=>{var r;200!==e.statusCode&&(e.statusCode<400||e.statusCode>=500)&&(null!=t?t:0)<10?this.enqueue(j({retriesPerformedSoFar:t},i)):null===(r=i.callback)||void 0===r||r.call(i,e)}}))}enqueue(e){var t=e.retriesPerformedSoFar||0;e.retriesPerformedSoFar=t+1;var i=function(e){var t=3e3*Math.pow(2,e),i=t/2,r=Math.min(18e5,t),n=(Math.random()-.5)*(r-i);return Math.ceil(r+n)}(t),r=Date.now()+i;this.queue.push({retryAt:r,requestOptions:e});var n=\"Enqueued failed request for retry in \".concat(i);navigator.onLine||(n+=\" (Browser is offline)\"),q.warn(n),this.isPolling||(this.isPolling=!0,this.poll())}poll(){this.poller&&clearTimeout(this.poller),this.poller=setTimeout((()=>{this.areWeOnline&&this.queue.length>0&&this.flush(),this.poll()}),this.pollIntervalMs)}flush(){var e=Date.now(),t=[],i=this.queue.filter((i=>i.retryAt<e||(t.push(i),!1)));if(this.queue=t,i.length>0)for(var{requestOptions:r}of i)this.retriableRequest(r)}unload(){for(var{requestOptions:e}of(this.poller&&(clearTimeout(this.poller),this.poller=void 0),this.queue))try{this.instance._send_request(j(j({},e),{},{transport:\"sendBeacon\"}))}catch(e){q.error(e)}this.queue=[]}}var kn,xn=B(\"[SessionId]\");class In{constructor(e,t,i){var r;if(W(this,\"_sessionIdChangedHandlers\",[]),!e.persistence)throw new Error(\"SessionIdManager requires a PostHogPersistence instance\");if(e.config.__preview_experimental_cookieless_mode)throw new Error(\"SessionIdManager cannot be used with __preview_experimental_cookieless_mode\");this.config=e.config,this.persistence=e.persistence,this._windowId=void 0,this._sessionId=void 0,this._sessionStartTimestamp=null,this._sessionActivityTimestamp=null,this._sessionIdGenerator=t||et,this._windowIdGenerator=i||et;var n=this.config.persistence_name||this.config.token,s=this.config.session_idle_timeout_seconds||1800;if(this._sessionTimeoutMs=1e3*dr(s,60,36e3,\"session_idle_timeout_seconds\",1800),e.register({$configured_session_timeout_ms:this._sessionTimeoutMs}),this.resetIdleTimer(),this._window_id_storage_key=\"ph_\"+n+\"_window_id\",this._primary_window_exists_storage_key=\"ph_\"+n+\"_primary_window_exists\",this._canUseSessionStorage()){var o=pt.parse(this._window_id_storage_key),a=pt.parse(this._primary_window_exists_storage_key);o&&!a?this._windowId=o:pt.remove(this._window_id_storage_key),pt.set(this._primary_window_exists_storage_key,!0)}if(null!==(r=this.config.bootstrap)&&void 0!==r&&r.sessionID)try{var l=(e=>{var t=e.replace(/-/g,\"\");if(32!==t.length)throw new Error(\"Not a valid UUID\");if(\"7\"!==t[12])throw new Error(\"Not a UUIDv7\");return parseInt(t.substring(0,12),16)})(this.config.bootstrap.sessionID);this._setSessionId(this.config.bootstrap.sessionID,(new Date).getTime(),l)}catch(e){xn.error(\"Invalid sessionID in bootstrap\",e)}this._listenToReloadWindow()}get sessionTimeoutMs(){return this._sessionTimeoutMs}onSessionId(e){return F(this._sessionIdChangedHandlers)&&(this._sessionIdChangedHandlers=[]),this._sessionIdChangedHandlers.push(e),this._sessionId&&e(this._sessionId,this._windowId),()=>{this._sessionIdChangedHandlers=this._sessionIdChangedHandlers.filter((t=>t!==e))}}_canUseSessionStorage(){return\"memory\"!==this.config.persistence&&!this.persistence.disabled&&pt.is_supported()}_setWindowId(e){e!==this._windowId&&(this._windowId=e,this._canUseSessionStorage()&&pt.set(this._window_id_storage_key,e))}_getWindowId(){return this._windowId?this._windowId:this._canUseSessionStorage()?pt.parse(this._window_id_storage_key):null}_setSessionId(e,t,i){e===this._sessionId&&t===this._sessionActivityTimestamp&&i===this._sessionStartTimestamp||(this._sessionStartTimestamp=i,this._sessionActivityTimestamp=t,this._sessionId=e,this.persistence.register({[we]:[t,e,i]}))}_getSessionId(){if(this._sessionId&&this._sessionActivityTimestamp&&this._sessionStartTimestamp)return[this._sessionActivityTimestamp,this._sessionId,this._sessionStartTimestamp];var e=this.persistence.props[we];return x(e)&&2===e.length&&e.push(e[0]),e||[0,null,0]}resetSessionId(){this._setSessionId(null,null,null)}_listenToReloadWindow(){null==t||t.addEventListener(\"beforeunload\",(()=>{this._canUseSessionStorage()&&pt.remove(this._primary_window_exists_storage_key)}))}checkAndGetSessionAndWindowId(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(this.config.__preview_experimental_cookieless_mode)throw new Error(\"checkAndGetSessionAndWindowId should not be called in __preview_experimental_cookieless_mode\");var i=t||(new Date).getTime(),[r,n,s]=this._getSessionId(),o=this._getWindowId(),a=M(s)&&s>0&&Math.abs(i-s)>864e5,l=!1,u=!n,c=!e&&Math.abs(i-r)>this.sessionTimeoutMs;u||c||a?(n=this._sessionIdGenerator(),o=this._windowIdGenerator(),xn.info(\"new session ID generated\",{sessionId:n,windowId:o,changeReason:{noSessionId:u,activityTimeout:c,sessionPastMaximumLength:a}}),s=i,l=!0):o||(o=this._windowIdGenerator(),l=!0);var d=0===r||!e||a?i:r,h=0===s?(new Date).getTime():s;return this._setWindowId(o),this._setSessionId(n,d,h),e||this.resetIdleTimer(),l&&this._sessionIdChangedHandlers.forEach((e=>e(n,o,l?{noSessionId:u,activityTimeout:c,sessionPastMaximumLength:a}:void 0))),{sessionId:n,windowId:o,sessionStartTimestamp:h,changeReason:l?{noSessionId:u,activityTimeout:c,sessionPastMaximumLength:a}:void 0,lastActivityTimestamp:r}}resetIdleTimer(){clearTimeout(this._enforceIdleTimeout),this._enforceIdleTimeout=setTimeout((()=>{this.resetSessionId()}),1.1*this.sessionTimeoutMs)}}!function(e){e.US=\"us\",e.EU=\"eu\",e.CUSTOM=\"custom\"}(kn||(kn={}));var Cn=\"i.posthog.com\";class Pn{constructor(e){W(this,\"_regionCache\",{}),this.instance=e}get apiHost(){var e=this.instance.config.api_host.trim().replace(/\\/$/,\"\");return\"https://app.posthog.com\"===e?\"https://us.i.posthog.com\":e}get uiHost(){var e,t=null===(e=this.instance.config.ui_host)||void 0===e?void 0:e.replace(/\\/$/,\"\");return t||(t=this.apiHost.replace(\".\".concat(Cn),\".posthog.com\")),\"https://app.posthog.com\"===t?\"https://us.posthog.com\":t}get region(){return this._regionCache[this.apiHost]||(/https:\\/\\/(app|us|us-assets)(\\.i)?\\.posthog\\.com/i.test(this.apiHost)?this._regionCache[this.apiHost]=kn.US:/https:\\/\\/(eu|eu-assets)(\\.i)?\\.posthog\\.com/i.test(this.apiHost)?this._regionCache[this.apiHost]=kn.EU:this._regionCache[this.apiHost]=kn.CUSTOM),this._regionCache[this.apiHost]}endpointFor(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:\"\";if(t&&(t=\"/\"===t[0]?t:\"/\".concat(t)),\"ui\"===e)return this.uiHost+t;if(this.region===kn.CUSTOM)return this.apiHost+t;var i=Cn+t;switch(e){case\"assets\":return\"https://\".concat(this.region,\"-assets.\").concat(i);case\"api\":return\"https://\".concat(this.region,\".\").concat(i)}}}var Fn=\"posthog-js\";function Rn(e){var{organization:t,projectId:i,prefix:r,severityAllowList:n=[\"error\"]}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return s=>{var o,a,l,u,c;if(!(\"*\"===n||n.includes(s.level))||!e.__loaded)return s;s.tags||(s.tags={});var d=e.requestRouter.endpointFor(\"ui\",\"/project/\".concat(e.config.token,\"/person/\").concat(e.get_distinct_id()));s.tags[\"PostHog Person URL\"]=d,e.sessionRecordingStarted()&&(s.tags[\"PostHog Recording URL\"]=e.get_session_replay_url({withTimestamp:!0}));var h=(null===(o=s.exception)||void 0===o?void 0:o.values)||[];h.map((e=>{e.stacktrace&&(e.stacktrace.type=\"raw\")}));var _={$exception_message:(null===(a=h[0])||void 0===a?void 0:a.value)||s.message,$exception_type:null===(l=h[0])||void 0===l?void 0:l.type,$exception_personURL:d,$exception_level:s.level,$exception_list:h,$sentry_event_id:s.event_id,$sentry_exception:s.exception,$sentry_exception_message:(null===(u=h[0])||void 0===u?void 0:u.value)||s.message,$sentry_exception_type:null===(c=h[0])||void 0===c?void 0:c.type,$sentry_tags:s.tags};return t&&i&&(_.$sentry_url=(r||\"https://sentry.io/organizations/\")+t+\"/issues/?project=\"+i+\"&query=\"+s.event_id),e.exceptions.sendExceptionEvent(_),s}}class Tn{constructor(e,t,i,r,n){this.name=Fn,this.setupOnce=function(s){s(Rn(e,{organization:t,projectId:i,prefix:r,severityAllowList:n}))}}}var $n=B(\"[SegmentIntegration]\");function On(e,t){var i=e.config.segment;if(!i)return t();!function(e,t){var i=e.config.segment;if(!i)return t();var r=i=>{var r=()=>i.anonymousId()||et();e.config.get_device_id=r,i.id()&&(e.register({distinct_id:i.id(),$device_id:r()}),e.persistence.set_property($e,\"identified\")),t()},n=i.user();\"then\"in n&&I(n.then)?n.then((e=>r(e))):r(n)}(e,(()=>{i.register((e=>{Promise&&Promise.resolve||$n.warn(\"This browser does not have Promise support, and can not use the segment integration\");var t=(t,i)=>{var r;if(!i)return t;t.event.userId||t.event.anonymousId===e.get_distinct_id()||($n.info(\"No userId set, resetting PostHog\"),e.reset()),t.event.userId&&t.event.userId!==e.get_distinct_id()&&($n.info(\"UserId set, identifying with PostHog\"),e.identify(t.event.userId));var n=e._calculate_event_properties(i,null!==(r=t.event.properties)&&void 0!==r?r:{},new Date);return t.event.properties=Object.assign({},n,t.event.properties),t};return{name:\"PostHog JS\",type:\"enrichment\",version:\"1.0.0\",isLoaded:()=>!0,load:()=>Promise.resolve(),track:e=>t(e,e.event.event),page:e=>t(e,\"$pageview\"),identify:e=>t(e,\"$identify\"),screen:e=>t(e,\"$screen\")}})(e)).then((()=>{t()}))}))}class Mn{constructor(e){this._instance=e}doPageView(e,i){var r,n=this._previousPageViewProperties(e,i);return this._currentPageview={pathname:null!==(r=null==t?void 0:t.location.pathname)&&void 0!==r?r:\"\",pageViewId:i,timestamp:e},this._instance.scrollManager.resetContext(),n}doPageLeave(e){var t;return this._previousPageViewProperties(e,null===(t=this._currentPageview)||void 0===t?void 0:t.pageViewId)}doEvent(){var e;return{$pageview_id:null===(e=this._currentPageview)||void 0===e?void 0:e.pageViewId}}_previousPageViewProperties(e,t){var i=this._currentPageview;if(!i)return{$pageview_id:t};var r={$pageview_id:t,$prev_pageview_id:i.pageViewId},n=this._instance.scrollManager.getContext();if(n&&!this._instance.config.disable_scroll_properties){var{maxScrollHeight:s,lastScrollY:o,maxScrollY:a,maxContentHeight:l,lastContentY:u,maxContentY:c}=n;if(!(F(s)||F(o)||F(a)||F(l)||F(u)||F(c))){s=Math.ceil(s),o=Math.ceil(o),a=Math.ceil(a),l=Math.ceil(l),u=Math.ceil(u),c=Math.ceil(c);var d=s<=1?1:dr(o/s,0,1),h=s<=1?1:dr(a/s,0,1),_=l<=1?1:dr(u/l,0,1),p=l<=1?1:dr(c/l,0,1);r=K(r,{$prev_pageview_last_scroll:o,$prev_pageview_last_scroll_percentage:d,$prev_pageview_max_scroll:a,$prev_pageview_max_scroll_percentage:h,$prev_pageview_last_content:u,$prev_pageview_last_content_percentage:_,$prev_pageview_max_content:c,$prev_pageview_max_content_percentage:p})}}return i.pathname&&(r.$prev_pageview_pathname=i.pathname),i.timestamp&&(r.$prev_pageview_duration=(e.getTime()-i.timestamp.getTime())/1e3),r}}var Ln,An,Dn,Nn,qn,Bn,Hn,Un,zn={},jn=[],Wn=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,Vn=Array.isArray;function Gn(e,t){for(var i in t)e[i]=t[i];return e}function Jn(e){var t=e.parentNode;t&&t.removeChild(e)}function Yn(e,t,i,r,n){var s={type:e,props:t,key:i,ref:r,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:null==n?++Dn:n,__i:-1,__u:0};return null==n&&null!=An.vnode&&An.vnode(s),s}function Kn(e){return e.children}function Xn(e,t){this.props=e,this.context=t}function Qn(e,t){if(null==t)return e.__?Qn(e.__,e.__i+1):null;for(var i;t<e.__k.length;t++)if(null!=(i=e.__k[t])&&null!=i.__e)return i.__e;return\"function\"==typeof e.type?Qn(e):null}function Zn(e){var t,i;if(null!=(e=e.__)&&null!=e.__c){for(e.__e=e.__c.base=null,t=0;t<e.__k.length;t++)if(null!=(i=e.__k[t])&&null!=i.__e){e.__e=e.__c.base=i.__e;break}return Zn(e)}}function es(e){(!e.__d&&(e.__d=!0)&&Nn.push(e)&&!ts.__r++||qn!==An.debounceRendering)&&((qn=An.debounceRendering)||Bn)(ts)}function ts(){var e,t,i,r,n,s,o,a,l;for(Nn.sort(Hn);e=Nn.shift();)e.__d&&(t=Nn.length,r=void 0,s=(n=(i=e).__v).__e,a=[],l=[],(o=i.__P)&&((r=Gn({},n)).__v=n.__v+1,An.vnode&&An.vnode(r),cs(o,r,n,i.__n,void 0!==o.ownerSVGElement,32&n.__u?[s]:null,a,null==s?Qn(n):s,!!(32&n.__u),l),r.__.__k[r.__i]=r,ds(a,r,l),r.__e!=s&&Zn(r)),Nn.length>t&&Nn.sort(Hn));ts.__r=0}function is(e,t,i,r,n,s,o,a,l,u,c){var d,h,_,p,v,g=r&&r.__k||jn,f=t.length;for(i.__d=l,rs(i,t,g),l=i.__d,d=0;d<f;d++)null!=(_=i.__k[d])&&\"boolean\"!=typeof _&&\"function\"!=typeof _&&(h=-1===_.__i?zn:g[_.__i]||zn,_.__i=d,cs(e,_,h,n,s,o,a,l,u,c),p=_.__e,_.ref&&h.ref!=_.ref&&(h.ref&&_s(h.ref,null,_),c.push(_.ref,_.__c||p,_)),null==v&&null!=p&&(v=p),65536&_.__u||h.__k===_.__k?l=ns(_,l,e):\"function\"==typeof _.type&&void 0!==_.__d?l=_.__d:p&&(l=p.nextSibling),_.__d=void 0,_.__u&=-196609);i.__d=l,i.__e=v}function rs(e,t,i){var r,n,s,o,a,l=t.length,u=i.length,c=u,d=0;for(e.__k=[],r=0;r<l;r++)null!=(n=e.__k[r]=null==(n=t[r])||\"boolean\"==typeof n||\"function\"==typeof n?null:\"string\"==typeof n||\"number\"==typeof n||\"bigint\"==typeof n||n.constructor==String?Yn(null,n,null,null,n):Vn(n)?Yn(Kn,{children:n},null,null,null):void 0===n.constructor&&n.__b>0?Yn(n.type,n.props,n.key,n.ref?n.ref:null,n.__v):n)?(n.__=e,n.__b=e.__b+1,a=ss(n,i,o=r+d,c),n.__i=a,s=null,-1!==a&&(c--,(s=i[a])&&(s.__u|=131072)),null==s||null===s.__v?(-1==a&&d--,\"function\"!=typeof n.type&&(n.__u|=65536)):a!==o&&(a===o+1?d++:a>o?c>l-o?d+=a-o:d--:d=a<o&&a==o-1?a-o:0,a!==r+d&&(n.__u|=65536))):(s=i[r])&&null==s.key&&s.__e&&(s.__e==e.__d&&(e.__d=Qn(s)),ps(s,s,!1),i[r]=null,c--);if(c)for(r=0;r<u;r++)null!=(s=i[r])&&0==(131072&s.__u)&&(s.__e==e.__d&&(e.__d=Qn(s)),ps(s,s))}function ns(e,t,i){var r,n;if(\"function\"==typeof e.type){for(r=e.__k,n=0;r&&n<r.length;n++)r[n]&&(r[n].__=e,t=ns(r[n],t,i));return t}return e.__e!=t&&(i.insertBefore(e.__e,t||null),t=e.__e),t&&t.nextSibling}function ss(e,t,i,r){var n=e.key,s=e.type,o=i-1,a=i+1,l=t[i];if(null===l||l&&n==l.key&&s===l.type)return i;if(r>(null!=l&&0==(131072&l.__u)?1:0))for(;o>=0||a<t.length;){if(o>=0){if((l=t[o])&&0==(131072&l.__u)&&n==l.key&&s===l.type)return o;o--}if(a<t.length){if((l=t[a])&&0==(131072&l.__u)&&n==l.key&&s===l.type)return a;a++}}return-1}function os(e,t,i){\"-\"===t[0]?e.setProperty(t,null==i?\"\":i):e[t]=null==i?\"\":\"number\"!=typeof i||Wn.test(t)?i:i+\"px\"}function as(e,t,i,r,n){var s;e:if(\"style\"===t)if(\"string\"==typeof i)e.style.cssText=i;else{if(\"string\"==typeof r&&(e.style.cssText=r=\"\"),r)for(t in r)i&&t in i||os(e.style,t,\"\");if(i)for(t in i)r&&i[t]===r[t]||os(e.style,t,i[t])}else if(\"o\"===t[0]&&\"n\"===t[1])s=t!==(t=t.replace(/(PointerCapture)$|Capture$/,\"$1\")),t=t.toLowerCase()in e?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+s]=i,i?r?i.u=r.u:(i.u=Date.now(),e.addEventListener(t,s?us:ls,s)):e.removeEventListener(t,s?us:ls,s);else{if(n)t=t.replace(/xlink(H|:h)/,\"h\").replace(/sName$/,\"s\");else if(\"width\"!==t&&\"height\"!==t&&\"href\"!==t&&\"list\"!==t&&\"form\"!==t&&\"tabIndex\"!==t&&\"download\"!==t&&\"rowSpan\"!==t&&\"colSpan\"!==t&&\"role\"!==t&&t in e)try{e[t]=null==i?\"\":i;break e}catch(e){}\"function\"==typeof i||(null==i||!1===i&&\"-\"!==t[4]?e.removeAttribute(t):e.setAttribute(t,i))}}function ls(e){var t=this.l[e.type+!1];if(e.t){if(e.t<=t.u)return}else e.t=Date.now();return t(An.event?An.event(e):e)}function us(e){return this.l[e.type+!0](An.event?An.event(e):e)}function cs(e,t,i,r,n,s,o,a,l,u){var c,d,h,_,p,v,g,f,m,b,y,w,S,E,k,x=t.type;if(void 0!==t.constructor)return null;128&i.__u&&(l=!!(32&i.__u),s=[a=t.__e=i.__e]),(c=An.__b)&&c(t);e:if(\"function\"==typeof x)try{if(f=t.props,m=(c=x.contextType)&&r[c.__c],b=c?m?m.props.value:c.__:r,i.__c?g=(d=t.__c=i.__c).__=d.__E:(\"prototype\"in x&&x.prototype.render?t.__c=d=new x(f,b):(t.__c=d=new Xn(f,b),d.constructor=x,d.render=vs),m&&m.sub(d),d.props=f,d.state||(d.state={}),d.context=b,d.__n=r,h=d.__d=!0,d.__h=[],d._sb=[]),null==d.__s&&(d.__s=d.state),null!=x.getDerivedStateFromProps&&(d.__s==d.state&&(d.__s=Gn({},d.__s)),Gn(d.__s,x.getDerivedStateFromProps(f,d.__s))),_=d.props,p=d.state,d.__v=t,h)null==x.getDerivedStateFromProps&&null!=d.componentWillMount&&d.componentWillMount(),null!=d.componentDidMount&&d.__h.push(d.componentDidMount);else{if(null==x.getDerivedStateFromProps&&f!==_&&null!=d.componentWillReceiveProps&&d.componentWillReceiveProps(f,b),!d.__e&&(null!=d.shouldComponentUpdate&&!1===d.shouldComponentUpdate(f,d.__s,b)||t.__v===i.__v)){for(t.__v!==i.__v&&(d.props=f,d.state=d.__s,d.__d=!1),t.__e=i.__e,t.__k=i.__k,t.__k.forEach((function(e){e&&(e.__=t)})),y=0;y<d._sb.length;y++)d.__h.push(d._sb[y]);d._sb=[],d.__h.length&&o.push(d);break e}null!=d.componentWillUpdate&&d.componentWillUpdate(f,d.__s,b),null!=d.componentDidUpdate&&d.__h.push((function(){d.componentDidUpdate(_,p,v)}))}if(d.context=b,d.props=f,d.__P=e,d.__e=!1,w=An.__r,S=0,\"prototype\"in x&&x.prototype.render){for(d.state=d.__s,d.__d=!1,w&&w(t),c=d.render(d.props,d.state,d.context),E=0;E<d._sb.length;E++)d.__h.push(d._sb[E]);d._sb=[]}else do{d.__d=!1,w&&w(t),c=d.render(d.props,d.state,d.context),d.state=d.__s}while(d.__d&&++S<25);d.state=d.__s,null!=d.getChildContext&&(r=Gn(Gn({},r),d.getChildContext())),h||null==d.getSnapshotBeforeUpdate||(v=d.getSnapshotBeforeUpdate(_,p)),is(e,Vn(k=null!=c&&c.type===Kn&&null==c.key?c.props.children:c)?k:[k],t,i,r,n,s,o,a,l,u),d.base=t.__e,t.__u&=-161,d.__h.length&&o.push(d),g&&(d.__E=d.__=null)}catch(e){t.__v=null,l||null!=s?(t.__e=a,t.__u|=l?160:32,s[s.indexOf(a)]=null):(t.__e=i.__e,t.__k=i.__k),An.__e(e,t,i)}else null==s&&t.__v===i.__v?(t.__k=i.__k,t.__e=i.__e):t.__e=hs(i.__e,t,i,r,n,s,o,l,u);(c=An.diffed)&&c(t)}function ds(e,t,i){t.__d=void 0;for(var r=0;r<i.length;r++)_s(i[r],i[++r],i[++r]);An.__c&&An.__c(t,e),e.some((function(t){try{e=t.__h,t.__h=[],e.some((function(e){e.call(t)}))}catch(e){An.__e(e,t.__v)}}))}function hs(e,t,i,r,n,s,o,a,l){var u,c,d,h,_,p,v,g=i.props,f=t.props,m=t.type;if(\"svg\"===m&&(n=!0),null!=s)for(u=0;u<s.length;u++)if((_=s[u])&&\"setAttribute\"in _==!!m&&(m?_.localName===m:3===_.nodeType)){e=_,s[u]=null;break}if(null==e){if(null===m)return document.createTextNode(f);e=n?document.createElementNS(\"http://www.w3.org/2000/svg\",m):document.createElement(m,f.is&&f),s=null,a=!1}if(null===m)g===f||a&&e.data===f||(e.data=f);else{if(s=s&&Ln.call(e.childNodes),g=i.props||zn,!a&&null!=s)for(g={},u=0;u<e.attributes.length;u++)g[(_=e.attributes[u]).name]=_.value;for(u in g)_=g[u],\"children\"==u||(\"dangerouslySetInnerHTML\"==u?d=_:\"key\"===u||u in f||as(e,u,null,_,n));for(u in f)_=f[u],\"children\"==u?h=_:\"dangerouslySetInnerHTML\"==u?c=_:\"value\"==u?p=_:\"checked\"==u?v=_:\"key\"===u||a&&\"function\"!=typeof _||g[u]===_||as(e,u,_,g[u],n);if(c)a||d&&(c.__html===d.__html||c.__html===e.innerHTML)||(e.innerHTML=c.__html),t.__k=[];else if(d&&(e.innerHTML=\"\"),is(e,Vn(h)?h:[h],t,i,r,n&&\"foreignObject\"!==m,s,o,s?s[0]:i.__k&&Qn(i,0),a,l),null!=s)for(u=s.length;u--;)null!=s[u]&&Jn(s[u]);a||(u=\"value\",void 0!==p&&(p!==e[u]||\"progress\"===m&&!p||\"option\"===m&&p!==g[u])&&as(e,u,p,g[u],!1),u=\"checked\",void 0!==v&&v!==e[u]&&as(e,u,v,g[u],!1))}return e}function _s(e,t,i){try{\"function\"==typeof e?e(t):e.current=t}catch(e){An.__e(e,i)}}function ps(e,t,i){var r,n;if(An.unmount&&An.unmount(e),(r=e.ref)&&(r.current&&r.current!==e.__e||_s(r,null,t)),null!=(r=e.__c)){if(r.componentWillUnmount)try{r.componentWillUnmount()}catch(e){An.__e(e,t)}r.base=r.__P=null,e.__c=void 0}if(r=e.__k)for(n=0;n<r.length;n++)r[n]&&ps(r[n],t,i||\"function\"!=typeof e.type);i||null==e.__e||Jn(e.__e),e.__=e.__e=e.__d=void 0}function vs(e,t,i){return this.constructor(e,i)}Ln=jn.slice,An={__e:function(e,t,i,r){for(var n,s,o;t=t.__;)if((n=t.__c)&&!n.__)try{if((s=n.constructor)&&null!=s.getDerivedStateFromError&&(n.setState(s.getDerivedStateFromError(e)),o=n.__d),null!=n.componentDidCatch&&(n.componentDidCatch(e,r||{}),o=n.__d),o)return n.__E=n}catch(t){e=t}throw e}},Dn=0,Xn.prototype.setState=function(e,t){var i;i=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=Gn({},this.state),\"function\"==typeof e&&(e=e(Gn({},i),this.props)),e&&Gn(i,e),null!=e&&this.__v&&(t&&this._sb.push(t),es(this))},Xn.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),es(this))},Xn.prototype.render=Kn,Nn=[],Bn=\"function\"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,Hn=function(e,t){return e.__v.__b-t.__v.__b},ts.__r=0,Un=0;var gs,fs,ms;!function(e,t){var i={__c:t=\"__cC\"+Un++,__:e,Consumer:function(e,t){return e.children(t)},Provider:function(e){var i,r;return this.getChildContext||(i=[],(r={})[t]=this,this.getChildContext=function(){return r},this.shouldComponentUpdate=function(e){this.props.value!==e.value&&i.some((function(e){e.__e=!0,es(e)}))},this.sub=function(e){i.push(e);var t=e.componentWillUnmount;e.componentWillUnmount=function(){i.splice(i.indexOf(e),1),t&&t.call(e)}}),e.children}};i.Provider.__=i.Consumer.contextType=i}({isPreviewMode:!1,previewPageIndex:0,handleCloseSurveyPopup:()=>{},isPopup:!0,onPreviewSubmit:()=>{}}),function(e){e.Popover=\"popover\",e.API=\"api\",e.Widget=\"widget\"}(gs||(gs={})),function(e){e.Open=\"open\",e.MultipleChoice=\"multiple_choice\",e.SingleChoice=\"single_choice\",e.Rating=\"rating\",e.Link=\"link\"}(fs||(fs={})),function(e){e.NextQuestion=\"next_question\",e.End=\"end\",e.ResponseBased=\"response_based\",e.SpecificQuestion=\"specific_question\"}(ms||(ms={}));class bs{constructor(){W(this,\"events\",{}),this.events={}}on(e,t){return this.events[e]||(this.events[e]=[]),this.events[e].push(t),()=>{this.events[e]=this.events[e].filter((e=>e!==t))}}emit(e,t){for(var i of this.events[e]||[])i(t);for(var r of this.events[\"*\"]||[])r(e,t)}}class ys{constructor(e){W(this,\"_debugEventEmitter\",new bs),W(this,\"checkStep\",((e,t)=>this.checkStepEvent(e,t)&&this.checkStepUrl(e,t)&&this.checkStepElement(e,t))),W(this,\"checkStepEvent\",((e,t)=>null==t||!t.event||(null==e?void 0:e.event)===(null==t?void 0:t.event))),this.instance=e,this.actionEvents=new Set,this.actionRegistry=new Set}init(){var e;if(!F(null===(e=this.instance)||void 0===e?void 0:e._addCaptureHook)){var t;null===(t=this.instance)||void 0===t||t._addCaptureHook(((e,t)=>{this.on(e,t)}))}}register(e){var t,i;if(!F(null===(t=this.instance)||void 0===t?void 0:t._addCaptureHook)&&(e.forEach((e=>{var t,i;null===(t=this.actionRegistry)||void 0===t||t.add(e),null===(i=e.steps)||void 0===i||i.forEach((e=>{var t;null===(t=this.actionEvents)||void 0===t||t.add((null==e?void 0:e.event)||\"\")}))})),null!==(i=this.instance)&&void 0!==i&&i.autocapture)){var r,n=new Set;e.forEach((e=>{var t;null===(t=e.steps)||void 0===t||t.forEach((e=>{null!=e&&e.selector&&n.add(null==e?void 0:e.selector)}))})),null===(r=this.instance)||void 0===r||r.autocapture.setElementSelectors(n)}}on(e,t){var i;null!=t&&0!=e.length&&(this.actionEvents.has(e)||this.actionEvents.has(null==t?void 0:t.event))&&this.actionRegistry&&(null===(i=this.actionRegistry)||void 0===i?void 0:i.size)>0&&this.actionRegistry.forEach((e=>{this.checkAction(t,e)&&this._debugEventEmitter.emit(\"actionCaptured\",e.name)}))}_addActionHook(e){this.onAction(\"actionCaptured\",(t=>e(t)))}checkAction(e,t){if(null==(null==t?void 0:t.steps))return!1;for(var i of t.steps)if(this.checkStep(e,i))return!0;return!1}onAction(e,t){return this._debugEventEmitter.on(e,t)}checkStepUrl(e,t){if(null!=t&&t.url){var i,r=null==e||null===(i=e.properties)||void 0===i?void 0:i.$current_url;if(!r||\"string\"!=typeof r)return!1;if(!ys.matchString(r,null==t?void 0:t.url,(null==t?void 0:t.url_matching)||\"contains\"))return!1}return!0}static matchString(e,i,r){switch(r){case\"regex\":return!!t&&ft(e,i);case\"exact\":return i===e;case\"contains\":var n=ys.escapeStringRegexp(i).replace(/_/g,\".\").replace(/%/g,\".*\");return ft(e,n);default:return!1}}static escapeStringRegexp(e){return e.replace(/[|\\\\{}()[\\]^$+*?.]/g,\"\\\\$&\").replace(/-/g,\"\\\\x2d\")}checkStepElement(e,t){if((null!=t&&t.href||null!=t&&t.tag_name||null!=t&&t.text)&&!this.getElementsList(e).some((e=>!(null!=t&&t.href&&!ys.matchString(e.href||\"\",null==t?void 0:t.href,(null==t?void 0:t.href_matching)||\"exact\"))&&((null==t||!t.tag_name||e.tag_name===(null==t?void 0:t.tag_name))&&!(null!=t&&t.text&&!ys.matchString(e.text||\"\",null==t?void 0:t.text,(null==t?void 0:t.text_matching)||\"exact\")&&!ys.matchString(e.$el_text||\"\",null==t?void 0:t.text,(null==t?void 0:t.text_matching)||\"exact\"))))))return!1;if(null!=t&&t.selector){var i,r=null==e||null===(i=e.properties)||void 0===i?void 0:i.$element_selectors;if(!r)return!1;if(!r.includes(null==t?void 0:t.selector))return!1}return!0}getElementsList(e){return null==(null==e?void 0:e.properties.$elements)?[]:null==e?void 0:e.properties.$elements}}class ws{constructor(e){this.instance=e,this.eventToSurveys=new Map,this.actionToSurveys=new Map}register(e){var t;F(null===(t=this.instance)||void 0===t?void 0:t._addCaptureHook)||(this.setupEventBasedSurveys(e),this.setupActionBasedSurveys(e))}setupActionBasedSurveys(e){var t=e.filter((e=>{var t,i,r,n;return(null===(t=e.conditions)||void 0===t?void 0:t.actions)&&(null===(i=e.conditions)||void 0===i||null===(r=i.actions)||void 0===r||null===(n=r.values)||void 0===n?void 0:n.length)>0}));if(0!==t.length){if(null==this.actionMatcher){this.actionMatcher=new ys(this.instance),this.actionMatcher.init();this.actionMatcher._addActionHook((e=>{this.onAction(e)}))}t.forEach((e=>{var t,i,r,n,s,o,a,l,u,c;e.conditions&&null!==(t=e.conditions)&&void 0!==t&&t.actions&&null!==(i=e.conditions)&&void 0!==i&&null!==(r=i.actions)&&void 0!==r&&r.values&&(null===(n=e.conditions)||void 0===n||null===(s=n.actions)||void 0===s||null===(o=s.values)||void 0===o?void 0:o.length)>0&&(null===(a=this.actionMatcher)||void 0===a||a.register(e.conditions.actions.values),null===(l=e.conditions)||void 0===l||null===(u=l.actions)||void 0===u||null===(c=u.values)||void 0===c||c.forEach((t=>{if(t&&t.name){var i=this.actionToSurveys.get(t.name);i&&i.push(e.id),this.actionToSurveys.set(t.name,i||[e.id])}})))}))}}setupEventBasedSurveys(e){var t;if(0!==e.filter((e=>{var t,i,r,n;return(null===(t=e.conditions)||void 0===t?void 0:t.events)&&(null===(i=e.conditions)||void 0===i||null===(r=i.events)||void 0===r||null===(n=r.values)||void 0===n?void 0:n.length)>0})).length){null===(t=this.instance)||void 0===t||t._addCaptureHook(((e,t)=>{this.onEvent(e,t)})),e.forEach((e=>{var t,i,r;null===(t=e.conditions)||void 0===t||null===(i=t.events)||void 0===i||null===(r=i.values)||void 0===r||r.forEach((t=>{if(t&&t.name){var i=this.eventToSurveys.get(t.name);i&&i.push(e.id),this.eventToSurveys.set(t.name,i||[e.id])}}))}))}}onEvent(e,t){var i,r,n=(null===(i=this.instance)||void 0===i||null===(r=i.persistence)||void 0===r?void 0:r.props[Re])||[];if(ws.SURVEY_SHOWN_EVENT_NAME==e&&t&&n.length>0){var s,o=null==t||null===(s=t.properties)||void 0===s?void 0:s.$survey_id;if(o){var a=n.indexOf(o);a>=0&&(n.splice(a,1),this._updateActivatedSurveys(n))}}else this.eventToSurveys.has(e)&&this._updateActivatedSurveys(n.concat(this.eventToSurveys.get(e)||[]))}onAction(e){var t,i,r=(null===(t=this.instance)||void 0===t||null===(i=t.persistence)||void 0===i?void 0:i.props[Re])||[];this.actionToSurveys.has(e)&&this._updateActivatedSurveys(r.concat(this.actionToSurveys.get(e)||[]))}_updateActivatedSurveys(e){var t,i;null===(t=this.instance)||void 0===t||null===(i=t.persistence)||void 0===i||i.register({[Re]:[...new Set(e)]})}getSurveys(){var e,t,i=null===(e=this.instance)||void 0===e||null===(t=e.persistence)||void 0===t?void 0:t.props[Re];return i||[]}getEventToSurveys(){return this.eventToSurveys}_getActionMatcher(){return this.actionMatcher}}W(ws,\"SURVEY_SHOWN_EVENT_NAME\",\"survey shown\");var Ss=B(\"[Surveys]\"),Es={icontains:e=>!!t&&t.location.href.toLowerCase().indexOf(e.toLowerCase())>-1,not_icontains:e=>!!t&&-1===t.location.href.toLowerCase().indexOf(e.toLowerCase()),regex:e=>!!t&&ft(t.location.href,e),not_regex:e=>!!t&&!ft(t.location.href,e),exact:e=>(null==t?void 0:t.location.href)===e,is_not:e=>(null==t?void 0:t.location.href)!==e};function ks(e,t,i){var r,n=e.questions[t],s=t+1;if(null===(r=n.branching)||void 0===r||!r.type)return t===e.questions.length-1?ms.End:s;if(n.branching.type===ms.End)return ms.End;if(n.branching.type===ms.SpecificQuestion){if(Number.isInteger(n.branching.index))return n.branching.index}else if(n.branching.type===ms.ResponseBased){if(n.type===fs.SingleChoice){var o,a,l=n.choices.indexOf(\"\".concat(i));if(null!==(o=n.branching)&&void 0!==o&&null!==(a=o.responseValues)&&void 0!==a&&a.hasOwnProperty(l)){var u=n.branching.responseValues[l];return Number.isInteger(u)?u:u===ms.End?ms.End:s}}else if(n.type===fs.Rating){var c,d;if(\"number\"!=typeof i||!Number.isInteger(i))throw new Error(\"The response type must be an integer\");var h=function(e,t){if(3===t){if(e<1||e>3)throw new Error(\"The response must be in range 1-3\");return 1===e?\"negative\":2===e?\"neutral\":\"positive\"}if(5===t){if(e<1||e>5)throw new Error(\"The response must be in range 1-5\");return e<=2?\"negative\":3===e?\"neutral\":\"positive\"}if(7===t){if(e<1||e>7)throw new Error(\"The response must be in range 1-7\");return e<=3?\"negative\":4===e?\"neutral\":\"positive\"}if(10===t){if(e<0||e>10)throw new Error(\"The response must be in range 0-10\");return e<=6?\"detractors\":e<=8?\"passives\":\"promoters\"}throw new Error(\"The scale must be one of: 3, 5, 7, 10\")}(i,n.scale);if(null!==(c=n.branching)&&void 0!==c&&null!==(d=c.responseValues)&&void 0!==d&&d.hasOwnProperty(h)){var _=n.branching.responseValues[h];return Number.isInteger(_)?_:_===ms.End?ms.End:s}}return s}return Ss.warn(\"Falling back to next question index due to unexpected branching type\"),s}class xs{constructor(e){W(this,\"getNextSurveyStep\",ks),this.instance=e,this._surveyEventReceiver=null}onRemoteConfig(e){this._decideServerResponse=!!e.surveys,this.loadIfEnabled()}reset(){localStorage.removeItem(\"lastSeenSurveyDate\");var e=(()=>{for(var e=[],t=0;t<localStorage.length;t++){var i=localStorage.key(t);null!=i&&i.startsWith(\"seenSurvey_\")&&e.push(i)}return e})();e.forEach((e=>localStorage.removeItem(e)))}loadIfEnabled(){var e,t,i,r=null==_||null===(e=_.__PosthogExtensions__)||void 0===e?void 0:e.generateSurveys;this.instance.config.disable_surveys||!this._decideServerResponse||r||(null==this._surveyEventReceiver&&(this._surveyEventReceiver=new ws(this.instance)),null===(t=_.__PosthogExtensions__)||void 0===t||null===(i=t.loadExternalDependency)||void 0===i||i.call(t,this.instance,\"surveys\",(e=>{var t,i;if(e)return Ss.error(\"Could not load surveys script\",e);this._surveyManager=null===(t=_.__PosthogExtensions__)||void 0===t||null===(i=t.generateSurveys)||void 0===i?void 0:i.call(t,this.instance)})))}getSurveys(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(this.instance.config.disable_surveys)return e([]);null==this._surveyEventReceiver&&(this._surveyEventReceiver=new ws(this.instance));var i=this.instance.get_property(Fe);if(i&&!t)return e(i);this.instance._send_request({url:this.instance.requestRouter.endpointFor(\"api\",\"/api/surveys/?token=\".concat(this.instance.config.token)),method:\"GET\",callback:t=>{var i;if(200!==t.statusCode||!t.json)return e([]);var r,n=t.json.surveys||[],s=n.filter((e=>{var t,i,r,n,s,o,a,l,u,c,d,h;return(null===(t=e.conditions)||void 0===t?void 0:t.events)&&(null===(i=e.conditions)||void 0===i||null===(r=i.events)||void 0===r?void 0:r.values)&&(null===(n=e.conditions)||void 0===n||null===(s=n.events)||void 0===s||null===(o=s.values)||void 0===o?void 0:o.length)>0||(null===(a=e.conditions)||void 0===a?void 0:a.actions)&&(null===(l=e.conditions)||void 0===l||null===(u=l.actions)||void 0===u?void 0:u.values)&&(null===(c=e.conditions)||void 0===c||null===(d=c.actions)||void 0===d||null===(h=d.values)||void 0===h?void 0:h.length)>0}));s.length>0&&(null===(r=this._surveyEventReceiver)||void 0===r||r.register(s));return null===(i=this.instance.persistence)||void 0===i||i.register({[Fe]:n}),e(n)}})}getActiveMatchingSurveys(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];this.getSurveys((t=>{var i,r=t.filter((e=>!(!e.start_date||e.end_date))).filter((e=>{var t,i,r,n;if(!e.conditions)return!0;var s=null===(t=e.conditions)||void 0===t||!t.url||Es[null!==(i=null===(r=e.conditions)||void 0===r?void 0:r.urlMatchType)&&void 0!==i?i:\"icontains\"](e.conditions.url),o=null===(n=e.conditions)||void 0===n||!n.selector||(null==a?void 0:a.querySelector(e.conditions.selector));return s&&o})),n=null===(i=this._surveyEventReceiver)||void 0===i?void 0:i.getSurveys(),s=r.filter((e=>{var t,i,r,s,o,a,l,u,c,d,h;if(!(e.linked_flag_key||e.targeting_flag_key||e.internal_targeting_flag_key||null!==(t=e.feature_flag_keys)&&void 0!==t&&t.length))return!0;var _=!e.linked_flag_key||this.instance.featureFlags.isFeatureEnabled(e.linked_flag_key),p=!e.targeting_flag_key||this.instance.featureFlags.isFeatureEnabled(e.targeting_flag_key),v=(null===(i=e.conditions)||void 0===i?void 0:i.events)&&(null===(r=e.conditions)||void 0===r||null===(s=r.events)||void 0===s?void 0:s.values)&&(null===(o=e.conditions)||void 0===o||null===(a=o.events)||void 0===a?void 0:a.values.length)>0,g=(null===(l=e.conditions)||void 0===l?void 0:l.actions)&&(null===(u=e.conditions)||void 0===u||null===(c=u.actions)||void 0===c?void 0:c.values)&&(null===(d=e.conditions)||void 0===d||null===(h=d.actions)||void 0===h?void 0:h.values.length)>0,f=!v&&!g||(null==n?void 0:n.includes(e.id)),m=this._canActivateRepeatedly(e),b=!(e.internal_targeting_flag_key&&!m)||this.instance.featureFlags.isFeatureEnabled(e.internal_targeting_flag_key),y=this.checkFlags(e);return _&&p&&b&&f&&y}));return e(s)}),t)}checkFlags(e){var t;return null===(t=e.feature_flag_keys)||void 0===t||!t.length||e.feature_flag_keys.every((e=>{var{key:t,value:i}=e;return!t||!i||this.instance.featureFlags.isFeatureEnabled(i)}))}_canActivateRepeatedly(e){var t;return O(null===(t=_.__PosthogExtensions__)||void 0===t?void 0:t.canActivateRepeatedly)?(Ss.warn(\"init was not called\"),!1):_.__PosthogExtensions__.canActivateRepeatedly(e)}canRenderSurvey(e){O(this._surveyManager)?Ss.warn(\"init was not called\"):this.getSurveys((t=>{var i=t.filter((t=>t.id===e))[0];this._surveyManager.canRenderSurvey(i)}))}renderSurvey(e,t){O(this._surveyManager)?Ss.warn(\"init was not called\"):this.getSurveys((i=>{var r=i.filter((t=>t.id===e))[0];this._surveyManager.renderSurvey(r,null==a?void 0:a.querySelector(t))}))}}var Is=B(\"[RateLimiter]\");class Cs{constructor(e){var t,i;W(this,\"serverLimits\",{}),W(this,\"lastEventRateLimited\",!1),W(this,\"checkForLimiting\",(e=>{var t=e.text;if(t&&t.length)try{(JSON.parse(t).quota_limited||[]).forEach((e=>{Is.info(\"\".concat(e||\"events\",\" is quota limited.\")),this.serverLimits[e]=(new Date).getTime()+6e4}))}catch(e){return void Is.warn('could not rate limit - continuing. Error: \"'.concat(null==e?void 0:e.message,'\"'),{text:t})}})),this.instance=e,this.captureEventsPerSecond=(null===(t=e.config.rate_limiting)||void 0===t?void 0:t.events_per_second)||10,this.captureEventsBurstLimit=Math.max((null===(i=e.config.rate_limiting)||void 0===i?void 0:i.events_burst_limit)||10*this.captureEventsPerSecond,this.captureEventsPerSecond),this.lastEventRateLimited=this.clientRateLimitContext(!0).isRateLimited}clientRateLimitContext(){var e,t,i,r=arguments.length>0&&void 0!==arguments[0]&&arguments[0],n=(new Date).getTime(),s=null!==(e=null===(t=this.instance.persistence)||void 0===t?void 0:t.get_property(Me))&&void 0!==e?e:{tokens:this.captureEventsBurstLimit,last:n};s.tokens+=(n-s.last)/1e3*this.captureEventsPerSecond,s.last=n,s.tokens>this.captureEventsBurstLimit&&(s.tokens=this.captureEventsBurstLimit);var o=s.tokens<1;return o||r||(s.tokens=Math.max(0,s.tokens-1)),!o||this.lastEventRateLimited||r||this.instance.capture(\"$$client_ingestion_warning\",{$$client_ingestion_warning_message:\"posthog-js client rate limited. Config is set to \".concat(this.captureEventsPerSecond,\" events per second and \").concat(this.captureEventsBurstLimit,\" events burst limit.\")},{skip_client_rate_limiting:!0}),this.lastEventRateLimited=o,null===(i=this.instance.persistence)||void 0===i||i.set_property(Me,s),{isRateLimited:o,remainingTokens:s.tokens}}isServerRateLimited(e){var t=this.serverLimits[e||\"events\"]||!1;return!1!==t&&(new Date).getTime()<t}}var Ps=e=>{var t=null==e?void 0:e.config;return j({initialPathName:(null==l?void 0:l.pathname)||\"\",referringDomain:wi.referringDomain()},wi.campaignParams({customTrackedParams:null==t?void 0:t.custom_campaign_params,maskPersonalDataProperties:null==t?void 0:t.mask_personal_data_properties,customPersonalDataProperties:null==t?void 0:t.custom_personal_data_properties}))};class Fs{constructor(e,t,i,r){W(this,\"_onSessionIdCallback\",(e=>{var t=this._getStoredProps();if(!t||t.sessionId!==e){var i={sessionId:e,props:this._sessionSourceParamGenerator(this.instance)};this._persistence.register({[Oe]:i})}})),this.instance=e,this._sessionIdManager=t,this._persistence=i,this._sessionSourceParamGenerator=r||Ps,this._sessionIdManager.onSessionId(this._onSessionIdCallback)}_getStoredProps(){return this._persistence.props[Oe]}getSessionProps(){var e,t=null===(e=this._getStoredProps())||void 0===e?void 0:e.props;return t?{$client_session_initial_referring_host:t.referringDomain,$client_session_initial_pathname:t.initialPathName,$client_session_initial_utm_source:t.utm_source,$client_session_initial_utm_campaign:t.utm_campaign,$client_session_initial_utm_medium:t.utm_medium,$client_session_initial_utm_content:t.utm_content,$client_session_initial_utm_term:t.utm_term}:{}}}var Rs=[\"ahrefsbot\",\"ahrefssiteaudit\",\"applebot\",\"baiduspider\",\"better uptime bot\",\"bingbot\",\"bingpreview\",\"bot.htm\",\"bot.php\",\"crawler\",\"deepscan\",\"duckduckbot\",\"facebookexternal\",\"facebookcatalog\",\"gptbot\",\"http://yandex.com/bots\",\"hubspot\",\"ia_archiver\",\"linkedinbot\",\"mj12bot\",\"msnbot\",\"nessus\",\"petalbot\",\"pinterest\",\"prerender\",\"rogerbot\",\"screaming frog\",\"semrushbot\",\"sitebulb\",\"slurp\",\"turnitin\",\"twitterbot\",\"vercelbot\",\"yahoo! slurp\",\"yandexbot\",\"headlesschrome\",\"cypress\",\"Google-HotelAdsVerifier\",\"adsbot-google\",\"apis-google\",\"duplexweb-google\",\"feedfetcher-google\",\"google favicon\",\"google web preview\",\"google-read-aloud\",\"googlebot\",\"googleweblight\",\"mediapartners-google\",\"storebot-google\",\"Bytespider;\"],Ts=function(e,t){if(!e)return!1;var i=e.toLowerCase();return Rs.concat(t||[]).some((e=>{var t=e.toLowerCase();return-1!==i.indexOf(t)}))},$s=function(e,t){if(!e)return!1;var i=e.userAgent;if(i&&Ts(i,t))return!0;try{var r=null==e?void 0:e.userAgentData;if(null!=r&&r.brands&&r.brands.some((e=>Ts(null==e?void 0:e.brand,t))))return!0}catch(e){}return!!e.webdriver};class Os{constructor(){this.clicks=[]}isRageClick(e,t,i){var r=this.clicks[this.clicks.length-1];if(r&&Math.abs(e-r.x)+Math.abs(t-r.y)<30&&i-r.timestamp<1e3){if(this.clicks.push({x:e,y:t,timestamp:i}),3===this.clicks.length)return!0}else this.clicks=[{x:e,y:t,timestamp:i}];return!1}}var Ms=B(\"[Dead Clicks]\"),Ls=()=>!0,As=e=>{var t,i=!(null===(t=e.instance.persistence)||void 0===t||!t.get_property(he)),r=e.instance.config.capture_dead_clicks;return L(r)?r:i};class Ds{get lazyLoadedDeadClicksAutocapture(){return this._lazyLoadedDeadClicksAutocapture}constructor(e,t,i){this.instance=e,this.isEnabled=t,this.onCapture=i,this.startIfEnabled()}onRemoteConfig(e){this.instance.persistence&&this.instance.persistence.register({[he]:null==e?void 0:e.captureDeadClicks}),this.startIfEnabled()}startIfEnabled(){this.isEnabled(this)&&this.loadScript((()=>{this.start()}))}loadScript(e){var t,i,r;null!==(t=_.__PosthogExtensions__)&&void 0!==t&&t.initDeadClicksAutocapture&&e(),null===(i=_.__PosthogExtensions__)||void 0===i||null===(r=i.loadExternalDependency)||void 0===r||r.call(i,this.instance,\"dead-clicks-autocapture\",(t=>{t?Ms.error(\"failed to load script\",t):e()}))}start(){var e;if(a){if(!this._lazyLoadedDeadClicksAutocapture&&null!==(e=_.__PosthogExtensions__)&&void 0!==e&&e.initDeadClicksAutocapture){var t=C(this.instance.config.capture_dead_clicks)?this.instance.config.capture_dead_clicks:{};t.__onCapture=this.onCapture,this._lazyLoadedDeadClicksAutocapture=_.__PosthogExtensions__.initDeadClicksAutocapture(this.instance,t),this._lazyLoadedDeadClicksAutocapture.start(a),Ms.info(\"starting...\")}}else Ms.error(\"`document` not found. Cannot start.\")}stop(){this._lazyLoadedDeadClicksAutocapture&&(this._lazyLoadedDeadClicksAutocapture.stop(),this._lazyLoadedDeadClicksAutocapture=void 0,Ms.info(\"stopping...\"))}}var Ns=B(\"[Heatmaps]\");function qs(e){return C(e)&&\"clientX\"in e&&\"clientY\"in e&&M(e.clientX)&&M(e.clientY)}class Bs{constructor(e){var i;W(this,\"rageclicks\",new Os),W(this,\"_enabledServerSide\",!1),W(this,\"_initialized\",!1),W(this,\"_flushInterval\",null),this.instance=e,this._enabledServerSide=!(null===(i=this.instance.persistence)||void 0===i||!i.props[ue]),null==t||t.addEventListener(\"beforeunload\",(()=>{this.flush()}))}get flushIntervalMilliseconds(){var e=5e3;return C(this.instance.config.capture_heatmaps)&&this.instance.config.capture_heatmaps.flush_interval_milliseconds&&(e=this.instance.config.capture_heatmaps.flush_interval_milliseconds),e}get isEnabled(){return F(this.instance.config.capture_heatmaps)?F(this.instance.config.enable_heatmaps)?this._enabledServerSide:this.instance.config.enable_heatmaps:!1!==this.instance.config.capture_heatmaps}startIfEnabled(){if(this.isEnabled){if(this._initialized)return;Ns.info(\"starting...\"),this._setupListeners(),this._flushInterval=setInterval(this.flush.bind(this),this.flushIntervalMilliseconds)}else{var e,t;clearInterval(null!==(e=this._flushInterval)&&void 0!==e?e:void 0),null===(t=this.deadClicksCapture)||void 0===t||t.stop(),this.getAndClearBuffer()}}onRemoteConfig(e){var t=!!e.heatmaps;this.instance.persistence&&this.instance.persistence.register({[ue]:t}),this._enabledServerSide=t,this.startIfEnabled()}getAndClearBuffer(){var e=this.buffer;return this.buffer=void 0,e}_onDeadClick(e){this._onClick(e.originalEvent,\"deadclick\")}_setupListeners(){t&&a&&(re(a,\"click\",(e=>this._onClick(e||(null==t?void 0:t.event))),!1,!0),re(a,\"mousemove\",(e=>this._onMouseMove(e||(null==t?void 0:t.event))),!1,!0),this.deadClicksCapture=new Ds(this.instance,Ls,this._onDeadClick.bind(this)),this.deadClicksCapture.startIfEnabled(),this._initialized=!0)}_getProperties(e,i){var r=this.instance.scrollManager.scrollY(),n=this.instance.scrollManager.scrollX(),s=this.instance.scrollManager.scrollElement(),o=function(e,i,r){for(var n=e;n&&Fi(n)&&!Ri(n,\"body\");){if(n===r)return!1;if(m(i,null==t?void 0:t.getComputedStyle(n).position))return!0;n=Bi(n)}return!1}(Ni(e),[\"fixed\",\"sticky\"],s);return{x:e.clientX+(o?0:n),y:e.clientY+(o?0:r),target_fixed:o,type:i}}_onClick(e){var t,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:\"click\";if(!Pi(e.target)&&qs(e)){var r=this._getProperties(e,i);null!==(t=this.rageclicks)&&void 0!==t&&t.isRageClick(e.clientX,e.clientY,(new Date).getTime())&&this._capture(j(j({},r),{},{type:\"rageclick\"})),this._capture(r)}}_onMouseMove(e){!Pi(e.target)&&qs(e)&&(clearTimeout(this._mouseMoveTimeout),this._mouseMoveTimeout=setTimeout((()=>{this._capture(this._getProperties(e,\"mousemove\"))}),500))}_capture(e){if(t){var i=t.location.href;this.buffer=this.buffer||{},this.buffer[i]||(this.buffer[i]=[]),this.buffer[i].push(e)}}flush(){this.buffer&&!P(this.buffer)&&this.instance.capture(\"$$heatmap\",{$heatmap_data:this.getAndClearBuffer()})}}class Hs{constructor(e){W(this,\"_updateScrollData\",(()=>{var e,t,i,r;this.context||(this.context={});var n=this.scrollElement(),s=this.scrollY(),o=n?Math.max(0,n.scrollHeight-n.clientHeight):0,a=s+((null==n?void 0:n.clientHeight)||0),l=(null==n?void 0:n.scrollHeight)||0;this.context.lastScrollY=Math.ceil(s),this.context.maxScrollY=Math.max(s,null!==(e=this.context.maxScrollY)&&void 0!==e?e:0),this.context.maxScrollHeight=Math.max(o,null!==(t=this.context.maxScrollHeight)&&void 0!==t?t:0),this.context.lastContentY=a,this.context.maxContentY=Math.max(a,null!==(i=this.context.maxContentY)&&void 0!==i?i:0),this.context.maxContentHeight=Math.max(l,null!==(r=this.context.maxContentHeight)&&void 0!==r?r:0)})),this.instance=e}getContext(){return this.context}resetContext(){var e=this.context;return setTimeout(this._updateScrollData,0),e}startMeasuringScrollPosition(){null==t||t.addEventListener(\"scroll\",this._updateScrollData,!0),null==t||t.addEventListener(\"scrollend\",this._updateScrollData,!0),null==t||t.addEventListener(\"resize\",this._updateScrollData)}scrollElement(){if(!this.instance.config.scroll_root_selector)return null==t?void 0:t.document.documentElement;var e=x(this.instance.config.scroll_root_selector)?this.instance.config.scroll_root_selector:[this.instance.config.scroll_root_selector];for(var i of e){var r=null==t?void 0:t.document.querySelector(i);if(r)return r}}scrollY(){if(this.instance.config.scroll_root_selector){var e=this.scrollElement();return e&&e.scrollTop||0}return t&&(t.scrollY||t.pageYOffset||t.document.documentElement.scrollTop)||0}scrollX(){if(this.instance.config.scroll_root_selector){var e=this.scrollElement();return e&&e.scrollLeft||0}return t&&(t.scrollX||t.pageXOffset||t.document.documentElement.scrollLeft)||0}}var Us=B(\"[AutoCapture]\");function zs(e,t){return t.length>e?t.slice(0,e)+\"...\":t}function js(e){if(e.previousElementSibling)return e.previousElementSibling;var t=e;do{t=t.previousSibling}while(t&&!Fi(t));return t}function Ws(e,t,i,r){var n=e.tagName.toLowerCase(),s={tag_name:n};qi.indexOf(n)>-1&&!i&&(\"a\"===n.toLowerCase()||\"button\"===n.toLowerCase()?s.$el_text=zs(1024,Xi(e)):s.$el_text=zs(1024,Di(e)));var o=Li(e);o.length>0&&(s.classes=o.filter((function(e){return\"\"!==e}))),Y(e.attributes,(function(i){var n;if((!zi(e)||-1!==[\"name\",\"id\",\"class\",\"aria-label\"].indexOf(i.name))&&((null==r||!r.includes(i.name))&&!t&&Ki(i.value)&&(n=i.name,!R(n)||\"_ngcontent\"!==n.substring(0,10)&&\"_nghost\"!==n.substring(0,7)))){var o=i.value;\"class\"===i.name&&(o=Oi(o).join(\" \")),s[\"attr__\"+i.name]=zs(1024,o)}}));for(var a=1,l=1,u=e;u=js(u);)a++,u.tagName===e.tagName&&l++;return s.nth_child=a,s.nth_of_type=l,s}function Vs(e,i){for(var r,n,{e:s,maskAllElementAttributes:o,maskAllText:a,elementAttributeIgnoreList:l,elementsChainAsString:u}=i,c=[e],d=e;d.parentNode&&!Ri(d,\"body\");)$i(d.parentNode)?(c.push(d.parentNode.host),d=d.parentNode.host):(c.push(d.parentNode),d=d.parentNode);var h,_=[],p={},v=!1,g=!1;if(Y(c,(e=>{var t=Ui(e);\"a\"===e.tagName.toLowerCase()&&(v=e.getAttribute(\"href\"),v=t&&v&&Ki(v)&&v),m(Li(e),\"ph-no-capture\")&&(g=!0),_.push(Ws(e,o,a,l));var i=function(e){if(!Ui(e))return{};var t={};return Y(e.attributes,(function(e){if(e.name&&0===e.name.indexOf(\"data-ph-capture-attribute\")){var i=e.name.replace(\"data-ph-capture-attribute-\",\"\"),r=e.value;i&&r&&Ki(r)&&(t[i]=r)}})),t}(e);K(p,i)})),g)return{props:{},explicitNoCapture:g};if(a||(\"a\"===e.tagName.toLowerCase()||\"button\"===e.tagName.toLowerCase()?_[0].$el_text=Xi(e):_[0].$el_text=Di(e)),v){var f,b;_[0].attr__href=v;var y=null===(f=gt(v))||void 0===f?void 0:f.host,w=null==t||null===(b=t.location)||void 0===b?void 0:b.host;y&&w&&y!==w&&(h=v)}return{props:K({$event_type:s.type,$ce_version:1},u?{}:{$elements:_},{$elements_chain:Zi(_)},null!==(r=_[0])&&void 0!==r&&r.$el_text?{$el_text:null===(n=_[0])||void 0===n?void 0:n.$el_text}:{},h&&\"click\"===s.type?{$external_click_url:h}:{},p)}}class Gs{constructor(e){W(this,\"_initialized\",!1),W(this,\"_isDisabledServerSide\",null),W(this,\"rageclicks\",new Os),W(this,\"_elementsChainAsString\",!1),this.instance=e,this._elementSelectors=null}get config(){var e,t,i=C(this.instance.config.autocapture)?this.instance.config.autocapture:{};return i.url_allowlist=null===(e=i.url_allowlist)||void 0===e?void 0:e.map((e=>new RegExp(e))),i.url_ignorelist=null===(t=i.url_ignorelist)||void 0===t?void 0:t.map((e=>new RegExp(e))),i}_addDomEventHandlers(){if(this.isBrowserSupported()){if(t&&a){var e=e=>{e=e||(null==t?void 0:t.event);try{this._captureEvent(e)}catch(e){Us.error(\"Failed to capture event\",e)}},i=e=>{e=e||(null==t?void 0:t.event),this._captureEvent(e,v)};re(a,\"submit\",e,!1,!0),re(a,\"change\",e,!1,!0),re(a,\"click\",e,!1,!0),this.config.capture_copied_text&&(re(a,\"copy\",i,!1,!0),re(a,\"cut\",i,!1,!0))}}else Us.info(\"Disabling Automatic Event Collection because this browser is not supported\")}startIfEnabled(){this.isEnabled&&!this._initialized&&(this._addDomEventHandlers(),this._initialized=!0)}onRemoteConfig(e){e.elementsChainAsString&&(this._elementsChainAsString=e.elementsChainAsString),this.instance.persistence&&this.instance.persistence.register({[le]:!!e.autocapture_opt_out}),this._isDisabledServerSide=!!e.autocapture_opt_out,this.startIfEnabled()}setElementSelectors(e){this._elementSelectors=e}getElementSelectors(e){var t,i=[];return null===(t=this._elementSelectors)||void 0===t||t.forEach((t=>{var r=null==a?void 0:a.querySelectorAll(t);null==r||r.forEach((r=>{e===r&&i.push(t)}))})),i}get isEnabled(){var e,t,i=null===(e=this.instance.persistence)||void 0===e?void 0:e.props[le],r=this._isDisabledServerSide;if($(r)&&!L(i)&&!this.instance.config.advanced_disable_decide)return!1;var n=null!==(t=this._isDisabledServerSide)&&void 0!==t?t:!!i;return!!this.instance.config.autocapture&&!n}_captureEvent(e){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:\"$autocapture\";if(this.isEnabled){var r,n=Ni(e);if(Ti(n)&&(n=n.parentNode||null),\"$autocapture\"===i&&\"click\"===e.type&&e instanceof MouseEvent)this.instance.config.rageclick&&null!==(r=this.rageclicks)&&void 0!==r&&r.isRageClick(e.clientX,e.clientY,(new Date).getTime())&&this._captureEvent(e,\"$rageclick\");var s=i===v;if(n&&Hi(n,e,this.config,s,s?[\"copy\",\"cut\"]:void 0)){var{props:o,explicitNoCapture:a}=Vs(n,{e:e,maskAllElementAttributes:this.instance.config.mask_all_element_attributes,maskAllText:this.instance.config.mask_all_text,elementAttributeIgnoreList:this.config.element_attribute_ignorelist,elementsChainAsString:this._elementsChainAsString});if(a)return!1;var l=this.getElementSelectors(n);if(l&&l.length>0&&(o.$element_selectors=l),i===v){var u,c=Ai(null==t||null===(u=t.getSelection())||void 0===u?void 0:u.toString()),d=e.type||\"clipboard\";if(!c)return!1;o.$selected_content=c,o.$copy_type=d}return this.instance.capture(i,o),!0}}}isBrowserSupported(){return I(null==a?void 0:a.querySelectorAll)}}var Js=B(\"[TracingHeaders]\");class Ys{constructor(e){W(this,\"_restoreXHRPatch\",void 0),W(this,\"_restoreFetchPatch\",void 0),W(this,\"_startCapturing\",(()=>{var e,t,i,r;F(this._restoreXHRPatch)&&(null===(e=_.__PosthogExtensions__)||void 0===e||null===(t=e.tracingHeadersPatchFns)||void 0===t||t._patchXHR(this.instance.sessionManager));F(this._restoreFetchPatch)&&(null===(i=_.__PosthogExtensions__)||void 0===i||null===(r=i.tracingHeadersPatchFns)||void 0===r||r._patchFetch(this.instance.sessionManager))})),this.instance=e}_loadScript(e){var t,i,r;null!==(t=_.__PosthogExtensions__)&&void 0!==t&&t.tracingHeadersPatchFns&&e(),null===(i=_.__PosthogExtensions__)||void 0===i||null===(r=i.loadExternalDependency)||void 0===r||r.call(i,this.instance,\"tracing-headers\",(t=>{if(t)return Js.error(\"failed to load script\",t);e()}))}startIfEnabledOrStop(){var e,t;this.instance.config.__add_tracing_headers?this._loadScript(this._startCapturing):(null===(e=this._restoreXHRPatch)||void 0===e||e.call(this),null===(t=this._restoreFetchPatch)||void 0===t||t.call(this),this._restoreXHRPatch=void 0,this._restoreFetchPatch=void 0)}}var Ks;!function(e){e[e.PENDING=-1]=\"PENDING\",e[e.DENIED=0]=\"DENIED\",e[e.GRANTED=1]=\"GRANTED\"}(Ks||(Ks={}));class Xs{constructor(e){this.instance=e}get config(){return this.instance.config}get consent(){return this.getDnt()?Ks.DENIED:this.storedConsent}isOptedOut(){return this.consent===Ks.DENIED||this.consent===Ks.PENDING&&this.config.opt_out_capturing_by_default}isOptedIn(){return!this.isOptedOut()}optInOut(e){this.storage.set(this.storageKey,e?1:0,this.config.cookie_expiration,this.config.cross_subdomain_cookie,this.config.secure_cookie)}reset(){this.storage.remove(this.storageKey,this.config.cross_subdomain_cookie)}get storageKey(){var{token:e,opt_out_capturing_cookie_prefix:t}=this.instance.config;return(t||\"__ph_opt_in_out_\")+e}get storedConsent(){var e=this.storage.get(this.storageKey);return\"1\"===e?Ks.GRANTED:\"0\"===e?Ks.DENIED:Ks.PENDING}get storage(){if(!this._storage){var e=this.config.opt_out_capturing_persistence_type;this._storage=\"localStorage\"===e?lt:ot;var t=\"localStorage\"===e?ot:lt;t.get(this.storageKey)&&(this._storage.get(this.storageKey)||this.optInOut(\"1\"===t.get(this.storageKey)),t.remove(this.storageKey,this.config.cross_subdomain_cookie))}return this._storage}getDnt(){return!!this.config.respect_dnt&&!!ne([null==o?void 0:o.doNotTrack,null==o?void 0:o.msDoNotTrack,_.doNotTrack],(e=>m([!0,1,\"1\",\"yes\"],e)))}}var Qs=B(\"[ExceptionAutocapture]\");class Zs{constructor(e){var i;W(this,\"originalOnUnhandledRejectionHandler\",void 0),W(this,\"startCapturing\",(()=>{var e,i,r,n;if(t&&this.isEnabled&&!this.hasHandlers&&!this.isCapturing){var s=null===(e=_.__PosthogExtensions__)||void 0===e||null===(i=e.errorWrappingFunctions)||void 0===i?void 0:i.wrapOnError,o=null===(r=_.__PosthogExtensions__)||void 0===r||null===(n=r.errorWrappingFunctions)||void 0===n?void 0:n.wrapUnhandledRejection;if(s&&o)try{this.unwrapOnError=s(this.captureException.bind(this)),this.unwrapUnhandledRejection=o(this.captureException.bind(this))}catch(e){Qs.error(\"failed to start\",e),this.stopCapturing()}else Qs.error(\"failed to load error wrapping functions - cannot start\")}})),this.instance=e,this.remoteEnabled=!(null===(i=this.instance.persistence)||void 0===i||!i.props[ce]),this.startIfEnabled()}get isEnabled(){var e;return null!==(e=this.remoteEnabled)&&void 0!==e&&e}get isCapturing(){var e;return!(null==t||null===(e=t.onerror)||void 0===e||!e.__POSTHOG_INSTRUMENTED__)}get hasHandlers(){return this.originalOnUnhandledRejectionHandler||this.unwrapOnError}startIfEnabled(){this.isEnabled&&!this.isCapturing&&(Qs.info(\"enabled, starting...\"),this.loadScript(this.startCapturing))}loadScript(e){var t,i;this.hasHandlers&&e(),null===(t=_.__PosthogExtensions__)||void 0===t||null===(i=t.loadExternalDependency)||void 0===i||i.call(t,this.instance,\"exception-autocapture\",(t=>{if(t)return Qs.error(\"failed to load script\",t);e()}))}stopCapturing(){var e,t;null===(e=this.unwrapOnError)||void 0===e||e.call(this),null===(t=this.unwrapUnhandledRejection)||void 0===t||t.call(this)}onRemoteConfig(e){var t=e.autocaptureExceptions;this.remoteEnabled=!!t||!1,this.instance.persistence&&this.instance.persistence.register({[ce]:this.remoteEnabled}),this.startIfEnabled()}captureException(e){var t=this.instance.requestRouter.endpointFor(\"ui\");e.$exception_personURL=\"\".concat(t,\"/project/\").concat(this.instance.config.token,\"/person/\").concat(this.instance.get_distinct_id()),this.instance.exceptions.sendExceptionEvent(e)}}var eo=B(\"[Web Vitals]\"),to=9e5;class io{constructor(e){var t;W(this,\"_enabledServerSide\",!1),W(this,\"_initialized\",!1),W(this,\"buffer\",{url:void 0,metrics:[],firstMetricTimestamp:void 0}),W(this,\"_flushToCapture\",(()=>{clearTimeout(this._delayedFlushTimer),0!==this.buffer.metrics.length&&(this.instance.capture(\"$web_vitals\",this.buffer.metrics.reduce(((e,t)=>j(j({},e),{},{[\"$web_vitals_\".concat(t.name,\"_event\")]:j({},t),[\"$web_vitals_\".concat(t.name,\"_value\")]:t.value})),{})),this.buffer={url:void 0,metrics:[],firstMetricTimestamp:void 0})})),W(this,\"_addToBuffer\",(e=>{var t,i=null===(t=this.instance.sessionManager)||void 0===t?void 0:t.checkAndGetSessionAndWindowId(!0);if(F(i))eo.error(\"Could not read session ID. Dropping metrics!\");else{this.buffer=this.buffer||{url:void 0,metrics:[],firstMetricTimestamp:void 0};var r=this._currentURL();if(!F(r))if(O(null==e?void 0:e.name)||O(null==e?void 0:e.value))eo.error(\"Invalid metric received\",e);else if(this._maxAllowedValue&&e.value>=this._maxAllowedValue)eo.error(\"Ignoring metric with value >= \"+this._maxAllowedValue,e);else this.buffer.url!==r&&(this._flushToCapture(),this._delayedFlushTimer=setTimeout(this._flushToCapture,this.flushToCaptureTimeoutMs)),F(this.buffer.url)&&(this.buffer.url=r),this.buffer.firstMetricTimestamp=F(this.buffer.firstMetricTimestamp)?Date.now():this.buffer.firstMetricTimestamp,e.attribution&&e.attribution.interactionTargetElement&&(e.attribution.interactionTargetElement=void 0),this.buffer.metrics.push(j(j({},e),{},{$current_url:r,$session_id:i.sessionId,$window_id:i.windowId,timestamp:Date.now()})),this.buffer.metrics.length===this.allowedMetrics.length&&this._flushToCapture()}})),W(this,\"_startCapturing\",(()=>{var e,t,i,r,n=_.__PosthogExtensions__;F(n)||F(n.postHogWebVitalsCallbacks)||({onLCP:e,onCLS:t,onFCP:i,onINP:r}=n.postHogWebVitalsCallbacks),e&&t&&i&&r?(this.allowedMetrics.indexOf(\"LCP\")>-1&&e(this._addToBuffer.bind(this)),this.allowedMetrics.indexOf(\"CLS\")>-1&&t(this._addToBuffer.bind(this)),this.allowedMetrics.indexOf(\"FCP\")>-1&&i(this._addToBuffer.bind(this)),this.allowedMetrics.indexOf(\"INP\")>-1&&r(this._addToBuffer.bind(this)),this._initialized=!0):eo.error(\"web vitals callbacks not loaded - not starting\")})),this.instance=e,this._enabledServerSide=!(null===(t=this.instance.persistence)||void 0===t||!t.props[de]),this.startIfEnabled()}get allowedMetrics(){var e,t,i=C(this.instance.config.capture_performance)?null===(e=this.instance.config.capture_performance)||void 0===e?void 0:e.web_vitals_allowed_metrics:void 0;return F(i)?(null===(t=this.instance.persistence)||void 0===t?void 0:t.props[_e])||[\"CLS\",\"FCP\",\"INP\",\"LCP\"]:i}get flushToCaptureTimeoutMs(){return(C(this.instance.config.capture_performance)?this.instance.config.capture_performance.web_vitals_delayed_flush_ms:void 0)||5e3}get _maxAllowedValue(){var e=C(this.instance.config.capture_performance)&&M(this.instance.config.capture_performance.__web_vitals_max_value)?this.instance.config.capture_performance.__web_vitals_max_value:to;return 0<e&&e<=6e4?to:e}get isEnabled(){var e=C(this.instance.config.capture_performance)?this.instance.config.capture_performance.web_vitals:void 0;return L(e)?e:this._enabledServerSide}startIfEnabled(){this.isEnabled&&!this._initialized&&(eo.info(\"enabled, starting...\"),this.loadScript(this._startCapturing))}onRemoteConfig(e){var t=C(e.capturePerformance)&&!!e.capturePerformance.web_vitals,i=C(e.capturePerformance)?e.capturePerformance.web_vitals_allowed_metrics:void 0;this.instance.persistence&&(this.instance.persistence.register({[de]:t}),this.instance.persistence.register({[_e]:i})),this._enabledServerSide=t,this.startIfEnabled()}loadScript(e){var t,i,r;null!==(t=_.__PosthogExtensions__)&&void 0!==t&&t.postHogWebVitalsCallbacks&&e(),null===(i=_.__PosthogExtensions__)||void 0===i||null===(r=i.loadExternalDependency)||void 0===r||r.call(i,this.instance,\"web-vitals\",(t=>{t?eo.error(\"failed to load script\",t):e()}))}_currentURL(){var e=t?t.location.href:void 0;return e||eo.error(\"Could not determine current URL\"),e}}var ro={icontains:(e,i)=>!!t&&i.href.toLowerCase().indexOf(e.toLowerCase())>-1,not_icontains:(e,i)=>!!t&&-1===i.href.toLowerCase().indexOf(e.toLowerCase()),regex:(e,i)=>!!t&&ft(i.href,e),not_regex:(e,i)=>!!t&&!ft(i.href,e),exact:(e,t)=>t.href===e,is_not:(e,t)=>t.href!==e};class no{constructor(e){var t=this;W(this,\"getWebExperimentsAndEvaluateDisplayLogic\",(function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];t.getWebExperiments((e=>{no.logInfo(\"retrieved web experiments from the server\"),t._flagToExperiments=new Map,e.forEach((e=>{if(e.feature_flag_key){var i;if(t._flagToExperiments)no.logInfo(\"setting flag key \",e.feature_flag_key,\" to web experiment \",e),null===(i=t._flagToExperiments)||void 0===i||i.set(e.feature_flag_key,e);var r=t.instance.getFeatureFlag(e.feature_flag_key);R(r)&&e.variants[r]&&t.applyTransforms(e.name,r,e.variants[r].transforms)}else if(e.variants)for(var n in e.variants){var s=e.variants[n];no.matchesTestVariant(s)&&t.applyTransforms(e.name,n,s.transforms)}}))}),e)})),this.instance=e,this.instance.onFeatureFlags((e=>{this.onFeatureFlags(e)}))}onFeatureFlags(e){if(this._is_bot())no.logInfo(\"Refusing to render web experiment since the viewer is a likely bot\");else if(!this.instance.config.disable_web_experiments){if(O(this._flagToExperiments))return this._flagToExperiments=new Map,this.loadIfEnabled(),void this.previewWebExperiment();no.logInfo(\"applying feature flags\",e),e.forEach((e=>{var t;if(this._flagToExperiments&&null!==(t=this._flagToExperiments)&&void 0!==t&&t.has(e)){var i,r=this.instance.getFeatureFlag(e),n=null===(i=this._flagToExperiments)||void 0===i?void 0:i.get(e);r&&null!=n&&n.variants[r]&&this.applyTransforms(n.name,r,n.variants[r].transforms)}}))}}previewWebExperiment(){var e=no.getWindowLocation();if(null!=e&&e.search){var t=bt(null==e?void 0:e.search,\"__experiment_id\"),i=bt(null==e?void 0:e.search,\"__experiment_variant\");t&&i&&(no.logInfo(\"previewing web experiments \".concat(t,\" && \").concat(i)),this.getWebExperiments((e=>{this.showPreviewWebExperiment(parseInt(t),i,e)}),!1,!0))}}loadIfEnabled(){this.instance.config.disable_web_experiments||this.getWebExperimentsAndEvaluateDisplayLogic()}getWebExperiments(e,t,i){if(this.instance.config.disable_web_experiments&&!i)return e([]);var r=this.instance.get_property(\"$web_experiments\");if(r&&!t)return e(r);this.instance._send_request({url:this.instance.requestRouter.endpointFor(\"api\",\"/api/web_experiments/?token=\".concat(this.instance.config.token)),method:\"GET\",callback:t=>{if(200!==t.statusCode||!t.json)return e([]);var i=t.json.experiments||[];return e(i)}})}showPreviewWebExperiment(e,t,i){var r=i.filter((t=>t.id===e));r&&r.length>0&&(no.logInfo(\"Previewing web experiment [\".concat(r[0].name,\"] with variant [\").concat(t,\"]\")),this.applyTransforms(r[0].name,t,r[0].variants[t].transforms))}static matchesTestVariant(e){return!O(e.conditions)&&(no.matchUrlConditions(e)&&no.matchUTMConditions(e))}static matchUrlConditions(e){var t;if(O(e.conditions)||O(null===(t=e.conditions)||void 0===t?void 0:t.url))return!0;var i,r,n,s=no.getWindowLocation();return!!s&&(null===(i=e.conditions)||void 0===i||!i.url||ro[null!==(r=null===(n=e.conditions)||void 0===n?void 0:n.urlMatchType)&&void 0!==r?r:\"icontains\"](e.conditions.url,s))}static getWindowLocation(){return null==t?void 0:t.location}static matchUTMConditions(e){var t;if(O(e.conditions)||O(null===(t=e.conditions)||void 0===t?void 0:t.utm))return!0;var i=wi.campaignParams();if(i.utm_source){var r,n,s,o,a,l,u,c,d,h,_,p,v,g,f,m,b=null===(r=e.conditions)||void 0===r||null===(n=r.utm)||void 0===n||!n.utm_campaign||(null===(s=e.conditions)||void 0===s||null===(o=s.utm)||void 0===o?void 0:o.utm_campaign)==i.utm_campaign,y=null===(a=e.conditions)||void 0===a||null===(l=a.utm)||void 0===l||!l.utm_source||(null===(u=e.conditions)||void 0===u||null===(c=u.utm)||void 0===c?void 0:c.utm_source)==i.utm_source,w=null===(d=e.conditions)||void 0===d||null===(h=d.utm)||void 0===h||!h.utm_medium||(null===(_=e.conditions)||void 0===_||null===(p=_.utm)||void 0===p?void 0:p.utm_medium)==i.utm_medium,S=null===(v=e.conditions)||void 0===v||null===(g=v.utm)||void 0===g||!g.utm_term||(null===(f=e.conditions)||void 0===f||null===(m=f.utm)||void 0===m?void 0:m.utm_term)==i.utm_term;return b&&w&&S&&y}return!1}static logInfo(e){for(var t=arguments.length,i=new Array(t>1?t-1:0),r=1;r<t;r++)i[r-1]=arguments[r];q.info(\"[WebExperiments] \".concat(e),i)}applyTransforms(e,t,i){this._is_bot()?no.logInfo(\"Refusing to render web experiment since the viewer is a likely bot\"):\"control\"!==t?i.forEach((i=>{if(i.selector){var r;no.logInfo(\"applying transform of variant \".concat(t,\" for experiment \").concat(e,\" \"),i);var n=null===(r=document)||void 0===r?void 0:r.querySelectorAll(i.selector);null==n||n.forEach((e=>{var t=e;i.attributes&&i.attributes.forEach((e=>{switch(e.name){case\"text\":t.innerText=e.value;break;case\"html\":t.innerHTML=e.value;break;case\"cssClass\":t.className=e.value;break;default:t.setAttribute(e.name,e.value)}})),i.text&&(t.innerText=i.text),i.html&&(t.parentElement?t.parentElement.innerHTML=i.html:t.innerHTML=i.html),i.css&&t.setAttribute(\"style\",i.css)}))}})):no.logInfo(\"Control variants leave the page unmodified.\")}_is_bot(){return o&&this.instance?$s(o,this.instance.config.custom_blocked_useragents):void 0}}class so{constructor(e){this.instance=e}sendExceptionEvent(e){this.instance.capture(\"$exception\",e,{_noTruncate:!0,_batchKey:\"exceptionEvent\"})}}var oo=[\"$set_once\",\"$set\"],ao=B(\"[SiteApps]\");class lo{constructor(e){this.instance=e,this.bufferedInvocations=[],this.apps={}}get isEnabled(){return!!this.instance.config.opt_in_site_apps}eventCollector(e,t){if(t){var i=this.globalsForEvent(t);this.bufferedInvocations.push(i),this.bufferedInvocations.length>1e3&&(this.bufferedInvocations=this.bufferedInvocations.slice(10))}}get siteAppLoaders(){var e,t;return null===(e=_._POSTHOG_REMOTE_CONFIG)||void 0===e||null===(t=e[this.instance.config.token])||void 0===t?void 0:t.siteApps}init(){if(this.isEnabled){var e=this.instance._addCaptureHook(this.eventCollector.bind(this));this.stopBuffering=()=>{e(),this.bufferedInvocations=[],this.stopBuffering=void 0}}}globalsForEvent(e){var t,i,r,n,s,o,a;if(!e)throw new Error(\"Event payload is required\");var l={},u=this.instance.get_property(\"$groups\")||[],c=this.instance.get_property(\"$stored_group_properties\")||{};for(var[d,h]of Object.entries(c))l[d]={id:u[d],type:d,properties:h};var{$set_once:_,$set:p}=e;return{event:j(j({},V(e,oo)),{},{properties:j(j(j({},e.properties),p?{$set:j(j({},null!==(t=null===(i=e.properties)||void 0===i?void 0:i.$set)&&void 0!==t?t:{}),p)}:{}),_?{$set_once:j(j({},null!==(r=null===(n=e.properties)||void 0===n?void 0:n.$set_once)&&void 0!==r?r:{}),_)}:{}),elements_chain:null!==(s=null===(o=e.properties)||void 0===o?void 0:o.$elements_chain)&&void 0!==s?s:\"\",distinct_id:null===(a=e.properties)||void 0===a?void 0:a.distinct_id}),person:{properties:this.instance.get_property(\"$stored_person_properties\")},groups:l}}setupSiteApp(e){var t={id:e.id,loaded:!1,errored:!1};this.apps[e.id]=t;var i=i=>{var r;for(var n of(this.apps[e.id].errored=!i,this.apps[e.id].loaded=!0,ao.info(\"Site app with id \".concat(e.id,\" \").concat(i?\"loaded\":\"errored\")),i&&this.bufferedInvocations.length&&(ao.info(\"Processing \".concat(this.bufferedInvocations.length,\" events for site app with id \").concat(e.id)),this.bufferedInvocations.forEach((e=>{var i;return null===(i=t.processEvent)||void 0===i?void 0:i.call(t,e)}))),Object.values(this.apps)))if(!n.loaded)return;null===(r=this.stopBuffering)||void 0===r||r.call(this)};try{var{processEvent:r}=e.init({posthog:this.instance,callback:e=>{i(e)}});r&&(t.processEvent=r)}catch(t){ao.error(\"Error while initializing PostHog app with config id \".concat(e.id),t),i(!1)}}onCapturedEvent(e){if(0!==Object.keys(this.apps).length){var t=this.globalsForEvent(e);for(var i of Object.values(this.apps))try{var r;null===(r=i.processEvent)||void 0===r||r.call(i,t)}catch(t){ao.error(\"Error while processing event \".concat(e.event,\" for site app \").concat(i.id),t)}}}onRemoteConfig(e){var t,i,r,n=this;if(null!==(t=this.siteAppLoaders)&&void 0!==t&&t.length){if(!this.isEnabled)return void ao.error('PostHog site apps are disabled. Enable the \"opt_in_site_apps\" config to proceed.');for(var s of this.siteAppLoaders)this.setupSiteApp(s);this.instance.on(\"eventCaptured\",(e=>this.onCapturedEvent(e)))}else if(null===(i=this.stopBuffering)||void 0===i||i.call(this),null!==(r=e.siteApps)&&void 0!==r&&r.length)if(this.isEnabled){var o=function(e,t){var i,r;_[\"__$$ph_site_app_\".concat(e)]=n.instance,null===(i=_.__PosthogExtensions__)||void 0===i||null===(r=i.loadSiteApp)||void 0===r||r.call(i,n.instance,t,(t=>{if(t)return ao.error(\"Error while initializing PostHog app with config id \".concat(e),t)}))};for(var{id:a,url:l}of e.siteApps)o(a,l)}else ao.error('PostHog site apps are disabled. Enable the \"opt_in_site_apps\" config to proceed.')}}function uo(e,t,i){return bn({distinct_id:e,userPropertiesToSet:t,userPropertiesToSetOnce:i})}var co={},ho=()=>{},_o=\"posthog\",po=!gn&&-1===(null==h?void 0:h.indexOf(\"MSIE\"))&&-1===(null==h?void 0:h.indexOf(\"Mozilla\")),vo=()=>{var e,i,r;return{api_host:\"https://us.i.posthog.com\",ui_host:null,token:\"\",autocapture:!0,rageclick:!0,cross_subdomain_cookie:(i=null==a?void 0:a.location,r=null==i?void 0:i.hostname,!!R(r)&&\"herokuapp.com\"!==r.split(\".\").slice(-2).join(\".\")),persistence:\"localStorage+cookie\",persistence_name:\"\",loaded:ho,store_google:!0,custom_campaign_params:[],custom_blocked_useragents:[],save_referrer:!0,capture_pageview:!0,capture_pageleave:\"if_capture_pageview\",debug:l&&R(null==l?void 0:l.search)&&-1!==l.search.indexOf(\"__posthog_debug=true\")||!1,verbose:!1,cookie_expiration:365,upgrade:!1,disable_session_recording:!1,disable_persistence:!1,disable_web_experiments:!0,disable_surveys:!1,enable_recording_console_log:void 0,secure_cookie:\"https:\"===(null==t||null===(e=t.location)||void 0===e?void 0:e.protocol),ip:!0,opt_out_capturing_by_default:!1,opt_out_persistence_by_default:!1,opt_out_useragent_filter:!1,opt_out_capturing_persistence_type:\"localStorage\",opt_out_capturing_cookie_prefix:null,opt_in_site_apps:!1,property_denylist:[],respect_dnt:!1,sanitize_properties:null,request_headers:{},inapp_protocol:\"//\",inapp_link_new_window:!1,request_batching:!0,properties_string_max_length:65535,session_recording:{},mask_all_element_attributes:!1,mask_all_text:!1,mask_personal_data_properties:!1,custom_personal_data_properties:[],advanced_disable_decide:!1,advanced_disable_feature_flags:!1,advanced_disable_feature_flags_on_first_load:!1,advanced_disable_toolbar_metrics:!1,feature_flag_request_timeout_ms:3e3,on_request_error:e=>{var t=\"Bad HTTP status: \"+e.statusCode+\" \"+e.text;q.error(t)},get_device_id:e=>e,_onCapture:ho,capture_performance:void 0,name:\"posthog\",bootstrap:{},disable_compression:!1,session_idle_timeout_seconds:1800,person_profiles:\"identified_only\",__add_tracing_headers:!1,before_send:void 0}},go=e=>{var t={};F(e.process_person)||(t.person_profiles=e.process_person),F(e.xhr_headers)||(t.request_headers=e.xhr_headers),F(e.cookie_name)||(t.persistence_name=e.cookie_name),F(e.disable_cookie)||(t.disable_persistence=e.disable_cookie);var i=K({},t,e);return x(e.property_blacklist)&&(F(e.property_denylist)?i.property_denylist=e.property_blacklist:x(e.property_denylist)?i.property_denylist=[...e.property_blacklist,...e.property_denylist]:q.error(\"Invalid value for property_denylist config: \"+e.property_denylist)),i};class fo{constructor(){W(this,\"__forceAllowLocalhost\",!1)}get _forceAllowLocalhost(){return this.__forceAllowLocalhost}set _forceAllowLocalhost(e){q.error(\"WebPerformanceObserver is deprecated and has no impact on network capture. Use `_forceAllowLocalhostNetworkCapture` on `posthog.sessionRecording`\"),this.__forceAllowLocalhost=e}}class mo{get decideEndpointWasHit(){var e,t;return null!==(e=null===(t=this.featureFlags)||void 0===t?void 0:t.hasLoadedFlags)&&void 0!==e&&e}constructor(){W(this,\"webPerformance\",new fo),W(this,\"version\",p.LIB_VERSION),W(this,\"_internalEventEmitter\",new bs),this.config=vo(),this.SentryIntegration=Tn,this.sentryIntegration=e=>function(e,t){var i=Rn(e,t);return{name:Fn,processEvent:e=>i(e)}}(this,e),this.__request_queue=[],this.__loaded=!1,this.analyticsDefaultEndpoint=\"/e/\",this._initialPageviewCaptured=!1,this._initialPersonProfilesConfig=null,this._cachedIdentify=null,this.featureFlags=new Ge(this),this.toolbar=new _n(this),this.scrollManager=new Hs(this),this.pageViewManager=new Mn(this),this.surveys=new xs(this),this.experiments=new no(this),this.exceptions=new so(this),this.rateLimiter=new Cs(this),this.requestRouter=new Pn(this),this.consent=new Xs(this),this.people={set:(e,t,i)=>{var r=R(e)?{[e]:t}:e;this.setPersonProperties(r),null==i||i({})},set_once:(e,t,i)=>{var r=R(e)?{[e]:t}:e;this.setPersonProperties(void 0,r),null==i||i({})}},this.on(\"eventCaptured\",(e=>q.info('send \"'.concat(null==e?void 0:e.event,'\"'),e)))}init(e,t,i){if(i&&i!==_o){var r,n=null!==(r=co[i])&&void 0!==r?r:new mo;return n._init(e,t,i),co[i]=n,co[_o][i]=n,n}return this._init(e,t,i)}_init(i){var r,n,s,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=arguments.length>2?arguments[2]:void 0;if(F(i)||T(i))return q.critical(\"PostHog was initialized without a token. This likely indicates a misconfiguration. Please check the first argument passed to posthog.init()\"),this;if(this.__loaded)return q.warn(\"You have already initialized PostHog! Re-initializing is a no-op\"),this;this.__loaded=!0,this.config={},this._triggered_notifs=[],o.person_profiles&&(this._initialPersonProfilesConfig=o.person_profiles),this.set_config(K({},vo(),go(o),{name:a,token:i})),this.config.on_xhr_error&&q.error(\"on_xhr_error is deprecated. Use on_request_error instead\"),this.compression=o.disable_compression?void 0:e.GZipJS,this.persistence=new Ei(this.config),this.sessionPersistence=\"sessionStorage\"===this.config.persistence||\"memory\"===this.config.persistence?this.persistence:new Ei(j(j({},this.config),{},{persistence:\"sessionStorage\"}));var l=j({},this.persistence.props),u=j({},this.sessionPersistence.props);if(this._requestQueue=new pn((e=>this._send_retriable_request(e))),this._retryQueue=new En(this),this.__request_queue=[],this.config.__preview_experimental_cookieless_mode||(this.sessionManager=new In(this),this.sessionPropsManager=new Fs(this,this.sessionManager,this.persistence)),new Ys(this).startIfEnabledOrStop(),this.siteApps=new lo(this),null===(r=this.siteApps)||void 0===r||r.init(),this.config.__preview_experimental_cookieless_mode||(this.sessionRecording=new on(this),this.sessionRecording.startIfEnabledOrStop()),this.config.disable_scroll_properties||this.scrollManager.startMeasuringScrollPosition(),this.autocapture=new Gs(this),this.autocapture.startIfEnabled(),this.surveys.loadIfEnabled(),this.heatmaps=new Bs(this),this.heatmaps.startIfEnabled(),this.webVitalsAutocapture=new io(this),this.exceptionObserver=new Zs(this),this.exceptionObserver.startIfEnabled(),this.deadClicksAutocapture=new Ds(this,As),this.deadClicksAutocapture.startIfEnabled(),p.DEBUG=p.DEBUG||this.config.debug,p.DEBUG&&q.info(\"Starting in debug mode\",{this:this,config:o,thisC:j({},this.config),p:l,s:u}),this._sync_opt_out_with_persistence(),void 0!==(null===(n=o.bootstrap)||void 0===n?void 0:n.distinctID)){var c,d,h=this.config.get_device_id(et()),_=null!==(c=o.bootstrap)&&void 0!==c&&c.isIdentifiedID?h:o.bootstrap.distinctID;this.persistence.set_property($e,null!==(d=o.bootstrap)&&void 0!==d&&d.isIdentifiedID?\"identified\":\"anonymous\"),this.register({distinct_id:o.bootstrap.distinctID,$device_id:_})}if(this._hasBootstrappedFeatureFlags()){var v,g,f=Object.keys((null===(v=o.bootstrap)||void 0===v?void 0:v.featureFlags)||{}).filter((e=>{var t,i;return!(null===(t=o.bootstrap)||void 0===t||null===(i=t.featureFlags)||void 0===i||!i[e])})).reduce(((e,t)=>{var i,r;return e[t]=(null===(i=o.bootstrap)||void 0===i||null===(r=i.featureFlags)||void 0===r?void 0:r[t])||!1,e}),{}),m=Object.keys((null===(g=o.bootstrap)||void 0===g?void 0:g.featureFlagPayloads)||{}).filter((e=>f[e])).reduce(((e,t)=>{var i,r,n,s;null!==(i=o.bootstrap)&&void 0!==i&&null!==(r=i.featureFlagPayloads)&&void 0!==r&&r[t]&&(e[t]=null===(n=o.bootstrap)||void 0===n||null===(s=n.featureFlagPayloads)||void 0===s?void 0:s[t]);return e}),{});this.featureFlags.receivedFeatureFlags({featureFlags:f,featureFlagPayloads:m})}if(this.config.__preview_experimental_cookieless_mode)this.register_once({distinct_id:Be,$device_id:null},\"\");else if(!this.get_distinct_id()){var b=this.config.get_device_id(et());this.register_once({distinct_id:b,$device_id:b},\"\"),this.persistence.set_property($e,\"anonymous\")}return null==t||null===(s=t.addEventListener)||void 0===s||s.call(t,\"onpagehide\"in self?\"pagehide\":\"unload\",this._handle_unload.bind(this)),this.toolbar.maybeLoadToolbar(),o.segment?On(this,(()=>this._loaded())):this._loaded(),I(this.config._onCapture)&&this.config._onCapture!==ho&&(q.warn(\"onCapture is deprecated. Please use `before_send` instead\"),this.on(\"eventCaptured\",(e=>this.config._onCapture(e.event,e)))),this}_onRemoteConfig(t){var i,r,n,s,o,l,u,c,d;if(!a||!a.body)return q.info(\"document not ready yet, trying again in 500 milliseconds...\"),void setTimeout((()=>{this._onRemoteConfig(t)}),500);this.compression=void 0,t.supportedCompression&&!this.config.disable_compression&&(this.compression=m(t.supportedCompression,e.GZipJS)?e.GZipJS:m(t.supportedCompression,e.Base64)?e.Base64:void 0),null!==(i=t.analytics)&&void 0!==i&&i.endpoint&&(this.analyticsDefaultEndpoint=t.analytics.endpoint),this.set_config({person_profiles:this._initialPersonProfilesConfig?this._initialPersonProfilesConfig:\"identified_only\"}),null===(r=this.siteApps)||void 0===r||r.onRemoteConfig(t),null===(n=this.sessionRecording)||void 0===n||n.onRemoteConfig(t),null===(s=this.autocapture)||void 0===s||s.onRemoteConfig(t),null===(o=this.heatmaps)||void 0===o||o.onRemoteConfig(t),null===(l=this.surveys)||void 0===l||l.onRemoteConfig(t),null===(u=this.webVitalsAutocapture)||void 0===u||u.onRemoteConfig(t),null===(c=this.exceptionObserver)||void 0===c||c.onRemoteConfig(t),null===(d=this.deadClicksAutocapture)||void 0===d||d.onRemoteConfig(t)}_loaded(){try{this.config.loaded(this)}catch(e){q.critical(\"`loaded` function failed\",e)}this._start_queue_if_opted_in(),this.config.capture_pageview&&setTimeout((()=>{this.consent.isOptedIn()&&this._captureInitialPageview()}),1),new ln(this).load(),this.featureFlags.decide()}_start_queue_if_opted_in(){var e;this.has_opted_out_capturing()||this.config.request_batching&&(null===(e=this._requestQueue)||void 0===e||e.enable())}_dom_loaded(){this.has_opted_out_capturing()||J(this.__request_queue,(e=>this._send_retriable_request(e))),this.__request_queue=[],this._start_queue_if_opted_in()}_handle_unload(){var e,t;this.config.request_batching?(this._shouldCapturePageleave()&&this.capture(\"$pageleave\"),null===(e=this._requestQueue)||void 0===e||e.unload(),null===(t=this._retryQueue)||void 0===t||t.unload()):this._shouldCapturePageleave()&&this.capture(\"$pageleave\",null,{transport:\"sendBeacon\"})}_send_request(e){this.__loaded&&(po?this.__request_queue.push(e):this.rateLimiter.isServerRateLimited(e.batchKey)||(e.transport=e.transport||this.config.api_transport,e.url=mn(e.url,{ip:this.config.ip?1:0}),e.headers=j({},this.config.request_headers),e.compression=\"best-available\"===e.compression?this.compression:e.compression,e.fetchOptions=e.fetchOptions||this.config.fetch_options,(e=>{var t,i,r,n=j({},e);n.timeout=n.timeout||6e4,n.url=mn(n.url,{_:(new Date).getTime().toString(),ver:p.LIB_VERSION,compression:n.compression});var s=null!==(t=n.transport)&&void 0!==t?t:\"fetch\",o=null!==(i=null===(r=ne(wn,(e=>e.transport===s)))||void 0===r?void 0:r.method)&&void 0!==i?i:wn[0].method;if(!o)throw new Error(\"No available transport method\");o(n)})(j(j({},e),{},{callback:t=>{var i,r,n;(this.rateLimiter.checkForLimiting(t),t.statusCode>=400)&&(null===(r=(n=this.config).on_request_error)||void 0===r||r.call(n,t));null===(i=e.callback)||void 0===i||i.call(e,t)}}))))}_send_retriable_request(e){this._retryQueue?this._retryQueue.retriableRequest(e):this._send_request(e)}_execute_array(e){var t,i=[],r=[],n=[];J(e,(e=>{e&&(t=e[0],x(t)?n.push(e):I(e)?e.call(this):x(e)&&\"alias\"===t?i.push(e):x(e)&&-1!==t.indexOf(\"capture\")&&I(this[t])?n.push(e):r.push(e))}));var s=function(e,t){J(e,(function(e){if(x(e[0])){var i=t;Y(e,(function(e){i=i[e[0]].apply(i,e.slice(1))}))}else this[e[0]].apply(this,e.slice(1))}),t)};s(i,this),s(r,this),s(n,this)}_hasBootstrappedFeatureFlags(){var e,t;return(null===(e=this.config.bootstrap)||void 0===e?void 0:e.featureFlags)&&Object.keys(null===(t=this.config.bootstrap)||void 0===t?void 0:t.featureFlags).length>0||!1}push(e){this._execute_array([e])}capture(e,t,i){var r;if(this.__loaded&&this.persistence&&this.sessionPersistence&&this._requestQueue){if(!this.consent.isOptedOut())if(!F(e)&&R(e)){if(this.config.opt_out_useragent_filter||!this._is_bot()){var n=null!=i&&i.skip_client_rate_limiting?void 0:this.rateLimiter.clientRateLimitContext();if(null==n||!n.isRateLimited){this.sessionPersistence.update_search_keyword(),this.config.store_google&&this.sessionPersistence.update_campaign_params(),this.config.save_referrer&&this.sessionPersistence.update_referrer_info(),(this.config.store_google||this.config.save_referrer)&&this.persistence.set_initial_person_info();var s=new Date,o=(null==i?void 0:i.timestamp)||s,a=et(),l={uuid:a,event:e,properties:this._calculate_event_properties(e,t||{},o,a)};n&&(l.properties.$lib_rate_limit_remaining_tokens=n.remainingTokens),(null==i?void 0:i.$set)&&(l.$set=null==i?void 0:i.$set);var u=this._calculate_set_once_properties(null==i?void 0:i.$set_once);u&&(l.$set_once=u),(l=ie(l,null!=i&&i._noTruncate?null:this.config.properties_string_max_length)).timestamp=o,F(null==i?void 0:i.timestamp)||(l.properties.$event_time_override_provided=!0,l.properties.$event_time_override_system_time=s);var c=j(j({},l.properties.$set),l.$set);if(P(c)||this.setPersonPropertiesForFlags(c),!O(this.config.before_send)){var d=this._runBeforeSend(l);if(!d)return;l=d}this._internalEventEmitter.emit(\"eventCaptured\",l);var h={method:\"POST\",url:null!==(r=null==i?void 0:i._url)&&void 0!==r?r:this.requestRouter.endpointFor(\"api\",this.analyticsDefaultEndpoint),data:l,compression:\"best-available\",batchKey:null==i?void 0:i._batchKey};return!this.config.request_batching||i&&(null==i||!i._batchKey)||null!=i&&i.send_instantly?this._send_retriable_request(h):this._requestQueue.enqueue(h),l}q.critical(\"This capture call is ignored due to client rate limiting.\")}}else q.error(\"No event name provided to posthog.capture\")}else q.uninitializedWarning(\"posthog.capture\")}_addCaptureHook(e){return this.on(\"eventCaptured\",(t=>e(t.event,t)))}_calculate_event_properties(e,t,i,r){if(i=i||new Date,!this.persistence||!this.sessionPersistence)return t;var n=this.persistence.remove_event_timer(e),s=j({},t);if(s.token=this.config.token,this.config.__preview_experimental_cookieless_mode&&(s.$cookieless_mode=!0),\"$snapshot\"===e){var o=j(j({},this.persistence.properties()),this.sessionPersistence.properties());return s.distinct_id=o.distinct_id,(!R(s.distinct_id)&&!M(s.distinct_id)||T(s.distinct_id))&&q.error(\"Invalid distinct_id for replay event. This indicates a bug in your implementation\"),s}var l,u=wi.properties({maskPersonalDataProperties:this.config.mask_personal_data_properties,customPersonalDataProperties:this.config.custom_personal_data_properties});if(this.sessionManager){var{sessionId:c,windowId:d}=this.sessionManager.checkAndGetSessionAndWindowId();s.$session_id=c,s.$window_id=d}if(this.sessionRecording&&(s.$recording_status=this.sessionRecording.status),this.requestRouter.region===kn.CUSTOM&&(s.$lib_custom_api_host=this.config.api_host),this.sessionPropsManager&&this.config.__preview_send_client_session_params&&(\"$pageview\"===e||\"$pageleave\"===e||\"$autocapture\"===e)){var _=this.sessionPropsManager.getSessionProps();s=K(s,_)}if(l=\"$pageview\"===e?this.pageViewManager.doPageView(i,r):\"$pageleave\"===e?this.pageViewManager.doPageLeave(i):this.pageViewManager.doEvent(),s=K(s,l),\"$pageview\"===e&&a&&(s.title=a.title),!F(n)){var p=i.getTime()-n;s.$duration=parseFloat((p/1e3).toFixed(3))}h&&this.config.opt_out_useragent_filter&&(s.$browser_type=this._is_bot()?\"bot\":\"browser\"),(s=K({},u,this.persistence.properties(),this.sessionPersistence.properties(),s)).$is_identified=this._isIdentified(),x(this.config.property_denylist)?Y(this.config.property_denylist,(function(e){delete s[e]})):q.error(\"Invalid value for property_denylist config: \"+this.config.property_denylist+\" or property_blacklist config: \"+this.config.property_blacklist);var v=this.config.sanitize_properties;v&&(q.error(\"sanitize_properties is deprecated. Use before_send instead\"),s=v(s,e));var g=this._hasPersonProcessing();return s.$process_person_profile=g,g&&this._requirePersonProcessing(\"_calculate_event_properties\"),s}_calculate_set_once_properties(e){if(!this.persistence||!this._hasPersonProcessing())return e;var t=K({},this.persistence.get_initial_props(),e||{}),i=this.config.sanitize_properties;return i&&(q.error(\"sanitize_properties is deprecated. Use before_send instead\"),t=i(t,\"$set_once\")),P(t)?void 0:t}register(e,t){var i;null===(i=this.persistence)||void 0===i||i.register(e,t)}register_once(e,t,i){var r;null===(r=this.persistence)||void 0===r||r.register_once(e,t,i)}register_for_session(e){var t;null===(t=this.sessionPersistence)||void 0===t||t.register(e)}unregister(e){var t;null===(t=this.persistence)||void 0===t||t.unregister(e)}unregister_for_session(e){var t;null===(t=this.sessionPersistence)||void 0===t||t.unregister(e)}_register_single(e,t){this.register({[e]:t})}getFeatureFlag(e,t){return this.featureFlags.getFeatureFlag(e,t)}getFeatureFlagPayload(e){var t=this.featureFlags.getFeatureFlagPayload(e);try{return JSON.parse(t)}catch(e){return t}}isFeatureEnabled(e,t){return this.featureFlags.isFeatureEnabled(e,t)}reloadFeatureFlags(){this.featureFlags.reloadFeatureFlags()}updateEarlyAccessFeatureEnrollment(e,t){this.featureFlags.updateEarlyAccessFeatureEnrollment(e,t)}getEarlyAccessFeatures(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return this.featureFlags.getEarlyAccessFeatures(e,t)}on(e,t){return this._internalEventEmitter.on(e,t)}onFeatureFlags(e){return this.featureFlags.onFeatureFlags(e)}onSessionId(e){var t,i;return null!==(t=null===(i=this.sessionManager)||void 0===i?void 0:i.onSessionId(e))&&void 0!==t?t:()=>{}}getSurveys(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];this.surveys.getSurveys(e,t)}getActiveMatchingSurveys(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];this.surveys.getActiveMatchingSurveys(e,t)}renderSurvey(e,t){this.surveys.renderSurvey(e,t)}canRenderSurvey(e){this.surveys.canRenderSurvey(e)}getNextSurveyStep(e,t,i){return this.surveys.getNextSurveyStep(e,t,i)}identify(e,t,i){if(!this.__loaded||!this.persistence)return q.uninitializedWarning(\"posthog.identify\");if(M(e)&&(e=e.toString(),q.warn(\"The first argument to posthog.identify was a number, but it should be a string. It has been converted to a string.\")),e){if([\"distinct_id\",\"distinctid\"].includes(e.toLowerCase()))q.critical('The string \"'.concat(e,'\" was set in posthog.identify which indicates an error. This ID should be unique to the user and not a hardcoded string.'));else if(this._requirePersonProcessing(\"posthog.identify\")){var r=this.get_distinct_id();if(this.register({$user_id:e}),!this.get_property(\"$device_id\")){var n=r;this.register_once({$had_persisted_distinct_id:!0,$device_id:n},\"\")}e!==r&&e!==this.get_property(oe)&&(this.unregister(oe),this.register({distinct_id:e}));var s=\"anonymous\"===(this.persistence.get_property($e)||\"anonymous\");e!==r&&s?(this.persistence.set_property($e,\"identified\"),this.setPersonPropertiesForFlags(t||{},!1),this.capture(\"$identify\",{distinct_id:e,$anon_distinct_id:r},{$set:t||{},$set_once:i||{}}),this.featureFlags.setAnonymousDistinctId(r),this._cachedIdentify=uo(e,t,i)):(t||i)&&(this._cachedIdentify!==uo(e,t,i)?(this.setPersonProperties(t,i),this._cachedIdentify=uo(e,t,i)):q.info(\"A duplicate posthog.identify call was made with the same properties. It has been ignored.\")),e!==r&&(this.reloadFeatureFlags(),this.unregister(Te))}}else q.error(\"Unique user id has not been set in posthog.identify\")}setPersonProperties(e,t){(e||t)&&this._requirePersonProcessing(\"posthog.setPersonProperties\")&&(this.setPersonPropertiesForFlags(e||{}),this.capture(\"$set\",{$set:e||{},$set_once:t||{}}))}group(e,t,i){if(e&&t){if(this._requirePersonProcessing(\"posthog.group\")){var r=this.getGroups();r[e]!==t&&this.resetGroupPropertiesForFlags(e),this.register({$groups:j(j({},r),{},{[e]:t})}),i&&(this.capture(\"$groupidentify\",{$group_type:e,$group_key:t,$group_set:i}),this.setGroupPropertiesForFlags({[e]:i})),r[e]===t||i||this.reloadFeatureFlags()}}else q.error(\"posthog.group requires a group type and group key\")}resetGroups(){this.register({$groups:{}}),this.resetGroupPropertiesForFlags(),this.reloadFeatureFlags()}setPersonPropertiesForFlags(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];this.featureFlags.setPersonPropertiesForFlags(e,t)}resetPersonPropertiesForFlags(){this.featureFlags.resetPersonPropertiesForFlags()}setGroupPropertiesForFlags(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];this._requirePersonProcessing(\"posthog.setGroupPropertiesForFlags\")&&this.featureFlags.setGroupPropertiesForFlags(e,t)}resetGroupPropertiesForFlags(e){this.featureFlags.resetGroupPropertiesForFlags(e)}reset(e){var t,i,r,n,s;if(q.info(\"reset\"),!this.__loaded)return q.uninitializedWarning(\"posthog.reset\");var o=this.get_property(\"$device_id\");if(this.consent.reset(),null===(t=this.persistence)||void 0===t||t.clear(),null===(i=this.sessionPersistence)||void 0===i||i.clear(),null===(r=this.surveys)||void 0===r||r.reset(),null===(n=this.persistence)||void 0===n||n.set_property($e,\"anonymous\"),null===(s=this.sessionManager)||void 0===s||s.resetSessionId(),this._cachedIdentify=null,this.config.__preview_experimental_cookieless_mode)this.register_once({distinct_id:Be,$device_id:null},\"\");else{var a=this.config.get_device_id(et());this.register_once({distinct_id:a,$device_id:e?a:o},\"\")}}get_distinct_id(){return this.get_property(\"distinct_id\")}getGroups(){return this.get_property(\"$groups\")||{}}get_session_id(){var e,t;return null!==(e=null===(t=this.sessionManager)||void 0===t?void 0:t.checkAndGetSessionAndWindowId(!0).sessionId)&&void 0!==e?e:\"\"}get_session_replay_url(e){if(!this.sessionManager)return\"\";var{sessionId:t,sessionStartTimestamp:i}=this.sessionManager.checkAndGetSessionAndWindowId(!0),r=this.requestRouter.endpointFor(\"ui\",\"/project/\".concat(this.config.token,\"/replay/\").concat(t));if(null!=e&&e.withTimestamp&&i){var n,s=null!==(n=e.timestampLookBack)&&void 0!==n?n:10;if(!i)return r;var o=Math.max(Math.floor(((new Date).getTime()-i)/1e3)-s,0);r+=\"?t=\".concat(o)}return r}alias(e,t){return e===this.get_property(se)?(q.critical(\"Attempting to create alias for existing People user - aborting.\"),-2):this._requirePersonProcessing(\"posthog.alias\")?(F(t)&&(t=this.get_distinct_id()),e!==t?(this._register_single(oe,e),this.capture(\"$create_alias\",{alias:e,distinct_id:t})):(q.warn(\"alias matches current distinct_id - skipping api call.\"),this.identify(e),-1)):void 0}set_config(e){var t,i,r,n,s=j({},this.config);C(e)&&(K(this.config,go(e)),null===(t=this.persistence)||void 0===t||t.update_config(this.config,s),this.sessionPersistence=\"sessionStorage\"===this.config.persistence||\"memory\"===this.config.persistence?this.persistence:new Ei(j(j({},this.config),{},{persistence:\"sessionStorage\"})),lt.is_supported()&&\"true\"===lt.get(\"ph_debug\")&&(this.config.debug=!0),this.config.debug&&(p.DEBUG=!0,q.info(\"set_config\",{config:e,oldConfig:s,newConfig:j({},this.config)})),null===(i=this.sessionRecording)||void 0===i||i.startIfEnabledOrStop(),null===(r=this.autocapture)||void 0===r||r.startIfEnabled(),null===(n=this.heatmaps)||void 0===n||n.startIfEnabled(),this.surveys.loadIfEnabled(),this._sync_opt_out_with_persistence())}startSessionRecording(e){var t=!0===e,i={sampling:t||!(null==e||!e.sampling),linked_flag:t||!(null==e||!e.linked_flag),url_trigger:t||!(null==e||!e.url_trigger),event_trigger:t||!(null==e||!e.event_trigger)};if(Object.values(i).some(Boolean)){var r,n,s,o,a;if(null===(r=this.sessionManager)||void 0===r||r.checkAndGetSessionAndWindowId(),i.sampling)null===(n=this.sessionRecording)||void 0===n||n.overrideSampling();if(i.linked_flag)null===(s=this.sessionRecording)||void 0===s||s.overrideLinkedFlag();if(i.url_trigger)null===(o=this.sessionRecording)||void 0===o||o.overrideTrigger(\"url\");if(i.event_trigger)null===(a=this.sessionRecording)||void 0===a||a.overrideTrigger(\"event\")}this.set_config({disable_session_recording:!1})}stopSessionRecording(){this.set_config({disable_session_recording:!0})}sessionRecordingStarted(){var e;return!(null===(e=this.sessionRecording)||void 0===e||!e.started)}captureException(e,t){var i,r=new Error(\"PostHog syntheticException\"),n=I(null===(i=_.__PosthogExtensions__)||void 0===i?void 0:i.parseErrorAsProperties)?_.__PosthogExtensions__.parseErrorAsProperties([e.message,void 0,void 0,void 0,e],{syntheticException:r}):j({$exception_level:\"error\",$exception_list:[{type:e.name,value:e.message,mechanism:{handled:!0,synthetic:!1}}]},t);this.exceptions.sendExceptionEvent(n)}loadToolbar(e){return this.toolbar.loadToolbar(e)}get_property(e){var t;return null===(t=this.persistence)||void 0===t?void 0:t.props[e]}getSessionProperty(e){var t;return null===(t=this.sessionPersistence)||void 0===t?void 0:t.props[e]}toString(){var e,t=null!==(e=this.config.name)&&void 0!==e?e:_o;return t!==_o&&(t=_o+\".\"+t),t}_isIdentified(){var e,t;return\"identified\"===(null===(e=this.persistence)||void 0===e?void 0:e.get_property($e))||\"identified\"===(null===(t=this.sessionPersistence)||void 0===t?void 0:t.get_property($e))}_hasPersonProcessing(){var e,t,i,r;return!(\"never\"===this.config.person_profiles||\"identified_only\"===this.config.person_profiles&&!this._isIdentified()&&P(this.getGroups())&&(null===(e=this.persistence)||void 0===e||null===(t=e.props)||void 0===t||!t[oe])&&(null===(i=this.persistence)||void 0===i||null===(r=i.props)||void 0===r||!r[Ne]))}_shouldCapturePageleave(){return!0===this.config.capture_pageleave||\"if_capture_pageview\"===this.config.capture_pageleave&&this.config.capture_pageview}createPersonProfile(){this._hasPersonProcessing()||this._requirePersonProcessing(\"posthog.createPersonProfile\")&&this.setPersonProperties({},{})}_requirePersonProcessing(e){return\"never\"===this.config.person_profiles?(q.error(e+' was called, but process_person is set to \"never\". This call will be ignored.'),!1):(this._register_single(Ne,!0),!0)}_sync_opt_out_with_persistence(){var e,t,i,r,n=this.consent.isOptedOut(),s=this.config.opt_out_persistence_by_default,o=this.config.disable_persistence||n&&!!s;(null===(e=this.persistence)||void 0===e?void 0:e.disabled)!==o&&(null===(i=this.persistence)||void 0===i||i.set_disabled(o));(null===(t=this.sessionPersistence)||void 0===t?void 0:t.disabled)!==o&&(null===(r=this.sessionPersistence)||void 0===r||r.set_disabled(o))}opt_in_capturing(e){var t;(this.consent.optInOut(!0),this._sync_opt_out_with_persistence(),F(null==e?void 0:e.captureEventName)||null!=e&&e.captureEventName)&&this.capture(null!==(t=null==e?void 0:e.captureEventName)&&void 0!==t?t:\"$opt_in\",null==e?void 0:e.captureProperties,{send_instantly:!0});this.config.capture_pageview&&this._captureInitialPageview()}opt_out_capturing(){this.consent.optInOut(!1),this._sync_opt_out_with_persistence()}has_opted_in_capturing(){return this.consent.isOptedIn()}has_opted_out_capturing(){return this.consent.isOptedOut()}clear_opt_in_out_capturing(){this.consent.reset(),this._sync_opt_out_with_persistence()}_is_bot(){return o?$s(o,this.config.custom_blocked_useragents):void 0}_captureInitialPageview(){a&&!this._initialPageviewCaptured&&(this._initialPageviewCaptured=!0,this.capture(\"$pageview\",{title:a.title},{send_instantly:!0}))}debug(e){!1===e?(null==t||t.console.log(\"You've disabled debug mode.\"),localStorage&&localStorage.removeItem(\"ph_debug\"),this.set_config({debug:!1})):(null==t||t.console.log(\"You're now in debug mode. All calls to PostHog will be logged in your console.\\nYou can disable this with `posthog.debug(false)`.\"),localStorage&&localStorage.setItem(\"ph_debug\",\"true\"),this.set_config({debug:!0}))}_runBeforeSend(e){if(O(this.config.before_send))return e;var t=x(this.config.before_send)?this.config.before_send:[this.config.before_send],i=e;for(var r of t){if(i=r(i),O(i)){var n=\"Event '\".concat(e.event,\"' was rejected in beforeSend function\");return D(e.event)?q.warn(\"\".concat(n,\". This can cause unexpected behavior.\")):q.info(n),null}i.properties&&!P(i.properties)||q.warn(\"Event '\".concat(e.event,\"' has no properties after beforeSend function, this is likely an error.\"))}return i}getPageViewId(){var e;return null===(e=this.pageViewManager._currentPageview)||void 0===e?void 0:e.pageViewId}}!function(e,t){for(var i=0;i<t.length;i++)e.prototype[t[i]]=ee(e.prototype[t[i]])}(mo,[\"identify\"]);var bo,yo=(bo=co[_o]=new mo,function(){function e(){e.done||(e.done=!0,po=!1,Y(co,(function(e){e._dom_loaded()})))}null!=a&&a.addEventListener&&(\"complete\"===a.readyState?e():a.addEventListener(\"DOMContentLoaded\",e,!1)),t&&re(t,\"load\",e,!0)}(),bo);\n//# sourceMappingURL=module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcG9zdGhvZy1qcy9kaXN0L21vZHVsZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBQUEsbWFBQW1hLElBQUksK0JBQStCLCtRQUErUSxhQUFhLHFDQUFxQyxTQUFTLEdBQUcsdURBQXVELGdCQUFnQix3QkFBd0Isa0JBQWtCLDBEQUEwRCxlQUFlLDRCQUE0Qix3RkFBd0YsbUNBQW1DLDJEQUEyRCxTQUFTLHVDQUF1QyxTQUFTLFNBQVMsbU9BQW1PLE9BQU8saUJBQWlCLDREQUE0RCwySUFBMkksSUFBSSx3QkFBd0IsV0FBVyxpQkFBaUIsOENBQThDLElBQUksc0JBQXNCLG1CQUFtQixpQkFBaUIsOENBQThDLElBQUksc0JBQXNCLG9CQUFvQixrQkFBa0IsOENBQThDLElBQUksc0JBQXNCLHFCQUFxQixxQkFBcUIsOENBQThDLElBQUksc0JBQXNCLHNCQUFzQiwwQkFBMEIsaUVBQWlFLGdEQUFnRCxTQUFTLGlGQUFpRix1TEFBdUwsV0FBVyxxQ0FBcUMsZ0NBQWdDLHdRQUF3USw0Q0FBNEMsaUdBQWlHLHVFQUF1RSxnQkFBZ0IscUJBQXFCLGlDQUFpQyxzQ0FBc0MsNEJBQTRCLHVEQUF1RCxzQkFBc0IsU0FBUyxjQUFjLFlBQVksbUJBQW1CLEtBQUsseUNBQXlDLHlDQUF5QyxZQUFZLHFJQUFxSSxnRUFBZ0UsR0FBRyxTQUFTLGtCQUFrQix5Q0FBeUMsa0RBQWtELFdBQVcsZ0JBQWdCLG9CQUFvQix3QkFBd0Isb0JBQW9CLFlBQVksa0JBQWtCLFFBQVEsV0FBVyx3Q0FBd0MsU0FBUyxNQUFNLGlDQUFpQyxzQ0FBc0MsUUFBUSxXQUFXLHlGQUF5RixTQUFTLG1EQUFtRCwwREFBMEQseURBQXlELHlGQUF5Rix1Q0FBdUMsK0JBQStCLDhDQUE4QyxTQUFTLCtDQUErQywyQ0FBMkMsVUFBVSxTQUFTLGtCQUFrQiwyQ0FBMkMsa0VBQWtFLElBQUksMkNBQTJDLGtCQUFrQixVQUFVLHdCQUF3QixTQUFTLDJEQUEyRCxpRUFBaUUsa0JBQWtCLHNEQUFzRCxJQUFJLHdCQUF3Qix3QkFBd0IsMENBQTBDLEtBQUssZUFBZSxzREFBc0QsSUFBSSx3QkFBd0Isd0JBQXdCLGlCQUFpQixVQUFVLEdBQUcsTUFBTSxjQUFjLG1EQUFtRCxJQUFJLHFCQUFxQixTQUFTLGtCQUFrQixJQUFJLFdBQVcsU0FBUyxRQUFRLGdCQUFnQixrQkFBa0IsSUFBSSw4Q0FBOEMsSUFBSSxzQkFBc0IsdUJBQXVCLFNBQVMsNEpBQTRKLGdCQUFnQixTQUFTLDBCQUEwQiwyQkFBMkIsTUFBTSxpQkFBaUIscUVBQXFFLDhFQUE4RSxhQUFhLFNBQVMsY0FBYyx3QkFBd0IsT0FBTyxNQUFNLElBQUksVUFBVSxrQkFBa0IsY0FBYyxvRkFBb0YsbUNBQW1DLG9CQUFvQiw4QkFBOEIscUJBQXFCLHFCQUFxQiwyREFBMkQsS0FBSyxvQkFBb0IscUJBQXFCLG1CQUFtQixtQ0FBbUMsV0FBVyxlQUFlLGtCQUFrQixpQ0FBaUMsUUFBUSw2REFBNkQsR0FBRyxpQkFBaUIsWUFBWSxXQUFXLDJCQUEyQixvdkNBQW92QyxTQUFTLHNCQUFzQixjQUFjLFVBQVUsU0FBUyxlQUFlLGtSQUFrUixTQUFTLHNFQUFzRSxLQUFLLHVKQUF1SiwwQkFBMEIsZUFBZSxHQUFHLHFCQUFxQiw0QkFBNEIsV0FBVywyQ0FBMkMsa0JBQWtCLHNFQUFzRSxtQkFBbUIsY0FBYyx5QkFBeUIsV0FBVyxvQkFBb0Isc0VBQXNFLDhDQUE4QywrQkFBK0Isa0JBQWtCLDBDQUEwQyxxQkFBcUIsNklBQTZJLDJCQUEyQixNQUFNLGlCQUFpQixpRUFBaUUsb0JBQW9CLDhGQUE4RiwwQkFBMEIseUJBQXlCLHNCQUFzQiwwQkFBMEIsdUJBQXVCLG1JQUFtSSxLQUFLLE9BQU8seVBBQXlQLDJKQUEySixzT0FBc08sYUFBYSwrTEFBK0wsR0FBRyw2R0FBNkcsdUdBQXVHLEdBQUcsa0JBQWtCLGdFQUFnRSxvRUFBb0UsOEZBQThGLGlLQUFpSyxPQUFPLGdEQUFnRCxzY0FBc2MsRUFBRSxTQUFTLHNGQUFzRix5QkFBeUIsaUNBQWlDLG9CQUFvQixnRUFBZ0Usb0dBQW9HLHdGQUF3RiwwQkFBMEIsc0NBQXNDLDZCQUE2QiwrRUFBK0UsMEJBQTBCLDhCQUE4Qix3QkFBd0Isc0RBQXNELGVBQWUsK0RBQStELDREQUE0RCwwQ0FBMEMsY0FBYyxTQUFTLGlCQUFpQixXQUFXLGVBQWUsZUFBZSxjQUFjLEVBQUUsS0FBSyxZQUFZLHNDQUFzQyxlQUFlLHVCQUF1QixrQ0FBa0MsWUFBWSxHQUFHLHNFQUFzRSxZQUFZLDhEQUE4RCx3SEFBd0gsNEVBQTRFLGNBQWMsWUFBWSxLQUFLLFdBQVcsZUFBZSxvQ0FBb0MsT0FBTyxFQUFFLHlDQUF5QyxPQUFPLEVBQUUsa0JBQWtCLHdEQUF3RCxJQUFJLHVCQUF1Qix5Q0FBeUMsT0FBTyw0Q0FBNEMsd0NBQXdDLHlFQUF5RSxxQ0FBcUMsSUFBSSw4Q0FBOEMsc0lBQXNJLFlBQVksMkJBQTJCLEVBQUUsTUFBTSxFQUFFLDhEQUE4RCwrQkFBK0Isb0NBQW9DLDBCQUEwQiwrRkFBK0YscUJBQXFCLDZCQUE2QixxSkFBcUosTUFBTSxXQUFXLGlDQUFpQyxxRUFBcUUsT0FBTyxTQUFTLEVBQUUsbUNBQW1DLCtDQUErQyxPQUFPLHdHQUF3RyxHQUFHLDhCQUE4QixJQUFJLHVCQUF1Qix5Q0FBeUMsaURBQWlELGdCQUFnQixJQUFJLCtCQUErQixzR0FBc0csd0JBQXdCLFdBQVcsT0FBTyx3Q0FBd0MsZ0NBQWdDLDZCQUE2Qiw4QkFBOEIsc0dBQXNHLHVEQUF1RCxXQUFXLHlCQUF5QiwyQkFBMkIsV0FBVyxPQUFPLHdDQUF3QyxnQ0FBZ0MsTUFBTSx5Q0FBeUMsd0JBQXdCLFdBQVcsTUFBTSxFQUFFLE9BQU8sRUFBRSxFQUFFLG1DQUFtQyxvQ0FBb0Msc0NBQXNDLGtEQUFrRCw0Q0FBNEMsRUFBRSwwQkFBMEIsU0FBUyxlQUFlLHdFQUF3RSw2QkFBNkIsZ05BQWdOLHlCQUF5Qix5UEFBeVAsV0FBVyxpQkFBaUIsb0JBQW9CLHNHQUFzRyxpRUFBaUUsU0FBUyxRQUFRLG1DQUFtQyxVQUFVLDZCQUE2QixhQUFhLFlBQVksS0FBSyxLQUFLLCtCQUErQiw2QkFBNkIsVUFBVSxTQUFTLGNBQWMsa0VBQWtFLFdBQVcsNkJBQTZCLFNBQVMsaUJBQWlCLDZCQUE2Qix5RUFBeUUsU0FBUyxTQUFTLGtCQUFrQixpQkFBaUIseURBQXlELEtBQUssa0NBQWtDLGtGQUFrRixzSUFBc0ksZUFBZSw0RUFBNEUsY0FBYywrSEFBK0gsWUFBWSxXQUFXLCtFQUErRSxVQUFVLDJFQUEyRSxTQUFTLGNBQWMseURBQXlELGFBQWEsb0dBQW9HLHdHQUF3RyxrQ0FBa0MsR0FBRyxJQUFJLGlCQUFpQixNQUFNLGtCQUFrQiwrREFBK0QsZ0JBQWdCLGVBQWUsa0RBQWtELGtGQUFrRixlQUFlLFNBQVMsRUFBRSxtQ0FBbUMsWUFBWSwyQ0FBMkMsbUJBQW1CLFVBQVUsSUFBSSxPQUFPLFdBQVcsa0JBQWtCLGlCQUFpQixLQUFLLHNFQUFzRSxZQUFZLGVBQWUsU0FBUyxRQUFRLHVDQUF1QyxpQ0FBaUMsaUJBQWlCLE1BQU0sSUFBSSxtQ0FBbUMsNkJBQTZCLFdBQVcsS0FBSyxlQUFlLGlCQUFpQiwyQkFBMkIsK0VBQStFLFVBQVUsYUFBYSxtQkFBbUIsTUFBTSxJQUFJLDRCQUE0QixVQUFVLFNBQVMseUJBQXlCLFNBQVMsMENBQTBDLE1BQU0sZUFBZSwyQ0FBMkMsMEJBQTBCLFNBQVMsU0FBUyx1REFBdUQsY0FBYyxZQUFZLGdHQUFnRyxTQUFTLFFBQVEsc0JBQXNCLElBQUksa0JBQWtCLFNBQVMsU0FBUyxhQUFhLHdCQUF3QixvQkFBb0IsU0FBUyxhQUFhLFNBQVMsd0JBQXdCLHlEQUF5RCxTQUFTLEtBQUssNkNBQTZDLHNDQUFzQyxtQkFBbUIsa0NBQWtDLGlCQUFpQixJQUFJLGdEQUFnRCxTQUFTLFlBQVksWUFBWSxtQkFBbUIsSUFBSSxpQ0FBaUMsVUFBVSxZQUFZLG1CQUFtQixJQUFJLHFEQUFxRCxTQUFTLGFBQWEsb0JBQW9CLElBQUksc0NBQXNDLFNBQVMsY0FBYyx5Q0FBeUMsT0FBTyxFQUFFLGtCQUFrQixJQUFJLFNBQVMsSUFBSSxrQkFBa0IsVUFBVSxtQ0FBbUMsSUFBSSxxQkFBcUIsVUFBVSxZQUFZLDJCQUEyQixJQUFJLDRCQUE0QixTQUFTLGdCQUFnQixrQkFBa0IsOENBQThDLFNBQVMsYUFBYSxzQkFBc0IsSUFBSSxxREFBcUQsU0FBUyxjQUFjLE9BQU8sS0FBSyx3QkFBd0IsU0FBUyxtQkFBbUIsbUNBQW1DLGlCQUFpQixtQkFBbUIsbUJBQW1CLG1CQUFtQixtQkFBbUIsUUFBUSxvQkFBb0IsY0FBYyxhQUFhLHdCQUF3QixvQkFBb0Isb0JBQW9CLFNBQVMsb0JBQW9CLDBEQUEwRCxTQUFTLE1BQU0sVUFBVSxtQkFBbUIsb0NBQW9DLGlCQUFpQixJQUFJLGtEQUFrRCxTQUFTLFlBQVksWUFBWSxtQkFBbUIsSUFBSSxtQ0FBbUMsVUFBVSxZQUFZLG1CQUFtQixJQUFJLHVEQUF1RCxTQUFTLGFBQWEsb0JBQW9CLElBQUksd0NBQXdDLFNBQVMsY0FBYyxxQ0FBcUMsMENBQTBDLDhCQUE4QixrQkFBa0Isb0JBQW9CLElBQUksY0FBYyxTQUFTLFNBQVMsU0FBUywyQkFBMkIsZ0JBQWdCLDBFQUEwRSwwQkFBMEIsK0lBQStJLGFBQWEsa0JBQWtCLHFFQUFxRSxXQUFXLEtBQUssc0JBQXNCLGFBQWEsSUFBSSxPQUFPLDhCQUE4QixXQUFXLElBQUksd0JBQXdCLFNBQVMsMkRBQTJELDRCQUE0QixvQkFBb0IsOEJBQThCLG9HQUFvRyxXQUFXLEtBQUssc0JBQXNCLHlEQUF5RCxRQUFRLDJEQUEyRCxrQkFBa0Isd0NBQXdDLG1CQUFtQiwwckJBQTByQixpSUFBaUksc0NBQXNDLG1DQUFtQyxxQkFBcUIsOFpBQThaLEtBQUssMGdCQUEwZ0IsdUJBQXVCLGVBQWUsZ0lBQWdJLHNEQUFzRCxzRUFBc0Usc0NBQXNDLFlBQVksdUJBQXVCLHNDQUFzQyxjQUFjLDhEQUE4RCxZQUFZLDRCQUE0Qix1QkFBdUIsY0FBYywwREFBMEQsU0FBUywyREFBMkQsb0VBQW9FLFlBQVksNEJBQTRCLHVCQUF1QixjQUFjLDZDQUE2QyxzQkFBc0IsWUFBWSw0QkFBNEIsaUJBQWlCLFNBQVMsMkZBQTJGLHVWQUF1VixFQUFFLFlBQVksRUFBRSxnT0FBZ08sSUFBSSwyUEFBMlAsNFFBQTRRLDBCQUEwQixJQUFJLGtGQUFrRiwyREFBMkQsZUFBZSwwQkFBMEIscURBQXFELHNDQUFzQyw0QkFBNEIsd0JBQXdCLGNBQWMsYUFBYSxLQUFLLDJCQUEyQixzTEFBc0wscUNBQXFDLG9EQUFvRCxVQUFVLG1CQUFtQiw0QkFBNEIsMkJBQTJCLFNBQVMsdUJBQXVCLGdDQUFnQyw0Q0FBNEMseUNBQXlDLHNCQUFzQixvQkFBb0IsWUFBWSxXQUFXLEtBQUssd0JBQXdCLHNDQUFzQyxZQUFZLDRCQUE0QixrREFBa0Qsa0NBQWtDLDZCQUE2QixnREFBZ0QsZ0JBQWdCLFlBQVksWUFBWSxLQUFLLGdEQUFnRCxjQUFjLGNBQWMsa0NBQWtDLFlBQVksMElBQTBJLHFCQUFxQiw2Q0FBNkMsNEJBQTRCLE1BQU0sNkZBQTZGLHlCQUF5QixPQUFPLG9FQUFvRSw4QkFBOEIsT0FBTyw2RUFBNkUsd0NBQXdDLE9BQU8sUUFBUSxNQUFNLGdJQUFnSSxNQUFNLHlCQUF5QixZQUFZLG9JQUFvSSxzQkFBc0IsR0FBRyxvREFBb0Qsc0JBQXNCLEdBQUcsU0FBUyxxQkFBcUIsSUFBSSx3REFBd0QsU0FBUyxRQUFRLDJCQUEyQixJQUFJLHFDQUFxQyxTQUFTLFFBQVEsdUJBQXVCLElBQUksNERBQTRELDJEQUEyRCxlQUFlLHlDQUF5QyxhQUFhLDRLQUE0SyxHQUFHLHNvQkFBc29CLEVBQUUsOEJBQThCLGVBQWUsa0JBQWtCLGFBQWEsNERBQTRELEdBQUcsdURBQXVELEdBQUcsOEVBQThFLFNBQVMsZUFBZSwyQkFBMkIsOENBQThDLFNBQVMsc0pBQXNKLDBHQUEwRyxrREFBa0QsZ0JBQWdCLHVHQUF1RywyRUFBMkUsa0NBQWtDLGtNQUFrTSxhQUFhLFNBQVMsbUNBQW1DLDZDQUE2QyxXQUFXLHdDQUF3Qyw2RUFBNkUseUJBQXlCLGlCQUFpQixVQUFVLEtBQUssT0FBTyxtQkFBbUIsb0NBQW9DLG1CQUFtQixNQUFNLE9BQU8sMEhBQTBILFNBQVMsb0VBQW9FLFFBQVEsNEJBQTRCLHFCQUFxQixTQUFTLDZEQUE2RCxTQUFTLGdCQUFnQix3RUFBd0UsMkJBQTJCLFNBQVMsY0FBYyxTQUFTLDRDQUE0QyxTQUFTLGdCQUFnQiwrREFBK0QsMkJBQTJCLFNBQVMsY0FBYyxvREFBb0QseUJBQXlCLGdDQUFnQyx5QkFBeUIscU1BQXFNLEVBQUUsMERBQTBELHdCQUF3QiwrQkFBK0IsdUJBQXVCLDZDQUE2QywwQkFBMEIsb0RBQW9ELDRCQUE0QixTQUFTLG9CQUFvQixXQUFXLDhFQUE4RSxFQUFFLG9CQUFvQixTQUFTLGVBQWUsb0JBQW9CLHNCQUFzQixzQkFBc0IsR0FBRyxHQUFHLHFCQUFxQixNQUFNLHVDQUF1QyxPQUFPLFNBQVMsY0FBYyxtQ0FBbUMsaUJBQWlCLEtBQUssbUJBQW1CLHdOQUF3Tix3Q0FBd0Msc0RBQXNELGdCQUFnQix3REFBd0QsdUJBQXVCLDZFQUE2RSxzQkFBc0IsNkJBQTZCLGNBQWMsMkRBQTJELHFCQUFxQix5QkFBeUIsb0NBQW9DLHNCQUFzQix5QkFBeUIsS0FBSyxzREFBc0QsZ0JBQWdCLHFCQUFxQixrQkFBa0IsNkJBQTZCLGVBQWUsUUFBUSxzREFBc0QsU0FBUyxLQUFLLGlDQUFpQyxTQUFTLGdEQUFnRCxTQUFTLG9DQUFvQyxlQUFlLHVFQUF1RSwrQkFBK0Isd0VBQXdFLFdBQVcsNERBQTRELE1BQU0sNERBQTRELG1CQUFtQixVQUFVLHlPQUF5Tyx3akJBQXdqQixFQUFFLGVBQWUsTUFBTSwySEFBMkgsZUFBZSwwQkFBMEIsaUJBQWlCLGtFQUFrRSxlQUFlLDBCQUEwQixlQUFlLDJCQUEyQixlQUFlLDhCQUE4QixlQUFlLHFDQUFxQyx3Q0FBd0MsZUFBZSxTQUFTLDJCQUEyQiwyQkFBMkIsTUFBTSw0R0FBNEcsTUFBTSxhQUFhLGFBQWEsZUFBZSw4SEFBOEgsZUFBZSxTQUFTLHFGQUFxRixNQUFNLHlFQUF5RSxRQUFRLGVBQWUsNkhBQTZILE1BQU0saUVBQWlFLGVBQWUsbUJBQW1CLHVCQUF1QixpQkFBaUIsc0pBQXNKLHlDQUF5QywyREFBMkQsNERBQTRELG1DQUFtQyw0QkFBNEIsd0NBQXdDLDRCQUE0Qiw0QkFBNEIsbUVBQW1FLEtBQUssb0JBQW9CLGtEQUFrRCxLQUFLLDRCQUE0QixvREFBb0QsY0FBYyxrQkFBa0IseUNBQXlDLGlCQUFpQixrQkFBa0IsbURBQW1ELE9BQU8sZ0JBQWdCLFdBQVcsaUNBQWlDLFNBQVMsZUFBZSxrQkFBa0IsOENBQThDLGlCQUFpQixrQkFBa0Isb0NBQW9DLE9BQU8sZ0JBQWdCLFdBQVcsaUNBQWlDLFNBQVMsZUFBZSw0QkFBNEIsMEVBQTBFLDhCQUE4QixVQUFVLG9CQUFvQixvREFBb0QseUZBQXlGLHNKQUFzSixlQUFlLFlBQVksNEJBQTRCLGdCQUFnQixZQUFZLHNEQUFzRCxrQ0FBa0MsaUJBQWlCLGdDQUFnQyxxQ0FBcUMsdUJBQXVCLFNBQVMsdUtBQXVLLFNBQVMsZUFBZSxnS0FBZ0ssZ0JBQWdCLEdBQUcsU0FBUyxFQUFFLGlCQUFpQixHQUFHLGtCQUFrQixFQUFFLE9BQU8sR0FBRyxjQUFjLEdBQUcsOEJBQThCLEdBQUcsd0JBQXdCLEVBQUUsT0FBTyxHQUFHLG1FQUFtRSxFQUFFLE1BQU0sRUFBRSxNQUFNLEVBQUUsd0VBQXdFLGVBQWUsaUVBQWlFLGlCQUFpQixTQUFTLCtEQUErRCw4QkFBOEIsU0FBUyxlQUFlLFlBQVksd0RBQXdELGVBQWUsU0FBUyx5RUFBeUUsTUFBTSw2RUFBNkUsWUFBWSxpSEFBaUgsU0FBUyw0QkFBNEIsS0FBSyxlQUFlLG1CQUFtQixpQkFBaUIsYUFBYSw2SEFBNkgsZ0JBQWdCLFNBQVMsWUFBWSxHQUFHLElBQUksRUFBRSwwR0FBMEcsU0FBUyxZQUFZLEdBQUcsYUFBYSxrQkFBa0IsR0FBRyxxQkFBcUIsMEJBQTBCLGVBQWUsMEJBQTBCLGdCQUFnQixXQUFXLDRDQUE0QywyQkFBMkIsV0FBVyxzQ0FBc0MsWUFBWSxHQUFHLGdCQUFnQixHQUFHLGFBQWEsa0JBQWtCLFdBQVcsb1BBQW9QLHdCQUF3QixTQUFTLCtCQUErQixnQkFBZ0IsV0FBVyx5QkFBeUIsS0FBSyxHQUFHLEtBQUssZUFBZSxpQ0FBaUMsZUFBZSxvQkFBb0IsNkJBQTZCLDhDQUE4QywrZUFBK2Usa1ZBQWtWLHFCQUFxQixpQkFBaUIsd0RBQXdELDBCQUEwQixJQUFJLHNHQUFzRyxpQkFBaUIsaUJBQWlCLFFBQVEsb0RBQW9ELFFBQVEsMElBQTBJLEtBQUssZUFBZSxhQUFhLGdNQUFnTSw4YUFBOGEsa0JBQWtCLDRGQUE0RixnQkFBZ0Isa0RBQWtELG9EQUFvRCwwREFBMEQsT0FBTyx3Q0FBd0MsbUJBQW1CLFFBQVEsK0NBQStDLHNQQUFzUCxnREFBZ0QsV0FBVyxFQUFFLGFBQWEsTUFBTSxFQUFFLDBCQUEwQixFQUFFLDBFQUEwRSxpQkFBaUIseUlBQXlJLGdCQUFnQix5R0FBeUcsZUFBZSxVQUFVLEVBQUUseUVBQXlFLEdBQUcsdUJBQXVCLDRVQUE0VSxTQUFTLGVBQWUsb0VBQW9FLDRFQUE0RSwyQkFBMkIsK0JBQStCLCtDQUErQyx5SUFBeUksR0FBRyx5Q0FBeUMsbUNBQW1DLDhEQUE4RCx1QkFBdUIsMENBQTBDLFlBQVksaUNBQWlDLG9CQUFvQiw0VEFBNFQsbUNBQW1DLDBDQUEwQyx1Q0FBdUMscURBQXFELG1EQUFtRCx3Q0FBd0MsK1NBQStTLFNBQVMsSUFBSSw4QkFBOEIsNkJBQTZCLDJTQUEyUyxxQkFBcUIsUUFBUSwrUkFBK1IseUJBQXlCLEtBQUssc0JBQXNCLG9CQUFvQixRQUFRLEtBQUssbUJBQW1CLFNBQVMscUJBQXFCLFlBQVksK0JBQStCLHNCQUFzQiw2Q0FBNkMsU0FBUyxNQUFNLG9DQUFvQyxzR0FBc0csdUJBQXVCLG1DQUFtQyxJQUFJLGdCQUFnQixrQkFBa0IsUUFBUSxJQUFJLDBCQUEwQixNQUFNLGVBQWUsV0FBVyxRQUFRLElBQUksdUVBQXVFLEtBQUssbUJBQW1CLHlCQUF5QixJQUFJLG1DQUFtQyxTQUFTLGdCQUFnQixTQUFTLE9BQU8sY0FBYyxXQUFXLE9BQU8sY0FBYyxXQUFXLE9BQU8sY0FBYyxXQUFXLE9BQU8sY0FBYyxrQkFBa0IsU0FBUyxNQUFNLGNBQWMsK0NBQStDLHdCQUF3QixvQkFBb0Isb0NBQW9DLHlEQUF5RCxnQ0FBZ0Msb0JBQW9CLFFBQVEsYUFBYSxzQkFBc0Isb0JBQW9CLFFBQVEsYUFBYSxxQ0FBcUMsa0JBQWtCLGlCQUFpQixXQUFXLGtCQUFrQixXQUFXLEVBQUUsMkJBQTJCLDBCQUEwQixTQUFTLHVCQUF1Qix5QkFBeUIsc0JBQXNCLGVBQWUsV0FBVyxhQUFhLEVBQUUsOEJBQThCLFVBQVUsd0JBQXdCLE9BQU8scUVBQXFFLHdCQUF3QixhQUFhLFFBQVEsSUFBSSx5QkFBeUIsbUNBQW1DLFFBQVEsSUFBSSxxQkFBcUIsMEJBQTBCLDhCQUE4QixHQUFHLElBQUksS0FBSyxhQUFhLG1CQUFtQix3QkFBd0IsV0FBVyxJQUFJLEVBQUUsYUFBYSw0QkFBNEIsS0FBSyxRQUFRLEtBQUssYUFBYSxzQkFBc0IsSUFBSSxvQkFBb0Isb0JBQW9CLDZEQUE2RCxnQkFBZ0IsbUJBQW1CLFdBQVcsRUFBRSxtREFBbUQsU0FBUyxLQUFLLEtBQUsseUJBQXlCLEtBQUssWUFBWSxLQUFLLE1BQU0sZ0JBQWdCLDhDQUE4QyxhQUFhLGFBQWEsSUFBSSxhQUFhLDBCQUEwQixLQUFLLElBQUksTUFBTSxXQUFXLDBCQUEwQixrQkFBa0IsZ0JBQWdCLFdBQVcsaUJBQWlCLFNBQVMsb0JBQW9CLHlCQUF5QiwwREFBMEQsWUFBWSxJQUFJLGtCQUFrQixpQkFBaUIsb0NBQW9DLHFCQUFxQix1SEFBdUgsV0FBVyxpQkFBaUIsUUFBUSxXQUFXLGlCQUFpQixxQ0FBcUMsaUJBQWlCLEtBQUssd0dBQXdHLCtDQUErQyw2QkFBNkIsZ0NBQWdDLGdCQUFnQixpREFBaUQsUUFBUSxJQUFJLHlCQUF5QixPQUFPLG9CQUFvQixJQUFJLEtBQUssV0FBVyxRQUFRLFdBQVcsS0FBSyxjQUFjLGlFQUFpRSx5QkFBeUIsUUFBUSxJQUFJLGlCQUFpQixlQUFlLGtFQUFrRSxjQUFjLDJEQUEyRCxnQ0FBZ0MsK0JBQStCLDhGQUE4RiwwQkFBMEIsTUFBTSxLQUFLLGdCQUFnQixJQUFJLDJCQUEyQixPQUFPLFNBQVMsaUJBQWlCLGlCQUFpQixPQUFPLGNBQWMsZ0JBQWdCLFdBQVcsMkJBQTJCLElBQUksY0FBYyxzQkFBc0Isd0JBQXdCLDZCQUE2QixzRkFBc0YsdUJBQXVCLEtBQUssVUFBVSxjQUFjLGlFQUFpRSxLQUFLLGtIQUFrSCxtQ0FBbUMscUVBQXFFLElBQUksS0FBSyw0QkFBNEIsdUJBQXVCLFVBQVUsNEJBQTRCLDBDQUEwQyxZQUFZLE1BQU0sV0FBVyxRQUFRLEtBQUssV0FBVyw0QkFBNEIsa0ZBQWtGLGdCQUFnQixFQUFFLHFCQUFxQixZQUFZLHNCQUFzQixLQUFLLFFBQVEscUJBQXFCLDBCQUEwQixRQUFRLElBQUksS0FBSyw2Q0FBNkMsaUJBQWlCLDhCQUE4QixNQUFNLGlDQUFpQywwQkFBMEIsMkNBQTJDLDRCQUE0Qiw4QkFBOEIseUJBQXlCLG1IQUFtSCxvQkFBb0IsS0FBSyxFQUFFLGtCQUFrQixrQkFBa0IsaUJBQWlCLHVJQUF1SSxPQUFPLFlBQVksWUFBWSw2QkFBNkIsZ0JBQWdCLGdEQUFnRCxpQkFBaUIsaUJBQWlCLEVBQUUsc0JBQXNCLE9BQU8saUNBQWlDLDZDQUE2QyxpQkFBaUIsZUFBZSxxRUFBcUUsNERBQTRELFNBQVMsS0FBSyxJQUFJLEtBQUssaUJBQWlCLDJCQUEyQixhQUFhLHNCQUFzQixxTkFBcU4saUJBQWlCLHdLQUF3Syw4Q0FBOEMsRUFBRSxlQUFlLHFCQUFxQixpQkFBaUIsV0FBVyxFQUFFLGFBQWEsd1FBQXdRLFNBQVMsK0JBQStCLGVBQWUsc0RBQXNELGlCQUFpQixvRUFBb0UsU0FBUyx1Q0FBdUMsNkVBQTZFLGtCQUFrQixRQUFRLCtHQUErRyxjQUFjLDRCQUE0QixxQkFBcUIscUdBQXFHLG9DQUFvQyxpQ0FBaUMsUUFBUSxrTEFBa0wsZ0JBQWdCLHFDQUFxQyxtQkFBbUIsc0JBQXNCLHlIQUF5SCx3QkFBd0IsdURBQXVELDRCQUE0Qix5QkFBeUIseUZBQXlGLGVBQWUsaUNBQWlDLDJGQUEyRixtQkFBbUIsc0JBQXNCLHdhQUF3YSxPQUFPLDhGQUE4Riw0QkFBNEIsNENBQTRDLGdNQUFnTSxxVUFBcVUsZ0JBQWdCLGlEQUFpRCxRQUFRLGlCQUFpQixxQ0FBcUMsbUJBQW1CLHNCQUFzQixxQ0FBcUMsbUJBQW1CLGFBQWEsNFFBQTRRLHVCQUF1QixNQUFNLCtLQUErSyx5QkFBeUIsTUFBTSxpTEFBaUwsb0JBQW9CLDBMQUEwTCxvRUFBb0UsZUFBZSxnakJBQWdqQixvQkFBb0IsNkJBQTZCLDRDQUE0QyxFQUFFLDRCQUE0QiwyQ0FBMkMsRUFBRSxzQ0FBc0MsK0JBQStCLGtDQUFrQyw0QkFBNEIsR0FBRyx3UUFBd1Esa0pBQWtKLElBQUksdUJBQXVCLHFEQUFxRCxxV0FBcVcsd0JBQXdCLHNjQUFzYyxJQUFJLDBCQUEwQixvR0FBb0csYUFBYSxxQ0FBcUMsT0FBTyxHQUFHLFNBQVMsd0RBQXdELHFHQUFxRyxZQUFZLGtEQUFrRCxzQ0FBc0MsNkxBQTZMLDBCQUEwQixnQkFBZ0IsWUFBWSw4MUJBQTgxQix3QkFBd0IsNkNBQTZDLFNBQVMsa0NBQWtDLHVCQUF1QixTQUFTLCtNQUErTSx5QkFBeUIsaUVBQWlFLE9BQU8sRUFBRSxLQUFLLE1BQU0sOERBQThELFVBQVUsR0FBRyxrQkFBa0IsZ0JBQWdCLCtTQUErUyxzRUFBc0UscUhBQXFILHNDQUFzQyxrQ0FBa0MsOENBQThDLDZCQUE2Qix5QkFBeUIsR0FBRyxtWUFBbVksaUJBQWlCLDBIQUEwSCw2QkFBNkIsSUFBSSx3QkFBd0IsOEJBQThCLHlDQUF5QywrTEFBK0wsWUFBWSxxSEFBcUgsd0NBQXdDLGlGQUFpRixrTkFBa04sOEZBQThGLEdBQUcsdUpBQXVKLE9BQU8scUVBQXFFLHNFQUFzRSxhQUFhLGtDQUFrQyw4Q0FBOEMsc0JBQXNCLEVBQUUsaUJBQWlCLG1KQUFtSixRQUFRLHVIQUF1SCxrSkFBa0osa0RBQWtELHVCQUF1QixHQUFHLDZGQUE2RixpQkFBaUIsVUFBVSwwSkFBMEosc0JBQXNCLE1BQU0sa0ZBQWtGLDhCQUE4QixpQ0FBaUMsNkxBQTZMLDhMQUE4TCx1QkFBdUIsU0FBUywySEFBMkgsbUNBQW1DLHVCQUF1QixJQUFJLHVCQUF1Qiw0R0FBNEcsOElBQThJLG1CQUFtQixJQUFJLDBCQUEwQixTQUFTLHFFQUFxRSxrRkFBa0Ysd0RBQXdELHdCQUF3Qiw0RUFBNEUsdUJBQXVCLDJFQUEyRSxrQkFBa0IsU0FBUyxtTEFBbUwsWUFBWSxxQ0FBcUMsaUVBQWlFLDBDQUEwQyxtQ0FBbUMsd0RBQXdELFlBQVksWUFBWSxzRkFBc0YsZ0NBQWdDLG1CQUFtQix1REFBdUQsb0JBQW9CLG9HQUFvRywwTEFBMEwsNEhBQTRILFdBQVcsT0FBTyw2QkFBNkIsRUFBRSxpQ0FBaUMsbUNBQW1DLFNBQVMsb0JBQW9CLFdBQVcsdUdBQXVHLDBFQUEwRSw2Q0FBNkMsNEJBQTRCLEVBQUUsc0hBQXNILHdCQUF3QixpRkFBaUYsc0NBQXNDLDhDQUE4Qyw0QkFBNEIsT0FBTyxzQkFBc0IseUlBQXlJLGdEQUFnRCw0SEFBNEgsK09BQStPLFNBQVMsZUFBZSxNQUFNLHdDQUF3QyxxQkFBcUIsaUNBQWlDLDhCQUE4QixjQUFjLDhCQUE4Qix5RUFBeUUsMkRBQTJELEtBQUssNElBQTRJLCtFQUErRSxNQUFNLGtCQUFrQixRQUFRLHNFQUFzRSw0SUFBNEksaUJBQWlCLGdDQUFnQyxzS0FBc0ssa0NBQWtDLFNBQVMsSUFBSSwyREFBMkQsVUFBVSxxQkFBcUIsTUFBTSw0Q0FBNEMsaUJBQWlCLG1HQUFtRyx1QkFBdUIsSUFBSSx5Q0FBeUMsTUFBTSxFQUFFLDZCQUE2QixFQUFFLDZFQUE2RSxNQUFNLEVBQUUsd0JBQXdCLFdBQVcsRUFBRSx3R0FBd0csRUFBRSxFQUFFLG1GQUFtRixNQUFNLEVBQUUsd0JBQXdCLFdBQVcsRUFBRSxnREFBZ0QsRUFBRSxFQUFFLFNBQVMscUVBQXFFLFNBQVMsU0FBUyw0RkFBNEYsaUZBQWlGLG9CQUFvQiw4Q0FBOEMscUNBQXFDLDZEQUE2RCxPQUFPLHFCQUFxQix1QkFBdUIsa0NBQWtDLGtDQUFrQyx5Q0FBeUMsc0RBQXNELElBQUksWUFBWSw2Q0FBNkMsMkJBQTJCLFNBQVMsT0FBTyx1RUFBdUUsU0FBUyxjQUFjLG9CQUFvQiwrREFBK0QsYUFBYSxlQUFlLDBGQUEwRiw4RUFBOEUsc0dBQXNHLG9CQUFvQixtQkFBbUIsd0RBQXdELHVCQUF1QixpSUFBaUksRUFBRSxHQUFHLDBCQUEwQiw0QkFBNEIseUVBQXlFLGlTQUFpUyxvQkFBb0IsUUFBUSxvQkFBb0IscUNBQXFDLHNJQUFzSSxFQUFFLDZCQUE2QixnQ0FBZ0MsMEVBQTBFLG1IQUFtSCxvQkFBb0IsUUFBUSxpSUFBaUksaUNBQWlDLGlFQUFpRSxrQkFBa0IsK0tBQStLLHFCQUFxQixHQUFHLG1CQUFtQixtSkFBbUosMEJBQTBCLGdDQUFnQywyQkFBMkIsbUpBQW1KLElBQUksc0VBQXNFLFNBQVMsZ0RBQWdELElBQUkscUJBQXFCLGtHQUFrRyxtQkFBbUIsTUFBTSw4REFBOEQsUUFBUSx5RUFBeUUsbUJBQW1CLHlCQUF5QixvQkFBb0Isb0NBQW9DLGtDQUFrQyxtSEFBbUgsMkJBQTJCLFNBQVMsZUFBZSxnQkFBZ0IsbUJBQW1CLFFBQVEsNkhBQTZILHVCQUF1QixVQUFVLHdUQUF3VCx5QkFBeUIsNkJBQTZCLHVJQUF1SSxXQUFXLEVBQUUsT0FBTyxJQUFJLG1JQUFtSSxnSUFBZ0ksOEJBQThCLG1JQUFtSSx1QkFBdUIsR0FBRyx1QkFBdUIsR0FBRyxTQUFTLDJDQUEyQyxrQkFBa0IsMlJBQTJSLDJJQUEySSxhQUFhLHFGQUFxRixXQUFXLEdBQUcsU0FBUyxlQUFlLGdCQUFnQixtQkFBbUIscUJBQXFCLGtCQUFrQixNQUFNLG9FQUFvRSxtQkFBbUIsd01BQXdNLG1CQUFtQiwrRUFBK0UsSUFBSSxPQUFPLElBQUksd0VBQXdFLFNBQVMsU0FBUyxnQ0FBZ0MsNkpBQTZKLHNOQUFzTiwySEFBMkgsU0FBUyxVQUFVLG9CQUFvQiwwQ0FBMEMseUVBQXlFLGVBQWUsd0NBQXdDLGtCQUFrQixrSEFBa0gsaUNBQWlDLE1BQU0sRUFBRSxxREFBcUQsSUFBSSxjQUFjLEdBQUcsRUFBRSxrREFBa0QsTUFBTSxFQUFFLGNBQWMsZ0VBQWdFLG1EQUFtRCxRQUFRLHdLQUF3SywrRkFBK0YseURBQXlELGdDQUFnQywyREFBMkQsR0FBRyxTQUFTLGVBQWUsMkJBQTJCLGtCQUFrQixvTUFBb00scUNBQXFDLFNBQVMsZUFBZSx5RkFBeUYsV0FBVyw2REFBNkQsU0FBUyx5QkFBeUIsK0NBQStDLDRHQUE0RyxXQUFXLHVCQUF1QixNQUFNLEVBQUUsdUJBQXVCLEdBQUcsR0FBRyxTQUFTLHdDQUF3QyxrQkFBa0IsV0FBVyxtREFBbUQsaURBQWlELHVDQUF1QyxrQ0FBa0MsaUNBQWlDLG9EQUFvRCxxQkFBcUIscUJBQXFCLHdCQUF3QixvQkFBb0IseURBQXlELGNBQWMsU0FBUyx5QkFBeUIsdUNBQXVDLHFCQUFxQixNQUFNLEVBQUUsUUFBUSxvREFBb0Qsb0JBQW9CLG1CQUFtQixpQkFBaUIsNEVBQTRFLElBQUksS0FBSyw2QkFBNkIscUtBQXFLLDJDQUEyQyx3Q0FBd0MsNEJBQTRCLElBQUksbUNBQW1DLG9CQUFvQixZQUFZLEdBQUcsWUFBWSx1REFBdUQsaUZBQWlGLElBQUkscUJBQXFCLEdBQUcsTUFBTSxpQkFBaUIsb0JBQW9CLFFBQVEsa0JBQWtCLFFBQVEsRUFBRSxPQUFPLDRDQUE0QyxpQkFBaUIsa0JBQWtCLHNHQUFzRyxlQUFlLFFBQVEsR0FBRyw2SkFBNkosa0JBQWtCLGdDQUFnQyw0QkFBNEIsTUFBTSwyQkFBMkIsU0FBUyx5RUFBeUUsT0FBTyx5RkFBeUYsWUFBWSxPQUFPLHlFQUF5RSxPQUFPLFlBQVksNkJBQTZCLFNBQVMscUNBQXFDLGtDQUFrQyxlQUFlLDJCQUEyQixjQUFjLGlDQUFpQyxtQkFBbUIsTUFBTSxZQUFZLEdBQUcsK0RBQStELE9BQU8scUpBQXFKLDhDQUE4QyxTQUFTLDRCQUE0QixzQkFBc0IscUJBQXFCLFNBQVMsV0FBVywrQ0FBK0MsZ0JBQWdCLE1BQU0sd0RBQXdELG9CQUFvQixFQUFFLGtEQUFrRCxjQUFjLDJCQUEyQixjQUFjLGlDQUFpQyxJQUFJLHFCQUFxQixtQ0FBbUMsMkJBQTJCLHdCQUF3QiwySEFBMkgscUJBQXFCLFNBQVMseUNBQXlDLHNCQUFzQixrQ0FBa0MsVUFBVSxnREFBZ0QsWUFBWSxrQ0FBa0Msa0NBQWtDLGdCQUFnQixXQUFXLEVBQUUsSUFBSSxPQUFPLHFCQUFxQixrQ0FBa0Msb0NBQW9DLE9BQU8sSUFBSSxrQkFBa0IsWUFBWSxFQUFFLGlDQUFpQyxTQUFTLGVBQWUsK05BQStOLGlDQUFpQyxzQ0FBc0Msb0JBQW9CLElBQUksb0JBQW9CLElBQUksd0JBQXdCLGFBQWEsNEJBQTRCLGNBQWMscUNBQXFDLE1BQU0sRUFBRSxhQUFhLE1BQU0sNEZBQTRGLHdCQUF3QixxREFBcUQsR0FBRyxXQUFXLGlDQUFpQyw0QkFBNEIsa0JBQWtCLDRFQUE0RSxzQkFBc0IsbUJBQW1CLGlCQUFpQiwyQkFBMkIsRUFBRSx3REFBd0QseUdBQXlHLE9BQU8sb0VBQW9FLGdFQUFnRSx1QkFBdUIsUUFBUSw0RUFBNEUsbUNBQW1DLGlCQUFpQiw4QkFBOEIsU0FBUyxRQUFRLGlCQUFpQiwrRUFBK0Usa0NBQWtDLE1BQU0sRUFBRSx1QkFBdUIsR0FBRyxTQUFTLFdBQVcsZUFBZSwyQkFBMkIsU0FBUyxtQkFBbUIsTUFBTSxvSUFBb0ksa0pBQWtKLG1PQUFtTyx1R0FBdUcsNEZBQTRGLHNEQUFzRCxnTEFBZ0wsZ0dBQWdHLGlIQUFpSCxpRUFBaUUsV0FBVyx5QkFBeUIscURBQXFELCtDQUErQyxzQ0FBc0MsbUNBQW1DLDJFQUEyRSxTQUFTLDZDQUE2Qyw2QkFBNkIsdUJBQXVCLDhCQUE4QixlQUFlLDZLQUE2SyxrRkFBa0Ysd0JBQXdCLHdGQUF3RixnQkFBZ0IsMkdBQTJHLGVBQWUsNkdBQTZHLHFCQUFxQix1TUFBdU0sYUFBYSxHQUFHLGdCQUFnQixtS0FBbUssaUNBQWlDLHNEQUFzRCxpQkFBaUIsbUNBQW1DLHdCQUF3QixpREFBaUQsaUZBQWlGLEdBQUcsZ0NBQWdDLDRIQUE0SCxzS0FBc0ssc0tBQXNLLHNHQUFzRyxxQ0FBcUMsNERBQTRELGtEQUFrRCxzREFBc0Qsc0lBQXNJLDJEQUEyRCxZQUFZLCtEQUErRCwyREFBMkQsaUNBQWlDLGlCQUFpQixpRkFBaUYsc0JBQXNCLDhCQUE4QixhQUFhLHNDQUFzQyxXQUFXLEdBQUcsdUJBQXVCLFNBQVMsZUFBZSx3QkFBd0Isa0JBQWtCLGNBQWMsNkRBQTZELGlFQUFpRSxhQUFhLHVGQUF1RiwySEFBMkgsYUFBYSwwVUFBMFUsZUFBZSxnRUFBZ0UsbUVBQW1FLGlEQUFpRCxXQUFXLFVBQVUsdUVBQXVFLCtEQUErRCxvQkFBb0IsZUFBZSxJQUFJLGtFQUFrRSwyREFBMkQsV0FBVyxjQUFjLHlEQUF5RCxrQkFBa0IsRUFBRSxrSEFBa0gsdUhBQXVILGlCQUFpQixHQUFHLCtEQUErRCxXQUFXLHdDQUF3QyxHQUFHLE9BQU8seWFBQXlhLHdKQUF3SixTQUFTLHVCQUF1Qix3Q0FBd0MsUUFBUSx3REFBd0QsS0FBSyxpQ0FBaUMsaUJBQWlCLHVCQUF1QixpQkFBaUIsZUFBZSx1QkFBdUIsaUJBQWlCLFVBQVUsZ0NBQWdDLDhDQUE4QyxrQ0FBa0MsbURBQW1ELFlBQVksNkNBQTZDLFNBQVMsZ0JBQWdCLHlIQUF5SCxjQUFjLE1BQU0sZUFBZSxzUEFBc1Asb0ZBQW9GLFdBQVcsMENBQTBDLDBCQUEwQixPQUFPLDJNQUEyTSxpQkFBaUIsSUFBSSxHQUFHLEdBQUcsU0FBUyxlQUFlLGlCQUFpQixnQkFBZ0IsOENBQThDLDhCQUE4QixpR0FBaUcsK0NBQStDLGVBQWUsTUFBTSw0R0FBNEcsVUFBVSxNQUFNLE9BQU8sK0VBQStFLGlDQUFpQyw0QkFBNEIsYUFBYSxnQkFBZ0IsT0FBTyw4Q0FBOEMsNkNBQTZDLHdEQUF3RCxJQUFJLDZGQUE2RixHQUFHLDBDQUEwQywwRkFBMEYsd0ZBQXdGLE9BQU8sb1JBQW9SLEdBQUcsOElBQThJLGlDQUFpQywrRkFBK0YsaUJBQWlCLHlCQUF5QixTQUFTLGVBQWUsbUJBQW1CLG9CQUFvQix1QkFBdUIsT0FBTyxtSUFBbUksOENBQThDLGVBQWUsa0JBQWtCLGlCQUFpQiw0QkFBNEIsaUJBQWlCLDZDQUE2QyxVQUFVLGVBQWUsb0RBQW9ELDJDQUEyQyxlQUFlLFFBQVEsZ0NBQWdDLDhCQUE4QixlQUFlLHdDQUF3Qyx1QkFBdUIsTUFBTSxjQUFjLGVBQWUsNEdBQTRHLGNBQWMsc0JBQXNCLGdCQUFnQixhQUFhLGdGQUFnRiw0TUFBNE0sU0FBUyxtQ0FBbUMsd0NBQXdDLGtDQUFrQyxJQUFJLG9YQUFvWCxnQkFBZ0IsbUJBQW1CLDRDQUE0QyxpQkFBaUIsSUFBSSwyTUFBMk0sV0FBVyw0YkFBNGIsYUFBYSxJQUFJLDZFQUE2RSxtQkFBbUIsUUFBUSw4QkFBOEIsZ0JBQWdCLGNBQWMscUNBQXFDLFNBQVMsMEVBQTBFLHFCQUFxQix3Q0FBd0MsOENBQThDLDJDQUEyQyxpQkFBaUIsRUFBRSxTQUFTLDhEQUE4RCxJQUFJLGVBQWUsOERBQThELEtBQUssU0FBUyxtQkFBbUIsaUdBQWlHLHVCQUF1QixNQUFNLHlEQUF5RCxLQUFLLHVGQUF1RixtREFBbUQsNEpBQTRKLDZHQUE2RyxLQUFLLDBEQUEwRCw0SkFBNEosa0JBQWtCLFFBQVEsVUFBVSw4RkFBOEYsZUFBZSx3QkFBd0IsUUFBUSxtQkFBbUIsb0JBQW9CLGlDQUFpQyxlQUFlLGlEQUFpRCxpQ0FBaUMsMkNBQTJDLHNDQUFzQywrREFBK0QsOEJBQThCLDJQQUEyUCxnSkFBZ0osc09BQXNPLEtBQUssaU5BQWlOLHlHQUF5RyxZQUFZLE9BQU8sZUFBZSx5QkFBeUIsaUNBQWlDLFFBQVEsaUhBQWlILDRCQUE0QixHQUFHLDRGQUE0Riw2RUFBNkUsZUFBZSx5QkFBeUIsU0FBUyxRQUFRLHFFQUFxRSxxQkFBcUIsa0RBQWtELGdRQUFnUSxTQUFTLDZHQUE2RyxzRkFBc0Ysb0JBQW9CLG1CQUFtQixhQUFhLFlBQVksV0FBVywyQkFBMkIsd0NBQXdDLElBQUkscUNBQXFDLFVBQVUsR0FBRyxTQUFTLGlCQUFpQixHQUFHLCtCQUErQiwrQ0FBK0MscUNBQXFDLFdBQVcsOEVBQThFLGNBQWMsTUFBTSxZQUFZLDhDQUE4QywyR0FBMkcsNkNBQTZDLEtBQUssZ0VBQWdFLEtBQUssc0JBQXNCLHdDQUF3Qyx3R0FBd0csb0tBQW9LLDBGQUEwRixnSUFBZ0ksSUFBSSxzQkFBc0IseUpBQXlKLFNBQVMsbUJBQW1CLElBQUksc0NBQXNDLFNBQVMsYUFBYSxtQkFBbUIsUUFBUSxzR0FBc0csOEJBQThCLHlCQUF5QixTQUFTLFlBQVksK0JBQStCLG1CQUFtQixXQUFXLGtEQUFrRCxrREFBa0QsbUJBQW1CLDZCQUE2QixnQkFBZ0Isc0JBQXNCLGNBQWMsT0FBTyx5QkFBeUIsbUtBQW1LLDRCQUE0QixTQUFTLElBQUksU0FBUywwQ0FBMEMsTUFBTSwrREFBK0QsNkNBQTZDLDhFQUE4RSxzQ0FBc0MscURBQXFELHVJQUF1SSwyQkFBMkIsZUFBZSxhQUFhLGVBQWUsT0FBTyw4Q0FBOEMscUJBQXFCLHNCQUFzQixRQUFRLHdDQUF3QywwQ0FBMEMsU0FBUyx3Q0FBd0MsZ0RBQWdELGVBQWUsR0FBRyxzQkFBc0IsVUFBVSw2QkFBNkIsa0NBQWtDLHVDQUF1QyxlQUFlLHVDQUF1QyxFQUFFLGlFQUFpRSxtQ0FBbUMsY0FBYyxrREFBa0QsV0FBVyxlQUFlLGdIQUFnSCxXQUFXLGVBQWUsbUhBQW1ILFdBQVcsR0FBRyxTQUFTLGNBQWMsa0JBQWtCLGlCQUFpQixRQUFRLHVFQUF1RSxrREFBa0QsVUFBVSxxQ0FBcUMsMENBQTBDLFNBQVMsZUFBZSw2VEFBNlQsT0FBTyxNQUFNLHNFQUFzRSxNQUFNLGlFQUFpRSxhQUFhLElBQUksWUFBWSxRQUFRLHNGQUFzRixRQUFRLG9HQUFvRyxNQUFNLDhFQUE4RSxHQUFHLHlEQUF5RCxnQkFBZ0IsZUFBZSxNQUFNLCtDQUErQyxzREFBc0QsR0FBRywrRUFBK0UsUUFBUSxNQUFNLHFOQUFxTiw2RUFBNkUsR0FBRyxrQkFBa0IsMENBQTBDLGlCQUFpQiwyQ0FBMkMscURBQXFELFNBQVMsY0FBYyx1Q0FBdUMsa0JBQWtCLG1CQUFtQiwyRUFBMkUsbUNBQW1DLGdHQUFnRyxTQUFTLDBCQUEwQixVQUFVLCtCQUErQix5QkFBeUIsbUZBQW1GLGVBQWUsa0JBQWtCLDZCQUE2Qix3QkFBd0IsNkNBQTZDLHNCQUFzQiwrZUFBK2Usd0JBQXdCLGlGQUFpRixlQUFlLG1EQUFtRCxTQUFTLG1CQUFtQiwrRkFBK0YsU0FBUyxlQUFlLHlFQUF5RSxZQUFZLE1BQU0sbUlBQW1JLDJCQUEyQixvQkFBb0IsWUFBWSx5TEFBeUwsR0FBRyxpQkFBaUIsNkJBQTZCLG1FQUFtRSx1Q0FBdUMsaUJBQWlCLEdBQUcsZUFBZSx3QkFBd0Isc2RBQXNkLGNBQWMsdUNBQXVDLDREQUE0RCxJQUFJLElBQUksMEJBQTBCLE1BQU0scUJBQXFCLFlBQVksdUxBQXVMLFdBQVcsaUVBQWlFLGtCQUFrQixrQkFBa0IsVUFBVSxzSEFBc0gsY0FBYyxzQ0FBc0MsMkRBQTJELEdBQUcsSUFBSSxhQUFhLDhHQUE4RyxpREFBaUQseUVBQXlFLE1BQU0sbUJBQW1CLHVEQUF1RCx3R0FBd0csWUFBWSw4R0FBOEcscUdBQXFHLDJCQUEyQixRQUFRLHdGQUF3RixxQkFBcUIsRUFBRSxhQUFhLHdHQUF3RyxhQUFhLG9CQUFvQiwyQkFBMkIsb0JBQW9CLDJCQUEyQiwrQ0FBK0MsMEJBQTBCLHlVQUF5VSxtQkFBbUIsNkJBQTZCLHdGQUF3RiwyQ0FBMkMsMkNBQTJDLGdFQUFnRSw2Q0FBNkMsNkJBQTZCLDBDQUEwQyxxR0FBcUcsb0NBQW9DLGtEQUFrRCw0QkFBNEIsUUFBUSxvR0FBb0csb0JBQW9CLFVBQVUsaUVBQWlFLG1EQUFtRCxVQUFVLGlFQUFpRSxrREFBa0QsVUFBVSxpRUFBaUUsa0RBQWtELFdBQVcsbUVBQW1FLHFEQUFxRCx5REFBeUQsWUFBWSxxR0FBcUcsb0NBQW9DLGtEQUFrRCxTQUFTLHlGQUF5RixTQUFTLGVBQWUsOEVBQThFLGtCQUFrQiw0REFBNEQsUUFBUSw4Q0FBOEMsWUFBWSxpQkFBaUIsc0JBQXNCLEtBQUssMEJBQTBCLGdEQUFnRCxTQUFTLElBQUksMkNBQTJDLGdCQUFnQiw2RkFBNkYsaVNBQWlTLFFBQVEsd0RBQXdELDRJQUE0SSxJQUFJLGNBQWMsOERBQThELHFEQUFxRCxtRkFBbUYscUNBQXFDLHFCQUFxQiw2QkFBNkIsdUlBQXVJLE1BQU0sNENBQTRDLDJDQUEyQyw0QkFBNEIsNGhCQUE0aEIsR0FBRyw4RUFBOEUscUVBQXFFLE9BQU8sUUFBUSxFQUFFLDRCQUE0Qiw4REFBOEQscUJBQXFCLGdFQUFnRSxZQUFZLDBCQUEwQixvUkFBb1IsWUFBWSw0RkFBNEYsMEJBQTBCLDRJQUE0SSw4MkJBQTgyQixxQkFBcUIsR0FBRyxZQUFZLEtBQUssY0FBYyxNQUFNLDZGQUE2RixJQUFJLGNBQWMsR0FBRyw2REFBNkQsR0FBRywwQkFBMEIsTUFBTSw2S0FBNkssbUJBQW1CLDJFQUEyRSxpQ0FBaUMsdUNBQXVDLEdBQUcsa0JBQWtCLDJFQUEyRSxpQ0FBaUMsc0VBQXNFLElBQUksMEJBQTBCLFNBQVMsZUFBZSxRQUFRLHdCQUF3QixtRUFBbUUsYUFBYSxtQkFBbUIsK0NBQStDLG1HQUFtRyxHQUFHLFNBQVMsd0dBQXdHLE9BQU8sR0FBRyxxWEFBcVgseUJBQXlCLGtNQUFrTSw0Q0FBNEMsNklBQTZJLGlCQUFpQixxSUFBcUksaU5BQWlOLEVBQUUsNkJBQTZCLHNHQUFzRywwQ0FBMEMsdUJBQXVCLHlDQUF5QyxzQ0FBc0MsV0FBVyw4QkFBOEIsVUFBVSxxRkFBcUYsb0JBQW9CLG9OQUFvTixJQUFJLFNBQVMscUJBQXFCLG1DQUFtQyw2QkFBNkIsd0JBQXdCLE9BQU8sb0VBQW9FLDRCQUE0QixPQUFPLEdBQUcsc0tBQXNLLGtCQUFrQixtQ0FBbUMsa0JBQWtCLHFFQUFxRSxVQUFVLDhWQUE4VixLQUFLLGd0QkFBZ3RCLG9CQUFvQixlQUFlLHNCQUFzQixrQ0FBa0Msc0JBQXNCLHdCQUF3QixHQUFHLGtCQUFrQixlQUFlLGtCQUFrQix1QkFBdUIsSUFBSSxxQ0FBcUMsZ0ZBQWdGLFVBQVUscUJBQXFCLFNBQVMsY0FBYyxlQUFlLG1CQUFtQix3Q0FBd0MsNkRBQTZELHFCQUFxQixvQkFBb0Isa0NBQWtDLG1CQUFtQixvQkFBb0IsRUFBRSxVQUFVLDJDQUEyQyxzSEFBc0gsaUJBQWlCLFNBQVMsc0NBQXNDLDZDQUE2QyxtQkFBbUIsd0VBQXdFLGtCQUFrQiwrREFBK0Qsd0NBQXdDLHdCQUF3QixpQkFBaUIsNENBQTRDLGFBQWEsR0FBRyxjQUFjLFVBQVUsd09BQXdPLDBDQUEwQyxHQUFHLFFBQVEsTUFBTSxNQUFNLHdIQUF3SCw4RkFBOEYsNk1BQTZNLHFEQUFxRCxPQUFPLDJKQUEySix1QkFBdUIsZUFBZSxzRUFBc0UsU0FBUyxlQUFlLE1BQU0sK1FBQStRLGFBQWEsR0FBRyxnQ0FBZ0MsVUFBVSw0TEFBNEwsZ0JBQWdCLGdNQUFnTSxpQkFBaUIsbUJBQW1CLDRCQUE0QixvSUFBb0ksS0FBSyxRQUFRLHFKQUFxSixrQkFBa0IsbUJBQW1CLCtEQUErRCxPQUFPLGtEQUFrRCxvQkFBb0Isa0JBQWtCLDRCQUE0QixnQkFBZ0IsMkNBQTJDLGtCQUFrQixrU0FBa1Msb0JBQW9CLG9KQUFvSixZQUFZLHdCQUF3QixFQUFFLGtCQUFrQiwrREFBK0QsUUFBUSxTQUFTLDZCQUE2QixPQUFPLCtEQUErRCxZQUFZLHVFQUF1RSx5QkFBeUIsK0JBQStCLHFIQUFxSCxNQUFNLEVBQUUsaUJBQWlCLHFCQUFxQixnQkFBZ0Isb0dBQW9HLGtEQUFrRCxRQUFRLFlBQVksTUFBTSxzQkFBc0IsMkJBQTJCLDZEQUE2RCxRQUFRLGlFQUFpRSx1Q0FBdUMsR0FBRyxTQUFTLGVBQWUsaUNBQWlDLFlBQVksOEJBQThCLEVBQUUsMEtBQTBLLHNiQUFzYixtQkFBbUIsYUFBYSxvQkFBb0IsZUFBZSxtQkFBbUIsOENBQThDLCtCQUErQixnTUFBZ00sZ0JBQWdCLCtGQUErRix5SUFBeUksZ0JBQWdCLGlEQUFpRCxlQUFlLFVBQVUsOENBQThDLDJCQUEyQix5QkFBeUIsOEVBQThFLFVBQVUsOENBQThDLDJCQUEyQiwwQkFBMEIsZ0ZBQWdGLDBCQUEwQixpQkFBaUIsdUNBQXVDLGVBQWUsNERBQTRELFFBQVEsR0FBRyxvQkFBb0IsaUJBQWlCLFNBQVMscUJBQXFCLGlDQUFpQyxZQUFZLDhIQUE4SCxZQUFZLDZDQUE2QyxhQUFhLGdDQUFnQyxNQUFNLDJNQUEyTSxjQUFjLHFFQUFxRSxHQUFHLG9CQUFvQixRQUFRLGdDQUFnQyx1Q0FBdUMsaUJBQWlCLGFBQWEsa0dBQWtHLGFBQWEsNEJBQTRCLHdHQUF3RyxlQUFlLFdBQVcsWUFBWSxZQUFZLGdJQUFnSSxrQkFBa0IsbUJBQW1CLFNBQVMsbUNBQW1DLDREQUE0RCxnRUFBZ0UsdUJBQXVCLEtBQUssSUFBSSxPQUFPLFlBQVksUUFBUSxzQkFBc0IscUhBQXFILFFBQVEsa0JBQWtCLDRHQUE0RyxtQkFBbUIsT0FBTyxTQUFTLGlDQUFpQyxLQUFLLEVBQUUsWUFBWSxFQUFFLHNCQUFzQiwwQ0FBMEMsdURBQXVELEdBQUcsc0JBQXNCLHNCQUFzQixHQUFHLEtBQUssU0FBUyxlQUFlLDJLQUEySyxhQUFhLGtGQUFrRiwyTEFBMkwsdUJBQXVCLDhCQUE4QixTQUFTLFVBQVUsOEJBQThCLElBQUksc0JBQXNCLFNBQVMsdUNBQXVDLE9BQU8sdURBQXVELGlKQUFpSiwyRkFBMkYsaUJBQWlCLHVGQUF1RixrQkFBa0IsOElBQThJLDZCQUE2QiwyRUFBMkUsdUJBQXVCLHlCQUF5Qix1QkFBdUIsV0FBVyxxRUFBcUUsMkNBQTJDLHdCQUF3QixpQkFBaUIsR0FBRyxLQUFLLGdCQUFnQiwyR0FBMkcsdUVBQXVFLDhEQUE4RCw2Q0FBNkMsaUJBQWlCLDRFQUE0RSxtQkFBbUIsY0FBYyxtUUFBbVEsWUFBWSxxREFBcUQsSUFBSSw0QkFBNEIsT0FBTyxtUEFBbVAsRUFBRSxjQUFjLGtDQUFrQyxrREFBa0QsdUdBQXVHLGVBQWUscUNBQXFDLHVDQUF1QyxxQkFBcUIsNkNBQTZDLDZCQUE2QixTQUFTLGVBQWUscUdBQXFHLFlBQVksdUtBQXVLLDJLQUEySyxtQkFBbUIsZUFBZSxVQUFVLDZOQUE2TixnREFBZ0QsSUFBSSxHQUFHLHVCQUF1QixRQUFRLHlRQUF5USxPQUFPLGFBQWEsMEVBQTBFLFdBQVcsR0FBRyxTQUFTLGVBQWUsZ0JBQWdCLGFBQWEsNEJBQTRCLGNBQWMsa0RBQWtELGFBQWEscUdBQXFHLFlBQVkseUJBQXlCLFlBQVksbUlBQW1JLFFBQVEsd0VBQXdFLGlCQUFpQixJQUFJLDBDQUEwQyxzQkFBc0IsZ0NBQWdDLG9CQUFvQix3Q0FBd0Msc0RBQXNELGNBQWMsbUJBQW1CLHFEQUFxRCx1Q0FBdUMsK0JBQStCLHVLQUF1SyxxQkFBcUIsU0FBUyw0SUFBNEksbUNBQW1DLFNBQVMsZUFBZSxNQUFNLG1GQUFtRixZQUFZLDREQUE0RCw2UEFBNlAsWUFBWSx5SEFBeUgsU0FBUyxtREFBbUQseUVBQXlFLDhIQUE4SCxnQkFBZ0IsTUFBTSxvREFBb0Qsa0JBQWtCLE1BQU0sZ0ZBQWdGLGtCQUFrQixvRUFBb0UsaUJBQWlCLDBHQUEwRyxjQUFjLFFBQVEsMktBQTJLLGdEQUFnRCxJQUFJLEdBQUcsZ0JBQWdCLFFBQVEsMkhBQTJILGtCQUFrQiw4QkFBOEIsMEZBQTBGLHdCQUF3Qix3QkFBd0Isb0JBQW9CLG9EQUFvRCxzTEFBc0wsZ0NBQWdDLFNBQVMsZUFBZSxNQUFNLDJFQUEyRSxrREFBa0QsaUNBQWlDLG9KQUFvSixNQUFNLEVBQUUsNkNBQTZDLHFEQUFxRCxLQUFLLGdCQUFnQixrREFBa0QsRUFBRSw4QkFBOEIsdUdBQXVHLGlFQUFpRSxLQUFLLDBCQUEwQixtREFBbUQseUJBQXlCLHNHQUFzRyxpSUFBaUksd2FBQXdhLE1BQU0sRUFBRSxrRkFBa0Ysb0ZBQW9GLGtDQUFrQyxzQ0FBc0Msd0NBQXdDLGdDQUFnQyx1WkFBdVosbUlBQW1JLHFCQUFxQixpS0FBaUssK0dBQStHLDhCQUE4QixxSUFBcUksdUJBQXVCLHlMQUF5TCx3QkFBd0IsZ0JBQWdCLDZHQUE2RyxzQ0FBc0MsaUJBQWlCLDRHQUE0RyxrQkFBa0Isa0pBQWtKLGdFQUFnRSxPQUFPLHNDQUFzQyxPQUFPLG1EQUFtRCxjQUFjLFVBQVUsMk5BQTJOLDBDQUEwQyxHQUFHLGNBQWMsK0JBQStCLHlEQUF5RCxRQUFRLHlRQUF5USxTQUFTLGVBQWUsV0FBVyw4REFBOEQsOERBQThELHlCQUF5QixvR0FBb0csdUJBQXVCLE1BQU0sNEtBQTRLLG9EQUFvRCwwRUFBMEUsNENBQTRDLG9CQUFvQixvRUFBb0UsR0FBRyxLQUFLLHFEQUFxRCx1QkFBdUIsR0FBRyxrQkFBa0IsbUdBQW1HLHVEQUF1RCwySEFBMkgsc0RBQXNELE1BQU0sc0ZBQXNGLHlHQUF5RyxvRkFBb0YsSUFBSSx1QkFBdUIsNkJBQTZCLHNCQUFzQix5R0FBeUcsd0dBQXdHLCtDQUErQyxXQUFXLGdCQUFnQiw4RkFBOEYseUJBQXlCLGlFQUFpRSxxREFBcUQscUJBQXFCLDZCQUE2QiwrSUFBK0ksNENBQTRDLDZCQUE2QixhQUFhLEVBQUUsZ0NBQWdDLDhCQUE4Qiw0S0FBNEssNkJBQTZCLDZFQUE2RSw2QkFBNkIsTUFBTSxpRkFBaUYsbUNBQW1DLGlMQUFpTCwyQkFBMkIsaUNBQWlDLDZCQUE2QixNQUFNLGlGQUFpRiwwQkFBMEIsaUJBQWlCLDR3QkFBNHdCLGtCQUFrQixTQUFTLGtCQUFrQixzREFBc0QsSUFBSSx3QkFBd0Isd0NBQXdDLHVCQUF1Qiw2SEFBNkgsZUFBZSxNQUFNLDBGQUEwRiw0RUFBNEUsd0JBQXdCLFFBQVEsd0NBQXdDLGVBQWUsK0JBQStCLE1BQU0sK0JBQStCLE1BQU0sbUNBQW1DLE1BQU0sd0NBQXdDLG1KQUFtSixJQUFJLDZEQUE2RCxVQUFVLHFGQUFxRixTQUFTLGVBQWUsZ0JBQWdCLHNCQUFzQixzQ0FBc0MsMENBQTBDLEdBQUcsK0NBQStDLFNBQVMsZUFBZSx5REFBeUQsZ0JBQWdCLDhDQUE4QyxvQkFBb0IsTUFBTSw4QkFBOEIscUlBQXFJLHFCQUFxQixRQUFRLCtIQUErSCxPQUFPLG1CQUFtQixvRUFBb0Usd0JBQXdCLDREQUE0RCxtQkFBbUIsa0JBQWtCLG1EQUFtRCxRQUFRLDBHQUEwRyx1Q0FBdUMsNkJBQTZCLElBQUksbUJBQW1CLEdBQUcsT0FBTyxZQUFZLFlBQVksRUFBRSxtQkFBbUIsa0JBQWtCLFdBQVcsOEVBQThFLEtBQUssR0FBRyxLQUFLLGdCQUFnQixtRkFBbUYsS0FBSyxHQUFHLCtLQUErSyxVQUFVLG1FQUFtRSxXQUFXLGdCQUFnQixPQUFPLDhCQUE4QixrQkFBa0IsVUFBVSxNQUFNLG9VQUFvVSxNQUFNLGdFQUFnRSxrREFBa0QseURBQXlELElBQUksSUFBSSxlQUFlLFNBQVMsbUNBQW1DLE1BQU0sRUFBRSxzQkFBc0IsU0FBUyx1RkFBdUYsbUJBQW1CLHNDQUFzQyw4QkFBOEIsMENBQTBDLE1BQU0sbURBQW1ELFNBQVMsNEZBQTRGLGtCQUFrQixpQkFBaUIseURBQXlELDRIQUE0SCxzREFBc0QsK0RBQStELCtIQUErSCxvQkFBb0IsUUFBUSw0SkFBNEoseUZBQXlGLElBQUksUUFBUSxXQUFXLHFCQUFxQixtR0FBbUcsbUJBQW1CLFdBQVcsOERBQThELEVBQUUsU0FBUyxVQUFVLGtIQUFrSCxVQUFVLE9BQU8scWpDQUFxakMseUhBQXlILCtUQUErVCxrREFBa0QsV0FBVyx3RkFBd0YseUlBQXlJLFFBQVEsU0FBUyxpT0FBaU8sVUFBVSxNQUFNLDZRQUE2USxTQUFTLGNBQWMsbUNBQW1DLDJCQUEyQixrQ0FBa0MsNEJBQTRCLDJMQUEyTCxTQUFTLDJCQUEyQixRQUFRLGtHQUFrRyxjQUFjLDBMQUEwTCxjQUFjLE9BQU8sOEJBQThCLDZlQUE2ZSxjQUFjLFlBQVksTUFBTSxHQUFHLHlDQUF5QyxFQUFFLG9CQUFvQixZQUFZLE1BQU0sR0FBRyxnREFBZ0QsR0FBRyxxRkFBcUYsWUFBWSxjQUFjLDhDQUE4Qyw0Q0FBNEMseUJBQXlCLFNBQVMscUVBQXFFLDBDQUEwQyxvTEFBb0wsd0dBQXdHLCtCQUErQix3SEFBd0gsYUFBYSxlQUFlLGtVQUFrVSxnQkFBZ0IsRUFBRSw2QkFBNkIsR0FBRyxVQUFVLCtCQUErQixnQ0FBZ0Msc2hDQUFzaEMsNkJBQTZCLHNCQUFzQiwyR0FBMkcsMEhBQTBILCtIQUErSCxnREFBZ0QsRUFBRSx3Q0FBd0Msb0ZBQW9GLGNBQWMsUUFBUSwwRkFBMEYsbUJBQW1CLFFBQVEsMEdBQTBHLElBQUkscUZBQXFGLG9DQUFvQyxZQUFZLDRMQUE0TCxTQUFTLElBQUksRUFBRSx3Q0FBd0MscUNBQXFDLEVBQUUsMEVBQTBFLCtCQUErQixLQUFLLGlDQUFpQyxzQ0FBc0Msb0JBQW9CLDJCQUEyQixtREFBbUQsc2FBQXNhLG1CQUFtQixzQkFBc0Isa0hBQWtILHdCQUF3QixPQUFPLDBUQUEwVCxzR0FBc0csOGZBQThmLFVBQVUsSUFBSSx5QkFBeUIsU0FBUyx5Q0FBeUMsK0VBQStFLHlEQUF5RCxvREFBb0QsMkJBQTJCLE1BQU0sc0hBQXNILGNBQWMscUpBQXFKLGlCQUFpQixRQUFRLG9RQUFvUSx1QkFBdUIsRUFBRSxpQkFBaUIsc0tBQXNLLHNCQUFzQixnQkFBZ0IsMEtBQTBLLGdCQUFnQixJQUFJLHlDQUF5Qyw4RUFBOEUsRUFBRSw4SkFBOEosdURBQXVELEtBQUssUUFBUSxNQUFNLEVBQUUsYUFBYSxVQUFVLGlJQUFpSSxnREFBZ0QsS0FBSywyQkFBMkIsNEVBQTRFLGtCQUFrQixxQkFBcUIsU0FBUyx5SUFBeUksR0FBRyxvQkFBb0IsaUJBQWlCLFlBQVksUUFBUSxpQkFBaUIsOEJBQThCLEdBQUcsdUNBQXVDLE1BQU0sOEJBQThCLCtCQUErQixRQUFRLHlLQUF5SyxRQUFRLHlCQUF5QixlQUFlLE1BQU0saUZBQWlGLDhDQUE4QywwREFBMEQsNEZBQTRGLDhCQUE4Qix1U0FBdVMsMkRBQTJELGtFQUFrRSxPQUFPLDZIQUE2SCxzRUFBc0UsNk9BQTZPLFlBQVksNEJBQTRCLDBFQUEwRSw2QkFBNkIsYUFBYSxJQUFJLG1EQUFtRCxPQUFPLDhNQUE4TSwySkFBMkoseUVBQXlFLDBEQUEwRCwrQ0FBK0MsbUJBQW1CLGtEQUFrRCxxQ0FBcUMsc0VBQXNFLG1EQUFtRCxJQUFJLDBIQUEwSCxZQUFZLHNFQUFzRSw0TEFBNEwsdUJBQXVCLDhJQUE4SSxFQUFFLHdCQUF3QixJQUFJLHVCQUF1QixxREFBcUQsK0JBQStCLHVTQUF1UyxpREFBaUQsU0FBUyxvTUFBb00sb0JBQW9CLDJDQUEyQyxpR0FBaUcsNExBQTRMLFlBQVksMEpBQTBKLHNDQUFzQyxvRkFBb0Ysa0NBQWtDLHFHQUFxRyxrQ0FBa0MsNERBQTRELFVBQVUsMkNBQTJDLG9DQUFvQyxtSEFBbUgsY0FBYyxNQUFNLHlEQUF5RCxxQkFBcUIsTUFBTSxnRUFBZ0Usd0JBQXdCLE1BQU0sOERBQThELGNBQWMsTUFBTSx5REFBeUQsMEJBQTBCLE1BQU0sZ0VBQWdFLHNCQUFzQixlQUFlLE1BQU0sRUFBRSxvQkFBb0IsNkNBQTZDLHlCQUF5QixpREFBaUQsSUFBSSxxQkFBcUIsU0FBUyxVQUFVLHNCQUFzQiwrQ0FBK0MscUJBQXFCLHVDQUF1Qyx3Q0FBd0MsMERBQTBELDBCQUEwQiw4REFBOEQscURBQXFELFFBQVEsMENBQTBDLGtCQUFrQiwyQ0FBMkMsZUFBZSxRQUFRLDBHQUEwRyxjQUFjLDhEQUE4RCw2QkFBNkIsNEJBQTRCLDhEQUE4RCwyQ0FBMkMsa0JBQWtCLCtCQUErQixtQkFBbUIsZ0NBQWdDLHlCQUF5Qiw2Q0FBNkMsZ0JBQWdCLHVGQUF1RiwwSkFBMEosME5BQTBOLDJEQUEyRCw2QkFBNkIsa0JBQWtCLFdBQVcsb0NBQW9DLFFBQVEsb0JBQW9CLDJDQUEyQyxLQUFLLHNFQUFzRSxjQUFjLEdBQUcscUVBQXFFLCtGQUErRiwrQkFBK0Isa0NBQWtDLEVBQUUsVUFBVSxpQkFBaUIsb1ZBQW9WLG9FQUFvRSx5QkFBeUIsNkdBQTZHLHVCQUF1QixVQUFVLGlCQUFpQixHQUFHLGFBQWEsU0FBUyxtREFBbUQsdUJBQXVCLDhEQUE4RCxjQUFjLE1BQU0sRUFBRSxNQUFNLEVBQUUscUNBQXFDLHdDQUF3QyxtQ0FBbUMsTUFBTSwyQ0FBMkMsa0VBQWtFLGNBQWMsZUFBZSxXQUFXLGdFQUFnRSwrQkFBK0IsaUVBQWlFLG1EQUFtRCxnQ0FBZ0Msa0RBQWtELDhCQUE4QixpRUFBaUUsdUhBQXVILGdDQUFnQyxrREFBa0QsU0FBUyxjQUFjLGlGQUFpRixzQ0FBc0MsNFpBQTRaLCtCQUErQixLQUFLLEtBQUssc0NBQXNDLG9CQUFvQiwrQkFBK0IsTUFBTSxrQkFBa0Isd0NBQXdDLFlBQVksd0NBQXdDLGlCQUFpQixRQUFRLG1JQUFtSSwwQkFBMEIsaUNBQWlDLElBQUksb0NBQW9DLHlKQUF5SixnQ0FBZ0Msd0RBQXdELGVBQWUsNkRBQTZELG1CQUFtQixTQUFTLFdBQVcsc1FBQXNRLHNCQUFzQixrR0FBa0csY0FBYyxrQkFBa0IsY0FBYyx5T0FBeU8sZ0JBQWdCLEVBQUUsNkJBQTZCLDhIQUE4SCxtQ0FBbUMsY0FBYyxtUUFBbVEseUJBQXlCLGdCQUFnQix1S0FBdUssbUNBQW1DLGNBQWMsK0pBQStKLHNGQUFzRix3RkFBd0YsNEZBQTRGLGlCQUFpQiw2QkFBNkIsRUFBRSx1QkFBdUIsaUJBQWlCLDZCQUE2QixFQUFFLDBCQUEwQixNQUFNLGtFQUFrRSxzQkFBc0IsdU5BQXVOLHFCQUFxQixLQUFLLDJDQUEyQyx1Q0FBdUMseUJBQXlCLEVBQUUsSUFBSSxzQ0FBc0MsZUFBZSxtQ0FBbUMsZ0JBQWdCLE1BQU0saUVBQWlFLHNCQUFzQixNQUFNLHdFQUF3RSxXQUFXLHFEQUFxRCw4QkFBOEIsZ0JBQWdCLFFBQVEsb0xBQW9MLHVCQUF1QixZQUFZLGtUQUFrVCwwQkFBMEIsOEhBQThILHNCQUFzQixzSEFBc0gsR0FBRyxFQUFFLDRCQUE0Qiw4S0FBOEssaUNBQWlDLCtIQUErSCw4SEFBOEgsNElBQTRJLG9CQUFvQixNQUFNLDJQQUEyUCxrQkFBa0IsRUFBRSw2REFBNkQsb0JBQW9CLGdFQUFnRSx5QkFBeUIsZ0NBQWdDLDBCQUEwQixpQ0FBaUMsNkJBQTZCLDJEQUEyRCxVQUFVLDREQUE0RCwwQkFBMEIsK0ZBQStGLGNBQWMsRUFBRSxrQkFBa0IsR0FBRyxTQUFTLGlJQUFpSSxTQUFTLHVPQUF1TyxTQUFTLEdBQUcsa0JBQWtCLHVDQUF1Qyx1RkFBdUYsZ0JBQWdCLGdCQUFnQix3RUFBd0UsOEZBQThGLDRJQUE0SSxTQUFTLGdCQUFnQixNQUFNLHlGQUF5RixlQUFlLFlBQVksV0FBVyw0Q0FBNEMsa0JBQWtCLHVDQUF1QyxhQUFhLDJDQUEyQyxnQkFBZ0IsSUFBSSw4SEFBOEgsT0FBb087QUFDaDNoTCIsInNvdXJjZXMiOlsiRzpcXHByb2plY3RzXFxzY3JlZW5waXBlXFxzY3JlZW5waXBlXFxwaXBlc1xcZm9jdXMtYW5hbHl0aWNzLWNvYWNoXFxub2RlX21vZHVsZXNcXHBvc3Rob2ctanNcXGRpc3RcXG1vZHVsZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgZSx0PVwidW5kZWZpbmVkXCIhPXR5cGVvZiB3aW5kb3c/d2luZG93OnZvaWQgMCxpPVwidW5kZWZpbmVkXCIhPXR5cGVvZiBnbG9iYWxUaGlzP2dsb2JhbFRoaXM6dCxyPUFycmF5LnByb3RvdHlwZSxuPXIuZm9yRWFjaCxzPXIuaW5kZXhPZixvPW51bGw9PWk/dm9pZCAwOmkubmF2aWdhdG9yLGE9bnVsbD09aT92b2lkIDA6aS5kb2N1bWVudCxsPW51bGw9PWk/dm9pZCAwOmkubG9jYXRpb24sdT1udWxsPT1pP3ZvaWQgMDppLmZldGNoLGM9bnVsbCE9aSYmaS5YTUxIdHRwUmVxdWVzdCYmXCJ3aXRoQ3JlZGVudGlhbHNcImluIG5ldyBpLlhNTEh0dHBSZXF1ZXN0P2kuWE1MSHR0cFJlcXVlc3Q6dm9pZCAwLGQ9bnVsbD09aT92b2lkIDA6aS5BYm9ydENvbnRyb2xsZXIsaD1udWxsPT1vP3ZvaWQgMDpvLnVzZXJBZ2VudCxfPW51bGwhPXQ/dDp7fSxwPXtERUJVRzohMSxMSUJfVkVSU0lPTjpcIjEuMjA3LjJcIn0sdj1cIiRjb3B5X2F1dG9jYXB0dXJlXCIsZz1bXCIkc25hcHNob3RcIixcIiRwYWdldmlld1wiLFwiJHBhZ2VsZWF2ZVwiLFwiJHNldFwiLFwic3VydmV5IGRpc21pc3NlZFwiLFwic3VydmV5IHNlbnRcIixcInN1cnZleSBzaG93blwiLFwiJGlkZW50aWZ5XCIsXCIkZ3JvdXBpZGVudGlmeVwiLFwiJGNyZWF0ZV9hbGlhc1wiLFwiJCRjbGllbnRfaW5nZXN0aW9uX3dhcm5pbmdcIixcIiR3ZWJfZXhwZXJpbWVudF9hcHBsaWVkXCIsXCIkZmVhdHVyZV9lbnJvbGxtZW50X3VwZGF0ZVwiLFwiJGZlYXR1cmVfZmxhZ19jYWxsZWRcIl07IWZ1bmN0aW9uKGUpe2UuR1ppcEpTPVwiZ3ppcC1qc1wiLGUuQmFzZTY0PVwiYmFzZTY0XCJ9KGV8fChlPXt9KSk7dmFyIGY9W1wiZmF0YWxcIixcImVycm9yXCIsXCJ3YXJuaW5nXCIsXCJsb2dcIixcImluZm9cIixcImRlYnVnXCJdO2Z1bmN0aW9uIG0oZSx0KXtyZXR1cm4tMSE9PWUuaW5kZXhPZih0KX12YXIgYj1mdW5jdGlvbihlKXtyZXR1cm4gZS5yZXBsYWNlKC9eW1xcc1xcdUZFRkZcXHhBMF0rfFtcXHNcXHVGRUZGXFx4QTBdKyQvZyxcIlwiKX0seT1mdW5jdGlvbihlKXtyZXR1cm4gZS5yZXBsYWNlKC9eXFwkLyxcIlwiKX07dmFyIHc9QXJyYXkuaXNBcnJheSxTPU9iamVjdC5wcm90b3R5cGUsRT1TLmhhc093blByb3BlcnR5LGs9Uy50b1N0cmluZyx4PXd8fGZ1bmN0aW9uKGUpe3JldHVyblwiW29iamVjdCBBcnJheV1cIj09PWsuY2FsbChlKX0sST1lPT5cImZ1bmN0aW9uXCI9PXR5cGVvZiBlLEM9ZT0+ZT09PU9iamVjdChlKSYmIXgoZSksUD1lPT57aWYoQyhlKSl7Zm9yKHZhciB0IGluIGUpaWYoRS5jYWxsKGUsdCkpcmV0dXJuITE7cmV0dXJuITB9cmV0dXJuITF9LEY9ZT0+dm9pZCAwPT09ZSxSPWU9PlwiW29iamVjdCBTdHJpbmddXCI9PWsuY2FsbChlKSxUPWU9PlIoZSkmJjA9PT1lLnRyaW0oKS5sZW5ndGgsJD1lPT5udWxsPT09ZSxPPWU9PkYoZSl8fCQoZSksTT1lPT5cIltvYmplY3QgTnVtYmVyXVwiPT1rLmNhbGwoZSksTD1lPT5cIltvYmplY3QgQm9vbGVhbl1cIj09PWsuY2FsbChlKSxBPWU9PmUgaW5zdGFuY2VvZiBGb3JtRGF0YSxEPWU9Pm0oZyxlKSxOPWU9Pnt2YXIgaT17X2xvZzpmdW5jdGlvbihpKXtpZih0JiYocC5ERUJVR3x8Xy5QT1NUSE9HX0RFQlVHKSYmIUYodC5jb25zb2xlKSYmdC5jb25zb2xlKXtmb3IodmFyIHI9KFwiX19ycndlYl9vcmlnaW5hbF9fXCJpbiB0LmNvbnNvbGVbaV0/dC5jb25zb2xlW2ldLl9fcnJ3ZWJfb3JpZ2luYWxfXzp0LmNvbnNvbGVbaV0pLG49YXJndW1lbnRzLmxlbmd0aCxzPW5ldyBBcnJheShuPjE/bi0xOjApLG89MTtvPG47bysrKXNbby0xXT1hcmd1bWVudHNbb107cihlLC4uLnMpfX0saW5mbzpmdW5jdGlvbigpe2Zvcih2YXIgZT1hcmd1bWVudHMubGVuZ3RoLHQ9bmV3IEFycmF5KGUpLHI9MDtyPGU7cisrKXRbcl09YXJndW1lbnRzW3JdO2kuX2xvZyhcImxvZ1wiLC4uLnQpfSx3YXJuOmZ1bmN0aW9uKCl7Zm9yKHZhciBlPWFyZ3VtZW50cy5sZW5ndGgsdD1uZXcgQXJyYXkoZSkscj0wO3I8ZTtyKyspdFtyXT1hcmd1bWVudHNbcl07aS5fbG9nKFwid2FyblwiLC4uLnQpfSxlcnJvcjpmdW5jdGlvbigpe2Zvcih2YXIgZT1hcmd1bWVudHMubGVuZ3RoLHQ9bmV3IEFycmF5KGUpLHI9MDtyPGU7cisrKXRbcl09YXJndW1lbnRzW3JdO2kuX2xvZyhcImVycm9yXCIsLi4udCl9LGNyaXRpY2FsOmZ1bmN0aW9uKCl7Zm9yKHZhciB0PWFyZ3VtZW50cy5sZW5ndGgsaT1uZXcgQXJyYXkodCkscj0wO3I8dDtyKyspaVtyXT1hcmd1bWVudHNbcl07Y29uc29sZS5lcnJvcihlLC4uLmkpfSx1bmluaXRpYWxpemVkV2FybmluZzplPT57aS5lcnJvcihcIllvdSBtdXN0IGluaXRpYWxpemUgUG9zdEhvZyBiZWZvcmUgY2FsbGluZyBcIi5jb25jYXQoZSkpfSxjcmVhdGVMb2dnZXI6dD0+TihcIlwiLmNvbmNhdChlLFwiIFwiKS5jb25jYXQodCkpfTtyZXR1cm4gaX0scT1OKFwiW1Bvc3RIb2cuanNdXCIpLEI9cS5jcmVhdGVMb2dnZXIsSD1CKFwiW0V4dGVybmFsU2NyaXB0c0xvYWRlcl1cIiksVT0oZSx0LGkpPT57aWYoZS5jb25maWcuZGlzYWJsZV9leHRlcm5hbF9kZXBlbmRlbmN5X2xvYWRpbmcpcmV0dXJuIEgud2FybihcIlwiLmNvbmNhdCh0LFwiIHdhcyByZXF1ZXN0ZWQgYnV0IGxvYWRpbmcgb2YgZXh0ZXJuYWwgc2NyaXB0cyBpcyBkaXNhYmxlZC5cIikpLGkoXCJMb2FkaW5nIG9mIGV4dGVybmFsIHNjcmlwdHMgaXMgZGlzYWJsZWRcIik7dmFyIHI9KCk9PntpZighYSlyZXR1cm4gaShcImRvY3VtZW50IG5vdCBmb3VuZFwiKTt2YXIgcj1hLmNyZWF0ZUVsZW1lbnQoXCJzY3JpcHRcIik7aWYoci50eXBlPVwidGV4dC9qYXZhc2NyaXB0XCIsci5jcm9zc09yaWdpbj1cImFub255bW91c1wiLHIuc3JjPXQsci5vbmxvYWQ9ZT0+aSh2b2lkIDAsZSksci5vbmVycm9yPWU9PmkoZSksZS5jb25maWcucHJlcGFyZV9leHRlcm5hbF9kZXBlbmRlbmN5X3NjcmlwdCYmKHI9ZS5jb25maWcucHJlcGFyZV9leHRlcm5hbF9kZXBlbmRlbmN5X3NjcmlwdChyKSksIXIpcmV0dXJuIGkoXCJwcmVwYXJlX2V4dGVybmFsX2RlcGVuZGVuY3lfc2NyaXB0IHJldHVybmVkIG51bGxcIik7dmFyIG4scz1hLnF1ZXJ5U2VsZWN0b3JBbGwoXCJib2R5ID4gc2NyaXB0XCIpO3MubGVuZ3RoPjA/bnVsbD09PShuPXNbMF0ucGFyZW50Tm9kZSl8fHZvaWQgMD09PW58fG4uaW5zZXJ0QmVmb3JlKHIsc1swXSk6YS5ib2R5LmFwcGVuZENoaWxkKHIpfTtudWxsIT1hJiZhLmJvZHk/cigpOm51bGw9PWF8fGEuYWRkRXZlbnRMaXN0ZW5lcihcIkRPTUNvbnRlbnRMb2FkZWRcIixyKX07ZnVuY3Rpb24geihlLHQpe3ZhciBpPU9iamVjdC5rZXlzKGUpO2lmKE9iamVjdC5nZXRPd25Qcm9wZXJ0eVN5bWJvbHMpe3ZhciByPU9iamVjdC5nZXRPd25Qcm9wZXJ0eVN5bWJvbHMoZSk7dCYmKHI9ci5maWx0ZXIoKGZ1bmN0aW9uKHQpe3JldHVybiBPYmplY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9yKGUsdCkuZW51bWVyYWJsZX0pKSksaS5wdXNoLmFwcGx5KGkscil9cmV0dXJuIGl9ZnVuY3Rpb24gaihlKXtmb3IodmFyIHQ9MTt0PGFyZ3VtZW50cy5sZW5ndGg7dCsrKXt2YXIgaT1udWxsIT1hcmd1bWVudHNbdF0/YXJndW1lbnRzW3RdOnt9O3QlMj96KE9iamVjdChpKSwhMCkuZm9yRWFjaCgoZnVuY3Rpb24odCl7VyhlLHQsaVt0XSl9KSk6T2JqZWN0LmdldE93blByb3BlcnR5RGVzY3JpcHRvcnM/T2JqZWN0LmRlZmluZVByb3BlcnRpZXMoZSxPYmplY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9ycyhpKSk6eihPYmplY3QoaSkpLmZvckVhY2goKGZ1bmN0aW9uKHQpe09iamVjdC5kZWZpbmVQcm9wZXJ0eShlLHQsT2JqZWN0LmdldE93blByb3BlcnR5RGVzY3JpcHRvcihpLHQpKX0pKX1yZXR1cm4gZX1mdW5jdGlvbiBXKGUsdCxpKXtyZXR1cm4gdCBpbiBlP09iamVjdC5kZWZpbmVQcm9wZXJ0eShlLHQse3ZhbHVlOmksZW51bWVyYWJsZTohMCxjb25maWd1cmFibGU6ITAsd3JpdGFibGU6ITB9KTplW3RdPWksZX1mdW5jdGlvbiBWKGUsdCl7aWYobnVsbD09ZSlyZXR1cm57fTt2YXIgaSxyLG49ZnVuY3Rpb24oZSx0KXtpZihudWxsPT1lKXJldHVybnt9O3ZhciBpLHIsbj17fSxzPU9iamVjdC5rZXlzKGUpO2ZvcihyPTA7cjxzLmxlbmd0aDtyKyspaT1zW3JdLHQuaW5kZXhPZihpKT49MHx8KG5baV09ZVtpXSk7cmV0dXJuIG59KGUsdCk7aWYoT2JqZWN0LmdldE93blByb3BlcnR5U3ltYm9scyl7dmFyIHM9T2JqZWN0LmdldE93blByb3BlcnR5U3ltYm9scyhlKTtmb3Iocj0wO3I8cy5sZW5ndGg7cisrKWk9c1tyXSx0LmluZGV4T2YoaSk+PTB8fE9iamVjdC5wcm90b3R5cGUucHJvcGVydHlJc0VudW1lcmFibGUuY2FsbChlLGkpJiYobltpXT1lW2ldKX1yZXR1cm4gbn1fLl9fUG9zdGhvZ0V4dGVuc2lvbnNfXz1fLl9fUG9zdGhvZ0V4dGVuc2lvbnNfX3x8e30sXy5fX1Bvc3Rob2dFeHRlbnNpb25zX18ubG9hZEV4dGVybmFsRGVwZW5kZW5jeT0oZSx0LGkpPT57dmFyIHI9XCIvc3RhdGljL1wiLmNvbmNhdCh0LFwiLmpzXCIpK1wiP3Y9XCIuY29uY2F0KGUudmVyc2lvbik7aWYoXCJyZW1vdGUtY29uZmlnXCI9PT10JiYocj1cIi9hcnJheS9cIi5jb25jYXQoZS5jb25maWcudG9rZW4sXCIvY29uZmlnLmpzXCIpKSxcInRvb2xiYXJcIj09PXQpe3ZhciBuPTNlNSxzPU1hdGguZmxvb3IoRGF0ZS5ub3coKS9uKSpuO3I9XCJcIi5jb25jYXQocixcIiZ0PVwiKS5jb25jYXQocyl9dmFyIG89ZS5yZXF1ZXN0Um91dGVyLmVuZHBvaW50Rm9yKFwiYXNzZXRzXCIscik7VShlLG8saSl9LF8uX19Qb3N0aG9nRXh0ZW5zaW9uc19fLmxvYWRTaXRlQXBwPShlLHQsaSk9Pnt2YXIgcj1lLnJlcXVlc3RSb3V0ZXIuZW5kcG9pbnRGb3IoXCJhcGlcIix0KTtVKGUscixpKX07dmFyIEc9e307ZnVuY3Rpb24gSihlLHQsaSl7aWYoeChlKSlpZihuJiZlLmZvckVhY2g9PT1uKWUuZm9yRWFjaCh0LGkpO2Vsc2UgaWYoXCJsZW5ndGhcImluIGUmJmUubGVuZ3RoPT09K2UubGVuZ3RoKWZvcih2YXIgcj0wLHM9ZS5sZW5ndGg7cjxzO3IrKylpZihyIGluIGUmJnQuY2FsbChpLGVbcl0scik9PT1HKXJldHVybn1mdW5jdGlvbiBZKGUsdCxpKXtpZighTyhlKSl7aWYoeChlKSlyZXR1cm4gSihlLHQsaSk7aWYoQShlKSl7Zm9yKHZhciByIG9mIGUuZW50cmllcygpKWlmKHQuY2FsbChpLHJbMV0sclswXSk9PT1HKXJldHVybn1lbHNlIGZvcih2YXIgbiBpbiBlKWlmKEUuY2FsbChlLG4pJiZ0LmNhbGwoaSxlW25dLG4pPT09RylyZXR1cm59fXZhciBLPWZ1bmN0aW9uKGUpe2Zvcih2YXIgdD1hcmd1bWVudHMubGVuZ3RoLGk9bmV3IEFycmF5KHQ+MT90LTE6MCkscj0xO3I8dDtyKyspaVtyLTFdPWFyZ3VtZW50c1tyXTtyZXR1cm4gSihpLChmdW5jdGlvbih0KXtmb3IodmFyIGkgaW4gdCl2b2lkIDAhPT10W2ldJiYoZVtpXT10W2ldKX0pKSxlfSxYPWZ1bmN0aW9uKGUpe2Zvcih2YXIgdD1hcmd1bWVudHMubGVuZ3RoLGk9bmV3IEFycmF5KHQ+MT90LTE6MCkscj0xO3I8dDtyKyspaVtyLTFdPWFyZ3VtZW50c1tyXTtyZXR1cm4gSihpLChmdW5jdGlvbih0KXtKKHQsKGZ1bmN0aW9uKHQpe2UucHVzaCh0KX0pKX0pKSxlfTtmdW5jdGlvbiBRKGUpe2Zvcih2YXIgdD1PYmplY3Qua2V5cyhlKSxpPXQubGVuZ3RoLHI9bmV3IEFycmF5KGkpO2ktLTspcltpXT1bdFtpXSxlW3RbaV1dXTtyZXR1cm4gcn12YXIgWj1mdW5jdGlvbihlKXt0cnl7cmV0dXJuIGUoKX1jYXRjaChlKXtyZXR1cm59fSxlZT1mdW5jdGlvbihlKXtyZXR1cm4gZnVuY3Rpb24oKXt0cnl7Zm9yKHZhciB0PWFyZ3VtZW50cy5sZW5ndGgsaT1uZXcgQXJyYXkodCkscj0wO3I8dDtyKyspaVtyXT1hcmd1bWVudHNbcl07cmV0dXJuIGUuYXBwbHkodGhpcyxpKX1jYXRjaChlKXtxLmNyaXRpY2FsKFwiSW1wbGVtZW50YXRpb24gZXJyb3IuIFBsZWFzZSB0dXJuIG9uIGRlYnVnIG1vZGUgYW5kIG9wZW4gYSB0aWNrZXQgb24gaHR0cHM6Ly9hcHAucG9zdGhvZy5jb20vaG9tZSNwYW5lbD1zdXBwb3J0JTNBc3VwcG9ydCUzQS5cIikscS5jcml0aWNhbChlKX19fSx0ZT1mdW5jdGlvbihlKXt2YXIgdD17fTtyZXR1cm4gWShlLChmdW5jdGlvbihlLGkpe1IoZSkmJmUubGVuZ3RoPjAmJih0W2ldPWUpfSkpLHR9O2Z1bmN0aW9uIGllKGUsdCl7cmV0dXJuIGk9ZSxyPWU9PlIoZSkmJiEkKHQpP2Uuc2xpY2UoMCx0KTplLG49bmV3IFNldCxmdW5jdGlvbiBlKHQsaSl7cmV0dXJuIHQhPT1PYmplY3QodCk/cj9yKHQsaSk6dDpuLmhhcyh0KT92b2lkIDA6KG4uYWRkKHQpLHgodCk/KHM9W10sSih0LCh0PT57cy5wdXNoKGUodCkpfSkpKToocz17fSxZKHQsKCh0LGkpPT57bi5oYXModCl8fChzW2ldPWUodCxpKSl9KSkpLHMpO3ZhciBzfShpKTt2YXIgaSxyLG59dmFyIHJlPWZ1bmN0aW9uKCl7ZnVuY3Rpb24gZSh0KXtyZXR1cm4gdCYmKHQucHJldmVudERlZmF1bHQ9ZS5wcmV2ZW50RGVmYXVsdCx0LnN0b3BQcm9wYWdhdGlvbj1lLnN0b3BQcm9wYWdhdGlvbiksdH1yZXR1cm4gZS5wcmV2ZW50RGVmYXVsdD1mdW5jdGlvbigpe3RoaXMucmV0dXJuVmFsdWU9ITF9LGUuc3RvcFByb3BhZ2F0aW9uPWZ1bmN0aW9uKCl7dGhpcy5jYW5jZWxCdWJibGU9ITB9LGZ1bmN0aW9uKGkscixuLHMsbyl7aWYoaSlpZihpLmFkZEV2ZW50TGlzdGVuZXImJiFzKWkuYWRkRXZlbnRMaXN0ZW5lcihyLG4sISFvKTtlbHNle3ZhciBhPVwib25cIityLGw9aVthXTtpW2FdPWZ1bmN0aW9uKGkscixuKXtyZXR1cm4gZnVuY3Rpb24ocyl7aWYocz1zfHxlKG51bGw9PXQ/dm9pZCAwOnQuZXZlbnQpKXt2YXIgbyxhPSEwO0kobikmJihvPW4ocykpO3ZhciBsPXIuY2FsbChpLHMpO3JldHVybiExIT09byYmITEhPT1sfHwoYT0hMSksYX19fShpLG4sbCl9ZWxzZSBxLmVycm9yKFwiTm8gdmFsaWQgZWxlbWVudCBwcm92aWRlZCB0byByZWdpc3Rlcl9ldmVudFwiKX19KCk7ZnVuY3Rpb24gbmUoZSx0KXtmb3IodmFyIGk9MDtpPGUubGVuZ3RoO2krKylpZih0KGVbaV0pKXJldHVybiBlW2ldfXZhciBzZT1cIiRwZW9wbGVfZGlzdGluY3RfaWRcIixvZT1cIl9fYWxpYXNcIixhZT1cIl9fdGltZXJzXCIsbGU9XCIkYXV0b2NhcHR1cmVfZGlzYWJsZWRfc2VydmVyX3NpZGVcIix1ZT1cIiRoZWF0bWFwc19lbmFibGVkX3NlcnZlcl9zaWRlXCIsY2U9XCIkZXhjZXB0aW9uX2NhcHR1cmVfZW5hYmxlZF9zZXJ2ZXJfc2lkZVwiLGRlPVwiJHdlYl92aXRhbHNfZW5hYmxlZF9zZXJ2ZXJfc2lkZVwiLGhlPVwiJGRlYWRfY2xpY2tzX2VuYWJsZWRfc2VydmVyX3NpZGVcIixfZT1cIiR3ZWJfdml0YWxzX2FsbG93ZWRfbWV0cmljc1wiLHBlPVwiJHNlc3Npb25fcmVjb3JkaW5nX2VuYWJsZWRfc2VydmVyX3NpZGVcIix2ZT1cIiRjb25zb2xlX2xvZ19yZWNvcmRpbmdfZW5hYmxlZF9zZXJ2ZXJfc2lkZVwiLGdlPVwiJHNlc3Npb25fcmVjb3JkaW5nX25ldHdvcmtfcGF5bG9hZF9jYXB0dXJlXCIsZmU9XCIkc2Vzc2lvbl9yZWNvcmRpbmdfY2FudmFzX3JlY29yZGluZ1wiLG1lPVwiJHJlcGxheV9zYW1wbGVfcmF0ZVwiLGJlPVwiJHJlcGxheV9taW5pbXVtX2R1cmF0aW9uXCIseWU9XCIkcmVwbGF5X3NjcmlwdF9jb25maWdcIix3ZT1cIiRzZXNpZFwiLFNlPVwiJHNlc3Npb25faXNfc2FtcGxlZFwiLEVlPVwiJHNlc3Npb25fcmVjb3JkaW5nX3VybF90cmlnZ2VyX2FjdGl2YXRlZF9zZXNzaW9uXCIsa2U9XCIkc2Vzc2lvbl9yZWNvcmRpbmdfZXZlbnRfdHJpZ2dlcl9hY3RpdmF0ZWRfc2Vzc2lvblwiLHhlPVwiJGVuYWJsZWRfZmVhdHVyZV9mbGFnc1wiLEllPVwiJGVhcmx5X2FjY2Vzc19mZWF0dXJlc1wiLENlPVwiJHN0b3JlZF9wZXJzb25fcHJvcGVydGllc1wiLFBlPVwiJHN0b3JlZF9ncm91cF9wcm9wZXJ0aWVzXCIsRmU9XCIkc3VydmV5c1wiLFJlPVwiJHN1cnZleXNfYWN0aXZhdGVkXCIsVGU9XCIkZmxhZ19jYWxsX3JlcG9ydGVkXCIsJGU9XCIkdXNlcl9zdGF0ZVwiLE9lPVwiJGNsaWVudF9zZXNzaW9uX3Byb3BzXCIsTWU9XCIkY2FwdHVyZV9yYXRlX2xpbWl0XCIsTGU9XCIkaW5pdGlhbF9jYW1wYWlnbl9wYXJhbXNcIixBZT1cIiRpbml0aWFsX3JlZmVycmVyX2luZm9cIixEZT1cIiRpbml0aWFsX3BlcnNvbl9pbmZvXCIsTmU9XCIkZXBwXCIscWU9XCJfX1BPU1RIT0dfVE9PTEJBUl9fXCIsQmU9XCIkcG9zdGhvZ19jb29raWVsZXNzXCIsSGU9W3NlLG9lLFwiX19jbXBuc1wiLGFlLHBlLHVlLHdlLHhlLCRlLEllLFBlLENlLEZlLFRlLE9lLE1lLExlLEFlLE5lXSxVZT1CKFwiW0ZlYXR1cmVGbGFnc11cIiksemU9XCIkYWN0aXZlX2ZlYXR1cmVfZmxhZ3NcIixqZT1cIiRvdmVycmlkZV9mZWF0dXJlX2ZsYWdzXCIsV2U9XCIkZmVhdHVyZV9mbGFnX3BheWxvYWRzXCIsVmU9ZT0+e3ZhciB0PXt9O2Zvcih2YXJbaSxyXW9mIFEoZXx8e30pKXImJih0W2ldPXIpO3JldHVybiB0fTtjbGFzcyBHZXtjb25zdHJ1Y3RvcihlKXtXKHRoaXMsXCJfb3ZlcnJpZGVfd2FybmluZ1wiLCExKSxXKHRoaXMsXCJfaGFzTG9hZGVkRmxhZ3NcIiwhMSksVyh0aGlzLFwiX3JlcXVlc3RJbkZsaWdodFwiLCExKSxXKHRoaXMsXCJfcmVsb2FkaW5nRGlzYWJsZWRcIiwhMSksVyh0aGlzLFwiX2FkZGl0aW9uYWxSZWxvYWRSZXF1ZXN0ZWRcIiwhMSksVyh0aGlzLFwiX2RlY2lkZUNhbGxlZFwiLCExKSxXKHRoaXMsXCJfZmxhZ3NMb2FkZWRGcm9tUmVtb3RlXCIsITEpLHRoaXMuaW5zdGFuY2U9ZSx0aGlzLmZlYXR1cmVGbGFnRXZlbnRIYW5kbGVycz1bXX1kZWNpZGUoKXtpZih0aGlzLmluc3RhbmNlLmNvbmZpZy5fX3ByZXZpZXdfcmVtb3RlX2NvbmZpZyl0aGlzLl9kZWNpZGVDYWxsZWQ9ITA7ZWxzZXt2YXIgZT0hdGhpcy5fcmVsb2FkRGVib3VuY2VyJiYodGhpcy5pbnN0YW5jZS5jb25maWcuYWR2YW5jZWRfZGlzYWJsZV9mZWF0dXJlX2ZsYWdzfHx0aGlzLmluc3RhbmNlLmNvbmZpZy5hZHZhbmNlZF9kaXNhYmxlX2ZlYXR1cmVfZmxhZ3Nfb25fZmlyc3RfbG9hZCk7dGhpcy5fY2FsbERlY2lkZUVuZHBvaW50KHtkaXNhYmxlRmxhZ3M6ZX0pfX1nZXQgaGFzTG9hZGVkRmxhZ3MoKXtyZXR1cm4gdGhpcy5faGFzTG9hZGVkRmxhZ3N9Z2V0RmxhZ3MoKXtyZXR1cm4gT2JqZWN0LmtleXModGhpcy5nZXRGbGFnVmFyaWFudHMoKSl9Z2V0RmxhZ1ZhcmlhbnRzKCl7dmFyIGU9dGhpcy5pbnN0YW5jZS5nZXRfcHJvcGVydHkoeGUpLHQ9dGhpcy5pbnN0YW5jZS5nZXRfcHJvcGVydHkoamUpO2lmKCF0KXJldHVybiBlfHx7fTtmb3IodmFyIGk9Syh7fSxlKSxyPU9iamVjdC5rZXlzKHQpLG49MDtuPHIubGVuZ3RoO24rKylpW3Jbbl1dPXRbcltuXV07cmV0dXJuIHRoaXMuX292ZXJyaWRlX3dhcm5pbmd8fChVZS53YXJuKFwiIE92ZXJyaWRpbmcgZmVhdHVyZSBmbGFncyFcIix7ZW5hYmxlZEZsYWdzOmUsb3ZlcnJpZGRlbkZsYWdzOnQsZmluYWxGbGFnczppfSksdGhpcy5fb3ZlcnJpZGVfd2FybmluZz0hMCksaX1nZXRGbGFnUGF5bG9hZHMoKXtyZXR1cm4gdGhpcy5pbnN0YW5jZS5nZXRfcHJvcGVydHkoV2UpfHx7fX1yZWxvYWRGZWF0dXJlRmxhZ3MoKXt0aGlzLl9yZWxvYWRpbmdEaXNhYmxlZHx8dGhpcy5pbnN0YW5jZS5jb25maWcuYWR2YW5jZWRfZGlzYWJsZV9mZWF0dXJlX2ZsYWdzfHx0aGlzLl9yZWxvYWREZWJvdW5jZXJ8fCh0aGlzLl9yZWxvYWREZWJvdW5jZXI9c2V0VGltZW91dCgoKCk9Pnt0aGlzLl9jYWxsRGVjaWRlRW5kcG9pbnQoKX0pLDUpKX1jbGVhckRlYm91bmNlcigpe2NsZWFyVGltZW91dCh0aGlzLl9yZWxvYWREZWJvdW5jZXIpLHRoaXMuX3JlbG9hZERlYm91bmNlcj12b2lkIDB9ZW5zdXJlRmxhZ3NMb2FkZWQoKXt0aGlzLl9oYXNMb2FkZWRGbGFnc3x8dGhpcy5fcmVxdWVzdEluRmxpZ2h0fHx0aGlzLl9yZWxvYWREZWJvdW5jZXJ8fHRoaXMucmVsb2FkRmVhdHVyZUZsYWdzKCl9c2V0QW5vbnltb3VzRGlzdGluY3RJZChlKXt0aGlzLiRhbm9uX2Rpc3RpbmN0X2lkPWV9c2V0UmVsb2FkaW5nUGF1c2VkKGUpe3RoaXMuX3JlbG9hZGluZ0Rpc2FibGVkPWV9X2NhbGxEZWNpZGVFbmRwb2ludCh0KXtpZih0aGlzLmNsZWFyRGVib3VuY2VyKCksIXRoaXMuaW5zdGFuY2UuY29uZmlnLmFkdmFuY2VkX2Rpc2FibGVfZGVjaWRlKWlmKHRoaXMuX3JlcXVlc3RJbkZsaWdodCl0aGlzLl9hZGRpdGlvbmFsUmVsb2FkUmVxdWVzdGVkPSEwO2Vsc2V7dmFyIGk9e3Rva2VuOnRoaXMuaW5zdGFuY2UuY29uZmlnLnRva2VuLGRpc3RpbmN0X2lkOnRoaXMuaW5zdGFuY2UuZ2V0X2Rpc3RpbmN0X2lkKCksZ3JvdXBzOnRoaXMuaW5zdGFuY2UuZ2V0R3JvdXBzKCksJGFub25fZGlzdGluY3RfaWQ6dGhpcy4kYW5vbl9kaXN0aW5jdF9pZCxwZXJzb25fcHJvcGVydGllczp0aGlzLmluc3RhbmNlLmdldF9wcm9wZXJ0eShDZSksZ3JvdXBfcHJvcGVydGllczp0aGlzLmluc3RhbmNlLmdldF9wcm9wZXJ0eShQZSl9OyhudWxsIT10JiZ0LmRpc2FibGVGbGFnc3x8dGhpcy5pbnN0YW5jZS5jb25maWcuYWR2YW5jZWRfZGlzYWJsZV9mZWF0dXJlX2ZsYWdzKSYmKGkuZGlzYWJsZV9mbGFncz0hMCksdGhpcy5fcmVxdWVzdEluRmxpZ2h0PSEwLHRoaXMuaW5zdGFuY2UuX3NlbmRfcmVxdWVzdCh7bWV0aG9kOlwiUE9TVFwiLHVybDp0aGlzLmluc3RhbmNlLnJlcXVlc3RSb3V0ZXIuZW5kcG9pbnRGb3IoXCJhcGlcIixcIi9kZWNpZGUvP3Y9M1wiKSxkYXRhOmksY29tcHJlc3Npb246dGhpcy5pbnN0YW5jZS5jb25maWcuZGlzYWJsZV9jb21wcmVzc2lvbj92b2lkIDA6ZS5CYXNlNjQsdGltZW91dDp0aGlzLmluc3RhbmNlLmNvbmZpZy5mZWF0dXJlX2ZsYWdfcmVxdWVzdF90aW1lb3V0X21zLGNhbGxiYWNrOmU9Pnt2YXIgdCxyLG49ITA7KDIwMD09PWUuc3RhdHVzQ29kZSYmKHRoaXMuJGFub25fZGlzdGluY3RfaWQ9dm9pZCAwLG49ITEpLHRoaXMuX3JlcXVlc3RJbkZsaWdodD0hMSx0aGlzLl9kZWNpZGVDYWxsZWQpfHwodGhpcy5fZGVjaWRlQ2FsbGVkPSEwLHRoaXMuaW5zdGFuY2UuX29uUmVtb3RlQ29uZmlnKG51bGwhPT0ocj1lLmpzb24pJiZ2b2lkIDAhPT1yP3I6e30pKTtpLmRpc2FibGVfZmxhZ3N8fCh0aGlzLl9mbGFnc0xvYWRlZEZyb21SZW1vdGU9IW4sdGhpcy5yZWNlaXZlZEZlYXR1cmVGbGFncyhudWxsIT09KHQ9ZS5qc29uKSYmdm9pZCAwIT09dD90Ont9LG4pLHRoaXMuX2FkZGl0aW9uYWxSZWxvYWRSZXF1ZXN0ZWQmJih0aGlzLl9hZGRpdGlvbmFsUmVsb2FkUmVxdWVzdGVkPSExLHRoaXMuX2NhbGxEZWNpZGVFbmRwb2ludCgpKSl9fSl9fWdldEZlYXR1cmVGbGFnKGUpe3ZhciB0PWFyZ3VtZW50cy5sZW5ndGg+MSYmdm9pZCAwIT09YXJndW1lbnRzWzFdP2FyZ3VtZW50c1sxXTp7fTtpZih0aGlzLl9oYXNMb2FkZWRGbGFnc3x8dGhpcy5nZXRGbGFncygpJiZ0aGlzLmdldEZsYWdzKCkubGVuZ3RoPjApe3ZhciBpLHIsbixzLG8sYT10aGlzLmdldEZsYWdWYXJpYW50cygpW2VdLGw9XCJcIi5jb25jYXQoYSksdT10aGlzLmluc3RhbmNlLmdldF9wcm9wZXJ0eShUZSl8fHt9O2lmKHQuc2VuZF9ldmVudHx8IShcInNlbmRfZXZlbnRcImluIHQpKWlmKCEoZSBpbiB1KXx8IXVbZV0uaW5jbHVkZXMobCkpeCh1W2VdKT91W2VdLnB1c2gobCk6dVtlXT1bbF0sbnVsbD09PShpPXRoaXMuaW5zdGFuY2UucGVyc2lzdGVuY2UpfHx2b2lkIDA9PT1pfHxpLnJlZ2lzdGVyKHtbVGVdOnV9KSx0aGlzLmluc3RhbmNlLmNhcHR1cmUoXCIkZmVhdHVyZV9mbGFnX2NhbGxlZFwiLHskZmVhdHVyZV9mbGFnOmUsJGZlYXR1cmVfZmxhZ19yZXNwb25zZTphLCRmZWF0dXJlX2ZsYWdfcGF5bG9hZDp0aGlzLmdldEZlYXR1cmVGbGFnUGF5bG9hZChlKXx8bnVsbCwkZmVhdHVyZV9mbGFnX2Jvb3RzdHJhcHBlZF9yZXNwb25zZToobnVsbD09PShyPXRoaXMuaW5zdGFuY2UuY29uZmlnLmJvb3RzdHJhcCl8fHZvaWQgMD09PXJ8fG51bGw9PT0obj1yLmZlYXR1cmVGbGFncyl8fHZvaWQgMD09PW4/dm9pZCAwOm5bZV0pfHxudWxsLCRmZWF0dXJlX2ZsYWdfYm9vdHN0cmFwcGVkX3BheWxvYWQ6KG51bGw9PT0ocz10aGlzLmluc3RhbmNlLmNvbmZpZy5ib290c3RyYXApfHx2b2lkIDA9PT1zfHxudWxsPT09KG89cy5mZWF0dXJlRmxhZ1BheWxvYWRzKXx8dm9pZCAwPT09bz92b2lkIDA6b1tlXSl8fG51bGwsJHVzZWRfYm9vdHN0cmFwX3ZhbHVlOiF0aGlzLl9mbGFnc0xvYWRlZEZyb21SZW1vdGV9KTtyZXR1cm4gYX1VZS53YXJuKCdnZXRGZWF0dXJlRmxhZyBmb3Iga2V5IFwiJytlK1wiXFxcIiBmYWlsZWQuIEZlYXR1cmUgZmxhZ3MgZGlkbid0IGxvYWQgaW4gdGltZS5cIil9Z2V0RmVhdHVyZUZsYWdQYXlsb2FkKGUpe3JldHVybiB0aGlzLmdldEZsYWdQYXlsb2FkcygpW2VdfWlzRmVhdHVyZUVuYWJsZWQoZSl7dmFyIHQ9YXJndW1lbnRzLmxlbmd0aD4xJiZ2b2lkIDAhPT1hcmd1bWVudHNbMV0/YXJndW1lbnRzWzFdOnt9O2lmKHRoaXMuX2hhc0xvYWRlZEZsYWdzfHx0aGlzLmdldEZsYWdzKCkmJnRoaXMuZ2V0RmxhZ3MoKS5sZW5ndGg+MClyZXR1cm4hIXRoaXMuZ2V0RmVhdHVyZUZsYWcoZSx0KTtVZS53YXJuKCdpc0ZlYXR1cmVFbmFibGVkIGZvciBrZXkgXCInK2UrXCJcXFwiIGZhaWxlZC4gRmVhdHVyZSBmbGFncyBkaWRuJ3QgbG9hZCBpbiB0aW1lLlwiKX1hZGRGZWF0dXJlRmxhZ3NIYW5kbGVyKGUpe3RoaXMuZmVhdHVyZUZsYWdFdmVudEhhbmRsZXJzLnB1c2goZSl9cmVtb3ZlRmVhdHVyZUZsYWdzSGFuZGxlcihlKXt0aGlzLmZlYXR1cmVGbGFnRXZlbnRIYW5kbGVycz10aGlzLmZlYXR1cmVGbGFnRXZlbnRIYW5kbGVycy5maWx0ZXIoKHQ9PnQhPT1lKSl9cmVjZWl2ZWRGZWF0dXJlRmxhZ3MoZSx0KXtpZih0aGlzLmluc3RhbmNlLnBlcnNpc3RlbmNlKXt0aGlzLl9oYXNMb2FkZWRGbGFncz0hMDt2YXIgaT10aGlzLmdldEZsYWdWYXJpYW50cygpLHI9dGhpcy5nZXRGbGFnUGF5bG9hZHMoKTshZnVuY3Rpb24oZSx0KXt2YXIgaT1hcmd1bWVudHMubGVuZ3RoPjImJnZvaWQgMCE9PWFyZ3VtZW50c1syXT9hcmd1bWVudHNbMl06e30scj1hcmd1bWVudHMubGVuZ3RoPjMmJnZvaWQgMCE9PWFyZ3VtZW50c1szXT9hcmd1bWVudHNbM106e30sbj1lLmZlYXR1cmVGbGFncyxzPWUuZmVhdHVyZUZsYWdQYXlsb2FkcztpZihuKWlmKHgobikpe3ZhciBvPXt9O2lmKG4pZm9yKHZhciBhPTA7YTxuLmxlbmd0aDthKyspb1tuW2FdXT0hMDt0JiZ0LnJlZ2lzdGVyKHtbemVdOm4sW3hlXTpvfSl9ZWxzZXt2YXIgbD1uLHU9cztlLmVycm9yc1doaWxlQ29tcHV0aW5nRmxhZ3MmJihsPWooaih7fSxpKSxsKSx1PWooaih7fSxyKSx1KSksdCYmdC5yZWdpc3Rlcih7W3plXTpPYmplY3Qua2V5cyhWZShsKSksW3hlXTpsfHx7fSxbV2VdOnV8fHt9fSl9fShlLHRoaXMuaW5zdGFuY2UucGVyc2lzdGVuY2UsaSxyKSx0aGlzLl9maXJlRmVhdHVyZUZsYWdzQ2FsbGJhY2tzKHQpfX1vdmVycmlkZShlKXt2YXIgdD1hcmd1bWVudHMubGVuZ3RoPjEmJnZvaWQgMCE9PWFyZ3VtZW50c1sxXSYmYXJndW1lbnRzWzFdO2lmKCF0aGlzLmluc3RhbmNlLl9fbG9hZGVkfHwhdGhpcy5pbnN0YW5jZS5wZXJzaXN0ZW5jZSlyZXR1cm4gVWUudW5pbml0aWFsaXplZFdhcm5pbmcoXCJwb3N0aG9nLmZlYXR1cmVfZmxhZ3Mub3ZlcnJpZGVcIik7aWYodGhpcy5fb3ZlcnJpZGVfd2FybmluZz10LCExPT09ZSl0aGlzLmluc3RhbmNlLnBlcnNpc3RlbmNlLnVucmVnaXN0ZXIoamUpO2Vsc2UgaWYoeChlKSl7Zm9yKHZhciBpPXt9LHI9MDtyPGUubGVuZ3RoO3IrKylpW2Vbcl1dPSEwO3RoaXMuaW5zdGFuY2UucGVyc2lzdGVuY2UucmVnaXN0ZXIoe1tqZV06aX0pfWVsc2UgdGhpcy5pbnN0YW5jZS5wZXJzaXN0ZW5jZS5yZWdpc3Rlcih7W2plXTplfSl9b25GZWF0dXJlRmxhZ3MoZSl7aWYodGhpcy5hZGRGZWF0dXJlRmxhZ3NIYW5kbGVyKGUpLHRoaXMuX2hhc0xvYWRlZEZsYWdzKXt2YXJ7ZmxhZ3M6dCxmbGFnVmFyaWFudHM6aX09dGhpcy5fcHJlcGFyZUZlYXR1cmVGbGFnc0ZvckNhbGxiYWNrcygpO2UodCxpKX1yZXR1cm4oKT0+dGhpcy5yZW1vdmVGZWF0dXJlRmxhZ3NIYW5kbGVyKGUpfXVwZGF0ZUVhcmx5QWNjZXNzRmVhdHVyZUVucm9sbG1lbnQoZSx0KXt2YXIgaSxyPSh0aGlzLmluc3RhbmNlLmdldF9wcm9wZXJ0eShJZSl8fFtdKS5maW5kKCh0PT50LmZsYWdLZXk9PT1lKSksbj17W1wiJGZlYXR1cmVfZW5yb2xsbWVudC9cIi5jb25jYXQoZSldOnR9LHM9eyRmZWF0dXJlX2ZsYWc6ZSwkZmVhdHVyZV9lbnJvbGxtZW50OnQsJHNldDpufTtyJiYocy4kZWFybHlfYWNjZXNzX2ZlYXR1cmVfbmFtZT1yLm5hbWUpLHRoaXMuaW5zdGFuY2UuY2FwdHVyZShcIiRmZWF0dXJlX2Vucm9sbG1lbnRfdXBkYXRlXCIscyksdGhpcy5zZXRQZXJzb25Qcm9wZXJ0aWVzRm9yRmxhZ3MobiwhMSk7dmFyIG89aihqKHt9LHRoaXMuZ2V0RmxhZ1ZhcmlhbnRzKCkpLHt9LHtbZV06dH0pO251bGw9PT0oaT10aGlzLmluc3RhbmNlLnBlcnNpc3RlbmNlKXx8dm9pZCAwPT09aXx8aS5yZWdpc3Rlcih7W3plXTpPYmplY3Qua2V5cyhWZShvKSksW3hlXTpvfSksdGhpcy5fZmlyZUZlYXR1cmVGbGFnc0NhbGxiYWNrcygpfWdldEVhcmx5QWNjZXNzRmVhdHVyZXMoZSl7dmFyIHQ9YXJndW1lbnRzLmxlbmd0aD4xJiZ2b2lkIDAhPT1hcmd1bWVudHNbMV0mJmFyZ3VtZW50c1sxXSxpPXRoaXMuaW5zdGFuY2UuZ2V0X3Byb3BlcnR5KEllKTtpZihpJiYhdClyZXR1cm4gZShpKTt0aGlzLmluc3RhbmNlLl9zZW5kX3JlcXVlc3Qoe3VybDp0aGlzLmluc3RhbmNlLnJlcXVlc3RSb3V0ZXIuZW5kcG9pbnRGb3IoXCJhcGlcIixcIi9hcGkvZWFybHlfYWNjZXNzX2ZlYXR1cmVzLz90b2tlbj1cIi5jb25jYXQodGhpcy5pbnN0YW5jZS5jb25maWcudG9rZW4pKSxtZXRob2Q6XCJHRVRcIixjYWxsYmFjazp0PT57dmFyIGk7aWYodC5qc29uKXt2YXIgcj10Lmpzb24uZWFybHlBY2Nlc3NGZWF0dXJlcztyZXR1cm4gbnVsbD09PShpPXRoaXMuaW5zdGFuY2UucGVyc2lzdGVuY2UpfHx2b2lkIDA9PT1pfHxpLnJlZ2lzdGVyKHtbSWVdOnJ9KSxlKHIpfX19KX1fcHJlcGFyZUZlYXR1cmVGbGFnc0ZvckNhbGxiYWNrcygpe3ZhciBlPXRoaXMuZ2V0RmxhZ3MoKSx0PXRoaXMuZ2V0RmxhZ1ZhcmlhbnRzKCk7cmV0dXJue2ZsYWdzOmUuZmlsdGVyKChlPT50W2VdKSksZmxhZ1ZhcmlhbnRzOk9iamVjdC5rZXlzKHQpLmZpbHRlcigoZT0+dFtlXSkpLnJlZHVjZSgoKGUsaSk9PihlW2ldPXRbaV0sZSkpLHt9KX19X2ZpcmVGZWF0dXJlRmxhZ3NDYWxsYmFja3MoZSl7dmFye2ZsYWdzOnQsZmxhZ1ZhcmlhbnRzOml9PXRoaXMuX3ByZXBhcmVGZWF0dXJlRmxhZ3NGb3JDYWxsYmFja3MoKTt0aGlzLmZlYXR1cmVGbGFnRXZlbnRIYW5kbGVycy5mb3JFYWNoKChyPT5yKHQsaSx7ZXJyb3JzTG9hZGluZzplfSkpKX1zZXRQZXJzb25Qcm9wZXJ0aWVzRm9yRmxhZ3MoZSl7dmFyIHQ9IShhcmd1bWVudHMubGVuZ3RoPjEmJnZvaWQgMCE9PWFyZ3VtZW50c1sxXSl8fGFyZ3VtZW50c1sxXSxpPXRoaXMuaW5zdGFuY2UuZ2V0X3Byb3BlcnR5KENlKXx8e307dGhpcy5pbnN0YW5jZS5yZWdpc3Rlcih7W0NlXTpqKGooe30saSksZSl9KSx0JiZ0aGlzLmluc3RhbmNlLnJlbG9hZEZlYXR1cmVGbGFncygpfXJlc2V0UGVyc29uUHJvcGVydGllc0ZvckZsYWdzKCl7dGhpcy5pbnN0YW5jZS51bnJlZ2lzdGVyKENlKX1zZXRHcm91cFByb3BlcnRpZXNGb3JGbGFncyhlKXt2YXIgdD0hKGFyZ3VtZW50cy5sZW5ndGg+MSYmdm9pZCAwIT09YXJndW1lbnRzWzFdKXx8YXJndW1lbnRzWzFdLGk9dGhpcy5pbnN0YW5jZS5nZXRfcHJvcGVydHkoUGUpfHx7fTswIT09T2JqZWN0LmtleXMoaSkubGVuZ3RoJiZPYmplY3Qua2V5cyhpKS5mb3JFYWNoKCh0PT57aVt0XT1qKGooe30saVt0XSksZVt0XSksZGVsZXRlIGVbdF19KSksdGhpcy5pbnN0YW5jZS5yZWdpc3Rlcih7W1BlXTpqKGooe30saSksZSl9KSx0JiZ0aGlzLmluc3RhbmNlLnJlbG9hZEZlYXR1cmVGbGFncygpfXJlc2V0R3JvdXBQcm9wZXJ0aWVzRm9yRmxhZ3MoZSl7aWYoZSl7dmFyIHQ9dGhpcy5pbnN0YW5jZS5nZXRfcHJvcGVydHkoUGUpfHx7fTt0aGlzLmluc3RhbmNlLnJlZ2lzdGVyKHtbUGVdOmooaih7fSx0KSx7fSx7W2VdOnt9fSl9KX1lbHNlIHRoaXMuaW5zdGFuY2UudW5yZWdpc3RlcihQZSl9fU1hdGgudHJ1bmN8fChNYXRoLnRydW5jPWZ1bmN0aW9uKGUpe3JldHVybiBlPDA/TWF0aC5jZWlsKGUpOk1hdGguZmxvb3IoZSl9KSxOdW1iZXIuaXNJbnRlZ2VyfHwoTnVtYmVyLmlzSW50ZWdlcj1mdW5jdGlvbihlKXtyZXR1cm4gTShlKSYmaXNGaW5pdGUoZSkmJk1hdGguZmxvb3IoZSk9PT1lfSk7dmFyIEplPVwiMDEyMzQ1Njc4OWFiY2RlZlwiO2NsYXNzIFlle2NvbnN0cnVjdG9yKGUpe2lmKHRoaXMuYnl0ZXM9ZSwxNiE9PWUubGVuZ3RoKXRocm93IG5ldyBUeXBlRXJyb3IoXCJub3QgMTI4LWJpdCBsZW5ndGhcIil9c3RhdGljIGZyb21GaWVsZHNWNyhlLHQsaSxyKXtpZighTnVtYmVyLmlzSW50ZWdlcihlKXx8IU51bWJlci5pc0ludGVnZXIodCl8fCFOdW1iZXIuaXNJbnRlZ2VyKGkpfHwhTnVtYmVyLmlzSW50ZWdlcihyKXx8ZTwwfHx0PDB8fGk8MHx8cjwwfHxlPjB4ZmZmZmZmZmZmZmZmfHx0PjQwOTV8fGk+MTA3Mzc0MTgyM3x8cj40Mjk0OTY3Mjk1KXRocm93IG5ldyBSYW5nZUVycm9yKFwiaW52YWxpZCBmaWVsZCB2YWx1ZVwiKTt2YXIgbj1uZXcgVWludDhBcnJheSgxNik7cmV0dXJuIG5bMF09ZS9NYXRoLnBvdygyLDQwKSxuWzFdPWUvTWF0aC5wb3coMiwzMiksblsyXT1lL01hdGgucG93KDIsMjQpLG5bM109ZS9NYXRoLnBvdygyLDE2KSxuWzRdPWUvTWF0aC5wb3coMiw4KSxuWzVdPWUsbls2XT0xMTJ8dD4+Pjgsbls3XT10LG5bOF09MTI4fGk+Pj4yNCxuWzldPWk+Pj4xNixuWzEwXT1pPj4+OCxuWzExXT1pLG5bMTJdPXI+Pj4yNCxuWzEzXT1yPj4+MTYsblsxNF09cj4+PjgsblsxNV09cixuZXcgWWUobil9dG9TdHJpbmcoKXtmb3IodmFyIGU9XCJcIix0PTA7dDx0aGlzLmJ5dGVzLmxlbmd0aDt0KyspZT1lK0plLmNoYXJBdCh0aGlzLmJ5dGVzW3RdPj4+NCkrSmUuY2hhckF0KDE1JnRoaXMuYnl0ZXNbdF0pLDMhPT10JiY1IT09dCYmNyE9PXQmJjkhPT10fHwoZSs9XCItXCIpO2lmKDM2IT09ZS5sZW5ndGgpdGhyb3cgbmV3IEVycm9yKFwiSW52YWxpZCBVVUlEdjcgd2FzIGdlbmVyYXRlZFwiKTtyZXR1cm4gZX1jbG9uZSgpe3JldHVybiBuZXcgWWUodGhpcy5ieXRlcy5zbGljZSgwKSl9ZXF1YWxzKGUpe3JldHVybiAwPT09dGhpcy5jb21wYXJlVG8oZSl9Y29tcGFyZVRvKGUpe2Zvcih2YXIgdD0wO3Q8MTY7dCsrKXt2YXIgaT10aGlzLmJ5dGVzW3RdLWUuYnl0ZXNbdF07aWYoMCE9PWkpcmV0dXJuIE1hdGguc2lnbihpKX1yZXR1cm4gMH19Y2xhc3MgS2V7Y29uc3RydWN0b3IoKXtXKHRoaXMsXCJ0aW1lc3RhbXBcIiwwKSxXKHRoaXMsXCJjb3VudGVyXCIsMCksVyh0aGlzLFwicmFuZG9tXCIsbmV3IFplKX1nZW5lcmF0ZSgpe3ZhciBlPXRoaXMuZ2VuZXJhdGVPckFib3J0KCk7aWYoRihlKSl7dGhpcy50aW1lc3RhbXA9MDt2YXIgdD10aGlzLmdlbmVyYXRlT3JBYm9ydCgpO2lmKEYodCkpdGhyb3cgbmV3IEVycm9yKFwiQ291bGQgbm90IGdlbmVyYXRlIFVVSUQgYWZ0ZXIgdGltZXN0YW1wIHJlc2V0XCIpO3JldHVybiB0fXJldHVybiBlfWdlbmVyYXRlT3JBYm9ydCgpe3ZhciBlPURhdGUubm93KCk7aWYoZT50aGlzLnRpbWVzdGFtcCl0aGlzLnRpbWVzdGFtcD1lLHRoaXMucmVzZXRDb3VudGVyKCk7ZWxzZXtpZighKGUrMWU0PnRoaXMudGltZXN0YW1wKSlyZXR1cm47dGhpcy5jb3VudGVyKyssdGhpcy5jb3VudGVyPjQzOTgwNDY1MTExMDMmJih0aGlzLnRpbWVzdGFtcCsrLHRoaXMucmVzZXRDb3VudGVyKCkpfXJldHVybiBZZS5mcm9tRmllbGRzVjcodGhpcy50aW1lc3RhbXAsTWF0aC50cnVuYyh0aGlzLmNvdW50ZXIvTWF0aC5wb3coMiwzMCkpLHRoaXMuY291bnRlciZNYXRoLnBvdygyLDMwKS0xLHRoaXMucmFuZG9tLm5leHRVaW50MzIoKSl9cmVzZXRDb3VudGVyKCl7dGhpcy5jb3VudGVyPTEwMjQqdGhpcy5yYW5kb20ubmV4dFVpbnQzMigpKygxMDIzJnRoaXMucmFuZG9tLm5leHRVaW50MzIoKSl9fXZhciBYZSxRZT1lPT57aWYoXCJ1bmRlZmluZWRcIiE9dHlwZW9mIFVVSURWN19ERU5ZX1dFQUtfUk5HJiZVVUlEVjdfREVOWV9XRUFLX1JORyl0aHJvdyBuZXcgRXJyb3IoXCJubyBjcnlwdG9ncmFwaGljYWxseSBzdHJvbmcgUk5HIGF2YWlsYWJsZVwiKTtmb3IodmFyIHQ9MDt0PGUubGVuZ3RoO3QrKyllW3RdPTY1NTM2Kk1hdGgudHJ1bmMoNjU1MzYqTWF0aC5yYW5kb20oKSkrTWF0aC50cnVuYyg2NTUzNipNYXRoLnJhbmRvbSgpKTtyZXR1cm4gZX07dCYmIUYodC5jcnlwdG8pJiZjcnlwdG8uZ2V0UmFuZG9tVmFsdWVzJiYoUWU9ZT0+Y3J5cHRvLmdldFJhbmRvbVZhbHVlcyhlKSk7Y2xhc3MgWmV7Y29uc3RydWN0b3IoKXtXKHRoaXMsXCJidWZmZXJcIixuZXcgVWludDMyQXJyYXkoOCkpLFcodGhpcyxcImN1cnNvclwiLDEvMCl9bmV4dFVpbnQzMigpe3JldHVybiB0aGlzLmN1cnNvcj49dGhpcy5idWZmZXIubGVuZ3RoJiYoUWUodGhpcy5idWZmZXIpLHRoaXMuY3Vyc29yPTApLHRoaXMuYnVmZmVyW3RoaXMuY3Vyc29yKytdfX12YXIgZXQ9KCk9PnR0KCkudG9TdHJpbmcoKSx0dD0oKT0+KFhlfHwoWGU9bmV3IEtlKSkuZ2VuZXJhdGUoKSxpdD1cIlRodSwgMDEgSmFuIDE5NzAgMDA6MDA6MDAgR01UXCIscnQ9XCJcIjt2YXIgbnQ9L1thLXowLTldW2EtejAtOS1dK1xcLlthLXpdezIsfSQvaTtmdW5jdGlvbiBzdChlLHQpe2lmKHQpe3ZhciBpPWZ1bmN0aW9uKGUpe3ZhciB0PWFyZ3VtZW50cy5sZW5ndGg+MSYmdm9pZCAwIT09YXJndW1lbnRzWzFdP2FyZ3VtZW50c1sxXTphO2lmKHJ0KXJldHVybiBydDtpZighdClyZXR1cm5cIlwiO2lmKFtcImxvY2FsaG9zdFwiLFwiMTI3LjAuMC4xXCJdLmluY2x1ZGVzKGUpKXJldHVyblwiXCI7Zm9yKHZhciBpPWUuc3BsaXQoXCIuXCIpLHI9TWF0aC5taW4oaS5sZW5ndGgsOCksbj1cImRtbl9jaGtfXCIrZXQoKSxzPW5ldyBSZWdFeHAoXCIoXnw7KVxcXFxzKlwiK24rXCI9MVwiKTshcnQmJnItLTspe3ZhciBvPWkuc2xpY2Uocikuam9pbihcIi5cIiksbD1uK1wiPTE7ZG9tYWluPS5cIitvO3QuY29va2llPWwscy50ZXN0KHQuY29va2llKSYmKHQuY29va2llPWwrXCI7ZXhwaXJlcz1cIitpdCxydD1vKX1yZXR1cm4gcnR9KGUpO2lmKCFpKXt2YXIgcj0oZT0+e3ZhciB0PWUubWF0Y2gobnQpO3JldHVybiB0P3RbMF06XCJcIn0pKGUpO3IhPT1pJiZxLmluZm8oXCJXYXJuaW5nOiBjb29raWUgc3ViZG9tYWluIGRpc2NvdmVyeSBtaXNtYXRjaFwiLHIsaSksaT1yfXJldHVybiBpP1wiOyBkb21haW49LlwiK2k6XCJcIn1yZXR1cm5cIlwifXZhciBvdD17aXNfc3VwcG9ydGVkOigpPT4hIWEsZXJyb3I6ZnVuY3Rpb24oZSl7cS5lcnJvcihcImNvb2tpZVN0b3JlIGVycm9yOiBcIitlKX0sZ2V0OmZ1bmN0aW9uKGUpe2lmKGEpe3RyeXtmb3IodmFyIHQ9ZStcIj1cIixpPWEuY29va2llLnNwbGl0KFwiO1wiKS5maWx0ZXIoKGU9PmUubGVuZ3RoKSkscj0wO3I8aS5sZW5ndGg7cisrKXtmb3IodmFyIG49aVtyXTtcIiBcIj09bi5jaGFyQXQoMCk7KW49bi5zdWJzdHJpbmcoMSxuLmxlbmd0aCk7aWYoMD09PW4uaW5kZXhPZih0KSlyZXR1cm4gZGVjb2RlVVJJQ29tcG9uZW50KG4uc3Vic3RyaW5nKHQubGVuZ3RoLG4ubGVuZ3RoKSl9fWNhdGNoKGUpe31yZXR1cm4gbnVsbH19LHBhcnNlOmZ1bmN0aW9uKGUpe3ZhciB0O3RyeXt0PUpTT04ucGFyc2Uob3QuZ2V0KGUpKXx8e319Y2F0Y2goZSl7fXJldHVybiB0fSxzZXQ6ZnVuY3Rpb24oZSx0LGkscixuKXtpZihhKXRyeXt2YXIgcz1cIlwiLG89XCJcIixsPXN0KGEubG9jYXRpb24uaG9zdG5hbWUscik7aWYoaSl7dmFyIHU9bmV3IERhdGU7dS5zZXRUaW1lKHUuZ2V0VGltZSgpKzI0KmkqNjAqNjAqMWUzKSxzPVwiOyBleHBpcmVzPVwiK3UudG9VVENTdHJpbmcoKX1uJiYobz1cIjsgc2VjdXJlXCIpO3ZhciBjPWUrXCI9XCIrZW5jb2RlVVJJQ29tcG9uZW50KEpTT04uc3RyaW5naWZ5KHQpKStzK1wiOyBTYW1lU2l0ZT1MYXg7IHBhdGg9L1wiK2wrbztyZXR1cm4gYy5sZW5ndGg+MzY4Ni40JiZxLndhcm4oXCJjb29raWVTdG9yZSB3YXJuaW5nOiBsYXJnZSBjb29raWUsIGxlbj1cIitjLmxlbmd0aCksYS5jb29raWU9YyxjfWNhdGNoKGUpe3JldHVybn19LHJlbW92ZTpmdW5jdGlvbihlLHQpe3RyeXtvdC5zZXQoZSxcIlwiLC0xLHQpfWNhdGNoKGUpe3JldHVybn19fSxhdD1udWxsLGx0PXtpc19zdXBwb3J0ZWQ6ZnVuY3Rpb24oKXtpZighJChhdCkpcmV0dXJuIGF0O3ZhciBlPSEwO2lmKEYodCkpZT0hMTtlbHNlIHRyeXt2YXIgaT1cIl9fbXBsc3N1cHBvcnRfX1wiO2x0LnNldChpLFwieHl6XCIpLCdcInh5elwiJyE9PWx0LmdldChpKSYmKGU9ITEpLGx0LnJlbW92ZShpKX1jYXRjaCh0KXtlPSExfXJldHVybiBlfHxxLmVycm9yKFwibG9jYWxTdG9yYWdlIHVuc3VwcG9ydGVkOyBmYWxsaW5nIGJhY2sgdG8gY29va2llIHN0b3JlXCIpLGF0PWUsZX0sZXJyb3I6ZnVuY3Rpb24oZSl7cS5lcnJvcihcImxvY2FsU3RvcmFnZSBlcnJvcjogXCIrZSl9LGdldDpmdW5jdGlvbihlKXt0cnl7cmV0dXJuIG51bGw9PXQ/dm9pZCAwOnQubG9jYWxTdG9yYWdlLmdldEl0ZW0oZSl9Y2F0Y2goZSl7bHQuZXJyb3IoZSl9cmV0dXJuIG51bGx9LHBhcnNlOmZ1bmN0aW9uKGUpe3RyeXtyZXR1cm4gSlNPTi5wYXJzZShsdC5nZXQoZSkpfHx7fX1jYXRjaChlKXt9cmV0dXJuIG51bGx9LHNldDpmdW5jdGlvbihlLGkpe3RyeXtudWxsPT10fHx0LmxvY2FsU3RvcmFnZS5zZXRJdGVtKGUsSlNPTi5zdHJpbmdpZnkoaSkpfWNhdGNoKGUpe2x0LmVycm9yKGUpfX0scmVtb3ZlOmZ1bmN0aW9uKGUpe3RyeXtudWxsPT10fHx0LmxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKGUpfWNhdGNoKGUpe2x0LmVycm9yKGUpfX19LHV0PVtcImRpc3RpbmN0X2lkXCIsd2UsU2UsTmUsRGVdLGN0PWooaih7fSxsdCkse30se3BhcnNlOmZ1bmN0aW9uKGUpe3RyeXt2YXIgdD17fTt0cnl7dD1vdC5wYXJzZShlKXx8e319Y2F0Y2goZSl7fXZhciBpPUsodCxKU09OLnBhcnNlKGx0LmdldChlKXx8XCJ7fVwiKSk7cmV0dXJuIGx0LnNldChlLGkpLGl9Y2F0Y2goZSl7fXJldHVybiBudWxsfSxzZXQ6ZnVuY3Rpb24oZSx0LGkscixuLHMpe3RyeXtsdC5zZXQoZSx0LHZvaWQgMCx2b2lkIDAscyk7dmFyIG89e307dXQuZm9yRWFjaCgoZT0+e3RbZV0mJihvW2VdPXRbZV0pfSkpLE9iamVjdC5rZXlzKG8pLmxlbmd0aCYmb3Quc2V0KGUsbyxpLHIsbixzKX1jYXRjaChlKXtsdC5lcnJvcihlKX19LHJlbW92ZTpmdW5jdGlvbihlLGkpe3RyeXtudWxsPT10fHx0LmxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKGUpLG90LnJlbW92ZShlLGkpfWNhdGNoKGUpe2x0LmVycm9yKGUpfX19KSxkdD17fSxodD17aXNfc3VwcG9ydGVkOmZ1bmN0aW9uKCl7cmV0dXJuITB9LGVycm9yOmZ1bmN0aW9uKGUpe3EuZXJyb3IoXCJtZW1vcnlTdG9yYWdlIGVycm9yOiBcIitlKX0sZ2V0OmZ1bmN0aW9uKGUpe3JldHVybiBkdFtlXXx8bnVsbH0scGFyc2U6ZnVuY3Rpb24oZSl7cmV0dXJuIGR0W2VdfHxudWxsfSxzZXQ6ZnVuY3Rpb24oZSx0KXtkdFtlXT10fSxyZW1vdmU6ZnVuY3Rpb24oZSl7ZGVsZXRlIGR0W2VdfX0sX3Q9bnVsbCxwdD17aXNfc3VwcG9ydGVkOmZ1bmN0aW9uKCl7aWYoISQoX3QpKXJldHVybiBfdDtpZihfdD0hMCxGKHQpKV90PSExO2Vsc2UgdHJ5e3ZhciBlPVwiX19zdXBwb3J0X19cIjtwdC5zZXQoZSxcInh5elwiKSwnXCJ4eXpcIichPT1wdC5nZXQoZSkmJihfdD0hMSkscHQucmVtb3ZlKGUpfWNhdGNoKGUpe190PSExfXJldHVybiBfdH0sZXJyb3I6ZnVuY3Rpb24oZSl7cS5lcnJvcihcInNlc3Npb25TdG9yYWdlIGVycm9yOiBcIixlKX0sZ2V0OmZ1bmN0aW9uKGUpe3RyeXtyZXR1cm4gbnVsbD09dD92b2lkIDA6dC5zZXNzaW9uU3RvcmFnZS5nZXRJdGVtKGUpfWNhdGNoKGUpe3B0LmVycm9yKGUpfXJldHVybiBudWxsfSxwYXJzZTpmdW5jdGlvbihlKXt0cnl7cmV0dXJuIEpTT04ucGFyc2UocHQuZ2V0KGUpKXx8bnVsbH1jYXRjaChlKXt9cmV0dXJuIG51bGx9LHNldDpmdW5jdGlvbihlLGkpe3RyeXtudWxsPT10fHx0LnNlc3Npb25TdG9yYWdlLnNldEl0ZW0oZSxKU09OLnN0cmluZ2lmeShpKSl9Y2F0Y2goZSl7cHQuZXJyb3IoZSl9fSxyZW1vdmU6ZnVuY3Rpb24oZSl7dHJ5e251bGw9PXR8fHQuc2Vzc2lvblN0b3JhZ2UucmVtb3ZlSXRlbShlKX1jYXRjaChlKXtwdC5lcnJvcihlKX19fSx2dD1bXCJsb2NhbGhvc3RcIixcIjEyNy4wLjAuMVwiXSxndD1lPT57dmFyIHQ9bnVsbD09YT92b2lkIDA6YS5jcmVhdGVFbGVtZW50KFwiYVwiKTtyZXR1cm4gRih0KT9udWxsOih0LmhyZWY9ZSx0KX0sZnQ9ZnVuY3Rpb24oZSx0KXtyZXR1cm4hIWZ1bmN0aW9uKGUpe3RyeXtuZXcgUmVnRXhwKGUpfWNhdGNoKGUpe3JldHVybiExfXJldHVybiEwfSh0KSYmbmV3IFJlZ0V4cCh0KS50ZXN0KGUpfSxtdD1mdW5jdGlvbihlKXt2YXIgdCxpLHI9YXJndW1lbnRzLmxlbmd0aD4xJiZ2b2lkIDAhPT1hcmd1bWVudHNbMV0/YXJndW1lbnRzWzFdOlwiJlwiLG49W107cmV0dXJuIFkoZSwoZnVuY3Rpb24oZSxyKXtGKGUpfHxGKHIpfHxcInVuZGVmaW5lZFwiPT09cnx8KHQ9ZW5jb2RlVVJJQ29tcG9uZW50KChlPT5lIGluc3RhbmNlb2YgRmlsZSkoZSk/ZS5uYW1lOmUudG9TdHJpbmcoKSksaT1lbmNvZGVVUklDb21wb25lbnQociksbltuLmxlbmd0aF09aStcIj1cIit0KX0pKSxuLmpvaW4ocil9LGJ0PWZ1bmN0aW9uKGUsdCl7Zm9yKHZhciBpLHI9KChlLnNwbGl0KFwiI1wiKVswXXx8XCJcIikuc3BsaXQoXCI/XCIpWzFdfHxcIlwiKS5zcGxpdChcIiZcIiksbj0wO248ci5sZW5ndGg7bisrKXt2YXIgcz1yW25dLnNwbGl0KFwiPVwiKTtpZihzWzBdPT09dCl7aT1zO2JyZWFrfX1pZigheChpKXx8aS5sZW5ndGg8MilyZXR1cm5cIlwiO3ZhciBvPWlbMV07dHJ5e289ZGVjb2RlVVJJQ29tcG9uZW50KG8pfWNhdGNoKGUpe3EuZXJyb3IoXCJTa2lwcGluZyBkZWNvZGluZyBmb3IgbWFsZm9ybWVkIHF1ZXJ5IHBhcmFtOiBcIitvKX1yZXR1cm4gby5yZXBsYWNlKC9cXCsvZyxcIiBcIil9LHl0PWZ1bmN0aW9uKGUsdCxpKXtpZighZXx8IXR8fCF0Lmxlbmd0aClyZXR1cm4gZTtmb3IodmFyIHI9ZS5zcGxpdChcIiNcIiksbj1yWzBdfHxcIlwiLHM9clsxXSxvPW4uc3BsaXQoXCI/XCIpLGE9b1sxXSxsPW9bMF0sdT0oYXx8XCJcIikuc3BsaXQoXCImXCIpLGM9W10sZD0wO2Q8dS5sZW5ndGg7ZCsrKXt2YXIgaD11W2RdLnNwbGl0KFwiPVwiKTt4KGgpJiYodC5pbmNsdWRlcyhoWzBdKT9jLnB1c2goaFswXStcIj1cIitpKTpjLnB1c2godVtkXSkpfXZhciBfPWw7cmV0dXJuIG51bGwhPWEmJihfKz1cIj9cIitjLmpvaW4oXCImXCIpKSxudWxsIT1zJiYoXys9XCIjXCIrcyksX30sd3Q9ZnVuY3Rpb24oZSx0KXt2YXIgaT1lLm1hdGNoKG5ldyBSZWdFeHAodCtcIj0oW14mXSopXCIpKTtyZXR1cm4gaT9pWzFdOm51bGx9LFN0PVwiTW9iaWxlXCIsRXQ9XCJpT1NcIixrdD1cIkFuZHJvaWRcIix4dD1cIlRhYmxldFwiLEl0PWt0K1wiIFwiK3h0LEN0PVwiaVBhZFwiLFB0PVwiQXBwbGVcIixGdD1QdCtcIiBXYXRjaFwiLFJ0PVwiU2FmYXJpXCIsVHQ9XCJCbGFja0JlcnJ5XCIsJHQ9XCJTYW1zdW5nXCIsT3Q9JHQrXCJCcm93c2VyXCIsTXQ9JHQrXCIgSW50ZXJuZXRcIixMdD1cIkNocm9tZVwiLEF0PUx0K1wiIE9TXCIsRHQ9THQrXCIgXCIrRXQsTnQ9XCJJbnRlcm5ldCBFeHBsb3JlclwiLHF0PU50K1wiIFwiK1N0LEJ0PVwiT3BlcmFcIixIdD1CdCtcIiBNaW5pXCIsVXQ9XCJFZGdlXCIsenQ9XCJNaWNyb3NvZnQgXCIrVXQsanQ9XCJGaXJlZm94XCIsV3Q9anQrXCIgXCIrRXQsVnQ9XCJOaW50ZW5kb1wiLEd0PVwiUGxheVN0YXRpb25cIixKdD1cIlhib3hcIixZdD1rdCtcIiBcIitTdCxLdD1TdCtcIiBcIitSdCxYdD1cIldpbmRvd3NcIixRdD1YdCtcIiBQaG9uZVwiLFp0PVwiTm9raWFcIixlaT1cIk91eWFcIix0aT1cIkdlbmVyaWNcIixpaT10aStcIiBcIitTdC50b0xvd2VyQ2FzZSgpLHJpPXRpK1wiIFwiK3h0LnRvTG93ZXJDYXNlKCksbmk9XCJLb25xdWVyb3JcIixzaT1cIihcXFxcZCsoXFxcXC5cXFxcZCspPylcIixvaT1uZXcgUmVnRXhwKFwiVmVyc2lvbi9cIitzaSksYWk9bmV3IFJlZ0V4cChKdCxcImlcIiksbGk9bmV3IFJlZ0V4cChHdCtcIiBcXFxcdytcIixcImlcIiksdWk9bmV3IFJlZ0V4cChWdCtcIiBcXFxcdytcIixcImlcIiksY2k9bmV3IFJlZ0V4cChUdCtcInxQbGF5Qm9va3xCQjEwXCIsXCJpXCIpLGRpPXtcIk5UMy41MVwiOlwiTlQgMy4xMVwiLFwiTlQ0LjBcIjpcIk5UIDQuMFwiLFwiNS4wXCI6XCIyMDAwXCIsNS4xOlwiWFBcIiw1LjI6XCJYUFwiLFwiNi4wXCI6XCJWaXN0YVwiLDYuMTpcIjdcIiw2LjI6XCI4XCIsNi4zOlwiOC4xXCIsNi40OlwiMTBcIixcIjEwLjBcIjpcIjEwXCJ9O3ZhciBoaT0oZSx0KT0+dCYmbSh0LFB0KXx8ZnVuY3Rpb24oZSl7cmV0dXJuIG0oZSxSdCkmJiFtKGUsTHQpJiYhbShlLGt0KX0oZSksX2k9ZnVuY3Rpb24oZSx0KXtyZXR1cm4gdD10fHxcIlwiLG0oZSxcIiBPUFIvXCIpJiZtKGUsXCJNaW5pXCIpP0h0Om0oZSxcIiBPUFIvXCIpP0J0OmNpLnRlc3QoZSk/VHQ6bShlLFwiSUVcIitTdCl8fG0oZSxcIldQRGVza3RvcFwiKT9xdDptKGUsT3QpP010Om0oZSxVdCl8fG0oZSxcIkVkZy9cIik/enQ6bShlLFwiRkJJT1NcIik/XCJGYWNlYm9vayBcIitTdDptKGUsXCJVQ1dFQlwiKXx8bShlLFwiVUNCcm93c2VyXCIpP1wiVUMgQnJvd3NlclwiOm0oZSxcIkNyaU9TXCIpP0R0Om0oZSxcIkNyTW9cIil8fG0oZSxMdCk/THQ6bShlLGt0KSYmbShlLFJ0KT9ZdDptKGUsXCJGeGlPU1wiKT9XdDptKGUudG9Mb3dlckNhc2UoKSxuaS50b0xvd2VyQ2FzZSgpKT9uaTpoaShlLHQpP20oZSxTdCk/S3Q6UnQ6bShlLGp0KT9qdDptKGUsXCJNU0lFXCIpfHxtKGUsXCJUcmlkZW50L1wiKT9OdDptKGUsXCJHZWNrb1wiKT9qdDpcIlwifSxwaT17W3F0XTpbbmV3IFJlZ0V4cChcInJ2OlwiK3NpKV0sW3p0XTpbbmV3IFJlZ0V4cChVdCtcIj9cXFxcL1wiK3NpKV0sW0x0XTpbbmV3IFJlZ0V4cChcIihcIitMdCtcInxDck1vKVxcXFwvXCIrc2kpXSxbRHRdOltuZXcgUmVnRXhwKFwiQ3JpT1NcXFxcL1wiK3NpKV0sXCJVQyBCcm93c2VyXCI6W25ldyBSZWdFeHAoXCIoVUNCcm93c2VyfFVDV0VCKVxcXFwvXCIrc2kpXSxbUnRdOltvaV0sW0t0XTpbb2ldLFtCdF06W25ldyBSZWdFeHAoXCIoT3BlcmF8T1BSKVxcXFwvXCIrc2kpXSxbanRdOltuZXcgUmVnRXhwKGp0K1wiXFxcXC9cIitzaSldLFtXdF06W25ldyBSZWdFeHAoXCJGeGlPU1xcXFwvXCIrc2kpXSxbbmldOltuZXcgUmVnRXhwKFwiS29ucXVlcm9yWzovXT9cIitzaSxcImlcIildLFtUdF06W25ldyBSZWdFeHAoVHQrXCIgXCIrc2kpLG9pXSxbWXRdOltuZXcgUmVnRXhwKFwiYW5kcm9pZFxcXFxzXCIrc2ksXCJpXCIpXSxbTXRdOltuZXcgUmVnRXhwKE90K1wiXFxcXC9cIitzaSldLFtOdF06W25ldyBSZWdFeHAoXCIocnY6fE1TSUUgKVwiK3NpKV0sTW96aWxsYTpbbmV3IFJlZ0V4cChcInJ2OlwiK3NpKV19LHZpPVtbbmV3IFJlZ0V4cChKdCtcIjsgXCIrSnQrXCIgKC4qPylbKTtdXCIsXCJpXCIpLGU9PltKdCxlJiZlWzFdfHxcIlwiXV0sW25ldyBSZWdFeHAoVnQsXCJpXCIpLFtWdCxcIlwiXV0sW25ldyBSZWdFeHAoR3QsXCJpXCIpLFtHdCxcIlwiXV0sW2NpLFtUdCxcIlwiXV0sW25ldyBSZWdFeHAoWHQsXCJpXCIpLChlLHQpPT57aWYoL1Bob25lLy50ZXN0KHQpfHwvV1BEZXNrdG9wLy50ZXN0KHQpKXJldHVybltRdCxcIlwiXTtpZihuZXcgUmVnRXhwKFN0KS50ZXN0KHQpJiYhL0lFTW9iaWxlXFxiLy50ZXN0KHQpKXJldHVybltYdCtcIiBcIitTdCxcIlwiXTt2YXIgaT0vV2luZG93cyBOVCAoWzAtOS5dKykvaS5leGVjKHQpO2lmKGkmJmlbMV0pe3ZhciByPWlbMV0sbj1kaVtyXXx8XCJcIjtyZXR1cm4vYXJtL2kudGVzdCh0KSYmKG49XCJSVFwiKSxbWHQsbl19cmV0dXJuW1h0LFwiXCJdfV0sWy8oKGlQaG9uZXxpUGFkfGlQb2QpLio/T1MgKFxcZCspXyhcXGQrKV8/KFxcZCspP3xpUGhvbmUpLyxlPT57aWYoZSYmZVszXSl7dmFyIHQ9W2VbM10sZVs0XSxlWzVdfHxcIjBcIl07cmV0dXJuW0V0LHQuam9pbihcIi5cIildfXJldHVybltFdCxcIlwiXX1dLFsvKHdhdGNoLipcXC8oXFxkK1xcLlxcZCtcXC5cXGQrKXx3YXRjaCBvcywoXFxkK1xcLlxcZCspLCkvaSxlPT57dmFyIHQ9XCJcIjtyZXR1cm4gZSYmZS5sZW5ndGg+PTMmJih0PUYoZVsyXSk/ZVszXTplWzJdKSxbXCJ3YXRjaE9TXCIsdF19XSxbbmV3IFJlZ0V4cChcIihcIitrdCtcIiAoXFxcXGQrKVxcXFwuKFxcXFxkKylcXFxcLj8oXFxcXGQrKT98XCIra3QrXCIpXCIsXCJpXCIpLGU9PntpZihlJiZlWzJdKXt2YXIgdD1bZVsyXSxlWzNdLGVbNF18fFwiMFwiXTtyZXR1cm5ba3QsdC5qb2luKFwiLlwiKV19cmV0dXJuW2t0LFwiXCJdfV0sWy9NYWMgT1MgWCAoXFxkKylbXy5dKFxcZCspW18uXT8oXFxkKyk/L2ksZT0+e3ZhciB0PVtcIk1hYyBPUyBYXCIsXCJcIl07aWYoZSYmZVsxXSl7dmFyIGk9W2VbMV0sZVsyXSxlWzNdfHxcIjBcIl07dFsxXT1pLmpvaW4oXCIuXCIpfXJldHVybiB0fV0sWy9NYWMvaSxbXCJNYWMgT1MgWFwiLFwiXCJdXSxbL0NyT1MvLFtBdCxcIlwiXV0sWy9MaW51eHxkZWJpYW4vaSxbXCJMaW51eFwiLFwiXCJdXV0sZ2k9ZnVuY3Rpb24oZSl7cmV0dXJuIHVpLnRlc3QoZSk/VnQ6bGkudGVzdChlKT9HdDphaS50ZXN0KGUpP0p0Om5ldyBSZWdFeHAoZWksXCJpXCIpLnRlc3QoZSk/ZWk6bmV3IFJlZ0V4cChcIihcIitRdCtcInxXUERlc2t0b3ApXCIsXCJpXCIpLnRlc3QoZSk/UXQ6L2lQYWQvLnRlc3QoZSk/Q3Q6L2lQb2QvLnRlc3QoZSk/XCJpUG9kIFRvdWNoXCI6L2lQaG9uZS8udGVzdChlKT9cImlQaG9uZVwiOi8od2F0Y2gpKD86ID9vc1ssL118XFxkLFxcZFxcLylbXFxkLl0rL2kudGVzdChlKT9GdDpjaS50ZXN0KGUpP1R0Oi8oa29ibylcXHMoZXJlYWRlcnx0b3VjaCkvaS50ZXN0KGUpP1wiS29ib1wiOm5ldyBSZWdFeHAoWnQsXCJpXCIpLnRlc3QoZSk/WnQ6LyhrZlthLXpdezJ9d2l8YWVvW2Mtcl17Mn0pKCBidWl8XFwpKS9pLnRlc3QoZSl8fC8oa2ZbYS16XSspKCBidWl8XFwpKS4rc2lsa1xcLy9pLnRlc3QoZSk/XCJLaW5kbGUgRmlyZVwiOi8oQW5kcm9pZHxaVEUpL2kudGVzdChlKT8hbmV3IFJlZ0V4cChTdCkudGVzdChlKXx8Lyg5MTM4QnxUQjc4MkJ8TmV4dXMgWzk3XXxwaXhlbCBjfEhVQVdFSVNIVHxCVFZ8bm9ibGUgbm9va3xzbWFydCB1bHRyYSA2KS9pLnRlc3QoZSk/L3BpeGVsW1xcZGF4bCBdezEsNn0vaS50ZXN0KGUpJiYhL3BpeGVsIGMvaS50ZXN0KGUpfHwvKGh1YXdlaW1lZC1hbDAwfHRhaC18QVBBfFNNLUc5MnxpOTgwfHp0ZXxVMzA0QUEpL2kudGVzdChlKXx8L2xteTQ3di9pLnRlc3QoZSkmJiEvUVRBUVozL2kudGVzdChlKT9rdDpJdDprdDpuZXcgUmVnRXhwKFwiKHBkYXxcIitTdCtcIilcIixcImlcIikudGVzdChlKT9paTpuZXcgUmVnRXhwKHh0LFwiaVwiKS50ZXN0KGUpJiYhbmV3IFJlZ0V4cCh4dCtcIiBwY1wiLFwiaVwiKS50ZXN0KGUpP3JpOlwiXCJ9LGZpPVwiaHR0cHM/Oi8vKC4qKVwiLG1pPVtcImdjbGlkXCIsXCJnY2xzcmNcIixcImRjbGlkXCIsXCJnYnJhaWRcIixcIndicmFpZFwiLFwiZmJjbGlkXCIsXCJtc2Nsa2lkXCIsXCJ0d2NsaWRcIixcImxpX2ZhdF9pZFwiLFwiaWdzaGlkXCIsXCJ0dGNsaWRcIixcInJkdF9jaWRcIixcImlyY2xpZFwiLFwiX2t4XCJdLGJpPVgoW1widXRtX3NvdXJjZVwiLFwidXRtX21lZGl1bVwiLFwidXRtX2NhbXBhaWduXCIsXCJ1dG1fY29udGVudFwiLFwidXRtX3Rlcm1cIixcImdhZF9zb3VyY2VcIixcIm1jX2NpZFwiXSxtaSkseWk9XCI8bWFza2VkPlwiLHdpPXtjYW1wYWlnblBhcmFtczpmdW5jdGlvbigpe3ZhcntjdXN0b21UcmFja2VkUGFyYW1zOmUsbWFza1BlcnNvbmFsRGF0YVByb3BlcnRpZXM6dCxjdXN0b21QZXJzb25hbERhdGFQcm9wZXJ0aWVzOml9PWFyZ3VtZW50cy5sZW5ndGg+MCYmdm9pZCAwIT09YXJndW1lbnRzWzBdP2FyZ3VtZW50c1swXTp7fTtpZighYSlyZXR1cm57fTt2YXIgcj10P1goW10sbWksaXx8W10pOltdO3JldHVybiB0aGlzLl9jYW1wYWlnblBhcmFtc0Zyb21VcmwoeXQoYS5VUkwscix5aSksZSl9LF9jYW1wYWlnblBhcmFtc0Zyb21Vcmw6ZnVuY3Rpb24oZSx0KXt2YXIgaT1iaS5jb25jYXQodHx8W10pLHI9e307cmV0dXJuIFkoaSwoZnVuY3Rpb24odCl7dmFyIGk9YnQoZSx0KTtyW3RdPWl8fG51bGx9KSkscn0sX3NlYXJjaEVuZ2luZTpmdW5jdGlvbihlKXtyZXR1cm4gZT8wPT09ZS5zZWFyY2goZmkrXCJnb29nbGUuKFteLz9dKilcIik/XCJnb29nbGVcIjowPT09ZS5zZWFyY2goZmkrXCJiaW5nLmNvbVwiKT9cImJpbmdcIjowPT09ZS5zZWFyY2goZmkrXCJ5YWhvby5jb21cIik/XCJ5YWhvb1wiOjA9PT1lLnNlYXJjaChmaStcImR1Y2tkdWNrZ28uY29tXCIpP1wiZHVja2R1Y2tnb1wiOm51bGw6bnVsbH0sX3NlYXJjaEluZm9Gcm9tUmVmZXJyZXI6ZnVuY3Rpb24oZSl7dmFyIHQ9d2kuX3NlYXJjaEVuZ2luZShlKSxpPVwieWFob29cIiE9dD9cInFcIjpcInBcIixyPXt9O2lmKCEkKHQpKXtyLiRzZWFyY2hfZW5naW5lPXQ7dmFyIG49YT9idChhLnJlZmVycmVyLGkpOlwiXCI7bi5sZW5ndGgmJihyLnBoX2tleXdvcmQ9bil9cmV0dXJuIHJ9LHNlYXJjaEluZm86ZnVuY3Rpb24oKXt2YXIgZT1udWxsPT1hP3ZvaWQgMDphLnJlZmVycmVyO3JldHVybiBlP3RoaXMuX3NlYXJjaEluZm9Gcm9tUmVmZXJyZXIoZSk6e319LGJyb3dzZXI6X2ksYnJvd3NlclZlcnNpb246ZnVuY3Rpb24oZSx0KXt2YXIgaT1faShlLHQpLHI9cGlbaV07aWYoRihyKSlyZXR1cm4gbnVsbDtmb3IodmFyIG49MDtuPHIubGVuZ3RoO24rKyl7dmFyIHM9cltuXSxvPWUubWF0Y2gocyk7aWYobylyZXR1cm4gcGFyc2VGbG9hdChvW28ubGVuZ3RoLTJdKX1yZXR1cm4gbnVsbH0sYnJvd3Nlckxhbmd1YWdlOmZ1bmN0aW9uKCl7cmV0dXJuIG5hdmlnYXRvci5sYW5ndWFnZXx8bmF2aWdhdG9yLnVzZXJMYW5ndWFnZX0sYnJvd3Nlckxhbmd1YWdlUHJlZml4OmZ1bmN0aW9uKCl7dmFyIGU9dGhpcy5icm93c2VyTGFuZ3VhZ2UoKTtyZXR1cm5cInN0cmluZ1wiPT10eXBlb2YgZT9lLnNwbGl0KFwiLVwiKVswXTp2b2lkIDB9LG9zOmZ1bmN0aW9uKGUpe2Zvcih2YXIgdD0wO3Q8dmkubGVuZ3RoO3QrKyl7dmFyW2kscl09dmlbdF0sbj1pLmV4ZWMoZSkscz1uJiYoSShyKT9yKG4sZSk6cik7aWYocylyZXR1cm4gc31yZXR1cm5bXCJcIixcIlwiXX0sZGV2aWNlOmdpLGRldmljZVR5cGU6ZnVuY3Rpb24oZSl7dmFyIHQ9Z2koZSk7cmV0dXJuIHQ9PT1DdHx8dD09PUl0fHxcIktvYm9cIj09PXR8fFwiS2luZGxlIEZpcmVcIj09PXR8fHQ9PT1yaT94dDp0PT09VnR8fHQ9PT1KdHx8dD09PUd0fHx0PT09ZWk/XCJDb25zb2xlXCI6dD09PUZ0P1wiV2VhcmFibGVcIjp0P1N0OlwiRGVza3RvcFwifSxyZWZlcnJlcjpmdW5jdGlvbigpe3JldHVybihudWxsPT1hP3ZvaWQgMDphLnJlZmVycmVyKXx8XCIkZGlyZWN0XCJ9LHJlZmVycmluZ0RvbWFpbjpmdW5jdGlvbigpe3ZhciBlO3JldHVybiBudWxsIT1hJiZhLnJlZmVycmVyJiYobnVsbD09PShlPWd0KGEucmVmZXJyZXIpKXx8dm9pZCAwPT09ZT92b2lkIDA6ZS5ob3N0KXx8XCIkZGlyZWN0XCJ9LHJlZmVycmVySW5mbzpmdW5jdGlvbigpe3JldHVybnskcmVmZXJyZXI6dGhpcy5yZWZlcnJlcigpLCRyZWZlcnJpbmdfZG9tYWluOnRoaXMucmVmZXJyaW5nRG9tYWluKCl9fSxpbml0aWFsUGVyc29uSW5mbzpmdW5jdGlvbigpe3JldHVybntyOnRoaXMucmVmZXJyZXIoKS5zdWJzdHJpbmcoMCwxZTMpLHU6bnVsbD09bD92b2lkIDA6bC5ocmVmLnN1YnN0cmluZygwLDFlMyl9fSxpbml0aWFsUGVyc29uUHJvcHNGcm9tSW5mbzpmdW5jdGlvbihlKXt2YXIgdCx7cjppLHU6cn09ZSxuPXskaW5pdGlhbF9yZWZlcnJlcjppLCRpbml0aWFsX3JlZmVycmluZ19kb21haW46bnVsbD09aT92b2lkIDA6XCIkZGlyZWN0XCI9PWk/XCIkZGlyZWN0XCI6bnVsbD09PSh0PWd0KGkpKXx8dm9pZCAwPT09dD92b2lkIDA6dC5ob3N0fTtpZihyKXtuLiRpbml0aWFsX2N1cnJlbnRfdXJsPXI7dmFyIHM9Z3Qocik7bi4kaW5pdGlhbF9ob3N0PW51bGw9PXM/dm9pZCAwOnMuaG9zdCxuLiRpbml0aWFsX3BhdGhuYW1lPW51bGw9PXM/dm9pZCAwOnMucGF0aG5hbWUsWSh0aGlzLl9jYW1wYWlnblBhcmFtc0Zyb21VcmwociksKGZ1bmN0aW9uKGUsdCl7bltcIiRpbml0aWFsX1wiK3kodCldPWV9KSl9aSYmWSh0aGlzLl9zZWFyY2hJbmZvRnJvbVJlZmVycmVyKGkpLChmdW5jdGlvbihlLHQpe25bXCIkaW5pdGlhbF9cIit5KHQpXT1lfSkpO3JldHVybiBufSx0aW1lem9uZTpmdW5jdGlvbigpe3RyeXtyZXR1cm4gSW50bC5EYXRlVGltZUZvcm1hdCgpLnJlc29sdmVkT3B0aW9ucygpLnRpbWVab25lfWNhdGNoKGUpe3JldHVybn19LHRpbWV6b25lT2Zmc2V0OmZ1bmN0aW9uKCl7dHJ5e3JldHVybihuZXcgRGF0ZSkuZ2V0VGltZXpvbmVPZmZzZXQoKX1jYXRjaChlKXtyZXR1cm59fSxwcm9wZXJ0aWVzOmZ1bmN0aW9uKCl7dmFye21hc2tQZXJzb25hbERhdGFQcm9wZXJ0aWVzOmUsY3VzdG9tUGVyc29uYWxEYXRhUHJvcGVydGllczppfT1hcmd1bWVudHMubGVuZ3RoPjAmJnZvaWQgMCE9PWFyZ3VtZW50c1swXT9hcmd1bWVudHNbMF06e307aWYoIWgpcmV0dXJue307dmFyIHI9ZT9YKFtdLG1pLGl8fFtdKTpbXSxbbixzXT13aS5vcyhoKTtyZXR1cm4gSyh0ZSh7JG9zOm4sJG9zX3ZlcnNpb246cywkYnJvd3Nlcjp3aS5icm93c2VyKGgsbmF2aWdhdG9yLnZlbmRvciksJGRldmljZTp3aS5kZXZpY2UoaCksJGRldmljZV90eXBlOndpLmRldmljZVR5cGUoaCksJHRpbWV6b25lOndpLnRpbWV6b25lKCksJHRpbWV6b25lX29mZnNldDp3aS50aW1lem9uZU9mZnNldCgpfSkseyRjdXJyZW50X3VybDp5dChudWxsPT1sP3ZvaWQgMDpsLmhyZWYscix5aSksJGhvc3Q6bnVsbD09bD92b2lkIDA6bC5ob3N0LCRwYXRobmFtZTpudWxsPT1sP3ZvaWQgMDpsLnBhdGhuYW1lLCRyYXdfdXNlcl9hZ2VudDpoLmxlbmd0aD4xZTM/aC5zdWJzdHJpbmcoMCw5OTcpK1wiLi4uXCI6aCwkYnJvd3Nlcl92ZXJzaW9uOndpLmJyb3dzZXJWZXJzaW9uKGgsbmF2aWdhdG9yLnZlbmRvciksJGJyb3dzZXJfbGFuZ3VhZ2U6d2kuYnJvd3Nlckxhbmd1YWdlKCksJGJyb3dzZXJfbGFuZ3VhZ2VfcHJlZml4OndpLmJyb3dzZXJMYW5ndWFnZVByZWZpeCgpLCRzY3JlZW5faGVpZ2h0Om51bGw9PXQ/dm9pZCAwOnQuc2NyZWVuLmhlaWdodCwkc2NyZWVuX3dpZHRoOm51bGw9PXQ/dm9pZCAwOnQuc2NyZWVuLndpZHRoLCR2aWV3cG9ydF9oZWlnaHQ6bnVsbD09dD92b2lkIDA6dC5pbm5lckhlaWdodCwkdmlld3BvcnRfd2lkdGg6bnVsbD09dD92b2lkIDA6dC5pbm5lcldpZHRoLCRsaWI6XCJ3ZWJcIiwkbGliX3ZlcnNpb246cC5MSUJfVkVSU0lPTiwkaW5zZXJ0X2lkOk1hdGgucmFuZG9tKCkudG9TdHJpbmcoMzYpLnN1YnN0cmluZygyLDEwKStNYXRoLnJhbmRvbSgpLnRvU3RyaW5nKDM2KS5zdWJzdHJpbmcoMiwxMCksJHRpbWU6RGF0ZS5ub3coKS8xZTN9KX0scGVvcGxlX3Byb3BlcnRpZXM6ZnVuY3Rpb24oKXtpZighaClyZXR1cm57fTt2YXJbZSx0XT13aS5vcyhoKTtyZXR1cm4gSyh0ZSh7JG9zOmUsJG9zX3ZlcnNpb246dCwkYnJvd3Nlcjp3aS5icm93c2VyKGgsbmF2aWdhdG9yLnZlbmRvcil9KSx7JGJyb3dzZXJfdmVyc2lvbjp3aS5icm93c2VyVmVyc2lvbihoLG5hdmlnYXRvci52ZW5kb3IpfSl9fSxTaT1bXCJjb29raWVcIixcImxvY2Fsc3RvcmFnZVwiLFwibG9jYWxzdG9yYWdlK2Nvb2tpZVwiLFwic2Vzc2lvbnN0b3JhZ2VcIixcIm1lbW9yeVwiXTtjbGFzcyBFaXtjb25zdHJ1Y3RvcihlKXt0aGlzLmNvbmZpZz1lLHRoaXMucHJvcHM9e30sdGhpcy5jYW1wYWlnbl9wYXJhbXNfc2F2ZWQ9ITEsdGhpcy5uYW1lPShlPT57dmFyIHQ9XCJcIjtyZXR1cm4gZS50b2tlbiYmKHQ9ZS50b2tlbi5yZXBsYWNlKC9cXCsvZyxcIlBMXCIpLnJlcGxhY2UoL1xcLy9nLFwiU0xcIikucmVwbGFjZSgvPS9nLFwiRVFcIikpLGUucGVyc2lzdGVuY2VfbmFtZT9cInBoX1wiK2UucGVyc2lzdGVuY2VfbmFtZTpcInBoX1wiK3QrXCJfcG9zdGhvZ1wifSkoZSksdGhpcy5zdG9yYWdlPXRoaXMuYnVpbGRTdG9yYWdlKGUpLHRoaXMubG9hZCgpLGUuZGVidWcmJnEuaW5mbyhcIlBlcnNpc3RlbmNlIGxvYWRlZFwiLGUucGVyc2lzdGVuY2Usaih7fSx0aGlzLnByb3BzKSksdGhpcy51cGRhdGVfY29uZmlnKGUsZSksdGhpcy5zYXZlKCl9YnVpbGRTdG9yYWdlKGUpey0xPT09U2kuaW5kZXhPZihlLnBlcnNpc3RlbmNlLnRvTG93ZXJDYXNlKCkpJiYocS5jcml0aWNhbChcIlVua25vd24gcGVyc2lzdGVuY2UgdHlwZSBcIitlLnBlcnNpc3RlbmNlK1wiOyBmYWxsaW5nIGJhY2sgdG8gbG9jYWxTdG9yYWdlK2Nvb2tpZVwiKSxlLnBlcnNpc3RlbmNlPVwibG9jYWxTdG9yYWdlK2Nvb2tpZVwiKTt2YXIgdD1lLnBlcnNpc3RlbmNlLnRvTG93ZXJDYXNlKCk7cmV0dXJuXCJsb2NhbHN0b3JhZ2VcIj09PXQmJmx0LmlzX3N1cHBvcnRlZCgpP2x0OlwibG9jYWxzdG9yYWdlK2Nvb2tpZVwiPT09dCYmY3QuaXNfc3VwcG9ydGVkKCk/Y3Q6XCJzZXNzaW9uc3RvcmFnZVwiPT09dCYmcHQuaXNfc3VwcG9ydGVkKCk/cHQ6XCJtZW1vcnlcIj09PXQ/aHQ6XCJjb29raWVcIj09PXQ/b3Q6Y3QuaXNfc3VwcG9ydGVkKCk/Y3Q6b3R9cHJvcGVydGllcygpe3ZhciBlPXt9O3JldHVybiBZKHRoaXMucHJvcHMsKGZ1bmN0aW9uKHQsaSl7aWYoaT09PXhlJiZDKHQpKWZvcih2YXIgcj1PYmplY3Qua2V5cyh0KSxuPTA7bjxyLmxlbmd0aDtuKyspZVtcIiRmZWF0dXJlL1wiLmNvbmNhdChyW25dKV09dFtyW25dXTtlbHNlIGE9aSxsPSExLCgkKG89SGUpP2w6cyYmby5pbmRleE9mPT09cz8tMSE9by5pbmRleE9mKGEpOihZKG8sKGZ1bmN0aW9uKGUpe2lmKGx8fChsPWU9PT1hKSlyZXR1cm4gR30pKSxsKSl8fChlW2ldPXQpO3ZhciBvLGEsbH0pKSxlfWxvYWQoKXtpZighdGhpcy5kaXNhYmxlZCl7dmFyIGU9dGhpcy5zdG9yYWdlLnBhcnNlKHRoaXMubmFtZSk7ZSYmKHRoaXMucHJvcHM9Syh7fSxlKSl9fXNhdmUoKXt0aGlzLmRpc2FibGVkfHx0aGlzLnN0b3JhZ2Uuc2V0KHRoaXMubmFtZSx0aGlzLnByb3BzLHRoaXMuZXhwaXJlX2RheXMsdGhpcy5jcm9zc19zdWJkb21haW4sdGhpcy5zZWN1cmUsdGhpcy5jb25maWcuZGVidWcpfXJlbW92ZSgpe3RoaXMuc3RvcmFnZS5yZW1vdmUodGhpcy5uYW1lLCExKSx0aGlzLnN0b3JhZ2UucmVtb3ZlKHRoaXMubmFtZSwhMCl9Y2xlYXIoKXt0aGlzLnJlbW92ZSgpLHRoaXMucHJvcHM9e319cmVnaXN0ZXJfb25jZShlLHQsaSl7aWYoQyhlKSl7Rih0KSYmKHQ9XCJOb25lXCIpLHRoaXMuZXhwaXJlX2RheXM9RihpKT90aGlzLmRlZmF1bHRfZXhwaXJ5Omk7dmFyIHI9ITE7aWYoWShlLCgoZSxpKT0+e3RoaXMucHJvcHMuaGFzT3duUHJvcGVydHkoaSkmJnRoaXMucHJvcHNbaV0hPT10fHwodGhpcy5wcm9wc1tpXT1lLHI9ITApfSkpLHIpcmV0dXJuIHRoaXMuc2F2ZSgpLCEwfXJldHVybiExfXJlZ2lzdGVyKGUsdCl7aWYoQyhlKSl7dGhpcy5leHBpcmVfZGF5cz1GKHQpP3RoaXMuZGVmYXVsdF9leHBpcnk6dDt2YXIgaT0hMTtpZihZKGUsKCh0LHIpPT57ZS5oYXNPd25Qcm9wZXJ0eShyKSYmdGhpcy5wcm9wc1tyXSE9PXQmJih0aGlzLnByb3BzW3JdPXQsaT0hMCl9KSksaSlyZXR1cm4gdGhpcy5zYXZlKCksITB9cmV0dXJuITF9dW5yZWdpc3RlcihlKXtlIGluIHRoaXMucHJvcHMmJihkZWxldGUgdGhpcy5wcm9wc1tlXSx0aGlzLnNhdmUoKSl9dXBkYXRlX2NhbXBhaWduX3BhcmFtcygpe2lmKCF0aGlzLmNhbXBhaWduX3BhcmFtc19zYXZlZCl7dmFyIGU9d2kuY2FtcGFpZ25QYXJhbXMoe2N1c3RvbVRyYWNrZWRQYXJhbXM6dGhpcy5jb25maWcuY3VzdG9tX2NhbXBhaWduX3BhcmFtcyxtYXNrUGVyc29uYWxEYXRhUHJvcGVydGllczp0aGlzLmNvbmZpZy5tYXNrX3BlcnNvbmFsX2RhdGFfcHJvcGVydGllcyxjdXN0b21QZXJzb25hbERhdGFQcm9wZXJ0aWVzOnRoaXMuY29uZmlnLmN1c3RvbV9wZXJzb25hbF9kYXRhX3Byb3BlcnRpZXN9KTtQKHRlKGUpKXx8dGhpcy5yZWdpc3RlcihlKSx0aGlzLmNhbXBhaWduX3BhcmFtc19zYXZlZD0hMH19dXBkYXRlX3NlYXJjaF9rZXl3b3JkKCl7dGhpcy5yZWdpc3Rlcih3aS5zZWFyY2hJbmZvKCkpfXVwZGF0ZV9yZWZlcnJlcl9pbmZvKCl7dGhpcy5yZWdpc3Rlcl9vbmNlKHdpLnJlZmVycmVySW5mbygpLHZvaWQgMCl9c2V0X2luaXRpYWxfcGVyc29uX2luZm8oKXt0aGlzLnByb3BzW0xlXXx8dGhpcy5wcm9wc1tBZV18fHRoaXMucmVnaXN0ZXJfb25jZSh7W0RlXTp3aS5pbml0aWFsUGVyc29uSW5mbygpfSx2b2lkIDApfWdldF9yZWZlcnJlcl9pbmZvKCl7cmV0dXJuIHRlKHskcmVmZXJyZXI6dGhpcy5wcm9wcy4kcmVmZXJyZXIsJHJlZmVycmluZ19kb21haW46dGhpcy5wcm9wcy4kcmVmZXJyaW5nX2RvbWFpbn0pfWdldF9pbml0aWFsX3Byb3BzKCl7dmFyIGU9e307WShbQWUsTGVdLCh0PT57dmFyIGk9dGhpcy5wcm9wc1t0XTtpJiZZKGksKGZ1bmN0aW9uKHQsaSl7ZVtcIiRpbml0aWFsX1wiK3koaSldPXR9KSl9KSk7dmFyIHQ9dGhpcy5wcm9wc1tEZV07aWYodCl7dmFyIGk9d2kuaW5pdGlhbFBlcnNvblByb3BzRnJvbUluZm8odCk7SyhlLGkpfXJldHVybiBlfXNhZmVfbWVyZ2UoZSl7cmV0dXJuIFkodGhpcy5wcm9wcywoZnVuY3Rpb24odCxpKXtpIGluIGV8fChlW2ldPXQpfSkpLGV9dXBkYXRlX2NvbmZpZyhlLHQpe2lmKHRoaXMuZGVmYXVsdF9leHBpcnk9dGhpcy5leHBpcmVfZGF5cz1lLmNvb2tpZV9leHBpcmF0aW9uLHRoaXMuc2V0X2Rpc2FibGVkKGUuZGlzYWJsZV9wZXJzaXN0ZW5jZSksdGhpcy5zZXRfY3Jvc3Nfc3ViZG9tYWluKGUuY3Jvc3Nfc3ViZG9tYWluX2Nvb2tpZSksdGhpcy5zZXRfc2VjdXJlKGUuc2VjdXJlX2Nvb2tpZSksZS5wZXJzaXN0ZW5jZSE9PXQucGVyc2lzdGVuY2Upe3ZhciBpPXRoaXMuYnVpbGRTdG9yYWdlKGUpLHI9dGhpcy5wcm9wczt0aGlzLmNsZWFyKCksdGhpcy5zdG9yYWdlPWksdGhpcy5wcm9wcz1yLHRoaXMuc2F2ZSgpfX1zZXRfZGlzYWJsZWQoZSl7dGhpcy5kaXNhYmxlZD1lLHRoaXMuZGlzYWJsZWQ/dGhpcy5yZW1vdmUoKTp0aGlzLnNhdmUoKX1zZXRfY3Jvc3Nfc3ViZG9tYWluKGUpe2UhPT10aGlzLmNyb3NzX3N1YmRvbWFpbiYmKHRoaXMuY3Jvc3Nfc3ViZG9tYWluPWUsdGhpcy5yZW1vdmUoKSx0aGlzLnNhdmUoKSl9Z2V0X2Nyb3NzX3N1YmRvbWFpbigpe3JldHVybiEhdGhpcy5jcm9zc19zdWJkb21haW59c2V0X3NlY3VyZShlKXtlIT09dGhpcy5zZWN1cmUmJih0aGlzLnNlY3VyZT1lLHRoaXMucmVtb3ZlKCksdGhpcy5zYXZlKCkpfXNldF9ldmVudF90aW1lcihlLHQpe3ZhciBpPXRoaXMucHJvcHNbYWVdfHx7fTtpW2VdPXQsdGhpcy5wcm9wc1thZV09aSx0aGlzLnNhdmUoKX1yZW1vdmVfZXZlbnRfdGltZXIoZSl7dmFyIHQ9KHRoaXMucHJvcHNbYWVdfHx7fSlbZV07cmV0dXJuIEYodCl8fChkZWxldGUgdGhpcy5wcm9wc1thZV1bZV0sdGhpcy5zYXZlKCkpLHR9Z2V0X3Byb3BlcnR5KGUpe3JldHVybiB0aGlzLnByb3BzW2VdfXNldF9wcm9wZXJ0eShlLHQpe3RoaXMucHJvcHNbZV09dCx0aGlzLnNhdmUoKX19ZnVuY3Rpb24ga2koZSl7dmFyIHQsaTtyZXR1cm4obnVsbD09PSh0PUpTT04uc3RyaW5naWZ5KGUsKGk9W10sZnVuY3Rpb24oZSx0KXtpZihDKHQpKXtmb3IoO2kubGVuZ3RoPjAmJmlbaS5sZW5ndGgtMV0hPT10aGlzOylpLnBvcCgpO3JldHVybiBpLmluY2x1ZGVzKHQpP1wiW0NpcmN1bGFyXVwiOihpLnB1c2godCksdCl9cmV0dXJuIHR9KSkpfHx2b2lkIDA9PT10P3ZvaWQgMDp0Lmxlbmd0aCl8fDB9ZnVuY3Rpb24geGkoZSl7dmFyIHQ9YXJndW1lbnRzLmxlbmd0aD4xJiZ2b2lkIDAhPT1hcmd1bWVudHNbMV0/YXJndW1lbnRzWzFdOjY2MDYwMjguODtpZihlLnNpemU+PXQmJmUuZGF0YS5sZW5ndGg+MSl7dmFyIGk9TWF0aC5mbG9vcihlLmRhdGEubGVuZ3RoLzIpLHI9ZS5kYXRhLnNsaWNlKDAsaSksbj1lLmRhdGEuc2xpY2UoaSk7cmV0dXJuW3hpKHtzaXplOmtpKHIpLGRhdGE6cixzZXNzaW9uSWQ6ZS5zZXNzaW9uSWQsd2luZG93SWQ6ZS53aW5kb3dJZH0pLHhpKHtzaXplOmtpKG4pLGRhdGE6bixzZXNzaW9uSWQ6ZS5zZXNzaW9uSWQsd2luZG93SWQ6ZS53aW5kb3dJZH0pXS5mbGF0TWFwKChlPT5lKSl9cmV0dXJuW2VdfXZhciBJaT0oZT0+KGVbZS5Eb21Db250ZW50TG9hZGVkPTBdPVwiRG9tQ29udGVudExvYWRlZFwiLGVbZS5Mb2FkPTFdPVwiTG9hZFwiLGVbZS5GdWxsU25hcHNob3Q9Ml09XCJGdWxsU25hcHNob3RcIixlW2UuSW5jcmVtZW50YWxTbmFwc2hvdD0zXT1cIkluY3JlbWVudGFsU25hcHNob3RcIixlW2UuTWV0YT00XT1cIk1ldGFcIixlW2UuQ3VzdG9tPTVdPVwiQ3VzdG9tXCIsZVtlLlBsdWdpbj02XT1cIlBsdWdpblwiLGUpKShJaXx8e30pLENpPShlPT4oZVtlLk11dGF0aW9uPTBdPVwiTXV0YXRpb25cIixlW2UuTW91c2VNb3ZlPTFdPVwiTW91c2VNb3ZlXCIsZVtlLk1vdXNlSW50ZXJhY3Rpb249Ml09XCJNb3VzZUludGVyYWN0aW9uXCIsZVtlLlNjcm9sbD0zXT1cIlNjcm9sbFwiLGVbZS5WaWV3cG9ydFJlc2l6ZT00XT1cIlZpZXdwb3J0UmVzaXplXCIsZVtlLklucHV0PTVdPVwiSW5wdXRcIixlW2UuVG91Y2hNb3ZlPTZdPVwiVG91Y2hNb3ZlXCIsZVtlLk1lZGlhSW50ZXJhY3Rpb249N109XCJNZWRpYUludGVyYWN0aW9uXCIsZVtlLlN0eWxlU2hlZXRSdWxlPThdPVwiU3R5bGVTaGVldFJ1bGVcIixlW2UuQ2FudmFzTXV0YXRpb249OV09XCJDYW52YXNNdXRhdGlvblwiLGVbZS5Gb250PTEwXT1cIkZvbnRcIixlW2UuTG9nPTExXT1cIkxvZ1wiLGVbZS5EcmFnPTEyXT1cIkRyYWdcIixlW2UuU3R5bGVEZWNsYXJhdGlvbj0xM109XCJTdHlsZURlY2xhcmF0aW9uXCIsZVtlLlNlbGVjdGlvbj0xNF09XCJTZWxlY3Rpb25cIixlW2UuQWRvcHRlZFN0eWxlU2hlZXQ9MTVdPVwiQWRvcHRlZFN0eWxlU2hlZXRcIixlW2UuQ3VzdG9tRWxlbWVudD0xNl09XCJDdXN0b21FbGVtZW50XCIsZSkpKENpfHx7fSk7ZnVuY3Rpb24gUGkoZSl7dmFyIHQ7cmV0dXJuIGUgaW5zdGFuY2VvZiBFbGVtZW50JiYoZS5pZD09PXFlfHwhKG51bGw9PT0odD1lLmNsb3Nlc3QpfHx2b2lkIDA9PT10fHwhdC5jYWxsKGUsXCIudG9vbGJhci1nbG9iYWwtZmFkZS1jb250YWluZXJcIikpKX1mdW5jdGlvbiBGaShlKXtyZXR1cm4hIWUmJjE9PT1lLm5vZGVUeXBlfWZ1bmN0aW9uIFJpKGUsdCl7cmV0dXJuISFlJiYhIWUudGFnTmFtZSYmZS50YWdOYW1lLnRvTG93ZXJDYXNlKCk9PT10LnRvTG93ZXJDYXNlKCl9ZnVuY3Rpb24gVGkoZSl7cmV0dXJuISFlJiYzPT09ZS5ub2RlVHlwZX1mdW5jdGlvbiAkaShlKXtyZXR1cm4hIWUmJjExPT09ZS5ub2RlVHlwZX1mdW5jdGlvbiBPaShlKXtyZXR1cm4gZT9iKGUpLnNwbGl0KC9cXHMrLyk6W119ZnVuY3Rpb24gTWkoZSl7dmFyIGk9bnVsbD09dD92b2lkIDA6dC5sb2NhdGlvbi5ocmVmO3JldHVybiEhKGkmJmUmJmUuc29tZSgoZT0+aS5tYXRjaChlKSkpKX1mdW5jdGlvbiBMaShlKXt2YXIgdD1cIlwiO3N3aXRjaCh0eXBlb2YgZS5jbGFzc05hbWUpe2Nhc2VcInN0cmluZ1wiOnQ9ZS5jbGFzc05hbWU7YnJlYWs7Y2FzZVwib2JqZWN0XCI6dD0oZS5jbGFzc05hbWUmJlwiYmFzZVZhbFwiaW4gZS5jbGFzc05hbWU/ZS5jbGFzc05hbWUuYmFzZVZhbDpudWxsKXx8ZS5nZXRBdHRyaWJ1dGUoXCJjbGFzc1wiKXx8XCJcIjticmVhaztkZWZhdWx0OnQ9XCJcIn1yZXR1cm4gT2kodCl9ZnVuY3Rpb24gQWkoZSl7cmV0dXJuIE8oZSk/bnVsbDpiKGUpLnNwbGl0KC8oXFxzKykvKS5maWx0ZXIoKGU9PktpKGUpKSkuam9pbihcIlwiKS5yZXBsYWNlKC9bXFxyXFxuXS9nLFwiIFwiKS5yZXBsYWNlKC9bIF0rL2csXCIgXCIpLnN1YnN0cmluZygwLDI1NSl9ZnVuY3Rpb24gRGkoZSl7dmFyIHQ9XCJcIjtyZXR1cm4gVWkoZSkmJiF6aShlKSYmZS5jaGlsZE5vZGVzJiZlLmNoaWxkTm9kZXMubGVuZ3RoJiZZKGUuY2hpbGROb2RlcywoZnVuY3Rpb24oZSl7dmFyIGk7VGkoZSkmJmUudGV4dENvbnRlbnQmJih0Kz1udWxsIT09KGk9QWkoZS50ZXh0Q29udGVudCkpJiZ2b2lkIDAhPT1pP2k6XCJcIil9KSksYih0KX1mdW5jdGlvbiBOaShlKXtyZXR1cm4gRihlLnRhcmdldCk/ZS5zcmNFbGVtZW50fHxudWxsOm51bGwhPT0odD1lLnRhcmdldCkmJnZvaWQgMCE9PXQmJnQuc2hhZG93Um9vdD9lLmNvbXBvc2VkUGF0aCgpWzBdfHxudWxsOmUudGFyZ2V0fHxudWxsO3ZhciB0fXZhciBxaT1bXCJhXCIsXCJidXR0b25cIixcImZvcm1cIixcImlucHV0XCIsXCJzZWxlY3RcIixcInRleHRhcmVhXCIsXCJsYWJlbFwiXTtmdW5jdGlvbiBCaShlKXt2YXIgdD1lLnBhcmVudE5vZGU7cmV0dXJuISghdHx8IUZpKHQpKSYmdH1mdW5jdGlvbiBIaShlLGkpe3ZhciByPWFyZ3VtZW50cy5sZW5ndGg+MiYmdm9pZCAwIT09YXJndW1lbnRzWzJdP2FyZ3VtZW50c1syXTp2b2lkIDAsbj1hcmd1bWVudHMubGVuZ3RoPjM/YXJndW1lbnRzWzNdOnZvaWQgMCxzPWFyZ3VtZW50cy5sZW5ndGg+ND9hcmd1bWVudHNbNF06dm9pZCAwO2lmKCF0fHwhZXx8UmkoZSxcImh0bWxcIil8fCFGaShlKSlyZXR1cm4hMTtpZihudWxsIT1yJiZyLnVybF9hbGxvd2xpc3QmJiFNaShyLnVybF9hbGxvd2xpc3QpKXJldHVybiExO2lmKG51bGwhPXImJnIudXJsX2lnbm9yZWxpc3QmJk1pKHIudXJsX2lnbm9yZWxpc3QpKXJldHVybiExO2lmKG51bGwhPXImJnIuZG9tX2V2ZW50X2FsbG93bGlzdCl7dmFyIG89ci5kb21fZXZlbnRfYWxsb3dsaXN0O2lmKG8mJiFvLnNvbWUoKGU9PmkudHlwZT09PWUpKSlyZXR1cm4hMX1mb3IodmFyIGE9ITEsbD1bZV0sdT0hMCxjPWU7Yy5wYXJlbnROb2RlJiYhUmkoYyxcImJvZHlcIik7KWlmKCRpKGMucGFyZW50Tm9kZSkpbC5wdXNoKGMucGFyZW50Tm9kZS5ob3N0KSxjPWMucGFyZW50Tm9kZS5ob3N0O2Vsc2V7aWYoISh1PUJpKGMpKSlicmVhaztpZihufHxxaS5pbmRleE9mKHUudGFnTmFtZS50b0xvd2VyQ2FzZSgpKT4tMSlhPSEwO2Vsc2V7dmFyIGQ9dC5nZXRDb21wdXRlZFN0eWxlKHUpO2QmJlwicG9pbnRlclwiPT09ZC5nZXRQcm9wZXJ0eVZhbHVlKFwiY3Vyc29yXCIpJiYoYT0hMCl9bC5wdXNoKHUpLGM9dX1pZighZnVuY3Rpb24oZSx0KXt2YXIgaT1udWxsPT10P3ZvaWQgMDp0LmVsZW1lbnRfYWxsb3dsaXN0O2lmKEYoaSkpcmV0dXJuITA7dmFyIHI9ZnVuY3Rpb24oZSl7aWYoaS5zb21lKCh0PT5lLnRhZ05hbWUudG9Mb3dlckNhc2UoKT09PXQpKSlyZXR1cm57djohMH19O2Zvcih2YXIgbiBvZiBlKXt2YXIgcz1yKG4pO2lmKFwib2JqZWN0XCI9PXR5cGVvZiBzKXJldHVybiBzLnZ9cmV0dXJuITF9KGwscikpcmV0dXJuITE7aWYoIWZ1bmN0aW9uKGUsdCl7dmFyIGk9bnVsbD09dD92b2lkIDA6dC5jc3Nfc2VsZWN0b3JfYWxsb3dsaXN0O2lmKEYoaSkpcmV0dXJuITA7dmFyIHI9ZnVuY3Rpb24oZSl7aWYoaS5zb21lKCh0PT5lLm1hdGNoZXModCkpKSlyZXR1cm57djohMH19O2Zvcih2YXIgbiBvZiBlKXt2YXIgcz1yKG4pO2lmKFwib2JqZWN0XCI9PXR5cGVvZiBzKXJldHVybiBzLnZ9cmV0dXJuITF9KGwscikpcmV0dXJuITE7dmFyIGg9dC5nZXRDb21wdXRlZFN0eWxlKGUpO2lmKGgmJlwicG9pbnRlclwiPT09aC5nZXRQcm9wZXJ0eVZhbHVlKFwiY3Vyc29yXCIpJiZcImNsaWNrXCI9PT1pLnR5cGUpcmV0dXJuITA7dmFyIF89ZS50YWdOYW1lLnRvTG93ZXJDYXNlKCk7c3dpdGNoKF8pe2Nhc2VcImh0bWxcIjpyZXR1cm4hMTtjYXNlXCJmb3JtXCI6cmV0dXJuKHN8fFtcInN1Ym1pdFwiXSkuaW5kZXhPZihpLnR5cGUpPj0wO2Nhc2VcImlucHV0XCI6Y2FzZVwic2VsZWN0XCI6Y2FzZVwidGV4dGFyZWFcIjpyZXR1cm4oc3x8W1wiY2hhbmdlXCIsXCJjbGlja1wiXSkuaW5kZXhPZihpLnR5cGUpPj0wO2RlZmF1bHQ6cmV0dXJuIGE/KHN8fFtcImNsaWNrXCJdKS5pbmRleE9mKGkudHlwZSk+PTA6KHN8fFtcImNsaWNrXCJdKS5pbmRleE9mKGkudHlwZSk+PTAmJihxaS5pbmRleE9mKF8pPi0xfHxcInRydWVcIj09PWUuZ2V0QXR0cmlidXRlKFwiY29udGVudGVkaXRhYmxlXCIpKX19ZnVuY3Rpb24gVWkoZSl7Zm9yKHZhciB0PWU7dC5wYXJlbnROb2RlJiYhUmkodCxcImJvZHlcIik7dD10LnBhcmVudE5vZGUpe3ZhciBpPUxpKHQpO2lmKG0oaSxcInBoLXNlbnNpdGl2ZVwiKXx8bShpLFwicGgtbm8tY2FwdHVyZVwiKSlyZXR1cm4hMX1pZihtKExpKGUpLFwicGgtaW5jbHVkZVwiKSlyZXR1cm4hMDt2YXIgcj1lLnR5cGV8fFwiXCI7aWYoUihyKSlzd2l0Y2goci50b0xvd2VyQ2FzZSgpKXtjYXNlXCJoaWRkZW5cIjpjYXNlXCJwYXNzd29yZFwiOnJldHVybiExfXZhciBuPWUubmFtZXx8ZS5pZHx8XCJcIjtpZihSKG4pKXtpZigvXmNjfGNhcmRudW18Y2NudW18Y3JlZGl0Y2FyZHxjc2N8Y3ZjfGN2dnxleHB8cGFzc3xwd2R8cm91dGluZ3xzZWNjb2RlfHNlY3VyaXR5Y29kZXxzZWN1cml0eW51bXxzb2NpYWxzZWN8c29jc2VjfHNzbi9pLnRlc3Qobi5yZXBsYWNlKC9bXmEtekEtWjAtOV0vZyxcIlwiKSkpcmV0dXJuITF9cmV0dXJuITB9ZnVuY3Rpb24gemkoZSl7cmV0dXJuISEoUmkoZSxcImlucHV0XCIpJiYhW1wiYnV0dG9uXCIsXCJjaGVja2JveFwiLFwic3VibWl0XCIsXCJyZXNldFwiXS5pbmNsdWRlcyhlLnR5cGUpfHxSaShlLFwic2VsZWN0XCIpfHxSaShlLFwidGV4dGFyZWFcIil8fFwidHJ1ZVwiPT09ZS5nZXRBdHRyaWJ1dGUoXCJjb250ZW50ZWRpdGFibGVcIikpfXZhciBqaT1cIig0WzAtOV17MTJ9KD86WzAtOV17M30pPyl8KDVbMS01XVswLTldezE0fSl8KDYoPzowMTF8NVswLTldezJ9KVswLTldezEyfSl8KDNbNDddWzAtOV17MTN9KXwoMyg/OjBbMC01XXxbNjhdWzAtOV0pWzAtOV17MTF9KXwoKD86MjEzMXwxODAwfDM1WzAtOV17M30pWzAtOV17MTF9KVwiLFdpPW5ldyBSZWdFeHAoXCJeKD86XCIuY29uY2F0KGppLFwiKSRcIikpLFZpPW5ldyBSZWdFeHAoamkpLEdpPVwiXFxcXGR7M30tP1xcXFxkezJ9LT9cXFxcZHs0fVwiLEppPW5ldyBSZWdFeHAoXCJeKFwiLmNvbmNhdChHaSxcIikkXCIpKSxZaT1uZXcgUmVnRXhwKFwiKFwiLmNvbmNhdChHaSxcIilcIikpO2Z1bmN0aW9uIEtpKGUpe3ZhciB0PSEoYXJndW1lbnRzLmxlbmd0aD4xJiZ2b2lkIDAhPT1hcmd1bWVudHNbMV0pfHxhcmd1bWVudHNbMV07aWYoTyhlKSlyZXR1cm4hMTtpZihSKGUpKXtpZihlPWIoZSksKHQ/V2k6VmkpLnRlc3QoKGV8fFwiXCIpLnJlcGxhY2UoL1stIF0vZyxcIlwiKSkpcmV0dXJuITE7aWYoKHQ/Smk6WWkpLnRlc3QoZSkpcmV0dXJuITF9cmV0dXJuITB9ZnVuY3Rpb24gWGkoZSl7dmFyIHQ9RGkoZSk7cmV0dXJuIEtpKHQ9XCJcIi5jb25jYXQodCxcIiBcIikuY29uY2F0KFFpKGUpKS50cmltKCkpP3Q6XCJcIn1mdW5jdGlvbiBRaShlKXt2YXIgdD1cIlwiO3JldHVybiBlJiZlLmNoaWxkTm9kZXMmJmUuY2hpbGROb2Rlcy5sZW5ndGgmJlkoZS5jaGlsZE5vZGVzLChmdW5jdGlvbihlKXt2YXIgaTtpZihlJiZcInNwYW5cIj09PShudWxsPT09KGk9ZS50YWdOYW1lKXx8dm9pZCAwPT09aT92b2lkIDA6aS50b0xvd2VyQ2FzZSgpKSl0cnl7dmFyIHI9RGkoZSk7dD1cIlwiLmNvbmNhdCh0LFwiIFwiKS5jb25jYXQocikudHJpbSgpLGUuY2hpbGROb2RlcyYmZS5jaGlsZE5vZGVzLmxlbmd0aCYmKHQ9XCJcIi5jb25jYXQodCxcIiBcIikuY29uY2F0KFFpKGUpKS50cmltKCkpfWNhdGNoKGUpe3EuZXJyb3IoXCJbQXV0b0NhcHR1cmVdXCIsZSl9fSkpLHR9ZnVuY3Rpb24gWmkoZSl7cmV0dXJuIGZ1bmN0aW9uKGUpe3ZhciB0PWUubWFwKChlPT57dmFyIHQsaSxyPVwiXCI7aWYoZS50YWdfbmFtZSYmKHIrPWUudGFnX25hbWUpLGUuYXR0cl9jbGFzcylmb3IodmFyIG4gb2YoZS5hdHRyX2NsYXNzLnNvcnQoKSxlLmF0dHJfY2xhc3MpKXIrPVwiLlwiLmNvbmNhdChuLnJlcGxhY2UoL1wiL2csXCJcIikpO3ZhciBzPWooaihqKGooe30sZS50ZXh0P3t0ZXh0OmUudGV4dH06e30pLHt9LHtcIm50aC1jaGlsZFwiOm51bGwhPT0odD1lLm50aF9jaGlsZCkmJnZvaWQgMCE9PXQ/dDowLFwibnRoLW9mLXR5cGVcIjpudWxsIT09KGk9ZS5udGhfb2ZfdHlwZSkmJnZvaWQgMCE9PWk/aTowfSxlLmhyZWY/e2hyZWY6ZS5ocmVmfTp7fSksZS5hdHRyX2lkP3thdHRyX2lkOmUuYXR0cl9pZH06e30pLGUuYXR0cmlidXRlcyksbz17fTtyZXR1cm4gUShzKS5zb3J0KCgoZSx0KT0+e3ZhcltpXT1lLFtyXT10O3JldHVybiBpLmxvY2FsZUNvbXBhcmUocil9KSkuZm9yRWFjaCgoZT0+e3Zhclt0LGldPWU7cmV0dXJuIG9bZXIodC50b1N0cmluZygpKV09ZXIoaS50b1N0cmluZygpKX0pKSxyKz1cIjpcIixyKz1RKHMpLm1hcCgoZT0+e3Zhclt0LGldPWU7cmV0dXJuXCJcIi5jb25jYXQodCwnPVwiJykuY29uY2F0KGksJ1wiJyl9KSkuam9pbihcIlwiKX0pKTtyZXR1cm4gdC5qb2luKFwiO1wiKX0oZnVuY3Rpb24oZSl7cmV0dXJuIGUubWFwKChlPT57dmFyIHQsaSxyPXt0ZXh0Om51bGw9PT0odD1lLiRlbF90ZXh0KXx8dm9pZCAwPT09dD92b2lkIDA6dC5zbGljZSgwLDQwMCksdGFnX25hbWU6ZS50YWdfbmFtZSxocmVmOm51bGw9PT0oaT1lLmF0dHJfX2hyZWYpfHx2b2lkIDA9PT1pP3ZvaWQgMDppLnNsaWNlKDAsMjA0OCksYXR0cl9jbGFzczp0cihlKSxhdHRyX2lkOmUuYXR0cl9faWQsbnRoX2NoaWxkOmUubnRoX2NoaWxkLG50aF9vZl90eXBlOmUubnRoX29mX3R5cGUsYXR0cmlidXRlczp7fX07cmV0dXJuIFEoZSkuZmlsdGVyKChlPT57dmFyW3RdPWU7cmV0dXJuIDA9PT10LmluZGV4T2YoXCJhdHRyX19cIil9KSkuZm9yRWFjaCgoZT0+e3Zhclt0LGldPWU7cmV0dXJuIHIuYXR0cmlidXRlc1t0XT1pfSkpLHJ9KSl9KGUpKX1mdW5jdGlvbiBlcihlKXtyZXR1cm4gZS5yZXBsYWNlKC9cInxcXFxcXCIvZywnXFxcXFwiJyl9ZnVuY3Rpb24gdHIoZSl7dmFyIHQ9ZS5hdHRyX19jbGFzcztyZXR1cm4gdD94KHQpP3Q6T2kodCk6dm9pZCAwfXZhciBpcj1cIltTZXNzaW9uUmVjb3JkaW5nXVwiLHJyPVwicmVkYWN0ZWRcIixucj17aW5pdGlhdG9yVHlwZXM6W1wiYXVkaW9cIixcImJlYWNvblwiLFwiYm9keVwiLFwiY3NzXCIsXCJlYXJseS1oaW50XCIsXCJlbWJlZFwiLFwiZmV0Y2hcIixcImZyYW1lXCIsXCJpZnJhbWVcIixcImljb25cIixcImltYWdlXCIsXCJpbWdcIixcImlucHV0XCIsXCJsaW5rXCIsXCJuYXZpZ2F0aW9uXCIsXCJvYmplY3RcIixcInBpbmdcIixcInNjcmlwdFwiLFwidHJhY2tcIixcInZpZGVvXCIsXCJ4bWxodHRwcmVxdWVzdFwiXSxtYXNrUmVxdWVzdEZuOmU9PmUscmVjb3JkSGVhZGVyczohMSxyZWNvcmRCb2R5OiExLHJlY29yZEluaXRpYWxSZXF1ZXN0czohMSxyZWNvcmRQZXJmb3JtYW5jZTohMSxwZXJmb3JtYW5jZUVudHJ5VHlwZVRvT2JzZXJ2ZTpbXCJmaXJzdC1pbnB1dFwiLFwibmF2aWdhdGlvblwiLFwicGFpbnRcIixcInJlc291cmNlXCJdLHBheWxvYWRTaXplTGltaXRCeXRlczoxZTYscGF5bG9hZEhvc3REZW55TGlzdDpbXCIubHItaW5nZXN0LmlvXCIsXCIuaW5nZXN0LnNlbnRyeS5pb1wiLFwiLmNsYXJpdHkubXNcIixcImFuYWx5dGljcy5nb29nbGUuY29tXCJdfSxzcj1bXCJhdXRob3JpemF0aW9uXCIsXCJ4LWZvcndhcmRlZC1mb3JcIixcImF1dGhvcml6YXRpb25cIixcImNvb2tpZVwiLFwic2V0LWNvb2tpZVwiLFwieC1hcGkta2V5XCIsXCJ4LXJlYWwtaXBcIixcInJlbW90ZS1hZGRyXCIsXCJmb3J3YXJkZWRcIixcInByb3h5LWF1dGhvcml6YXRpb25cIixcIngtY3NyZi10b2tlblwiLFwieC1jc3JmdG9rZW5cIixcIngteHNyZi10b2tlblwiXSxvcj1bXCJwYXNzd29yZFwiLFwic2VjcmV0XCIsXCJwYXNzd2RcIixcImFwaV9rZXlcIixcImFwaWtleVwiLFwiYXV0aFwiLFwiY3JlZGVudGlhbHNcIixcIm15c3FsX3B3ZFwiLFwicHJpdmF0ZWtleVwiLFwicHJpdmF0ZV9rZXlcIixcInRva2VuXCJdLGFyPVtcIi9zL1wiLFwiL2UvXCIsXCIvaS9cIl07ZnVuY3Rpb24gbHIoZSx0LGkscil7aWYoTyhlKSlyZXR1cm4gZTt2YXIgbj0obnVsbD09dD92b2lkIDA6dFtcImNvbnRlbnQtbGVuZ3RoXCJdKXx8ZnVuY3Rpb24oZSl7cmV0dXJuIG5ldyBCbG9iKFtlXSkuc2l6ZX0oZSk7cmV0dXJuIFIobikmJihuPXBhcnNlSW50KG4pKSxuPmk/aXIrXCIgXCIuY29uY2F0KHIsXCIgYm9keSB0b28gbGFyZ2UgdG8gcmVjb3JkIChcIikuY29uY2F0KG4sXCIgYnl0ZXMpXCIpOmV9ZnVuY3Rpb24gdXIoZSx0KXtpZihPKGUpKXJldHVybiBlO3ZhciBpPWU7cmV0dXJuIEtpKGksITEpfHwoaT1pcitcIiBcIit0K1wiIGJvZHkgXCIrcnIpLFkob3IsKGU9Pnt2YXIgcixuO251bGwhPT0ocj1pKSYmdm9pZCAwIT09ciYmci5sZW5ndGgmJi0xIT09KG51bGw9PT0obj1pKXx8dm9pZCAwPT09bj92b2lkIDA6bi5pbmRleE9mKGUpKSYmKGk9aXIrXCIgXCIrdCtcIiBib2R5IFwiK3JyK1wiIGFzIG1pZ2h0IGNvbnRhaW46IFwiK2UpfSkpLGl9dmFyIGNyPShlLHQpPT57dmFyIGkscixuLHM9e3BheWxvYWRTaXplTGltaXRCeXRlczpuci5wYXlsb2FkU2l6ZUxpbWl0Qnl0ZXMscGVyZm9ybWFuY2VFbnRyeVR5cGVUb09ic2VydmU6Wy4uLm5yLnBlcmZvcm1hbmNlRW50cnlUeXBlVG9PYnNlcnZlXSxwYXlsb2FkSG9zdERlbnlMaXN0OlsuLi50LnBheWxvYWRIb3N0RGVueUxpc3R8fFtdLC4uLm5yLnBheWxvYWRIb3N0RGVueUxpc3RdfSxvPSExIT09ZS5zZXNzaW9uX3JlY29yZGluZy5yZWNvcmRIZWFkZXJzJiZ0LnJlY29yZEhlYWRlcnMsYT0hMSE9PWUuc2Vzc2lvbl9yZWNvcmRpbmcucmVjb3JkQm9keSYmdC5yZWNvcmRCb2R5LGw9ITEhPT1lLmNhcHR1cmVfcGVyZm9ybWFuY2UmJnQucmVjb3JkUGVyZm9ybWFuY2UsdT0oaT1zLG49TWF0aC5taW4oMWU2LG51bGwhPT0ocj1pLnBheWxvYWRTaXplTGltaXRCeXRlcykmJnZvaWQgMCE9PXI/cjoxZTYpLGU9PihudWxsIT1lJiZlLnJlcXVlc3RCb2R5JiYoZS5yZXF1ZXN0Qm9keT1scihlLnJlcXVlc3RCb2R5LGUucmVxdWVzdEhlYWRlcnMsbixcIlJlcXVlc3RcIikpLG51bGwhPWUmJmUucmVzcG9uc2VCb2R5JiYoZS5yZXNwb25zZUJvZHk9bHIoZS5yZXNwb25zZUJvZHksZS5yZXNwb25zZUhlYWRlcnMsbixcIlJlc3BvbnNlXCIpKSxlKSksYz10PT57cmV0dXJuIHUoKChlLHQpPT57dmFyIGkscj1ndChlLm5hbWUpLG49MD09PXQuaW5kZXhPZihcImh0dHBcIik/bnVsbD09PShpPWd0KHQpKXx8dm9pZCAwPT09aT92b2lkIDA6aS5wYXRobmFtZTp0O1wiL1wiPT09biYmKG49XCJcIik7dmFyIHM9bnVsbD09cj92b2lkIDA6ci5wYXRobmFtZS5yZXBsYWNlKG58fFwiXCIsXCJcIik7aWYoIShyJiZzJiZhci5zb21lKChlPT4wPT09cy5pbmRleE9mKGUpKSkpKXJldHVybiBlfSkoKHI9KGk9dCkucmVxdWVzdEhlYWRlcnMsTyhyKXx8WShPYmplY3Qua2V5cyhudWxsIT1yP3I6e30pLChlPT57c3IuaW5jbHVkZXMoZS50b0xvd2VyQ2FzZSgpKSYmKHJbZV09cnIpfSkpLGkpLGUuYXBpX2hvc3QpKTt2YXIgaSxyfSxkPUkoZS5zZXNzaW9uX3JlY29yZGluZy5tYXNrTmV0d29ya1JlcXVlc3RGbik7cmV0dXJuIGQmJkkoZS5zZXNzaW9uX3JlY29yZGluZy5tYXNrQ2FwdHVyZWROZXR3b3JrUmVxdWVzdEZuKSYmcS53YXJuKFwiQm90aCBgbWFza05ldHdvcmtSZXF1ZXN0Rm5gIGFuZCBgbWFza0NhcHR1cmVkTmV0d29ya1JlcXVlc3RGbmAgYXJlIGRlZmluZWQuIGBtYXNrTmV0d29ya1JlcXVlc3RGbmAgd2lsbCBiZSBpZ25vcmVkLlwiKSxkJiYoZS5zZXNzaW9uX3JlY29yZGluZy5tYXNrQ2FwdHVyZWROZXR3b3JrUmVxdWVzdEZuPXQ9Pnt2YXIgaT1lLnNlc3Npb25fcmVjb3JkaW5nLm1hc2tOZXR3b3JrUmVxdWVzdEZuKHt1cmw6dC5uYW1lfSk7cmV0dXJuIGooaih7fSx0KSx7fSx7bmFtZTpudWxsPT1pP3ZvaWQgMDppLnVybH0pfSkscy5tYXNrUmVxdWVzdEZuPUkoZS5zZXNzaW9uX3JlY29yZGluZy5tYXNrQ2FwdHVyZWROZXR3b3JrUmVxdWVzdEZuKT90PT57dmFyIGkscixuLHM9Yyh0KTtyZXR1cm4gcyYmbnVsbCE9PShpPW51bGw9PT0ocj0obj1lLnNlc3Npb25fcmVjb3JkaW5nKS5tYXNrQ2FwdHVyZWROZXR3b3JrUmVxdWVzdEZuKXx8dm9pZCAwPT09cj92b2lkIDA6ci5jYWxsKG4scykpJiZ2b2lkIDAhPT1pP2k6dm9pZCAwfTplPT5mdW5jdGlvbihlKXtpZighRihlKSlyZXR1cm4gZS5yZXF1ZXN0Qm9keT11cihlLnJlcXVlc3RCb2R5LFwiUmVxdWVzdFwiKSxlLnJlc3BvbnNlQm9keT11cihlLnJlc3BvbnNlQm9keSxcIlJlc3BvbnNlXCIpLGV9KGMoZSkpLGooaihqKHt9LG5yKSxzKSx7fSx7cmVjb3JkSGVhZGVyczpvLHJlY29yZEJvZHk6YSxyZWNvcmRQZXJmb3JtYW5jZTpsLHJlY29yZEluaXRpYWxSZXF1ZXN0czpsfSl9O2Z1bmN0aW9uIGRyKGUsdCxpLHIsbil7cmV0dXJuIHQ+aSYmKHEud2FybihcIm1pbiBjYW5ub3QgYmUgZ3JlYXRlciB0aGFuIG1heC5cIiksdD1pKSxNKGUpP2U+aT8ociYmcS53YXJuKHIrXCIgY2Fubm90IGJlICBncmVhdGVyIHRoYW4gbWF4OiBcIitpK1wiLiBVc2luZyBtYXggdmFsdWUgaW5zdGVhZC5cIiksaSk6ZTx0PyhyJiZxLndhcm4ocitcIiBjYW5ub3QgYmUgbGVzcyB0aGFuIG1pbjogXCIrdCtcIi4gVXNpbmcgbWluIHZhbHVlIGluc3RlYWQuXCIpLHQpOmU6KHImJnEud2FybihyK1wiIG11c3QgYmUgYSBudW1iZXIuIHVzaW5nIG1heCBvciBmYWxsYmFjay4gbWF4OiBcIitpK1wiLCBmYWxsYmFjazogXCIrbiksZHIobnx8aSx0LGkscikpfWNsYXNzIGhye2NvbnN0cnVjdG9yKGUpe3ZhciB0LGkscj1hcmd1bWVudHMubGVuZ3RoPjEmJnZvaWQgMCE9PWFyZ3VtZW50c1sxXT9hcmd1bWVudHNbMV06e307Vyh0aGlzLFwiYnVja2V0U2l6ZVwiLDEwMCksVyh0aGlzLFwicmVmaWxsUmF0ZVwiLDEwKSxXKHRoaXMsXCJtdXRhdGlvbkJ1Y2tldHNcIix7fSksVyh0aGlzLFwibG9nZ2VkVHJhY2tlclwiLHt9KSxXKHRoaXMsXCJyZWZpbGxCdWNrZXRzXCIsKCgpPT57T2JqZWN0LmtleXModGhpcy5tdXRhdGlvbkJ1Y2tldHMpLmZvckVhY2goKGU9Pnt0aGlzLm11dGF0aW9uQnVja2V0c1tlXT10aGlzLm11dGF0aW9uQnVja2V0c1tlXSt0aGlzLnJlZmlsbFJhdGUsdGhpcy5tdXRhdGlvbkJ1Y2tldHNbZV0+PXRoaXMuYnVja2V0U2l6ZSYmZGVsZXRlIHRoaXMubXV0YXRpb25CdWNrZXRzW2VdfSkpfSkpLFcodGhpcyxcImdldE5vZGVPclJlbGV2YW50UGFyZW50XCIsKGU9Pnt2YXIgdD10aGlzLnJyd2ViLm1pcnJvci5nZXROb2RlKGUpO2lmKFwic3ZnXCIhPT0obnVsbD09dD92b2lkIDA6dC5ub2RlTmFtZSkmJnQgaW5zdGFuY2VvZiBFbGVtZW50KXt2YXIgaT10LmNsb3Nlc3QoXCJzdmdcIik7aWYoaSlyZXR1cm5bdGhpcy5ycndlYi5taXJyb3IuZ2V0SWQoaSksaV19cmV0dXJuW2UsdF19KSksVyh0aGlzLFwibnVtYmVyT2ZDaGFuZ2VzXCIsKGU9Pnt2YXIgdCxpLHIsbixzLG8sYSxsO3JldHVybihudWxsIT09KHQ9bnVsbD09PShpPWUucmVtb3Zlcyl8fHZvaWQgMD09PWk/dm9pZCAwOmkubGVuZ3RoKSYmdm9pZCAwIT09dD90OjApKyhudWxsIT09KHI9bnVsbD09PShuPWUuYXR0cmlidXRlcyl8fHZvaWQgMD09PW4/dm9pZCAwOm4ubGVuZ3RoKSYmdm9pZCAwIT09cj9yOjApKyhudWxsIT09KHM9bnVsbD09PShvPWUudGV4dHMpfHx2b2lkIDA9PT1vP3ZvaWQgMDpvLmxlbmd0aCkmJnZvaWQgMCE9PXM/czowKSsobnVsbCE9PShhPW51bGw9PT0obD1lLmFkZHMpfHx2b2lkIDA9PT1sP3ZvaWQgMDpsLmxlbmd0aCkmJnZvaWQgMCE9PWE/YTowKX0pKSxXKHRoaXMsXCJ0aHJvdHRsZU11dGF0aW9uc1wiLChlPT57aWYoMyE9PWUudHlwZXx8MCE9PWUuZGF0YS5zb3VyY2UpcmV0dXJuIGU7dmFyIHQ9ZS5kYXRhLGk9dGhpcy5udW1iZXJPZkNoYW5nZXModCk7dC5hdHRyaWJ1dGVzJiYodC5hdHRyaWJ1dGVzPXQuYXR0cmlidXRlcy5maWx0ZXIoKGU9Pnt2YXIgdCxpLHIsW24sc109dGhpcy5nZXROb2RlT3JSZWxldmFudFBhcmVudChlLmlkKTtpZigwPT09dGhpcy5tdXRhdGlvbkJ1Y2tldHNbbl0pcmV0dXJuITE7KHRoaXMubXV0YXRpb25CdWNrZXRzW25dPW51bGwhPT0odD10aGlzLm11dGF0aW9uQnVja2V0c1tuXSkmJnZvaWQgMCE9PXQ/dDp0aGlzLmJ1Y2tldFNpemUsdGhpcy5tdXRhdGlvbkJ1Y2tldHNbbl09TWF0aC5tYXgodGhpcy5tdXRhdGlvbkJ1Y2tldHNbbl0tMSwwKSwwPT09dGhpcy5tdXRhdGlvbkJ1Y2tldHNbbl0pJiYodGhpcy5sb2dnZWRUcmFja2VyW25dfHwodGhpcy5sb2dnZWRUcmFja2VyW25dPSEwLG51bGw9PT0oaT0ocj10aGlzLm9wdGlvbnMpLm9uQmxvY2tlZE5vZGUpfHx2b2lkIDA9PT1pfHxpLmNhbGwocixuLHMpKSk7cmV0dXJuIGV9KSkpO3ZhciByPXRoaXMubnVtYmVyT2ZDaGFuZ2VzKHQpO3JldHVybiAwIT09cnx8aT09PXI/ZTp2b2lkIDB9KSksdGhpcy5ycndlYj1lLHRoaXMub3B0aW9ucz1yLHRoaXMucmVmaWxsUmF0ZT1kcihudWxsIT09KHQ9dGhpcy5vcHRpb25zLnJlZmlsbFJhdGUpJiZ2b2lkIDAhPT10P3Q6dGhpcy5yZWZpbGxSYXRlLDAsMTAwLFwibXV0YXRpb24gdGhyb3R0bGluZyByZWZpbGwgcmF0ZVwiKSx0aGlzLmJ1Y2tldFNpemU9ZHIobnVsbCE9PShpPXRoaXMub3B0aW9ucy5idWNrZXRTaXplKSYmdm9pZCAwIT09aT9pOnRoaXMuYnVja2V0U2l6ZSwwLDEwMCxcIm11dGF0aW9uIHRocm90dGxpbmcgYnVja2V0IHNpemVcIiksc2V0SW50ZXJ2YWwoKCgpPT57dGhpcy5yZWZpbGxCdWNrZXRzKCl9KSwxZTMpfX12YXIgX3I9VWludDhBcnJheSxwcj1VaW50MTZBcnJheSx2cj1VaW50MzJBcnJheSxncj1uZXcgX3IoWzAsMCwwLDAsMCwwLDAsMCwxLDEsMSwxLDIsMiwyLDIsMywzLDMsMyw0LDQsNCw0LDUsNSw1LDUsMCwwLDAsMF0pLGZyPW5ldyBfcihbMCwwLDAsMCwxLDEsMiwyLDMsMyw0LDQsNSw1LDYsNiw3LDcsOCw4LDksOSwxMCwxMCwxMSwxMSwxMiwxMiwxMywxMywwLDBdKSxtcj1uZXcgX3IoWzE2LDE3LDE4LDAsOCw3LDksNiwxMCw1LDExLDQsMTIsMywxMywyLDE0LDEsMTVdKSxicj1mdW5jdGlvbihlLHQpe2Zvcih2YXIgaT1uZXcgcHIoMzEpLHI9MDtyPDMxOysrcilpW3JdPXQrPTE8PGVbci0xXTt2YXIgbj1uZXcgdnIoaVszMF0pO2ZvcihyPTE7cjwzMDsrK3IpZm9yKHZhciBzPWlbcl07czxpW3IrMV07KytzKW5bc109cy1pW3JdPDw1fHI7cmV0dXJuW2ksbl19LHlyPWJyKGdyLDIpLHdyPXlyWzBdLFNyPXlyWzFdO3dyWzI4XT0yNTgsU3JbMjU4XT0yODtmb3IodmFyIEVyPWJyKGZyLDApWzFdLGtyPW5ldyBwcigzMjc2OCkseHI9MDt4cjwzMjc2ODsrK3hyKXt2YXIgSXI9KDQzNjkwJnhyKT4+PjF8KDIxODQ1JnhyKTw8MTtJcj0oNjE2ODAmKElyPSg1MjQyOCZJcik+Pj4yfCgxMzEwNyZJcik8PDIpKT4+PjR8KDM4NTUmSXIpPDw0LGtyW3hyXT0oKDY1MjgwJklyKT4+Pjh8KDI1NSZJcik8PDgpPj4+MX12YXIgQ3I9ZnVuY3Rpb24oZSx0LGkpe2Zvcih2YXIgcj1lLmxlbmd0aCxuPTAscz1uZXcgcHIodCk7bjxyOysrbikrK3NbZVtuXS0xXTt2YXIgbyxhPW5ldyBwcih0KTtmb3Iobj0wO248dDsrK24pYVtuXT1hW24tMV0rc1tuLTFdPDwxO2lmKGkpe289bmV3IHByKDE8PHQpO3ZhciBsPTE1LXQ7Zm9yKG49MDtuPHI7KytuKWlmKGVbbl0pZm9yKHZhciB1PW48PDR8ZVtuXSxjPXQtZVtuXSxkPWFbZVtuXS0xXSsrPDxjLGg9ZHwoMTw8YyktMTtkPD1oOysrZClvW2tyW2RdPj4+bF09dX1lbHNlIGZvcihvPW5ldyBwcihyKSxuPTA7bjxyOysrbilvW25dPWtyW2FbZVtuXS0xXSsrXT4+PjE1LWVbbl07cmV0dXJuIG99LFByPW5ldyBfcigyODgpO2Zvcih4cj0wO3hyPDE0NDsrK3hyKVByW3hyXT04O2Zvcih4cj0xNDQ7eHI8MjU2OysreHIpUHJbeHJdPTk7Zm9yKHhyPTI1Njt4cjwyODA7Kyt4cilQclt4cl09Nztmb3IoeHI9MjgwO3hyPDI4ODsrK3hyKVByW3hyXT04O3ZhciBGcj1uZXcgX3IoMzIpO2Zvcih4cj0wO3hyPDMyOysreHIpRnJbeHJdPTU7dmFyIFJyPUNyKFByLDksMCksVHI9Q3IoRnIsNSwwKSwkcj1mdW5jdGlvbihlKXtyZXR1cm4oZS84Pj4wKSsoNyZlJiYxKX0sT3I9ZnVuY3Rpb24oZSx0LGkpeyhudWxsPT1pfHxpPmUubGVuZ3RoKSYmKGk9ZS5sZW5ndGgpO3ZhciByPW5ldyhlIGluc3RhbmNlb2YgcHI/cHI6ZSBpbnN0YW5jZW9mIHZyP3ZyOl9yKShpLXQpO3JldHVybiByLnNldChlLnN1YmFycmF5KHQsaSkpLHJ9LE1yPWZ1bmN0aW9uKGUsdCxpKXtpPDw9NyZ0O3ZhciByPXQvOD4+MDtlW3JdfD1pLGVbcisxXXw9aT4+Pjh9LExyPWZ1bmN0aW9uKGUsdCxpKXtpPDw9NyZ0O3ZhciByPXQvOD4+MDtlW3JdfD1pLGVbcisxXXw9aT4+PjgsZVtyKzJdfD1pPj4+MTZ9LEFyPWZ1bmN0aW9uKGUsdCl7Zm9yKHZhciBpPVtdLHI9MDtyPGUubGVuZ3RoOysrcillW3JdJiZpLnB1c2goe3M6cixmOmVbcl19KTt2YXIgbj1pLmxlbmd0aCxzPWkuc2xpY2UoKTtpZighbilyZXR1cm5bbmV3IF9yKDApLDBdO2lmKDE9PW4pe3ZhciBvPW5ldyBfcihpWzBdLnMrMSk7cmV0dXJuIG9baVswXS5zXT0xLFtvLDFdfWkuc29ydCgoZnVuY3Rpb24oZSx0KXtyZXR1cm4gZS5mLXQuZn0pKSxpLnB1c2goe3M6LTEsZjoyNTAwMX0pO3ZhciBhPWlbMF0sbD1pWzFdLHU9MCxjPTEsZD0yO2ZvcihpWzBdPXtzOi0xLGY6YS5mK2wuZixsOmEscjpsfTtjIT1uLTE7KWE9aVtpW3VdLmY8aVtkXS5mP3UrKzpkKytdLGw9aVt1IT1jJiZpW3VdLmY8aVtkXS5mP3UrKzpkKytdLGlbYysrXT17czotMSxmOmEuZitsLmYsbDphLHI6bH07dmFyIGg9c1swXS5zO2ZvcihyPTE7cjxuOysrcilzW3JdLnM+aCYmKGg9c1tyXS5zKTt2YXIgXz1uZXcgcHIoaCsxKSxwPURyKGlbYy0xXSxfLDApO2lmKHA+dCl7cj0wO3ZhciB2PTAsZz1wLXQsZj0xPDxnO2ZvcihzLnNvcnQoKGZ1bmN0aW9uKGUsdCl7cmV0dXJuIF9bdC5zXS1fW2Uuc118fGUuZi10LmZ9KSk7cjxuOysrcil7dmFyIG09c1tyXS5zO2lmKCEoX1ttXT50KSlicmVhazt2Kz1mLSgxPDxwLV9bbV0pLF9bbV09dH1mb3Iodj4+Pj1nO3Y+MDspe3ZhciBiPXNbcl0ucztfW2JdPHQ/di09MTw8dC1fW2JdKystMTorK3J9Zm9yKDtyPj0wJiZ2Oy0tcil7dmFyIHk9c1tyXS5zO19beV09PXQmJigtLV9beV0sKyt2KX1wPXR9cmV0dXJuW25ldyBfcihfKSxwXX0sRHI9ZnVuY3Rpb24oZSx0LGkpe3JldHVybi0xPT1lLnM/TWF0aC5tYXgoRHIoZS5sLHQsaSsxKSxEcihlLnIsdCxpKzEpKTp0W2Uuc109aX0sTnI9ZnVuY3Rpb24oZSl7Zm9yKHZhciB0PWUubGVuZ3RoO3QmJiFlWy0tdF07KTtmb3IodmFyIGk9bmV3IHByKCsrdCkscj0wLG49ZVswXSxzPTEsbz1mdW5jdGlvbihlKXtpW3IrK109ZX0sYT0xO2E8PXQ7KythKWlmKGVbYV09PW4mJmEhPXQpKytzO2Vsc2V7aWYoIW4mJnM+Mil7Zm9yKDtzPjEzODtzLT0xMzgpbygzMjc1NCk7cz4yJiYobyhzPjEwP3MtMTE8PDV8Mjg2OTA6cy0zPDw1fDEyMzA1KSxzPTApfWVsc2UgaWYocz4zKXtmb3IobyhuKSwtLXM7cz42O3MtPTYpbyg4MzA0KTtzPjImJihvKHMtMzw8NXw4MjA4KSxzPTApfWZvcig7cy0tOylvKG4pO3M9MSxuPWVbYV19cmV0dXJuW2kuc3ViYXJyYXkoMCxyKSx0XX0scXI9ZnVuY3Rpb24oZSx0KXtmb3IodmFyIGk9MCxyPTA7cjx0Lmxlbmd0aDsrK3IpaSs9ZVtyXSp0W3JdO3JldHVybiBpfSxCcj1mdW5jdGlvbihlLHQsaSl7dmFyIHI9aS5sZW5ndGgsbj0kcih0KzIpO2Vbbl09MjU1JnIsZVtuKzFdPXI+Pj44LGVbbisyXT0yNTVeZVtuXSxlW24rM109MjU1XmVbbisxXTtmb3IodmFyIHM9MDtzPHI7KytzKWVbbitzKzRdPWlbc107cmV0dXJuIDgqKG4rNCtyKX0sSHI9ZnVuY3Rpb24oZSx0LGkscixuLHMsbyxhLGwsdSxjKXtNcih0LGMrKyxpKSwrK25bMjU2XTtmb3IodmFyIGQ9QXIobiwxNSksaD1kWzBdLF89ZFsxXSxwPUFyKHMsMTUpLHY9cFswXSxnPXBbMV0sZj1OcihoKSxtPWZbMF0sYj1mWzFdLHk9TnIodiksdz15WzBdLFM9eVsxXSxFPW5ldyBwcigxOSksaz0wO2s8bS5sZW5ndGg7KytrKUVbMzEmbVtrXV0rKztmb3Ioaz0wO2s8dy5sZW5ndGg7KytrKUVbMzEmd1trXV0rKztmb3IodmFyIHg9QXIoRSw3KSxJPXhbMF0sQz14WzFdLFA9MTk7UD40JiYhSVttcltQLTFdXTstLVApO3ZhciBGLFIsVCwkLE89dSs1PDwzLE09cXIobixQcikrcXIocyxGcikrbyxMPXFyKG4saCkrcXIocyx2KStvKzE0KzMqUCtxcihFLEkpKygyKkVbMTZdKzMqRVsxN10rNypFWzE4XSk7aWYoTzw9TSYmTzw9TClyZXR1cm4gQnIodCxjLGUuc3ViYXJyYXkobCxsK3UpKTtpZihNcih0LGMsMSsoTDxNKSksYys9MixMPE0pe0Y9Q3IoaCxfLDApLFI9aCxUPUNyKHYsZywwKSwkPXY7dmFyIEE9Q3IoSSxDLDApO01yKHQsYyxiLTI1NyksTXIodCxjKzUsUy0xKSxNcih0LGMrMTAsUC00KSxjKz0xNDtmb3Ioaz0wO2s8UDsrK2spTXIodCxjKzMqayxJW21yW2tdXSk7Yys9MypQO2Zvcih2YXIgRD1bbSx3XSxOPTA7TjwyOysrTil7dmFyIHE9RFtOXTtmb3Ioaz0wO2s8cS5sZW5ndGg7KytrKXt2YXIgQj0zMSZxW2tdO01yKHQsYyxBW0JdKSxjKz1JW0JdLEI+MTUmJihNcih0LGMscVtrXT4+PjUmMTI3KSxjKz1xW2tdPj4+MTIpfX19ZWxzZSBGPVJyLFI9UHIsVD1UciwkPUZyO2ZvcihrPTA7azxhOysraylpZihyW2tdPjI1NSl7Qj1yW2tdPj4+MTgmMzE7THIodCxjLEZbQisyNTddKSxjKz1SW0IrMjU3XSxCPjcmJihNcih0LGMscltrXT4+PjIzJjMxKSxjKz1ncltCXSk7dmFyIEg9MzEmcltrXTtMcih0LGMsVFtIXSksYys9JFtIXSxIPjMmJihMcih0LGMscltrXT4+PjUmODE5MSksYys9ZnJbSF0pfWVsc2UgTHIodCxjLEZbcltrXV0pLGMrPVJbcltrXV07cmV0dXJuIExyKHQsYyxGWzI1Nl0pLGMrUlsyNTZdfSxVcj1uZXcgdnIoWzY1NTQwLDEzMTA4MCwxMzEwODgsMTMxMTA0LDI2MjE3NiwxMDQ4NzA0LDEwNDg4MzIsMjExNDU2MCwyMTE3NjMyXSksenI9ZnVuY3Rpb24oKXtmb3IodmFyIGU9bmV3IHZyKDI1NiksdD0wO3Q8MjU2OysrdCl7Zm9yKHZhciBpPXQscj05Oy0tcjspaT0oMSZpJiYzOTg4MjkyMzg0KV5pPj4+MTtlW3RdPWl9cmV0dXJuIGV9KCksanI9ZnVuY3Rpb24oKXt2YXIgZT00Mjk0OTY3Mjk1O3JldHVybntwOmZ1bmN0aW9uKHQpe2Zvcih2YXIgaT1lLHI9MDtyPHQubGVuZ3RoOysrcilpPXpyWzI1NSZpXnRbcl1dXmk+Pj44O2U9aX0sZDpmdW5jdGlvbigpe3JldHVybiA0Mjk0OTY3Mjk1XmV9fX0sV3I9ZnVuY3Rpb24oZSx0LGkscixuKXtyZXR1cm4gZnVuY3Rpb24oZSx0LGkscixuLHMpe3ZhciBvPWUubGVuZ3RoLGE9bmV3IF9yKHIrbys1KigxK01hdGguZmxvb3Ioby83ZTMpKStuKSxsPWEuc3ViYXJyYXkocixhLmxlbmd0aC1uKSx1PTA7aWYoIXR8fG88OClmb3IodmFyIGM9MDtjPD1vO2MrPTY1NTM1KXt2YXIgZD1jKzY1NTM1O2Q8bz91PUJyKGwsdSxlLnN1YmFycmF5KGMsZCkpOihsW2NdPXMsdT1CcihsLHUsZS5zdWJhcnJheShjLG8pKSl9ZWxzZXtmb3IodmFyIGg9VXJbdC0xXSxfPWg+Pj4xMyxwPTgxOTEmaCx2PSgxPDxpKS0xLGc9bmV3IHByKDMyNzY4KSxmPW5ldyBwcih2KzEpLG09TWF0aC5jZWlsKGkvMyksYj0yKm0seT1mdW5jdGlvbih0KXtyZXR1cm4oZVt0XV5lW3QrMV08PG1eZVt0KzJdPDxiKSZ2fSx3PW5ldyB2cigyNWUzKSxTPW5ldyBwcigyODgpLEU9bmV3IHByKDMyKSxrPTAseD0wLEk9KGM9MCwwKSxDPTAsUD0wO2M8bzsrK2Mpe3ZhciBGPXkoYyksUj0zMjc2NyZjLFQ9ZltGXTtpZihnW1JdPVQsZltGXT1SLEM8PWMpe3ZhciAkPW8tYztpZigoaz43ZTN8fEk+MjQ1NzYpJiYkPjQyMyl7dT1IcihlLGwsMCx3LFMsRSx4LEksUCxjLVAsdSksST1rPXg9MCxQPWM7Zm9yKHZhciBPPTA7TzwyODY7KytPKVNbT109MDtmb3IoTz0wO088MzA7KytPKUVbT109MH12YXIgTT0yLEw9MCxBPXAsRD1SLVQmMzI3Njc7aWYoJD4yJiZGPT15KGMtRCkpZm9yKHZhciBOPU1hdGgubWluKF8sJCktMSxxPU1hdGgubWluKDMyNzY3LGMpLEI9TWF0aC5taW4oMjU4LCQpO0Q8PXEmJi0tQSYmUiE9VDspe2lmKGVbYytNXT09ZVtjK00tRF0pe2Zvcih2YXIgSD0wO0g8QiYmZVtjK0hdPT1lW2MrSC1EXTsrK0gpO2lmKEg+TSl7aWYoTT1ILEw9RCxIPk4pYnJlYWs7dmFyIFU9TWF0aC5taW4oRCxILTIpLHo9MDtmb3IoTz0wO088VTsrK08pe3ZhciBqPWMtRCtPKzMyNzY4JjMyNzY3LFc9ai1nW2pdKzMyNzY4JjMyNzY3O1c+eiYmKHo9VyxUPWopfX19RCs9KFI9VCktKFQ9Z1tSXSkrMzI3NjgmMzI3Njd9aWYoTCl7d1tJKytdPTI2ODQzNTQ1NnxTcltNXTw8MTh8RXJbTF07dmFyIFY9MzEmU3JbTV0sRz0zMSZFcltMXTt4Kz1ncltWXStmcltHXSwrK1NbMjU3K1ZdLCsrRVtHXSxDPWMrTSwrK2t9ZWxzZSB3W0krK109ZVtjXSwrK1NbZVtjXV19fXU9SHIoZSxsLHMsdyxTLEUseCxJLFAsYy1QLHUpfXJldHVybiBPcihhLDAsciskcih1KStuKX0oZSxudWxsPT10LmxldmVsPzY6dC5sZXZlbCxudWxsPT10Lm1lbT9NYXRoLmNlaWwoMS41Kk1hdGgubWF4KDgsTWF0aC5taW4oMTMsTWF0aC5sb2coZS5sZW5ndGgpKSkpOjEyK3QubWVtLGksciwhbil9LFZyPWZ1bmN0aW9uKGUsdCxpKXtmb3IoO2k7Kyt0KWVbdF09aSxpPj4+PTh9LEdyPWZ1bmN0aW9uKGUsdCl7dmFyIGk9dC5maWxlbmFtZTtpZihlWzBdPTMxLGVbMV09MTM5LGVbMl09OCxlWzhdPXQubGV2ZWw8Mj80Ojk9PXQubGV2ZWw/MjowLGVbOV09MywwIT10Lm10aW1lJiZWcihlLDQsTWF0aC5mbG9vcihuZXcgRGF0ZSh0Lm10aW1lfHxEYXRlLm5vdygpKS8xZTMpKSxpKXtlWzNdPTg7Zm9yKHZhciByPTA7cjw9aS5sZW5ndGg7KytyKWVbcisxMF09aS5jaGFyQ29kZUF0KHIpfX0sSnI9ZnVuY3Rpb24oZSl7cmV0dXJuIDEwKyhlLmZpbGVuYW1lJiZlLmZpbGVuYW1lLmxlbmd0aCsxfHwwKX07ZnVuY3Rpb24gWXIoZSx0KXt2b2lkIDA9PT10JiYodD17fSk7dmFyIGk9anIoKSxyPWUubGVuZ3RoO2kucChlKTt2YXIgbj1XcihlLHQsSnIodCksOCkscz1uLmxlbmd0aDtyZXR1cm4gR3Iobix0KSxWcihuLHMtOCxpLmQoKSksVnIobixzLTQsciksbn1mdW5jdGlvbiBLcihlLHQpe3ZhciBpPWUubGVuZ3RoO2lmKFwidW5kZWZpbmVkXCIhPXR5cGVvZiBUZXh0RW5jb2RlcilyZXR1cm4obmV3IFRleHRFbmNvZGVyKS5lbmNvZGUoZSk7Zm9yKHZhciByPW5ldyBfcihlLmxlbmd0aCsoZS5sZW5ndGg+Pj4xKSksbj0wLHM9ZnVuY3Rpb24oZSl7cltuKytdPWV9LG89MDtvPGk7KytvKXtpZihuKzU+ci5sZW5ndGgpe3ZhciBhPW5ldyBfcihuKzgrKGktbzw8MSkpO2Euc2V0KHIpLHI9YX12YXIgbD1lLmNoYXJDb2RlQXQobyk7bDwxMjh8fHQ/cyhsKTpsPDIwNDg/KHMoMTkyfGw+Pj42KSxzKDEyOHw2MyZsKSk6bD41NTI5NSYmbDw1NzM0ND8ocygyNDB8KGw9NjU1MzYrKDEwNDc1NTImbCl8MTAyMyZlLmNoYXJDb2RlQXQoKytvKSk+Pj4xOCkscygxMjh8bD4+PjEyJjYzKSxzKDEyOHxsPj4+NiY2MykscygxMjh8NjMmbCkpOihzKDIyNHxsPj4+MTIpLHMoMTI4fGw+Pj42JjYzKSxzKDEyOHw2MyZsKSl9cmV0dXJuIE9yKHIsMCxuKX12YXIgWHI9XCJbU2Vzc2lvblJlY29yZGluZ11cIixRcj1CKFhyKSxacj0zZTUsZW49W0NpLk1vdXNlTW92ZSxDaS5Nb3VzZUludGVyYWN0aW9uLENpLlNjcm9sbCxDaS5WaWV3cG9ydFJlc2l6ZSxDaS5JbnB1dCxDaS5Ub3VjaE1vdmUsQ2kuTWVkaWFJbnRlcmFjdGlvbixDaS5EcmFnXSx0bj1lPT4oe3Jyd2ViTWV0aG9kOmUsZW5xdWV1ZWRBdDpEYXRlLm5vdygpLGF0dGVtcHQ6MX0pO2Z1bmN0aW9uIHJuKGUpe3JldHVybiBmdW5jdGlvbihlLHQpe2Zvcih2YXIgaT1cIlwiLHI9MDtyPGUubGVuZ3RoOyl7dmFyIG49ZVtyKytdO248MTI4fHx0P2krPVN0cmluZy5mcm9tQ2hhckNvZGUobik6bjwyMjQ/aSs9U3RyaW5nLmZyb21DaGFyQ29kZSgoMzEmbik8PDZ8NjMmZVtyKytdKTpuPDI0MD9pKz1TdHJpbmcuZnJvbUNoYXJDb2RlKCgxNSZuKTw8MTJ8KDYzJmVbcisrXSk8PDZ8NjMmZVtyKytdKToobj0oKDE1Jm4pPDwxOHwoNjMmZVtyKytdKTw8MTJ8KDYzJmVbcisrXSk8PDZ8NjMmZVtyKytdKS02NTUzNixpKz1TdHJpbmcuZnJvbUNoYXJDb2RlKDU1Mjk2fG4+PjEwLDU2MzIwfDEwMjMmbikpfXJldHVybiBpfShZcihLcihKU09OLnN0cmluZ2lmeShlKSkpLCEwKX1mdW5jdGlvbiBubihlKXtyZXR1cm4gZS50eXBlPT09SWkuQ3VzdG9tJiZcInNlc3Npb25JZGxlXCI9PT1lLmRhdGEudGFnfWZ1bmN0aW9uIHNuKGUsdCl7cmV0dXJuIHQuc29tZSgodD0+XCJyZWdleFwiPT09dC5tYXRjaGluZyYmbmV3IFJlZ0V4cCh0LnVybCkudGVzdChlKSkpfWNsYXNzIG9ue2dldCBzZXNzaW9uSWRsZVRocmVzaG9sZE1pbGxpc2Vjb25kcygpe3JldHVybiB0aGlzLmluc3RhbmNlLmNvbmZpZy5zZXNzaW9uX3JlY29yZGluZy5zZXNzaW9uX2lkbGVfdGhyZXNob2xkX21zfHwzZTV9Z2V0IHJyd2ViUmVjb3JkKCl7dmFyIGUsdDtyZXR1cm4gbnVsbD09X3x8bnVsbD09PShlPV8uX19Qb3N0aG9nRXh0ZW5zaW9uc19fKXx8dm9pZCAwPT09ZXx8bnVsbD09PSh0PWUucnJ3ZWIpfHx2b2lkIDA9PT10P3ZvaWQgMDp0LnJlY29yZH1nZXQgc3RhcnRlZCgpe3JldHVybiB0aGlzLl9jYXB0dXJlU3RhcnRlZH1nZXQgc2Vzc2lvbk1hbmFnZXIoKXtpZighdGhpcy5pbnN0YW5jZS5zZXNzaW9uTWFuYWdlcil0aHJvdyBuZXcgRXJyb3IoWHIrXCIgbXVzdCBiZSBzdGFydGVkIHdpdGggYSB2YWxpZCBzZXNzaW9uTWFuYWdlci5cIik7cmV0dXJuIHRoaXMuaW5zdGFuY2Uuc2Vzc2lvbk1hbmFnZXJ9Z2V0IGZ1bGxTbmFwc2hvdEludGVydmFsTWlsbGlzKCl7dmFyIGUsdDtyZXR1cm5cInRyaWdnZXJfcGVuZGluZ1wiPT09dGhpcy50cmlnZ2VyU3RhdHVzPzZlNDpudWxsIT09KGU9bnVsbD09PSh0PXRoaXMuaW5zdGFuY2UuY29uZmlnLnNlc3Npb25fcmVjb3JkaW5nKXx8dm9pZCAwPT09dD92b2lkIDA6dC5mdWxsX3NuYXBzaG90X2ludGVydmFsX21pbGxpcykmJnZvaWQgMCE9PWU/ZTpacn1nZXQgaXNTYW1wbGVkKCl7dmFyIGU9dGhpcy5pbnN0YW5jZS5nZXRfcHJvcGVydHkoU2UpO3JldHVybiBMKGUpP2U6bnVsbH1nZXQgc2Vzc2lvbkR1cmF0aW9uKCl7dmFyIGUsdCxpPW51bGw9PT0oZT10aGlzLmJ1ZmZlcil8fHZvaWQgMD09PWU/dm9pZCAwOmUuZGF0YVsobnVsbD09PSh0PXRoaXMuYnVmZmVyKXx8dm9pZCAwPT09dD92b2lkIDA6dC5kYXRhLmxlbmd0aCktMV0se3Nlc3Npb25TdGFydFRpbWVzdGFtcDpyfT10aGlzLnNlc3Npb25NYW5hZ2VyLmNoZWNrQW5kR2V0U2Vzc2lvbkFuZFdpbmRvd0lkKCEwKTtyZXR1cm4gaT9pLnRpbWVzdGFtcC1yOm51bGx9Z2V0IGlzUmVjb3JkaW5nRW5hYmxlZCgpe3ZhciBlPSEhdGhpcy5pbnN0YW5jZS5nZXRfcHJvcGVydHkocGUpLGk9IXRoaXMuaW5zdGFuY2UuY29uZmlnLmRpc2FibGVfc2Vzc2lvbl9yZWNvcmRpbmc7cmV0dXJuIHQmJmUmJml9Z2V0IGlzQ29uc29sZUxvZ0NhcHR1cmVFbmFibGVkKCl7dmFyIGU9ISF0aGlzLmluc3RhbmNlLmdldF9wcm9wZXJ0eSh2ZSksdD10aGlzLmluc3RhbmNlLmNvbmZpZy5lbmFibGVfcmVjb3JkaW5nX2NvbnNvbGVfbG9nO3JldHVybiBudWxsIT10P3Q6ZX1nZXQgY2FudmFzUmVjb3JkaW5nKCl7dmFyIGUsdCxpLHIsbixzLG89dGhpcy5pbnN0YW5jZS5jb25maWcuc2Vzc2lvbl9yZWNvcmRpbmcuY2FwdHVyZUNhbnZhcyxhPXRoaXMuaW5zdGFuY2UuZ2V0X3Byb3BlcnR5KGZlKSxsPW51bGwhPT0oZT1udWxsIT09KHQ9bnVsbD09bz92b2lkIDA6by5yZWNvcmRDYW52YXMpJiZ2b2lkIDAhPT10P3Q6bnVsbD09YT92b2lkIDA6YS5lbmFibGVkKSYmdm9pZCAwIT09ZSYmZSx1PW51bGwhPT0oaT1udWxsIT09KHI9bnVsbD09bz92b2lkIDA6by5jYW52YXNGcHMpJiZ2b2lkIDAhPT1yP3I6bnVsbD09YT92b2lkIDA6YS5mcHMpJiZ2b2lkIDAhPT1pP2k6MCxjPW51bGwhPT0obj1udWxsIT09KHM9bnVsbD09bz92b2lkIDA6by5jYW52YXNRdWFsaXR5KSYmdm9pZCAwIT09cz9zOm51bGw9PWE/dm9pZCAwOmEucXVhbGl0eSkmJnZvaWQgMCE9PW4/bjowO3JldHVybntlbmFibGVkOmwsZnBzOmRyKHUsMCwxMixcImNhbnZhcyByZWNvcmRpbmcgZnBzXCIpLHF1YWxpdHk6ZHIoYywwLDEsXCJjYW52YXMgcmVjb3JkaW5nIHF1YWxpdHlcIil9fWdldCBuZXR3b3JrUGF5bG9hZENhcHR1cmUoKXt2YXIgZSx0LGk9dGhpcy5pbnN0YW5jZS5nZXRfcHJvcGVydHkoZ2UpLHI9e3JlY29yZEhlYWRlcnM6bnVsbD09PShlPXRoaXMuaW5zdGFuY2UuY29uZmlnLnNlc3Npb25fcmVjb3JkaW5nKXx8dm9pZCAwPT09ZT92b2lkIDA6ZS5yZWNvcmRIZWFkZXJzLHJlY29yZEJvZHk6bnVsbD09PSh0PXRoaXMuaW5zdGFuY2UuY29uZmlnLnNlc3Npb25fcmVjb3JkaW5nKXx8dm9pZCAwPT09dD92b2lkIDA6dC5yZWNvcmRCb2R5fSxuPShudWxsPT1yP3ZvaWQgMDpyLnJlY29yZEhlYWRlcnMpfHwobnVsbD09aT92b2lkIDA6aS5yZWNvcmRIZWFkZXJzKSxzPShudWxsPT1yP3ZvaWQgMDpyLnJlY29yZEJvZHkpfHwobnVsbD09aT92b2lkIDA6aS5yZWNvcmRCb2R5KSxvPUModGhpcy5pbnN0YW5jZS5jb25maWcuY2FwdHVyZV9wZXJmb3JtYW5jZSk/dGhpcy5pbnN0YW5jZS5jb25maWcuY2FwdHVyZV9wZXJmb3JtYW5jZS5uZXR3b3JrX3RpbWluZzp0aGlzLmluc3RhbmNlLmNvbmZpZy5jYXB0dXJlX3BlcmZvcm1hbmNlLGE9ISEoTChvKT9vOm51bGw9PWk/dm9pZCAwOmkuY2FwdHVyZVBlcmZvcm1hbmNlKTtyZXR1cm4gbnx8c3x8YT97cmVjb3JkSGVhZGVyczpuLHJlY29yZEJvZHk6cyxyZWNvcmRQZXJmb3JtYW5jZTphfTp2b2lkIDB9Z2V0IHNhbXBsZVJhdGUoKXt2YXIgZT10aGlzLmluc3RhbmNlLmdldF9wcm9wZXJ0eShtZSk7cmV0dXJuIE0oZSk/ZTpudWxsfWdldCBtaW5pbXVtRHVyYXRpb24oKXt2YXIgZT10aGlzLmluc3RhbmNlLmdldF9wcm9wZXJ0eShiZSk7cmV0dXJuIE0oZSk/ZTpudWxsfWdldCBzdGF0dXMoKXtyZXR1cm4gdGhpcy5yZWNlaXZlZERlY2lkZT90aGlzLmlzUmVjb3JkaW5nRW5hYmxlZD90aGlzLl91cmxCbG9ja2VkP1wicGF1c2VkXCI6Tyh0aGlzLl9saW5rZWRGbGFnKXx8dGhpcy5fbGlua2VkRmxhZ1NlZW4/XCJ0cmlnZ2VyX3BlbmRpbmdcIj09PXRoaXMudHJpZ2dlclN0YXR1cz9cImJ1ZmZlcmluZ1wiOkwodGhpcy5pc1NhbXBsZWQpP3RoaXMuaXNTYW1wbGVkP1wic2FtcGxlZFwiOlwiZGlzYWJsZWRcIjpcImFjdGl2ZVwiOlwiYnVmZmVyaW5nXCI6XCJkaXNhYmxlZFwiOlwiYnVmZmVyaW5nXCJ9Z2V0IHVybFRyaWdnZXJTdGF0dXMoKXt2YXIgZTtyZXR1cm4gMD09PXRoaXMuX3VybFRyaWdnZXJzLmxlbmd0aD9cInRyaWdnZXJfZGlzYWJsZWRcIjoobnVsbD09PShlPXRoaXMuaW5zdGFuY2UpfHx2b2lkIDA9PT1lP3ZvaWQgMDplLmdldF9wcm9wZXJ0eShFZSkpPT09dGhpcy5zZXNzaW9uSWQ/XCJ0cmlnZ2VyX2FjdGl2YXRlZFwiOlwidHJpZ2dlcl9wZW5kaW5nXCJ9Z2V0IGV2ZW50VHJpZ2dlclN0YXR1cygpe3ZhciBlO3JldHVybiAwPT09dGhpcy5fZXZlbnRUcmlnZ2Vycy5sZW5ndGg/XCJ0cmlnZ2VyX2Rpc2FibGVkXCI6KG51bGw9PT0oZT10aGlzLmluc3RhbmNlKXx8dm9pZCAwPT09ZT92b2lkIDA6ZS5nZXRfcHJvcGVydHkoa2UpKT09PXRoaXMuc2Vzc2lvbklkP1widHJpZ2dlcl9hY3RpdmF0ZWRcIjpcInRyaWdnZXJfcGVuZGluZ1wifWdldCB0cmlnZ2VyU3RhdHVzKCl7dmFyIGU9XCJ0cmlnZ2VyX2FjdGl2YXRlZFwiPT09dGhpcy5ldmVudFRyaWdnZXJTdGF0dXN8fFwidHJpZ2dlcl9hY3RpdmF0ZWRcIj09PXRoaXMudXJsVHJpZ2dlclN0YXR1cyx0PVwidHJpZ2dlcl9wZW5kaW5nXCI9PT10aGlzLmV2ZW50VHJpZ2dlclN0YXR1c3x8XCJ0cmlnZ2VyX3BlbmRpbmdcIj09PXRoaXMudXJsVHJpZ2dlclN0YXR1cztyZXR1cm4gZT9cInRyaWdnZXJfYWN0aXZhdGVkXCI6dD9cInRyaWdnZXJfcGVuZGluZ1wiOlwidHJpZ2dlcl9kaXNhYmxlZFwifWNvbnN0cnVjdG9yKGUpe2lmKFcodGhpcyxcInF1ZXVlZFJSV2ViRXZlbnRzXCIsW10pLFcodGhpcyxcImlzSWRsZVwiLCExKSxXKHRoaXMsXCJfbGlua2VkRmxhZ1NlZW5cIiwhMSksVyh0aGlzLFwiX2xhc3RBY3Rpdml0eVRpbWVzdGFtcFwiLERhdGUubm93KCkpLFcodGhpcyxcIl9saW5rZWRGbGFnXCIsbnVsbCksVyh0aGlzLFwiX3JlbW92ZVBhZ2VWaWV3Q2FwdHVyZUhvb2tcIix2b2lkIDApLFcodGhpcyxcIl9vblNlc3Npb25JZExpc3RlbmVyXCIsdm9pZCAwKSxXKHRoaXMsXCJfcGVyc2lzdERlY2lkZU9uU2Vzc2lvbkxpc3RlbmVyXCIsdm9pZCAwKSxXKHRoaXMsXCJfc2FtcGxpbmdTZXNzaW9uTGlzdGVuZXJcIix2b2lkIDApLFcodGhpcyxcIl91cmxUcmlnZ2Vyc1wiLFtdKSxXKHRoaXMsXCJfdXJsQmxvY2tsaXN0XCIsW10pLFcodGhpcyxcIl91cmxCbG9ja2VkXCIsITEpLFcodGhpcyxcIl9ldmVudFRyaWdnZXJzXCIsW10pLFcodGhpcyxcIl9yZW1vdmVFdmVudFRyaWdnZXJDYXB0dXJlSG9va1wiLHZvaWQgMCksVyh0aGlzLFwiX2ZvcmNlQWxsb3dMb2NhbGhvc3ROZXR3b3JrQ2FwdHVyZVwiLCExKSxXKHRoaXMsXCJfb25CZWZvcmVVbmxvYWRcIiwoKCk9Pnt0aGlzLl9mbHVzaEJ1ZmZlcigpfSkpLFcodGhpcyxcIl9vbk9mZmxpbmVcIiwoKCk9Pnt0aGlzLl90cnlBZGRDdXN0b21FdmVudChcImJyb3dzZXIgb2ZmbGluZVwiLHt9KX0pKSxXKHRoaXMsXCJfb25PbmxpbmVcIiwoKCk9Pnt0aGlzLl90cnlBZGRDdXN0b21FdmVudChcImJyb3dzZXIgb25saW5lXCIse30pfSkpLFcodGhpcyxcIl9vblZpc2liaWxpdHlDaGFuZ2VcIiwoKCk9PntpZihudWxsIT1hJiZhLnZpc2liaWxpdHlTdGF0ZSl7dmFyIGU9XCJ3aW5kb3cgXCIrYS52aXNpYmlsaXR5U3RhdGU7dGhpcy5fdHJ5QWRkQ3VzdG9tRXZlbnQoZSx7fSl9fSkpLHRoaXMuaW5zdGFuY2U9ZSx0aGlzLl9jYXB0dXJlU3RhcnRlZD0hMSx0aGlzLl9lbmRwb2ludD1cIi9zL1wiLHRoaXMuc3RvcFJyd2ViPXZvaWQgMCx0aGlzLnJlY2VpdmVkRGVjaWRlPSExLCF0aGlzLmluc3RhbmNlLnNlc3Npb25NYW5hZ2VyKXRocm93IFFyLmVycm9yKFwic3RhcnRlZCB3aXRob3V0IHZhbGlkIHNlc3Npb25NYW5hZ2VyXCIpLG5ldyBFcnJvcihYcitcIiBzdGFydGVkIHdpdGhvdXQgdmFsaWQgc2Vzc2lvbk1hbmFnZXIuIFRoaXMgaXMgYSBidWcuXCIpO2lmKHRoaXMuaW5zdGFuY2UuY29uZmlnLl9fcHJldmlld19leHBlcmltZW50YWxfY29va2llbGVzc19tb2RlKXRocm93IG5ldyBFcnJvcihYcitcIiBjYW5ub3QgYmUgdXNlZCB3aXRoIF9fcHJldmlld19leHBlcmltZW50YWxfY29va2llbGVzc19tb2RlLlwiKTt2YXJ7c2Vzc2lvbklkOnQsd2luZG93SWQ6aX09dGhpcy5zZXNzaW9uTWFuYWdlci5jaGVja0FuZEdldFNlc3Npb25BbmRXaW5kb3dJZCgpO3RoaXMuc2Vzc2lvbklkPXQsdGhpcy53aW5kb3dJZD1pLHRoaXMuYnVmZmVyPXRoaXMuY2xlYXJCdWZmZXIoKSx0aGlzLnNlc3Npb25JZGxlVGhyZXNob2xkTWlsbGlzZWNvbmRzPj10aGlzLnNlc3Npb25NYW5hZ2VyLnNlc3Npb25UaW1lb3V0TXMmJlFyLndhcm4oXCJzZXNzaW9uX2lkbGVfdGhyZXNob2xkX21zIChcIi5jb25jYXQodGhpcy5zZXNzaW9uSWRsZVRocmVzaG9sZE1pbGxpc2Vjb25kcyxcIikgaXMgZ3JlYXRlciB0aGFuIHRoZSBzZXNzaW9uIHRpbWVvdXQgKFwiKS5jb25jYXQodGhpcy5zZXNzaW9uTWFuYWdlci5zZXNzaW9uVGltZW91dE1zLFwiKS4gU2Vzc2lvbiB3aWxsIG5ldmVyIGJlIGRldGVjdGVkIGFzIGlkbGVcIikpfXN0YXJ0SWZFbmFibGVkT3JTdG9wKGUpe3RoaXMuaXNSZWNvcmRpbmdFbmFibGVkPyh0aGlzLl9zdGFydENhcHR1cmUoZSksbnVsbD09dHx8dC5hZGRFdmVudExpc3RlbmVyKFwiYmVmb3JldW5sb2FkXCIsdGhpcy5fb25CZWZvcmVVbmxvYWQpLG51bGw9PXR8fHQuYWRkRXZlbnRMaXN0ZW5lcihcIm9mZmxpbmVcIix0aGlzLl9vbk9mZmxpbmUpLG51bGw9PXR8fHQuYWRkRXZlbnRMaXN0ZW5lcihcIm9ubGluZVwiLHRoaXMuX29uT25saW5lKSxudWxsPT10fHx0LmFkZEV2ZW50TGlzdGVuZXIoXCJ2aXNpYmlsaXR5Y2hhbmdlXCIsdGhpcy5fb25WaXNpYmlsaXR5Q2hhbmdlKSx0aGlzLl9zZXR1cFNhbXBsaW5nKCksdGhpcy5fYWRkRXZlbnRUcmlnZ2VyTGlzdGVuZXIoKSxPKHRoaXMuX3JlbW92ZVBhZ2VWaWV3Q2FwdHVyZUhvb2spJiYodGhpcy5fcmVtb3ZlUGFnZVZpZXdDYXB0dXJlSG9vaz10aGlzLmluc3RhbmNlLm9uKFwiZXZlbnRDYXB0dXJlZFwiLChlPT57dHJ5e2lmKFwiJHBhZ2V2aWV3XCI9PT1lLmV2ZW50KXt2YXIgdD1udWxsIT1lJiZlLnByb3BlcnRpZXMuJGN1cnJlbnRfdXJsP3RoaXMuX21hc2tVcmwobnVsbD09ZT92b2lkIDA6ZS5wcm9wZXJ0aWVzLiRjdXJyZW50X3VybCk6XCJcIjtpZighdClyZXR1cm47dGhpcy5fdHJ5QWRkQ3VzdG9tRXZlbnQoXCIkcGFnZXZpZXdcIix7aHJlZjp0fSl9fWNhdGNoKGUpe1FyLmVycm9yKFwiQ291bGQgbm90IGFkZCAkcGFnZXZpZXcgdG8gcnJ3ZWIgc2Vzc2lvblwiLGUpfX0pKSksdGhpcy5fb25TZXNzaW9uSWRMaXN0ZW5lcnx8KHRoaXMuX29uU2Vzc2lvbklkTGlzdGVuZXI9dGhpcy5zZXNzaW9uTWFuYWdlci5vblNlc3Npb25JZCgoKGUsdCxpKT0+e3ZhciByLG4scyxvO2kmJih0aGlzLl90cnlBZGRDdXN0b21FdmVudChcIiRzZXNzaW9uX2lkX2NoYW5nZVwiLHtzZXNzaW9uSWQ6ZSx3aW5kb3dJZDp0LGNoYW5nZVJlYXNvbjppfSksbnVsbD09PShyPXRoaXMuaW5zdGFuY2UpfHx2b2lkIDA9PT1yfHxudWxsPT09KG49ci5wZXJzaXN0ZW5jZSl8fHZvaWQgMD09PW58fG4udW5yZWdpc3RlcihrZSksbnVsbD09PShzPXRoaXMuaW5zdGFuY2UpfHx2b2lkIDA9PT1zfHxudWxsPT09KG89cy5wZXJzaXN0ZW5jZSl8fHZvaWQgMD09PW98fG8udW5yZWdpc3RlcihFZSkpfSkpKSk6dGhpcy5zdG9wUmVjb3JkaW5nKCl9c3RvcFJlY29yZGluZygpe3ZhciBlLGkscixuO3RoaXMuX2NhcHR1cmVTdGFydGVkJiZ0aGlzLnN0b3BScndlYiYmKHRoaXMuc3RvcFJyd2ViKCksdGhpcy5zdG9wUnJ3ZWI9dm9pZCAwLHRoaXMuX2NhcHR1cmVTdGFydGVkPSExLG51bGw9PXR8fHQucmVtb3ZlRXZlbnRMaXN0ZW5lcihcImJlZm9yZXVubG9hZFwiLHRoaXMuX29uQmVmb3JlVW5sb2FkKSxudWxsPT10fHx0LnJlbW92ZUV2ZW50TGlzdGVuZXIoXCJvZmZsaW5lXCIsdGhpcy5fb25PZmZsaW5lKSxudWxsPT10fHx0LnJlbW92ZUV2ZW50TGlzdGVuZXIoXCJvbmxpbmVcIix0aGlzLl9vbk9ubGluZSksbnVsbD09dHx8dC5yZW1vdmVFdmVudExpc3RlbmVyKFwidmlzaWJpbGl0eWNoYW5nZVwiLHRoaXMuX29uVmlzaWJpbGl0eUNoYW5nZSksdGhpcy5jbGVhckJ1ZmZlcigpLGNsZWFySW50ZXJ2YWwodGhpcy5fZnVsbFNuYXBzaG90VGltZXIpLG51bGw9PT0oZT10aGlzLl9yZW1vdmVQYWdlVmlld0NhcHR1cmVIb29rKXx8dm9pZCAwPT09ZXx8ZS5jYWxsKHRoaXMpLHRoaXMuX3JlbW92ZVBhZ2VWaWV3Q2FwdHVyZUhvb2s9dm9pZCAwLG51bGw9PT0oaT10aGlzLl9yZW1vdmVFdmVudFRyaWdnZXJDYXB0dXJlSG9vayl8fHZvaWQgMD09PWl8fGkuY2FsbCh0aGlzKSx0aGlzLl9yZW1vdmVFdmVudFRyaWdnZXJDYXB0dXJlSG9vaz12b2lkIDAsbnVsbD09PShyPXRoaXMuX29uU2Vzc2lvbklkTGlzdGVuZXIpfHx2b2lkIDA9PT1yfHxyLmNhbGwodGhpcyksdGhpcy5fb25TZXNzaW9uSWRMaXN0ZW5lcj12b2lkIDAsbnVsbD09PShuPXRoaXMuX3NhbXBsaW5nU2Vzc2lvbkxpc3RlbmVyKXx8dm9pZCAwPT09bnx8bi5jYWxsKHRoaXMpLHRoaXMuX3NhbXBsaW5nU2Vzc2lvbkxpc3RlbmVyPXZvaWQgMCxRci5pbmZvKFwic3RvcHBlZFwiKSl9bWFrZVNhbXBsaW5nRGVjaXNpb24oZSl7dmFyIHQsaT10aGlzLnNlc3Npb25JZCE9PWUscj10aGlzLnNhbXBsZVJhdGU7aWYoTShyKSl7dmFyIG4scz10aGlzLmlzU2FtcGxlZCxvPWl8fCFMKHMpO2lmKG8pbj1NYXRoLnJhbmRvbSgpPHI7ZWxzZSBuPXM7byYmKG4/dGhpcy5fcmVwb3J0U3RhcnRlZChcInNhbXBsZWRcIik6UXIud2FybihcIlNhbXBsZSByYXRlIChcIi5jb25jYXQocixcIikgaGFzIGRldGVybWluZWQgdGhhdCB0aGlzIHNlc3Npb25JZCAoXCIpLmNvbmNhdChlLFwiKSB3aWxsIG5vdCBiZSBzZW50IHRvIHRoZSBzZXJ2ZXIuXCIpKSx0aGlzLl90cnlBZGRDdXN0b21FdmVudChcInNhbXBsaW5nRGVjaXNpb25NYWRlXCIse3NhbXBsZVJhdGU6cixpc1NhbXBsZWQ6bn0pKSxudWxsPT09KHQ9dGhpcy5pbnN0YW5jZS5wZXJzaXN0ZW5jZSl8fHZvaWQgMD09PXR8fHQucmVnaXN0ZXIoe1tTZV06bn0pfWVsc2V7dmFyIGE7bnVsbD09PShhPXRoaXMuaW5zdGFuY2UucGVyc2lzdGVuY2UpfHx2b2lkIDA9PT1hfHxhLnJlZ2lzdGVyKHtbU2VdOm51bGx9KX19b25SZW1vdGVDb25maWcoZSl7dmFyIHQsaSxyLG4scyxvOyh0aGlzLl90cnlBZGRDdXN0b21FdmVudChcIiRyZW1vdGVfY29uZmlnX3JlY2VpdmVkXCIsZSksdGhpcy5fcGVyc2lzdFJlbW90ZUNvbmZpZyhlKSx0aGlzLl9saW5rZWRGbGFnPShudWxsPT09KHQ9ZS5zZXNzaW9uUmVjb3JkaW5nKXx8dm9pZCAwPT09dD92b2lkIDA6dC5saW5rZWRGbGFnKXx8bnVsbCxudWxsIT09KGk9ZS5zZXNzaW9uUmVjb3JkaW5nKSYmdm9pZCAwIT09aSYmaS5lbmRwb2ludCkmJih0aGlzLl9lbmRwb2ludD1udWxsPT09KG89ZS5zZXNzaW9uUmVjb3JkaW5nKXx8dm9pZCAwPT09bz92b2lkIDA6by5lbmRwb2ludCk7aWYodGhpcy5fc2V0dXBTYW1wbGluZygpLCFPKHRoaXMuX2xpbmtlZEZsYWcpJiYhdGhpcy5fbGlua2VkRmxhZ1NlZW4pe3ZhciBhPVIodGhpcy5fbGlua2VkRmxhZyk/dGhpcy5fbGlua2VkRmxhZzp0aGlzLl9saW5rZWRGbGFnLmZsYWcsbD1SKHRoaXMuX2xpbmtlZEZsYWcpP251bGw6dGhpcy5fbGlua2VkRmxhZy52YXJpYW50O3RoaXMuaW5zdGFuY2Uub25GZWF0dXJlRmxhZ3MoKChlLHQpPT57dmFyIGk9Qyh0KSYmYSBpbiB0LHI9bD90W2FdPT09bDppO3ImJnRoaXMuX3JlcG9ydFN0YXJ0ZWQoXCJsaW5rZWRfZmxhZ19tYXRjaGVkXCIse2xpbmtlZEZsYWc6YSxsaW5rZWRWYXJpYW50Omx9KSx0aGlzLl9saW5rZWRGbGFnU2Vlbj1yfSkpfW51bGwhPT0ocj1lLnNlc3Npb25SZWNvcmRpbmcpJiZ2b2lkIDAhPT1yJiZyLnVybFRyaWdnZXJzJiYodGhpcy5fdXJsVHJpZ2dlcnM9ZS5zZXNzaW9uUmVjb3JkaW5nLnVybFRyaWdnZXJzKSxudWxsIT09KG49ZS5zZXNzaW9uUmVjb3JkaW5nKSYmdm9pZCAwIT09biYmbi51cmxCbG9ja2xpc3QmJih0aGlzLl91cmxCbG9ja2xpc3Q9ZS5zZXNzaW9uUmVjb3JkaW5nLnVybEJsb2NrbGlzdCksbnVsbCE9PShzPWUuc2Vzc2lvblJlY29yZGluZykmJnZvaWQgMCE9PXMmJnMuZXZlbnRUcmlnZ2VycyYmKHRoaXMuX2V2ZW50VHJpZ2dlcnM9ZS5zZXNzaW9uUmVjb3JkaW5nLmV2ZW50VHJpZ2dlcnMpLHRoaXMucmVjZWl2ZWREZWNpZGU9ITAsdGhpcy5zdGFydElmRW5hYmxlZE9yU3RvcCgpfV9zZXR1cFNhbXBsaW5nKCl7TSh0aGlzLnNhbXBsZVJhdGUpJiZPKHRoaXMuX3NhbXBsaW5nU2Vzc2lvbkxpc3RlbmVyKSYmKHRoaXMuX3NhbXBsaW5nU2Vzc2lvbkxpc3RlbmVyPXRoaXMuc2Vzc2lvbk1hbmFnZXIub25TZXNzaW9uSWQoKGU9Pnt0aGlzLm1ha2VTYW1wbGluZ0RlY2lzaW9uKGUpfSkpKX1fcGVyc2lzdFJlbW90ZUNvbmZpZyhlKXtpZih0aGlzLmluc3RhbmNlLnBlcnNpc3RlbmNlKXt2YXIgdCxpPXRoaXMuaW5zdGFuY2UucGVyc2lzdGVuY2Uscj0oKT0+e3ZhciB0LHIsbixzLG8sYSxsLHUsYz1udWxsPT09KHQ9ZS5zZXNzaW9uUmVjb3JkaW5nKXx8dm9pZCAwPT09dD92b2lkIDA6dC5zYW1wbGVSYXRlLGQ9TyhjKT9udWxsOnBhcnNlRmxvYXQoYyksaD1udWxsPT09KHI9ZS5zZXNzaW9uUmVjb3JkaW5nKXx8dm9pZCAwPT09cj92b2lkIDA6ci5taW5pbXVtRHVyYXRpb25NaWxsaXNlY29uZHM7aS5yZWdpc3Rlcih7W3BlXTohIWUuc2Vzc2lvblJlY29yZGluZyxbdmVdOm51bGw9PT0obj1lLnNlc3Npb25SZWNvcmRpbmcpfHx2b2lkIDA9PT1uP3ZvaWQgMDpuLmNvbnNvbGVMb2dSZWNvcmRpbmdFbmFibGVkLFtnZV06aih7Y2FwdHVyZVBlcmZvcm1hbmNlOmUuY2FwdHVyZVBlcmZvcm1hbmNlfSxudWxsPT09KHM9ZS5zZXNzaW9uUmVjb3JkaW5nKXx8dm9pZCAwPT09cz92b2lkIDA6cy5uZXR3b3JrUGF5bG9hZENhcHR1cmUpLFtmZV06e2VuYWJsZWQ6bnVsbD09PShvPWUuc2Vzc2lvblJlY29yZGluZyl8fHZvaWQgMD09PW8/dm9pZCAwOm8ucmVjb3JkQ2FudmFzLGZwczpudWxsPT09KGE9ZS5zZXNzaW9uUmVjb3JkaW5nKXx8dm9pZCAwPT09YT92b2lkIDA6YS5jYW52YXNGcHMscXVhbGl0eTpudWxsPT09KGw9ZS5zZXNzaW9uUmVjb3JkaW5nKXx8dm9pZCAwPT09bD92b2lkIDA6bC5jYW52YXNRdWFsaXR5fSxbbWVdOmQsW2JlXTpGKGgpP251bGw6aCxbeWVdOm51bGw9PT0odT1lLnNlc3Npb25SZWNvcmRpbmcpfHx2b2lkIDA9PT11P3ZvaWQgMDp1LnNjcmlwdENvbmZpZ30pfTtyKCksbnVsbD09PSh0PXRoaXMuX3BlcnNpc3REZWNpZGVPblNlc3Npb25MaXN0ZW5lcil8fHZvaWQgMD09PXR8fHQuY2FsbCh0aGlzKSx0aGlzLl9wZXJzaXN0RGVjaWRlT25TZXNzaW9uTGlzdGVuZXI9dGhpcy5zZXNzaW9uTWFuYWdlci5vblNlc3Npb25JZChyKX19bG9nKGUpe3ZhciB0LGk9YXJndW1lbnRzLmxlbmd0aD4xJiZ2b2lkIDAhPT1hcmd1bWVudHNbMV0/YXJndW1lbnRzWzFdOlwibG9nXCI7bnVsbD09PSh0PXRoaXMuaW5zdGFuY2Uuc2Vzc2lvblJlY29yZGluZyl8fHZvaWQgMD09PXR8fHQub25SUndlYkVtaXQoe3R5cGU6NixkYXRhOntwbHVnaW46XCJycndlYi9jb25zb2xlQDFcIixwYXlsb2FkOntsZXZlbDppLHRyYWNlOltdLHBheWxvYWQ6W0pTT04uc3RyaW5naWZ5KGUpXX19LHRpbWVzdGFtcDpEYXRlLm5vdygpfSl9X3N0YXJ0Q2FwdHVyZShlKXtpZighRihPYmplY3QuYXNzaWduKSYmIUYoQXJyYXkuZnJvbSkmJiEodGhpcy5fY2FwdHVyZVN0YXJ0ZWR8fHRoaXMuaW5zdGFuY2UuY29uZmlnLmRpc2FibGVfc2Vzc2lvbl9yZWNvcmRpbmd8fHRoaXMuaW5zdGFuY2UuY29uc2VudC5pc09wdGVkT3V0KCkpKXt2YXIgdCxpO2lmKHRoaXMuX2NhcHR1cmVTdGFydGVkPSEwLHRoaXMuc2Vzc2lvbk1hbmFnZXIuY2hlY2tBbmRHZXRTZXNzaW9uQW5kV2luZG93SWQoKSx0aGlzLnJyd2ViUmVjb3JkKXRoaXMuX29uU2NyaXB0TG9hZGVkKCk7ZWxzZSBudWxsPT09KHQ9Xy5fX1Bvc3Rob2dFeHRlbnNpb25zX18pfHx2b2lkIDA9PT10fHxudWxsPT09KGk9dC5sb2FkRXh0ZXJuYWxEZXBlbmRlbmN5KXx8dm9pZCAwPT09aXx8aS5jYWxsKHQsdGhpcy5pbnN0YW5jZSx0aGlzLnNjcmlwdE5hbWUsKGU9PntpZihlKXJldHVybiBRci5lcnJvcihcImNvdWxkIG5vdCBsb2FkIHJlY29yZGVyXCIsZSk7dGhpcy5fb25TY3JpcHRMb2FkZWQoKX0pKTtRci5pbmZvKFwic3RhcnRpbmdcIiksXCJhY3RpdmVcIj09PXRoaXMuc3RhdHVzJiZ0aGlzLl9yZXBvcnRTdGFydGVkKGV8fFwicmVjb3JkaW5nX2luaXRpYWxpemVkXCIpfX1nZXQgc2NyaXB0TmFtZSgpe3ZhciBlLHQsaTtyZXR1cm4obnVsbD09PShlPXRoaXMuaW5zdGFuY2UpfHx2b2lkIDA9PT1lfHxudWxsPT09KHQ9ZS5wZXJzaXN0ZW5jZSl8fHZvaWQgMD09PXR8fG51bGw9PT0oaT10LmdldF9wcm9wZXJ0eSh5ZSkpfHx2b2lkIDA9PT1pP3ZvaWQgMDppLnNjcmlwdCl8fFwicmVjb3JkZXJcIn1pc0ludGVyYWN0aXZlRXZlbnQoZSl7dmFyIHQ7cmV0dXJuIDM9PT1lLnR5cGUmJi0xIT09ZW4uaW5kZXhPZihudWxsPT09KHQ9ZS5kYXRhKXx8dm9pZCAwPT09dD92b2lkIDA6dC5zb3VyY2UpfV91cGRhdGVXaW5kb3dBbmRTZXNzaW9uSWRzKGUpe3ZhciB0PXRoaXMuaXNJbnRlcmFjdGl2ZUV2ZW50KGUpO3R8fHRoaXMuaXNJZGxlfHxlLnRpbWVzdGFtcC10aGlzLl9sYXN0QWN0aXZpdHlUaW1lc3RhbXA+dGhpcy5zZXNzaW9uSWRsZVRocmVzaG9sZE1pbGxpc2Vjb25kcyYmKHRoaXMuaXNJZGxlPSEwLGNsZWFySW50ZXJ2YWwodGhpcy5fZnVsbFNuYXBzaG90VGltZXIpLHRoaXMuX3RyeUFkZEN1c3RvbUV2ZW50KFwic2Vzc2lvbklkbGVcIix7ZXZlbnRUaW1lc3RhbXA6ZS50aW1lc3RhbXAsbGFzdEFjdGl2aXR5VGltZXN0YW1wOnRoaXMuX2xhc3RBY3Rpdml0eVRpbWVzdGFtcCx0aHJlc2hvbGQ6dGhpcy5zZXNzaW9uSWRsZVRocmVzaG9sZE1pbGxpc2Vjb25kcyxidWZmZXJMZW5ndGg6dGhpcy5idWZmZXIuZGF0YS5sZW5ndGgsYnVmZmVyU2l6ZTp0aGlzLmJ1ZmZlci5zaXplfSksdGhpcy5fZmx1c2hCdWZmZXIoKSk7dmFyIGk9ITE7aWYodCYmKHRoaXMuX2xhc3RBY3Rpdml0eVRpbWVzdGFtcD1lLnRpbWVzdGFtcCx0aGlzLmlzSWRsZSYmKHRoaXMuaXNJZGxlPSExLHRoaXMuX3RyeUFkZEN1c3RvbUV2ZW50KFwic2Vzc2lvbk5vTG9uZ2VySWRsZVwiLHtyZWFzb246XCJ1c2VyIGFjdGl2aXR5XCIsdHlwZTplLnR5cGV9KSxpPSEwKSksIXRoaXMuaXNJZGxlKXt2YXJ7d2luZG93SWQ6cixzZXNzaW9uSWQ6bn09dGhpcy5zZXNzaW9uTWFuYWdlci5jaGVja0FuZEdldFNlc3Npb25BbmRXaW5kb3dJZCghdCxlLnRpbWVzdGFtcCkscz10aGlzLnNlc3Npb25JZCE9PW4sbz10aGlzLndpbmRvd0lkIT09cjt0aGlzLndpbmRvd0lkPXIsdGhpcy5zZXNzaW9uSWQ9bixzfHxvPyh0aGlzLnN0b3BSZWNvcmRpbmcoKSx0aGlzLnN0YXJ0SWZFbmFibGVkT3JTdG9wKFwic2Vzc2lvbl9pZF9jaGFuZ2VkXCIpKTppJiZ0aGlzLl9zY2hlZHVsZUZ1bGxTbmFwc2hvdCgpfX1fdHJ5UlJXZWJNZXRob2QoZSl7dHJ5e3JldHVybiBlLnJyd2ViTWV0aG9kKCksITB9Y2F0Y2godCl7cmV0dXJuIHRoaXMucXVldWVkUlJXZWJFdmVudHMubGVuZ3RoPDEwP3RoaXMucXVldWVkUlJXZWJFdmVudHMucHVzaCh7ZW5xdWV1ZWRBdDplLmVucXVldWVkQXR8fERhdGUubm93KCksYXR0ZW1wdDplLmF0dGVtcHQrKyxycndlYk1ldGhvZDplLnJyd2ViTWV0aG9kfSk6UXIud2FybihcImNvdWxkIG5vdCBlbWl0IHF1ZXVlZCBycndlYiBldmVudC5cIix0LGUpLCExfX1fdHJ5QWRkQ3VzdG9tRXZlbnQoZSx0KXtyZXR1cm4gdGhpcy5fdHJ5UlJXZWJNZXRob2QodG4oKCgpPT50aGlzLnJyd2ViUmVjb3JkLmFkZEN1c3RvbUV2ZW50KGUsdCkpKSl9X3RyeVRha2VGdWxsU25hcHNob3QoKXtyZXR1cm4gdGhpcy5fdHJ5UlJXZWJNZXRob2QodG4oKCgpPT50aGlzLnJyd2ViUmVjb3JkLnRha2VGdWxsU25hcHNob3QoKSkpKX1fb25TY3JpcHRMb2FkZWQoKXt2YXIgZSx0PXtibG9ja0NsYXNzOlwicGgtbm8tY2FwdHVyZVwiLGJsb2NrU2VsZWN0b3I6dm9pZCAwLGlnbm9yZUNsYXNzOlwicGgtaWdub3JlLWlucHV0XCIsbWFza1RleHRDbGFzczpcInBoLW1hc2tcIixtYXNrVGV4dFNlbGVjdG9yOnZvaWQgMCxtYXNrVGV4dEZuOnZvaWQgMCxtYXNrQWxsSW5wdXRzOiEwLG1hc2tJbnB1dE9wdGlvbnM6e3Bhc3N3b3JkOiEwfSxtYXNrSW5wdXRGbjp2b2lkIDAsc2xpbURPTU9wdGlvbnM6e30sY29sbGVjdEZvbnRzOiExLGlubGluZVN0eWxlc2hlZXQ6ITAscmVjb3JkQ3Jvc3NPcmlnaW5JZnJhbWVzOiExfSxpPXRoaXMuaW5zdGFuY2UuY29uZmlnLnNlc3Npb25fcmVjb3JkaW5nO2Zvcih2YXJbcixuXW9mIE9iamVjdC5lbnRyaWVzKGl8fHt9KSlyIGluIHQmJihcIm1hc2tJbnB1dE9wdGlvbnNcIj09PXI/dC5tYXNrSW5wdXRPcHRpb25zPWooe3Bhc3N3b3JkOiEwfSxuKTp0W3JdPW4pO2lmKHRoaXMuY2FudmFzUmVjb3JkaW5nJiZ0aGlzLmNhbnZhc1JlY29yZGluZy5lbmFibGVkJiYodC5yZWNvcmRDYW52YXM9ITAsdC5zYW1wbGluZz17Y2FudmFzOnRoaXMuY2FudmFzUmVjb3JkaW5nLmZwc30sdC5kYXRhVVJMT3B0aW9ucz17dHlwZTpcImltYWdlL3dlYnBcIixxdWFsaXR5OnRoaXMuY2FudmFzUmVjb3JkaW5nLnF1YWxpdHl9KSx0aGlzLnJyd2ViUmVjb3JkKXt0aGlzLm11dGF0aW9uUmF0ZUxpbWl0ZXI9bnVsbCE9PShlPXRoaXMubXV0YXRpb25SYXRlTGltaXRlcikmJnZvaWQgMCE9PWU/ZTpuZXcgaHIodGhpcy5ycndlYlJlY29yZCx7cmVmaWxsUmF0ZTp0aGlzLmluc3RhbmNlLmNvbmZpZy5zZXNzaW9uX3JlY29yZGluZy5fX211dGF0aW9uUmF0ZUxpbWl0ZXJSZWZpbGxSYXRlLGJ1Y2tldFNpemU6dGhpcy5pbnN0YW5jZS5jb25maWcuc2Vzc2lvbl9yZWNvcmRpbmcuX19tdXRhdGlvblJhdGVMaW1pdGVyQnVja2V0U2l6ZSxvbkJsb2NrZWROb2RlOihlLHQpPT57dmFyIGk9XCJUb28gbWFueSBtdXRhdGlvbnMgb24gbm9kZSAnXCIuY29uY2F0KGUsXCInLiBSYXRlIGxpbWl0aW5nLiBUaGlzIGNvdWxkIGJlIGR1ZSB0byBTVkcgYW5pbWF0aW9ucyBvciBzb21ldGhpbmcgc2ltaWxhclwiKTtRci5pbmZvKGkse25vZGU6dH0pLHRoaXMubG9nKFhyK1wiIFwiK2ksXCJ3YXJuXCIpfX0pO3ZhciBzPXRoaXMuX2dhdGhlclJSV2ViUGx1Z2lucygpO3RoaXMuc3RvcFJyd2ViPXRoaXMucnJ3ZWJSZWNvcmQoaih7ZW1pdDplPT57dGhpcy5vblJSd2ViRW1pdChlKX0scGx1Z2luczpzfSx0KSksdGhpcy5fbGFzdEFjdGl2aXR5VGltZXN0YW1wPURhdGUubm93KCksdGhpcy5pc0lkbGU9ITEsdGhpcy5fdHJ5QWRkQ3VzdG9tRXZlbnQoXCIkc2Vzc2lvbl9vcHRpb25zXCIse3Nlc3Npb25SZWNvcmRpbmdPcHRpb25zOnQsYWN0aXZlUGx1Z2luczpzLm1hcCgoZT0+bnVsbD09ZT92b2lkIDA6ZS5uYW1lKSl9KSx0aGlzLl90cnlBZGRDdXN0b21FdmVudChcIiRwb3N0aG9nX2NvbmZpZ1wiLHtjb25maWc6dGhpcy5pbnN0YW5jZS5jb25maWd9KX1lbHNlIFFyLmVycm9yKFwib25TY3JpcHRMb2FkZWQgd2FzIGNhbGxlZCBidXQgcnJ3ZWJSZWNvcmQgaXMgbm90IGF2YWlsYWJsZS4gVGhpcyBpbmRpY2F0ZXMgc29tZXRoaW5nIGhhcyBnb25lIHdyb25nLlwiKX1fc2NoZWR1bGVGdWxsU25hcHNob3QoKXtpZih0aGlzLl9mdWxsU25hcHNob3RUaW1lciYmY2xlYXJJbnRlcnZhbCh0aGlzLl9mdWxsU25hcHNob3RUaW1lciksIXRoaXMuaXNJZGxlKXt2YXIgZT10aGlzLmZ1bGxTbmFwc2hvdEludGVydmFsTWlsbGlzO2UmJih0aGlzLl9mdWxsU25hcHNob3RUaW1lcj1zZXRJbnRlcnZhbCgoKCk9Pnt0aGlzLl90cnlUYWtlRnVsbFNuYXBzaG90KCl9KSxlKSl9fV9nYXRoZXJSUldlYlBsdWdpbnMoKXt2YXIgZSx0LGkscixuPVtdLHM9bnVsbD09PShlPV8uX19Qb3N0aG9nRXh0ZW5zaW9uc19fKXx8dm9pZCAwPT09ZXx8bnVsbD09PSh0PWUucnJ3ZWJQbHVnaW5zKXx8dm9pZCAwPT09dD92b2lkIDA6dC5nZXRSZWNvcmRDb25zb2xlUGx1Z2luO3MmJnRoaXMuaXNDb25zb2xlTG9nQ2FwdHVyZUVuYWJsZWQmJm4ucHVzaChzKCkpO3ZhciBvPW51bGw9PT0oaT1fLl9fUG9zdGhvZ0V4dGVuc2lvbnNfXyl8fHZvaWQgMD09PWl8fG51bGw9PT0ocj1pLnJyd2ViUGx1Z2lucyl8fHZvaWQgMD09PXI/dm9pZCAwOnIuZ2V0UmVjb3JkTmV0d29ya1BsdWdpbjt0aGlzLm5ldHdvcmtQYXlsb2FkQ2FwdHVyZSYmSShvKSYmKCF2dC5pbmNsdWRlcyhsb2NhdGlvbi5ob3N0bmFtZSl8fHRoaXMuX2ZvcmNlQWxsb3dMb2NhbGhvc3ROZXR3b3JrQ2FwdHVyZT9uLnB1c2gobyhjcih0aGlzLmluc3RhbmNlLmNvbmZpZyx0aGlzLm5ldHdvcmtQYXlsb2FkQ2FwdHVyZSkpKTpRci5pbmZvKFwiTmV0d29ya0NhcHR1cmUgbm90IHN0YXJ0ZWQgYmVjYXVzZSB3ZSBhcmUgb24gbG9jYWxob3N0LlwiKSk7cmV0dXJuIG59b25SUndlYkVtaXQoZSl7dmFyIHQ7aWYodGhpcy5fcHJvY2Vzc1F1ZXVlZEV2ZW50cygpLGUmJkMoZSkpe2lmKGUudHlwZT09PUlpLk1ldGEpe3ZhciBpPXRoaXMuX21hc2tVcmwoZS5kYXRhLmhyZWYpO2lmKHRoaXMuX2xhc3RIcmVmPWksIWkpcmV0dXJuO2UuZGF0YS5ocmVmPWl9ZWxzZSB0aGlzLl9wYWdlVmlld0ZhbGxCYWNrKCk7aWYodGhpcy5fY2hlY2tVcmxUcmlnZ2VyQ29uZGl0aW9ucygpLFwicGF1c2VkXCIhPT10aGlzLnN0YXR1c3x8ZnVuY3Rpb24oZSl7cmV0dXJuIGUudHlwZT09PUlpLkN1c3RvbSYmXCJyZWNvcmRpbmcgcGF1c2VkXCI9PT1lLmRhdGEudGFnfShlKSl7ZS50eXBlPT09SWkuRnVsbFNuYXBzaG90JiZ0aGlzLl9zY2hlZHVsZUZ1bGxTbmFwc2hvdCgpLGUudHlwZT09PUlpLkZ1bGxTbmFwc2hvdCYmXCJ0cmlnZ2VyX3BlbmRpbmdcIj09PXRoaXMudHJpZ2dlclN0YXR1cyYmdGhpcy5jbGVhckJ1ZmZlcigpO3ZhciByPXRoaXMubXV0YXRpb25SYXRlTGltaXRlcj90aGlzLm11dGF0aW9uUmF0ZUxpbWl0ZXIudGhyb3R0bGVNdXRhdGlvbnMoZSk6ZTtpZihyKXt2YXIgbj1mdW5jdGlvbihlKXt2YXIgdD1lO2lmKHQmJkModCkmJjY9PT10LnR5cGUmJkModC5kYXRhKSYmXCJycndlYi9jb25zb2xlQDFcIj09PXQuZGF0YS5wbHVnaW4pe3QuZGF0YS5wYXlsb2FkLnBheWxvYWQubGVuZ3RoPjEwJiYodC5kYXRhLnBheWxvYWQucGF5bG9hZD10LmRhdGEucGF5bG9hZC5wYXlsb2FkLnNsaWNlKDAsMTApLHQuZGF0YS5wYXlsb2FkLnBheWxvYWQucHVzaChcIi4uLlt0cnVuY2F0ZWRdXCIpKTtmb3IodmFyIGk9W10scj0wO3I8dC5kYXRhLnBheWxvYWQucGF5bG9hZC5sZW5ndGg7cisrKXQuZGF0YS5wYXlsb2FkLnBheWxvYWRbcl0mJnQuZGF0YS5wYXlsb2FkLnBheWxvYWRbcl0ubGVuZ3RoPjJlMz9pLnB1c2godC5kYXRhLnBheWxvYWQucGF5bG9hZFtyXS5zbGljZSgwLDJlMykrXCIuLi5bdHJ1bmNhdGVkXVwiKTppLnB1c2godC5kYXRhLnBheWxvYWQucGF5bG9hZFtyXSk7cmV0dXJuIHQuZGF0YS5wYXlsb2FkLnBheWxvYWQ9aSxlfXJldHVybiBlfShyKTtpZih0aGlzLl91cGRhdGVXaW5kb3dBbmRTZXNzaW9uSWRzKG4pLCF0aGlzLmlzSWRsZXx8bm4obikpe2lmKG5uKG4pKXt2YXIgcz1uLmRhdGEucGF5bG9hZDtpZihzKXt2YXIgbz1zLmxhc3RBY3Rpdml0eVRpbWVzdGFtcCxhPXMudGhyZXNob2xkO24udGltZXN0YW1wPW8rYX19dmFyIGw9bnVsbD09PSh0PXRoaXMuaW5zdGFuY2UuY29uZmlnLnNlc3Npb25fcmVjb3JkaW5nLmNvbXByZXNzX2V2ZW50cyl8fHZvaWQgMD09PXR8fHQ/ZnVuY3Rpb24oZSl7aWYoa2koZSk8MTAyNClyZXR1cm4gZTt0cnl7aWYoZS50eXBlPT09SWkuRnVsbFNuYXBzaG90KXJldHVybiBqKGooe30sZSkse30se2RhdGE6cm4oZS5kYXRhKSxjdjpcIjIwMjQtMTBcIn0pO2lmKGUudHlwZT09PUlpLkluY3JlbWVudGFsU25hcHNob3QmJmUuZGF0YS5zb3VyY2U9PT1DaS5NdXRhdGlvbilyZXR1cm4gaihqKHt9LGUpLHt9LHtjdjpcIjIwMjQtMTBcIixkYXRhOmooaih7fSxlLmRhdGEpLHt9LHt0ZXh0czpybihlLmRhdGEudGV4dHMpLGF0dHJpYnV0ZXM6cm4oZS5kYXRhLmF0dHJpYnV0ZXMpLHJlbW92ZXM6cm4oZS5kYXRhLnJlbW92ZXMpLGFkZHM6cm4oZS5kYXRhLmFkZHMpfSl9KTtpZihlLnR5cGU9PT1JaS5JbmNyZW1lbnRhbFNuYXBzaG90JiZlLmRhdGEuc291cmNlPT09Q2kuU3R5bGVTaGVldFJ1bGUpcmV0dXJuIGooaih7fSxlKSx7fSx7Y3Y6XCIyMDI0LTEwXCIsZGF0YTpqKGooe30sZS5kYXRhKSx7fSx7YWRkczpybihlLmRhdGEuYWRkcykscmVtb3ZlczpybihlLmRhdGEucmVtb3Zlcyl9KX0pfWNhdGNoKGUpe1FyLmVycm9yKFwiY291bGQgbm90IGNvbXByZXNzIGV2ZW50IC0gd2lsbCB1c2UgdW5jb21wcmVzc2VkIGV2ZW50XCIsZSl9cmV0dXJuIGV9KG4pOm4sdT17JHNuYXBzaG90X2J5dGVzOmtpKGwpLCRzbmFwc2hvdF9kYXRhOmwsJHNlc3Npb25faWQ6dGhpcy5zZXNzaW9uSWQsJHdpbmRvd19pZDp0aGlzLndpbmRvd0lkfTtcImRpc2FibGVkXCIhPT10aGlzLnN0YXR1cz90aGlzLl9jYXB0dXJlU25hcHNob3RCdWZmZXJlZCh1KTp0aGlzLmNsZWFyQnVmZmVyKCl9fX19fV9wYWdlVmlld0ZhbGxCYWNrKCl7aWYoIXRoaXMuaW5zdGFuY2UuY29uZmlnLmNhcHR1cmVfcGFnZXZpZXcmJnQpe3ZhciBlPXRoaXMuX21hc2tVcmwodC5sb2NhdGlvbi5ocmVmKTt0aGlzLl9sYXN0SHJlZiE9PWUmJih0aGlzLl90cnlBZGRDdXN0b21FdmVudChcIiR1cmxfY2hhbmdlZFwiLHtocmVmOmV9KSx0aGlzLl9sYXN0SHJlZj1lKX19X3Byb2Nlc3NRdWV1ZWRFdmVudHMoKXtpZih0aGlzLnF1ZXVlZFJSV2ViRXZlbnRzLmxlbmd0aCl7dmFyIGU9Wy4uLnRoaXMucXVldWVkUlJXZWJFdmVudHNdO3RoaXMucXVldWVkUlJXZWJFdmVudHM9W10sZS5mb3JFYWNoKChlPT57RGF0ZS5ub3coKS1lLmVucXVldWVkQXQ8PTJlMyYmdGhpcy5fdHJ5UlJXZWJNZXRob2QoZSl9KSl9fV9tYXNrVXJsKGUpe3ZhciB0PXRoaXMuaW5zdGFuY2UuY29uZmlnLnNlc3Npb25fcmVjb3JkaW5nO2lmKHQubWFza05ldHdvcmtSZXF1ZXN0Rm4pe3ZhciBpLHI9e3VybDplfTtyZXR1cm4gbnVsbD09PShpPXI9dC5tYXNrTmV0d29ya1JlcXVlc3RGbihyKSl8fHZvaWQgMD09PWk/dm9pZCAwOmkudXJsfXJldHVybiBlfWNsZWFyQnVmZmVyKCl7cmV0dXJuIHRoaXMuYnVmZmVyPXtzaXplOjAsZGF0YTpbXSxzZXNzaW9uSWQ6dGhpcy5zZXNzaW9uSWQsd2luZG93SWQ6dGhpcy53aW5kb3dJZH0sdGhpcy5idWZmZXJ9X2ZsdXNoQnVmZmVyKCl7dGhpcy5mbHVzaEJ1ZmZlclRpbWVyJiYoY2xlYXJUaW1lb3V0KHRoaXMuZmx1c2hCdWZmZXJUaW1lciksdGhpcy5mbHVzaEJ1ZmZlclRpbWVyPXZvaWQgMCk7dmFyIGU9dGhpcy5taW5pbXVtRHVyYXRpb24sdD10aGlzLnNlc3Npb25EdXJhdGlvbixpPU0odCkmJnQ+PTAscj1NKGUpJiZpJiZ0PGU7aWYoXCJidWZmZXJpbmdcIj09PXRoaXMuc3RhdHVzfHxcInBhdXNlZFwiPT09dGhpcy5zdGF0dXN8fHIpcmV0dXJuIHRoaXMuZmx1c2hCdWZmZXJUaW1lcj1zZXRUaW1lb3V0KCgoKT0+e3RoaXMuX2ZsdXNoQnVmZmVyKCl9KSwyZTMpLHRoaXMuYnVmZmVyO3RoaXMuYnVmZmVyLmRhdGEubGVuZ3RoPjAmJnhpKHRoaXMuYnVmZmVyKS5mb3JFYWNoKChlPT57dGhpcy5fY2FwdHVyZVNuYXBzaG90KHskc25hcHNob3RfYnl0ZXM6ZS5zaXplLCRzbmFwc2hvdF9kYXRhOmUuZGF0YSwkc2Vzc2lvbl9pZDplLnNlc3Npb25JZCwkd2luZG93X2lkOmUud2luZG93SWQsJGxpYjpcIndlYlwiLCRsaWJfdmVyc2lvbjpwLkxJQl9WRVJTSU9OfSl9KSk7cmV0dXJuIHRoaXMuY2xlYXJCdWZmZXIoKX1fY2FwdHVyZVNuYXBzaG90QnVmZmVyZWQoZSl7dmFyIHQsaT0yKygobnVsbD09PSh0PXRoaXMuYnVmZmVyKXx8dm9pZCAwPT09dD92b2lkIDA6dC5kYXRhLmxlbmd0aCl8fDApOyF0aGlzLmlzSWRsZSYmKHRoaXMuYnVmZmVyLnNpemUrZS4kc25hcHNob3RfYnl0ZXMraT45NDM3MTguNHx8dGhpcy5idWZmZXIuc2Vzc2lvbklkIT09dGhpcy5zZXNzaW9uSWQpJiYodGhpcy5idWZmZXI9dGhpcy5fZmx1c2hCdWZmZXIoKSksdGhpcy5idWZmZXIuc2l6ZSs9ZS4kc25hcHNob3RfYnl0ZXMsdGhpcy5idWZmZXIuZGF0YS5wdXNoKGUuJHNuYXBzaG90X2RhdGEpLHRoaXMuZmx1c2hCdWZmZXJUaW1lcnx8dGhpcy5pc0lkbGV8fCh0aGlzLmZsdXNoQnVmZmVyVGltZXI9c2V0VGltZW91dCgoKCk9Pnt0aGlzLl9mbHVzaEJ1ZmZlcigpfSksMmUzKSl9X2NhcHR1cmVTbmFwc2hvdChlKXt0aGlzLmluc3RhbmNlLmNhcHR1cmUoXCIkc25hcHNob3RcIixlLHtfdXJsOnRoaXMuaW5zdGFuY2UucmVxdWVzdFJvdXRlci5lbmRwb2ludEZvcihcImFwaVwiLHRoaXMuX2VuZHBvaW50KSxfbm9UcnVuY2F0ZTohMCxfYmF0Y2hLZXk6XCJyZWNvcmRpbmdzXCIsc2tpcF9jbGllbnRfcmF0ZV9saW1pdGluZzohMH0pfV9jaGVja1VybFRyaWdnZXJDb25kaXRpb25zKCl7aWYodm9pZCAwIT09dCYmdC5sb2NhdGlvbi5ocmVmKXt2YXIgZT10LmxvY2F0aW9uLmhyZWYsaT1cInBhdXNlZFwiPT09dGhpcy5zdGF0dXMscj1zbihlLHRoaXMuX3VybEJsb2NrbGlzdCk7ciYmIWk/dGhpcy5fcGF1c2VSZWNvcmRpbmcoKTohciYmaSYmdGhpcy5fcmVzdW1lUmVjb3JkaW5nKCksc24oZSx0aGlzLl91cmxUcmlnZ2VycykmJnRoaXMuX2FjdGl2YXRlVHJpZ2dlcihcInVybFwiKX19X2FjdGl2YXRlVHJpZ2dlcihlKXt2YXIgdCxpO1widHJpZ2dlcl9wZW5kaW5nXCI9PT10aGlzLnRyaWdnZXJTdGF0dXMmJihudWxsPT09KHQ9dGhpcy5pbnN0YW5jZSl8fHZvaWQgMD09PXR8fG51bGw9PT0oaT10LnBlcnNpc3RlbmNlKXx8dm9pZCAwPT09aXx8aS5yZWdpc3Rlcih7W1widXJsXCI9PT1lP0VlOmtlXTp0aGlzLnNlc3Npb25JZH0pLHRoaXMuX2ZsdXNoQnVmZmVyKCksdGhpcy5fcmVwb3J0U3RhcnRlZChlK1wiX3RyaWdnZXJfbWF0Y2hlZFwiKSl9X3BhdXNlUmVjb3JkaW5nKCl7XCJwYXVzZWRcIiE9PXRoaXMuc3RhdHVzJiYodGhpcy5fdXJsQmxvY2tlZD0hMCxjbGVhckludGVydmFsKHRoaXMuX2Z1bGxTbmFwc2hvdFRpbWVyKSxRci5pbmZvKFwicmVjb3JkaW5nIHBhdXNlZCBkdWUgdG8gVVJMIGJsb2NrZXJcIiksdGhpcy5fdHJ5QWRkQ3VzdG9tRXZlbnQoXCJyZWNvcmRpbmcgcGF1c2VkXCIse3JlYXNvbjpcInVybCBibG9ja2VyXCJ9KSl9X3Jlc3VtZVJlY29yZGluZygpe1wicGF1c2VkXCI9PT10aGlzLnN0YXR1cyYmKHRoaXMuX3VybEJsb2NrZWQ9ITEsdGhpcy5fdHJ5VGFrZUZ1bGxTbmFwc2hvdCgpLHRoaXMuX3NjaGVkdWxlRnVsbFNuYXBzaG90KCksdGhpcy5fdHJ5QWRkQ3VzdG9tRXZlbnQoXCJyZWNvcmRpbmcgcmVzdW1lZFwiLHtyZWFzb246XCJsZWZ0IGJsb2NrZWQgdXJsXCJ9KSxRci5pbmZvKFwicmVjb3JkaW5nIHJlc3VtZWRcIikpfV9hZGRFdmVudFRyaWdnZXJMaXN0ZW5lcigpezAhPT10aGlzLl9ldmVudFRyaWdnZXJzLmxlbmd0aCYmTyh0aGlzLl9yZW1vdmVFdmVudFRyaWdnZXJDYXB0dXJlSG9vaykmJih0aGlzLl9yZW1vdmVFdmVudFRyaWdnZXJDYXB0dXJlSG9vaz10aGlzLmluc3RhbmNlLm9uKFwiZXZlbnRDYXB0dXJlZFwiLChlPT57dHJ5e3RoaXMuX2V2ZW50VHJpZ2dlcnMuaW5jbHVkZXMoZS5ldmVudCkmJnRoaXMuX2FjdGl2YXRlVHJpZ2dlcihcImV2ZW50XCIpfWNhdGNoKGUpe1FyLmVycm9yKFwiQ291bGQgbm90IGFjdGl2YXRlIGV2ZW50IHRyaWdnZXJcIixlKX19KSkpfW92ZXJyaWRlTGlua2VkRmxhZygpe3RoaXMuX2xpbmtlZEZsYWdTZWVuPSEwLHRoaXMuX3RyeVRha2VGdWxsU25hcHNob3QoKSx0aGlzLl9yZXBvcnRTdGFydGVkKFwibGlua2VkX2ZsYWdfb3ZlcnJpZGRlblwiKX1vdmVycmlkZVNhbXBsaW5nKCl7dmFyIGU7bnVsbD09PShlPXRoaXMuaW5zdGFuY2UucGVyc2lzdGVuY2UpfHx2b2lkIDA9PT1lfHxlLnJlZ2lzdGVyKHtbU2VdOiEwfSksdGhpcy5fdHJ5VGFrZUZ1bGxTbmFwc2hvdCgpLHRoaXMuX3JlcG9ydFN0YXJ0ZWQoXCJzYW1wbGluZ19vdmVycmlkZGVuXCIpfW92ZXJyaWRlVHJpZ2dlcihlKXt0aGlzLl9hY3RpdmF0ZVRyaWdnZXIoZSl9X3JlcG9ydFN0YXJ0ZWQoZSx0KXt0aGlzLmluc3RhbmNlLnJlZ2lzdGVyX2Zvcl9zZXNzaW9uKHskc2Vzc2lvbl9yZWNvcmRpbmdfc3RhcnRfcmVhc29uOmV9KSxRci5pbmZvKGUucmVwbGFjZShcIl9cIixcIiBcIiksdCksbShbXCJyZWNvcmRpbmdfaW5pdGlhbGl6ZWRcIixcInNlc3Npb25faWRfY2hhbmdlZFwiXSxlKXx8dGhpcy5fdHJ5QWRkQ3VzdG9tRXZlbnQoZSx0KX19dmFyIGFuPUIoXCJbUmVtb3RlQ29uZmlnXVwiKTtjbGFzcyBsbntjb25zdHJ1Y3RvcihlKXt0aGlzLmluc3RhbmNlPWV9Z2V0IHJlbW90ZUNvbmZpZygpe3ZhciBlLHQ7cmV0dXJuIG51bGw9PT0oZT1fLl9QT1NUSE9HX1JFTU9URV9DT05GSUcpfHx2b2lkIDA9PT1lfHxudWxsPT09KHQ9ZVt0aGlzLmluc3RhbmNlLmNvbmZpZy50b2tlbl0pfHx2b2lkIDA9PT10P3ZvaWQgMDp0LmNvbmZpZ31fbG9hZFJlbW90ZUNvbmZpZ0pzKGUpe3ZhciB0LGkscjtudWxsIT09KHQ9Xy5fX1Bvc3Rob2dFeHRlbnNpb25zX18pJiZ2b2lkIDAhPT10JiZ0LmxvYWRFeHRlcm5hbERlcGVuZGVuY3k/bnVsbD09PShpPV8uX19Qb3N0aG9nRXh0ZW5zaW9uc19fKXx8dm9pZCAwPT09aXx8bnVsbD09PShyPWkubG9hZEV4dGVybmFsRGVwZW5kZW5jeSl8fHZvaWQgMD09PXJ8fHIuY2FsbChpLHRoaXMuaW5zdGFuY2UsXCJyZW1vdGUtY29uZmlnXCIsKCgpPT5lKHRoaXMucmVtb3RlQ29uZmlnKSkpOihhbi5lcnJvcihcIlBvc3RIb2cgRXh0ZW5zaW9ucyBub3QgZm91bmQuIENhbm5vdCBsb2FkIHJlbW90ZSBjb25maWcuXCIpLGUoKSl9X2xvYWRSZW1vdGVDb25maWdKU09OKGUpe3RoaXMuaW5zdGFuY2UuX3NlbmRfcmVxdWVzdCh7bWV0aG9kOlwiR0VUXCIsdXJsOnRoaXMuaW5zdGFuY2UucmVxdWVzdFJvdXRlci5lbmRwb2ludEZvcihcImFzc2V0c1wiLFwiL2FycmF5L1wiLmNvbmNhdCh0aGlzLmluc3RhbmNlLmNvbmZpZy50b2tlbixcIi9jb25maWdcIikpLGNhbGxiYWNrOnQ9PntlKHQuanNvbil9fSl9bG9hZCgpe3RyeXtpZih0aGlzLnJlbW90ZUNvbmZpZylyZXR1cm4gYW4uaW5mbyhcIlVzaW5nIHByZWxvYWRlZCByZW1vdGUgY29uZmlnXCIsdGhpcy5yZW1vdGVDb25maWcpLHZvaWQgdGhpcy5vblJlbW90ZUNvbmZpZyh0aGlzLnJlbW90ZUNvbmZpZyk7aWYodGhpcy5pbnN0YW5jZS5jb25maWcuYWR2YW5jZWRfZGlzYWJsZV9kZWNpZGUpcmV0dXJuIHZvaWQgYW4ud2FybihcIlJlbW90ZSBjb25maWcgaXMgZGlzYWJsZWQuIEZhbGxpbmcgYmFjayB0byBsb2NhbCBjb25maWcuXCIpO3RoaXMuX2xvYWRSZW1vdGVDb25maWdKcygoZT0+e2lmKCFlKXJldHVybiBhbi5pbmZvKFwiTm8gY29uZmlnIGZvdW5kIGFmdGVyIGxvYWRpbmcgcmVtb3RlIEpTIGNvbmZpZy4gRmFsbGluZyBiYWNrIHRvIEpTT04uXCIpLHZvaWQgdGhpcy5fbG9hZFJlbW90ZUNvbmZpZ0pTT04oKGU9Pnt0aGlzLm9uUmVtb3RlQ29uZmlnKGUpfSkpO3RoaXMub25SZW1vdGVDb25maWcoZSl9KSl9Y2F0Y2goZSl7YW4uZXJyb3IoXCJFcnJvciBsb2FkaW5nIHJlbW90ZSBjb25maWdcIixlKX19b25SZW1vdGVDb25maWcoZSl7ZT90aGlzLmluc3RhbmNlLmNvbmZpZy5fX3ByZXZpZXdfcmVtb3RlX2NvbmZpZz8odGhpcy5pbnN0YW5jZS5fb25SZW1vdGVDb25maWcoZSksITEhPT1lLmhhc0ZlYXR1cmVGbGFncyYmdGhpcy5pbnN0YW5jZS5mZWF0dXJlRmxhZ3MuZW5zdXJlRmxhZ3NMb2FkZWQoKSk6YW4uaW5mbyhcIl9fcHJldmlld19yZW1vdGVfY29uZmlnIGlzIGRpc2FibGVkLiBMb2dnaW5nIGNvbmZpZyBpbnN0ZWFkXCIsZSk6YW4uZXJyb3IoXCJGYWlsZWQgdG8gZmV0Y2ggcmVtb3RlIGNvbmZpZyBmcm9tIFBvc3RIb2cuXCIpfX12YXIgdW4sY249bnVsbCE9dCYmdC5sb2NhdGlvbj93dCh0LmxvY2F0aW9uLmhhc2gsXCJfX3Bvc3Rob2dcIil8fHd0KGxvY2F0aW9uLmhhc2gsXCJzdGF0ZVwiKTpudWxsLGRuPVwiX3Bvc3RIb2dUb29sYmFyUGFyYW1zXCIsaG49QihcIltUb29sYmFyXVwiKTshZnVuY3Rpb24oZSl7ZVtlLlVOSU5JVElBTElaRUQ9MF09XCJVTklOSVRJQUxJWkVEXCIsZVtlLkxPQURJTkc9MV09XCJMT0FESU5HXCIsZVtlLkxPQURFRD0yXT1cIkxPQURFRFwifSh1bnx8KHVuPXt9KSk7Y2xhc3MgX257Y29uc3RydWN0b3IoZSl7dGhpcy5pbnN0YW5jZT1lfXNldFRvb2xiYXJTdGF0ZShlKXtfLnBoX3Rvb2xiYXJfc3RhdGU9ZX1nZXRUb29sYmFyU3RhdGUoKXt2YXIgZTtyZXR1cm4gbnVsbCE9PShlPV8ucGhfdG9vbGJhcl9zdGF0ZSkmJnZvaWQgMCE9PWU/ZTp1bi5VTklOSVRJQUxJWkVEfW1heWJlTG9hZFRvb2xiYXIoKXt2YXIgZSxpLHI9YXJndW1lbnRzLmxlbmd0aD4wJiZ2b2lkIDAhPT1hcmd1bWVudHNbMF0/YXJndW1lbnRzWzBdOnZvaWQgMCxuPWFyZ3VtZW50cy5sZW5ndGg+MSYmdm9pZCAwIT09YXJndW1lbnRzWzFdP2FyZ3VtZW50c1sxXTp2b2lkIDAscz1hcmd1bWVudHMubGVuZ3RoPjImJnZvaWQgMCE9PWFyZ3VtZW50c1syXT9hcmd1bWVudHNbMl06dm9pZCAwO2lmKCF0fHwhYSlyZXR1cm4hMTtyPW51bGwhPT0oZT1yKSYmdm9pZCAwIT09ZT9lOnQubG9jYXRpb24scz1udWxsIT09KGk9cykmJnZvaWQgMCE9PWk/aTp0Lmhpc3Rvcnk7dHJ5e2lmKCFuKXt0cnl7dC5sb2NhbFN0b3JhZ2Uuc2V0SXRlbShcInRlc3RcIixcInRlc3RcIiksdC5sb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbShcInRlc3RcIil9Y2F0Y2goZSl7cmV0dXJuITF9bj1udWxsPT10P3ZvaWQgMDp0LmxvY2FsU3RvcmFnZX12YXIgbyxsPWNufHx3dChyLmhhc2gsXCJfX3Bvc3Rob2dcIil8fHd0KHIuaGFzaCxcInN0YXRlXCIpLHU9bD9aKCgoKT0+SlNPTi5wYXJzZShhdG9iKGRlY29kZVVSSUNvbXBvbmVudChsKSkpKSl8fFooKCgpPT5KU09OLnBhcnNlKGRlY29kZVVSSUNvbXBvbmVudChsKSkpKTpudWxsO3JldHVybiB1JiZcInBoX2F1dGhvcml6ZVwiPT09dS5hY3Rpb24/KChvPXUpLnNvdXJjZT1cInVybFwiLG8mJk9iamVjdC5rZXlzKG8pLmxlbmd0aD4wJiYodS5kZXNpcmVkSGFzaD9yLmhhc2g9dS5kZXNpcmVkSGFzaDpzP3MucmVwbGFjZVN0YXRlKHMuc3RhdGUsXCJcIixyLnBhdGhuYW1lK3Iuc2VhcmNoKTpyLmhhc2g9XCJcIikpOigobz1KU09OLnBhcnNlKG4uZ2V0SXRlbShkbil8fFwie31cIikpLnNvdXJjZT1cImxvY2Fsc3RvcmFnZVwiLGRlbGV0ZSBvLnVzZXJJbnRlbnQpLCEoIW8udG9rZW58fHRoaXMuaW5zdGFuY2UuY29uZmlnLnRva2VuIT09by50b2tlbikmJih0aGlzLmxvYWRUb29sYmFyKG8pLCEwKX1jYXRjaChlKXtyZXR1cm4hMX19X2NhbGxMb2FkVG9vbGJhcihlKXt2YXIgdD1fLnBoX2xvYWRfdG9vbGJhcnx8Xy5waF9sb2FkX2VkaXRvcjshTyh0KSYmSSh0KT90KGUsdGhpcy5pbnN0YW5jZSk6aG4ud2FybihcIk5vIHRvb2xiYXIgbG9hZCBmdW5jdGlvbiBmb3VuZFwiKX1sb2FkVG9vbGJhcihlKXt2YXIgaT0hKG51bGw9PWF8fCFhLmdldEVsZW1lbnRCeUlkKHFlKSk7aWYoIXR8fGkpcmV0dXJuITE7dmFyIHI9XCJjdXN0b21cIj09PXRoaXMuaW5zdGFuY2UucmVxdWVzdFJvdXRlci5yZWdpb24mJnRoaXMuaW5zdGFuY2UuY29uZmlnLmFkdmFuY2VkX2Rpc2FibGVfdG9vbGJhcl9tZXRyaWNzLG49aihqKHt0b2tlbjp0aGlzLmluc3RhbmNlLmNvbmZpZy50b2tlbn0sZSkse30se2FwaVVSTDp0aGlzLmluc3RhbmNlLnJlcXVlc3RSb3V0ZXIuZW5kcG9pbnRGb3IoXCJ1aVwiKX0scj97aW5zdHJ1bWVudDohMX06e30pO2lmKHQubG9jYWxTdG9yYWdlLnNldEl0ZW0oZG4sSlNPTi5zdHJpbmdpZnkoaihqKHt9LG4pLHt9LHtzb3VyY2U6dm9pZCAwfSkpKSx0aGlzLmdldFRvb2xiYXJTdGF0ZSgpPT09dW4uTE9BREVEKXRoaXMuX2NhbGxMb2FkVG9vbGJhcihuKTtlbHNlIGlmKHRoaXMuZ2V0VG9vbGJhclN0YXRlKCk9PT11bi5VTklOSVRJQUxJWkVEKXt2YXIgcyxvO3RoaXMuc2V0VG9vbGJhclN0YXRlKHVuLkxPQURJTkcpLG51bGw9PT0ocz1fLl9fUG9zdGhvZ0V4dGVuc2lvbnNfXyl8fHZvaWQgMD09PXN8fG51bGw9PT0obz1zLmxvYWRFeHRlcm5hbERlcGVuZGVuY3kpfHx2b2lkIDA9PT1vfHxvLmNhbGwocyx0aGlzLmluc3RhbmNlLFwidG9vbGJhclwiLChlPT57aWYoZSlyZXR1cm4gaG4uZXJyb3IoXCJbVG9vbGJhcl0gRmFpbGVkIHRvIGxvYWRcIixlKSx2b2lkIHRoaXMuc2V0VG9vbGJhclN0YXRlKHVuLlVOSU5JVElBTElaRUQpO3RoaXMuc2V0VG9vbGJhclN0YXRlKHVuLkxPQURFRCksdGhpcy5fY2FsbExvYWRUb29sYmFyKG4pfSkpLHJlKHQsXCJ0dXJib2xpbmtzOmxvYWRcIiwoKCk9Pnt0aGlzLnNldFRvb2xiYXJTdGF0ZSh1bi5VTklOSVRJQUxJWkVEKSx0aGlzLmxvYWRUb29sYmFyKG4pfSkpfXJldHVybiEwfV9sb2FkRWRpdG9yKGUpe3JldHVybiB0aGlzLmxvYWRUb29sYmFyKGUpfW1heWJlTG9hZEVkaXRvcigpe3ZhciBlPWFyZ3VtZW50cy5sZW5ndGg+MCYmdm9pZCAwIT09YXJndW1lbnRzWzBdP2FyZ3VtZW50c1swXTp2b2lkIDAsdD1hcmd1bWVudHMubGVuZ3RoPjEmJnZvaWQgMCE9PWFyZ3VtZW50c1sxXT9hcmd1bWVudHNbMV06dm9pZCAwLGk9YXJndW1lbnRzLmxlbmd0aD4yJiZ2b2lkIDAhPT1hcmd1bWVudHNbMl0/YXJndW1lbnRzWzJdOnZvaWQgMDtyZXR1cm4gdGhpcy5tYXliZUxvYWRUb29sYmFyKGUsdCxpKX19Y2xhc3MgcG57Y29uc3RydWN0b3IoZSl7Vyh0aGlzLFwiaXNQYXVzZWRcIiwhMCksVyh0aGlzLFwicXVldWVcIixbXSksVyh0aGlzLFwiZmx1c2hUaW1lb3V0TXNcIiwzZTMpLHRoaXMuc2VuZFJlcXVlc3Q9ZX1lbnF1ZXVlKGUpe3RoaXMucXVldWUucHVzaChlKSx0aGlzLmZsdXNoVGltZW91dHx8dGhpcy5zZXRGbHVzaFRpbWVvdXQoKX11bmxvYWQoKXt0aGlzLmNsZWFyRmx1c2hUaW1lb3V0KCk7dmFyIGU9dGhpcy5xdWV1ZS5sZW5ndGg+MD90aGlzLmZvcm1hdFF1ZXVlKCk6e30sdD1PYmplY3QudmFsdWVzKGUpLGk9Wy4uLnQuZmlsdGVyKChlPT4wPT09ZS51cmwuaW5kZXhPZihcIi9lXCIpKSksLi4udC5maWx0ZXIoKGU9PjAhPT1lLnVybC5pbmRleE9mKFwiL2VcIikpKV07aS5tYXAoKGU9Pnt0aGlzLnNlbmRSZXF1ZXN0KGooaih7fSxlKSx7fSx7dHJhbnNwb3J0Olwic2VuZEJlYWNvblwifSkpfSkpfWVuYWJsZSgpe3RoaXMuaXNQYXVzZWQ9ITEsdGhpcy5zZXRGbHVzaFRpbWVvdXQoKX1zZXRGbHVzaFRpbWVvdXQoKXt2YXIgZT10aGlzO3RoaXMuaXNQYXVzZWR8fCh0aGlzLmZsdXNoVGltZW91dD1zZXRUaW1lb3V0KCgoKT0+e2lmKHRoaXMuY2xlYXJGbHVzaFRpbWVvdXQoKSx0aGlzLnF1ZXVlLmxlbmd0aD4wKXt2YXIgdD10aGlzLmZvcm1hdFF1ZXVlKCksaT1mdW5jdGlvbihpKXt2YXIgcj10W2ldLG49KG5ldyBEYXRlKS5nZXRUaW1lKCk7ci5kYXRhJiZ4KHIuZGF0YSkmJlkoci5kYXRhLChlPT57ZS5vZmZzZXQ9TWF0aC5hYnMoZS50aW1lc3RhbXAtbiksZGVsZXRlIGUudGltZXN0YW1wfSkpLGUuc2VuZFJlcXVlc3Qocil9O2Zvcih2YXIgciBpbiB0KWkocil9fSksdGhpcy5mbHVzaFRpbWVvdXRNcykpfWNsZWFyRmx1c2hUaW1lb3V0KCl7Y2xlYXJUaW1lb3V0KHRoaXMuZmx1c2hUaW1lb3V0KSx0aGlzLmZsdXNoVGltZW91dD12b2lkIDB9Zm9ybWF0UXVldWUoKXt2YXIgZT17fTtyZXR1cm4gWSh0aGlzLnF1ZXVlLCh0PT57dmFyIGkscj10LG49KHI/ci5iYXRjaEtleTpudWxsKXx8ci51cmw7RihlW25dKSYmKGVbbl09aihqKHt9LHIpLHt9LHtkYXRhOltdfSkpLG51bGw9PT0oaT1lW25dLmRhdGEpfHx2b2lkIDA9PT1pfHxpLnB1c2goci5kYXRhKX0pKSx0aGlzLnF1ZXVlPVtdLGV9fXZhciB2bj1mdW5jdGlvbihlKXt2YXIgdCxpLHIsbixzPVwiXCI7Zm9yKHQ9aT0wLHI9KGU9KGUrXCJcIikucmVwbGFjZSgvXFxyXFxuL2csXCJcXG5cIikucmVwbGFjZSgvXFxyL2csXCJcXG5cIikpLmxlbmd0aCxuPTA7bjxyO24rKyl7dmFyIG89ZS5jaGFyQ29kZUF0KG4pLGE9bnVsbDtvPDEyOD9pKys6YT1vPjEyNyYmbzwyMDQ4P1N0cmluZy5mcm9tQ2hhckNvZGUobz4+NnwxOTIsNjMmb3wxMjgpOlN0cmluZy5mcm9tQ2hhckNvZGUobz4+MTJ8MjI0LG8+PjYmNjN8MTI4LDYzJm98MTI4KSwkKGEpfHwoaT50JiYocys9ZS5zdWJzdHJpbmcodCxpKSkscys9YSx0PWk9bisxKX1yZXR1cm4gaT50JiYocys9ZS5zdWJzdHJpbmcodCxlLmxlbmd0aCkpLHN9LGduPSEhY3x8ISF1LGZuPVwidGV4dC9wbGFpblwiLG1uPShlLHQpPT57dmFyW2kscl09ZS5zcGxpdChcIj9cIiksbj1qKHt9LHQpO251bGw9PXJ8fHIuc3BsaXQoXCImXCIpLmZvckVhY2goKGU9Pnt2YXJbdF09ZS5zcGxpdChcIj1cIik7ZGVsZXRlIG5bdF19KSk7dmFyIHM9bXQobik7cmV0dXJuIHM9cz8ocj9yK1wiJlwiOlwiXCIpK3M6cixcIlwiLmNvbmNhdChpLFwiP1wiKS5jb25jYXQocyl9LGJuPShlLHQpPT5KU09OLnN0cmluZ2lmeShlLCgoZSx0KT0+XCJiaWdpbnRcIj09dHlwZW9mIHQ/dC50b1N0cmluZygpOnQpLHQpLHluPXQ9Pnt2YXJ7ZGF0YTppLGNvbXByZXNzaW9uOnJ9PXQ7aWYoaSl7aWYocj09PWUuR1ppcEpTKXt2YXIgbj1ZcihLcihibihpKSkse210aW1lOjB9KSxzPW5ldyBCbG9iKFtuXSx7dHlwZTpmbn0pO3JldHVybntjb250ZW50VHlwZTpmbixib2R5OnMsZXN0aW1hdGVkU2l6ZTpzLnNpemV9fWlmKHI9PT1lLkJhc2U2NCl7dmFyIG89ZnVuY3Rpb24oZSl7dmFyIHQsaSxyLG4scyxvPVwiQUJDREVGR0hJSktMTU5PUFFSU1RVVldYWVphYmNkZWZnaGlqa2xtbm9wcXJzdHV2d3h5ejAxMjM0NTY3ODkrLz1cIixhPTAsbD0wLHU9XCJcIixjPVtdO2lmKCFlKXJldHVybiBlO2U9dm4oZSk7ZG97dD0ocz1lLmNoYXJDb2RlQXQoYSsrKTw8MTZ8ZS5jaGFyQ29kZUF0KGErKyk8PDh8ZS5jaGFyQ29kZUF0KGErKykpPj4xOCY2MyxpPXM+PjEyJjYzLHI9cz4+NiY2MyxuPTYzJnMsY1tsKytdPW8uY2hhckF0KHQpK28uY2hhckF0KGkpK28uY2hhckF0KHIpK28uY2hhckF0KG4pfXdoaWxlKGE8ZS5sZW5ndGgpO3N3aXRjaCh1PWMuam9pbihcIlwiKSxlLmxlbmd0aCUzKXtjYXNlIDE6dT11LnNsaWNlKDAsLTIpK1wiPT1cIjticmVhaztjYXNlIDI6dT11LnNsaWNlKDAsLTEpK1wiPVwifXJldHVybiB1fShibihpKSksYT0oZT0+XCJkYXRhPVwiK2VuY29kZVVSSUNvbXBvbmVudChcInN0cmluZ1wiPT10eXBlb2YgZT9lOmJuKGUpKSkobyk7cmV0dXJue2NvbnRlbnRUeXBlOlwiYXBwbGljYXRpb24veC13d3ctZm9ybS11cmxlbmNvZGVkXCIsYm9keTphLGVzdGltYXRlZFNpemU6bmV3IEJsb2IoW2FdKS5zaXplfX12YXIgbD1ibihpKTtyZXR1cm57Y29udGVudFR5cGU6XCJhcHBsaWNhdGlvbi9qc29uXCIsYm9keTpsLGVzdGltYXRlZFNpemU6bmV3IEJsb2IoW2xdKS5zaXplfX19LHduPVtdO3UmJnduLnB1c2goe3RyYW5zcG9ydDpcImZldGNoXCIsbWV0aG9kOmU9Pnt2YXIgdCxpLHtjb250ZW50VHlwZTpyLGJvZHk6bixlc3RpbWF0ZWRTaXplOnN9PW51bGwhPT0odD15bihlKSkmJnZvaWQgMCE9PXQ/dDp7fSxvPW5ldyBIZWFkZXJzO1koZS5oZWFkZXJzLChmdW5jdGlvbihlLHQpe28uYXBwZW5kKHQsZSl9KSksciYmby5hcHBlbmQoXCJDb250ZW50LVR5cGVcIixyKTt2YXIgYT1lLnVybCxsPW51bGw7aWYoZCl7dmFyIGM9bmV3IGQ7bD17c2lnbmFsOmMuc2lnbmFsLHRpbWVvdXQ6c2V0VGltZW91dCgoKCk9PmMuYWJvcnQoKSksZS50aW1lb3V0KX19dShhLGooe21ldGhvZDoobnVsbD09ZT92b2lkIDA6ZS5tZXRob2QpfHxcIkdFVFwiLGhlYWRlcnM6byxrZWVwYWxpdmU6XCJQT1NUXCI9PT1lLm1ldGhvZCYmKHN8fDApPDUyNDI4LjgsYm9keTpuLHNpZ25hbDpudWxsPT09KGk9bCl8fHZvaWQgMD09PWk/dm9pZCAwOmkuc2lnbmFsfSxlLmZldGNoT3B0aW9ucykpLnRoZW4oKHQ9PnQudGV4dCgpLnRoZW4oKGk9Pnt2YXIgcixuPXtzdGF0dXNDb2RlOnQuc3RhdHVzLHRleHQ6aX07aWYoMjAwPT09dC5zdGF0dXMpdHJ5e24uanNvbj1KU09OLnBhcnNlKGkpfWNhdGNoKGUpe3EuZXJyb3IoZSl9bnVsbD09PShyPWUuY2FsbGJhY2spfHx2b2lkIDA9PT1yfHxyLmNhbGwoZSxuKX0pKSkpLmNhdGNoKCh0PT57dmFyIGk7cS5lcnJvcih0KSxudWxsPT09KGk9ZS5jYWxsYmFjayl8fHZvaWQgMD09PWl8fGkuY2FsbChlLHtzdGF0dXNDb2RlOjAsdGV4dDp0fSl9KSkuZmluYWxseSgoKCk9Pmw/Y2xlYXJUaW1lb3V0KGwudGltZW91dCk6bnVsbCkpfX0pLGMmJnduLnB1c2goe3RyYW5zcG9ydDpcIlhIUlwiLG1ldGhvZDplPT57dmFyIHQsaT1uZXcgYztpLm9wZW4oZS5tZXRob2R8fFwiR0VUXCIsZS51cmwsITApO3Zhcntjb250ZW50VHlwZTpyLGJvZHk6bn09bnVsbCE9PSh0PXluKGUpKSYmdm9pZCAwIT09dD90Ont9O1koZS5oZWFkZXJzLChmdW5jdGlvbihlLHQpe2kuc2V0UmVxdWVzdEhlYWRlcih0LGUpfSkpLHImJmkuc2V0UmVxdWVzdEhlYWRlcihcIkNvbnRlbnQtVHlwZVwiLHIpLGUudGltZW91dCYmKGkudGltZW91dD1lLnRpbWVvdXQpLGkud2l0aENyZWRlbnRpYWxzPSEwLGkub25yZWFkeXN0YXRlY2hhbmdlPSgpPT57aWYoND09PWkucmVhZHlTdGF0ZSl7dmFyIHQscj17c3RhdHVzQ29kZTppLnN0YXR1cyx0ZXh0OmkucmVzcG9uc2VUZXh0fTtpZigyMDA9PT1pLnN0YXR1cyl0cnl7ci5qc29uPUpTT04ucGFyc2UoaS5yZXNwb25zZVRleHQpfWNhdGNoKGUpe31udWxsPT09KHQ9ZS5jYWxsYmFjayl8fHZvaWQgMD09PXR8fHQuY2FsbChlLHIpfX0saS5zZW5kKG4pfX0pLG51bGwhPW8mJm8uc2VuZEJlYWNvbiYmd24ucHVzaCh7dHJhbnNwb3J0Olwic2VuZEJlYWNvblwiLG1ldGhvZDplPT57dmFyIHQ9bW4oZS51cmwse2JlYWNvbjpcIjFcIn0pO3RyeXt2YXIgaSx7Y29udGVudFR5cGU6cixib2R5Om59PW51bGwhPT0oaT15bihlKSkmJnZvaWQgMCE9PWk/aTp7fSxzPVwic3RyaW5nXCI9PXR5cGVvZiBuP25ldyBCbG9iKFtuXSx7dHlwZTpyfSk6bjtvLnNlbmRCZWFjb24odCxzKX1jYXRjaChlKXt9fX0pO3ZhciBTbj1bXCJyZXRyaWVzUGVyZm9ybWVkU29GYXJcIl07Y2xhc3MgRW57Y29uc3RydWN0b3IoZSl7Vyh0aGlzLFwiaXNQb2xsaW5nXCIsITEpLFcodGhpcyxcInBvbGxJbnRlcnZhbE1zXCIsM2UzKSxXKHRoaXMsXCJxdWV1ZVwiLFtdKSx0aGlzLmluc3RhbmNlPWUsdGhpcy5xdWV1ZT1bXSx0aGlzLmFyZVdlT25saW5lPSEwLCFGKHQpJiZcIm9uTGluZVwiaW4gdC5uYXZpZ2F0b3ImJih0aGlzLmFyZVdlT25saW5lPXQubmF2aWdhdG9yLm9uTGluZSx0LmFkZEV2ZW50TGlzdGVuZXIoXCJvbmxpbmVcIiwoKCk9Pnt0aGlzLmFyZVdlT25saW5lPSEwLHRoaXMuZmx1c2goKX0pKSx0LmFkZEV2ZW50TGlzdGVuZXIoXCJvZmZsaW5lXCIsKCgpPT57dGhpcy5hcmVXZU9ubGluZT0hMX0pKSl9cmV0cmlhYmxlUmVxdWVzdChlKXt2YXJ7cmV0cmllc1BlcmZvcm1lZFNvRmFyOnR9PWUsaT1WKGUsU24pO00odCkmJnQ+MCYmKGkudXJsPW1uKGkudXJsLHtyZXRyeV9jb3VudDp0fSkpLHRoaXMuaW5zdGFuY2UuX3NlbmRfcmVxdWVzdChqKGooe30saSkse30se2NhbGxiYWNrOmU9Pnt2YXIgcjsyMDAhPT1lLnN0YXR1c0NvZGUmJihlLnN0YXR1c0NvZGU8NDAwfHxlLnN0YXR1c0NvZGU+PTUwMCkmJihudWxsIT10P3Q6MCk8MTA/dGhpcy5lbnF1ZXVlKGooe3JldHJpZXNQZXJmb3JtZWRTb0Zhcjp0fSxpKSk6bnVsbD09PShyPWkuY2FsbGJhY2spfHx2b2lkIDA9PT1yfHxyLmNhbGwoaSxlKX19KSl9ZW5xdWV1ZShlKXt2YXIgdD1lLnJldHJpZXNQZXJmb3JtZWRTb0Zhcnx8MDtlLnJldHJpZXNQZXJmb3JtZWRTb0Zhcj10KzE7dmFyIGk9ZnVuY3Rpb24oZSl7dmFyIHQ9M2UzKk1hdGgucG93KDIsZSksaT10LzIscj1NYXRoLm1pbigxOGU1LHQpLG49KE1hdGgucmFuZG9tKCktLjUpKihyLWkpO3JldHVybiBNYXRoLmNlaWwocituKX0odCkscj1EYXRlLm5vdygpK2k7dGhpcy5xdWV1ZS5wdXNoKHtyZXRyeUF0OnIscmVxdWVzdE9wdGlvbnM6ZX0pO3ZhciBuPVwiRW5xdWV1ZWQgZmFpbGVkIHJlcXVlc3QgZm9yIHJldHJ5IGluIFwiLmNvbmNhdChpKTtuYXZpZ2F0b3Iub25MaW5lfHwobis9XCIgKEJyb3dzZXIgaXMgb2ZmbGluZSlcIikscS53YXJuKG4pLHRoaXMuaXNQb2xsaW5nfHwodGhpcy5pc1BvbGxpbmc9ITAsdGhpcy5wb2xsKCkpfXBvbGwoKXt0aGlzLnBvbGxlciYmY2xlYXJUaW1lb3V0KHRoaXMucG9sbGVyKSx0aGlzLnBvbGxlcj1zZXRUaW1lb3V0KCgoKT0+e3RoaXMuYXJlV2VPbmxpbmUmJnRoaXMucXVldWUubGVuZ3RoPjAmJnRoaXMuZmx1c2goKSx0aGlzLnBvbGwoKX0pLHRoaXMucG9sbEludGVydmFsTXMpfWZsdXNoKCl7dmFyIGU9RGF0ZS5ub3coKSx0PVtdLGk9dGhpcy5xdWV1ZS5maWx0ZXIoKGk9PmkucmV0cnlBdDxlfHwodC5wdXNoKGkpLCExKSkpO2lmKHRoaXMucXVldWU9dCxpLmxlbmd0aD4wKWZvcih2YXJ7cmVxdWVzdE9wdGlvbnM6cn1vZiBpKXRoaXMucmV0cmlhYmxlUmVxdWVzdChyKX11bmxvYWQoKXtmb3IodmFye3JlcXVlc3RPcHRpb25zOmV9b2YodGhpcy5wb2xsZXImJihjbGVhclRpbWVvdXQodGhpcy5wb2xsZXIpLHRoaXMucG9sbGVyPXZvaWQgMCksdGhpcy5xdWV1ZSkpdHJ5e3RoaXMuaW5zdGFuY2UuX3NlbmRfcmVxdWVzdChqKGooe30sZSkse30se3RyYW5zcG9ydDpcInNlbmRCZWFjb25cIn0pKX1jYXRjaChlKXtxLmVycm9yKGUpfXRoaXMucXVldWU9W119fXZhciBrbix4bj1CKFwiW1Nlc3Npb25JZF1cIik7Y2xhc3MgSW57Y29uc3RydWN0b3IoZSx0LGkpe3ZhciByO2lmKFcodGhpcyxcIl9zZXNzaW9uSWRDaGFuZ2VkSGFuZGxlcnNcIixbXSksIWUucGVyc2lzdGVuY2UpdGhyb3cgbmV3IEVycm9yKFwiU2Vzc2lvbklkTWFuYWdlciByZXF1aXJlcyBhIFBvc3RIb2dQZXJzaXN0ZW5jZSBpbnN0YW5jZVwiKTtpZihlLmNvbmZpZy5fX3ByZXZpZXdfZXhwZXJpbWVudGFsX2Nvb2tpZWxlc3NfbW9kZSl0aHJvdyBuZXcgRXJyb3IoXCJTZXNzaW9uSWRNYW5hZ2VyIGNhbm5vdCBiZSB1c2VkIHdpdGggX19wcmV2aWV3X2V4cGVyaW1lbnRhbF9jb29raWVsZXNzX21vZGVcIik7dGhpcy5jb25maWc9ZS5jb25maWcsdGhpcy5wZXJzaXN0ZW5jZT1lLnBlcnNpc3RlbmNlLHRoaXMuX3dpbmRvd0lkPXZvaWQgMCx0aGlzLl9zZXNzaW9uSWQ9dm9pZCAwLHRoaXMuX3Nlc3Npb25TdGFydFRpbWVzdGFtcD1udWxsLHRoaXMuX3Nlc3Npb25BY3Rpdml0eVRpbWVzdGFtcD1udWxsLHRoaXMuX3Nlc3Npb25JZEdlbmVyYXRvcj10fHxldCx0aGlzLl93aW5kb3dJZEdlbmVyYXRvcj1pfHxldDt2YXIgbj10aGlzLmNvbmZpZy5wZXJzaXN0ZW5jZV9uYW1lfHx0aGlzLmNvbmZpZy50b2tlbixzPXRoaXMuY29uZmlnLnNlc3Npb25faWRsZV90aW1lb3V0X3NlY29uZHN8fDE4MDA7aWYodGhpcy5fc2Vzc2lvblRpbWVvdXRNcz0xZTMqZHIocyw2MCwzNmUzLFwic2Vzc2lvbl9pZGxlX3RpbWVvdXRfc2Vjb25kc1wiLDE4MDApLGUucmVnaXN0ZXIoeyRjb25maWd1cmVkX3Nlc3Npb25fdGltZW91dF9tczp0aGlzLl9zZXNzaW9uVGltZW91dE1zfSksdGhpcy5yZXNldElkbGVUaW1lcigpLHRoaXMuX3dpbmRvd19pZF9zdG9yYWdlX2tleT1cInBoX1wiK24rXCJfd2luZG93X2lkXCIsdGhpcy5fcHJpbWFyeV93aW5kb3dfZXhpc3RzX3N0b3JhZ2Vfa2V5PVwicGhfXCIrbitcIl9wcmltYXJ5X3dpbmRvd19leGlzdHNcIix0aGlzLl9jYW5Vc2VTZXNzaW9uU3RvcmFnZSgpKXt2YXIgbz1wdC5wYXJzZSh0aGlzLl93aW5kb3dfaWRfc3RvcmFnZV9rZXkpLGE9cHQucGFyc2UodGhpcy5fcHJpbWFyeV93aW5kb3dfZXhpc3RzX3N0b3JhZ2Vfa2V5KTtvJiYhYT90aGlzLl93aW5kb3dJZD1vOnB0LnJlbW92ZSh0aGlzLl93aW5kb3dfaWRfc3RvcmFnZV9rZXkpLHB0LnNldCh0aGlzLl9wcmltYXJ5X3dpbmRvd19leGlzdHNfc3RvcmFnZV9rZXksITApfWlmKG51bGwhPT0ocj10aGlzLmNvbmZpZy5ib290c3RyYXApJiZ2b2lkIDAhPT1yJiZyLnNlc3Npb25JRCl0cnl7dmFyIGw9KGU9Pnt2YXIgdD1lLnJlcGxhY2UoLy0vZyxcIlwiKTtpZigzMiE9PXQubGVuZ3RoKXRocm93IG5ldyBFcnJvcihcIk5vdCBhIHZhbGlkIFVVSURcIik7aWYoXCI3XCIhPT10WzEyXSl0aHJvdyBuZXcgRXJyb3IoXCJOb3QgYSBVVUlEdjdcIik7cmV0dXJuIHBhcnNlSW50KHQuc3Vic3RyaW5nKDAsMTIpLDE2KX0pKHRoaXMuY29uZmlnLmJvb3RzdHJhcC5zZXNzaW9uSUQpO3RoaXMuX3NldFNlc3Npb25JZCh0aGlzLmNvbmZpZy5ib290c3RyYXAuc2Vzc2lvbklELChuZXcgRGF0ZSkuZ2V0VGltZSgpLGwpfWNhdGNoKGUpe3huLmVycm9yKFwiSW52YWxpZCBzZXNzaW9uSUQgaW4gYm9vdHN0cmFwXCIsZSl9dGhpcy5fbGlzdGVuVG9SZWxvYWRXaW5kb3coKX1nZXQgc2Vzc2lvblRpbWVvdXRNcygpe3JldHVybiB0aGlzLl9zZXNzaW9uVGltZW91dE1zfW9uU2Vzc2lvbklkKGUpe3JldHVybiBGKHRoaXMuX3Nlc3Npb25JZENoYW5nZWRIYW5kbGVycykmJih0aGlzLl9zZXNzaW9uSWRDaGFuZ2VkSGFuZGxlcnM9W10pLHRoaXMuX3Nlc3Npb25JZENoYW5nZWRIYW5kbGVycy5wdXNoKGUpLHRoaXMuX3Nlc3Npb25JZCYmZSh0aGlzLl9zZXNzaW9uSWQsdGhpcy5fd2luZG93SWQpLCgpPT57dGhpcy5fc2Vzc2lvbklkQ2hhbmdlZEhhbmRsZXJzPXRoaXMuX3Nlc3Npb25JZENoYW5nZWRIYW5kbGVycy5maWx0ZXIoKHQ9PnQhPT1lKSl9fV9jYW5Vc2VTZXNzaW9uU3RvcmFnZSgpe3JldHVyblwibWVtb3J5XCIhPT10aGlzLmNvbmZpZy5wZXJzaXN0ZW5jZSYmIXRoaXMucGVyc2lzdGVuY2UuZGlzYWJsZWQmJnB0LmlzX3N1cHBvcnRlZCgpfV9zZXRXaW5kb3dJZChlKXtlIT09dGhpcy5fd2luZG93SWQmJih0aGlzLl93aW5kb3dJZD1lLHRoaXMuX2NhblVzZVNlc3Npb25TdG9yYWdlKCkmJnB0LnNldCh0aGlzLl93aW5kb3dfaWRfc3RvcmFnZV9rZXksZSkpfV9nZXRXaW5kb3dJZCgpe3JldHVybiB0aGlzLl93aW5kb3dJZD90aGlzLl93aW5kb3dJZDp0aGlzLl9jYW5Vc2VTZXNzaW9uU3RvcmFnZSgpP3B0LnBhcnNlKHRoaXMuX3dpbmRvd19pZF9zdG9yYWdlX2tleSk6bnVsbH1fc2V0U2Vzc2lvbklkKGUsdCxpKXtlPT09dGhpcy5fc2Vzc2lvbklkJiZ0PT09dGhpcy5fc2Vzc2lvbkFjdGl2aXR5VGltZXN0YW1wJiZpPT09dGhpcy5fc2Vzc2lvblN0YXJ0VGltZXN0YW1wfHwodGhpcy5fc2Vzc2lvblN0YXJ0VGltZXN0YW1wPWksdGhpcy5fc2Vzc2lvbkFjdGl2aXR5VGltZXN0YW1wPXQsdGhpcy5fc2Vzc2lvbklkPWUsdGhpcy5wZXJzaXN0ZW5jZS5yZWdpc3Rlcih7W3dlXTpbdCxlLGldfSkpfV9nZXRTZXNzaW9uSWQoKXtpZih0aGlzLl9zZXNzaW9uSWQmJnRoaXMuX3Nlc3Npb25BY3Rpdml0eVRpbWVzdGFtcCYmdGhpcy5fc2Vzc2lvblN0YXJ0VGltZXN0YW1wKXJldHVyblt0aGlzLl9zZXNzaW9uQWN0aXZpdHlUaW1lc3RhbXAsdGhpcy5fc2Vzc2lvbklkLHRoaXMuX3Nlc3Npb25TdGFydFRpbWVzdGFtcF07dmFyIGU9dGhpcy5wZXJzaXN0ZW5jZS5wcm9wc1t3ZV07cmV0dXJuIHgoZSkmJjI9PT1lLmxlbmd0aCYmZS5wdXNoKGVbMF0pLGV8fFswLG51bGwsMF19cmVzZXRTZXNzaW9uSWQoKXt0aGlzLl9zZXRTZXNzaW9uSWQobnVsbCxudWxsLG51bGwpfV9saXN0ZW5Ub1JlbG9hZFdpbmRvdygpe251bGw9PXR8fHQuYWRkRXZlbnRMaXN0ZW5lcihcImJlZm9yZXVubG9hZFwiLCgoKT0+e3RoaXMuX2NhblVzZVNlc3Npb25TdG9yYWdlKCkmJnB0LnJlbW92ZSh0aGlzLl9wcmltYXJ5X3dpbmRvd19leGlzdHNfc3RvcmFnZV9rZXkpfSkpfWNoZWNrQW5kR2V0U2Vzc2lvbkFuZFdpbmRvd0lkKCl7dmFyIGU9YXJndW1lbnRzLmxlbmd0aD4wJiZ2b2lkIDAhPT1hcmd1bWVudHNbMF0mJmFyZ3VtZW50c1swXSx0PWFyZ3VtZW50cy5sZW5ndGg+MSYmdm9pZCAwIT09YXJndW1lbnRzWzFdP2FyZ3VtZW50c1sxXTpudWxsO2lmKHRoaXMuY29uZmlnLl9fcHJldmlld19leHBlcmltZW50YWxfY29va2llbGVzc19tb2RlKXRocm93IG5ldyBFcnJvcihcImNoZWNrQW5kR2V0U2Vzc2lvbkFuZFdpbmRvd0lkIHNob3VsZCBub3QgYmUgY2FsbGVkIGluIF9fcHJldmlld19leHBlcmltZW50YWxfY29va2llbGVzc19tb2RlXCIpO3ZhciBpPXR8fChuZXcgRGF0ZSkuZ2V0VGltZSgpLFtyLG4sc109dGhpcy5fZ2V0U2Vzc2lvbklkKCksbz10aGlzLl9nZXRXaW5kb3dJZCgpLGE9TShzKSYmcz4wJiZNYXRoLmFicyhpLXMpPjg2NGU1LGw9ITEsdT0hbixjPSFlJiZNYXRoLmFicyhpLXIpPnRoaXMuc2Vzc2lvblRpbWVvdXRNczt1fHxjfHxhPyhuPXRoaXMuX3Nlc3Npb25JZEdlbmVyYXRvcigpLG89dGhpcy5fd2luZG93SWRHZW5lcmF0b3IoKSx4bi5pbmZvKFwibmV3IHNlc3Npb24gSUQgZ2VuZXJhdGVkXCIse3Nlc3Npb25JZDpuLHdpbmRvd0lkOm8sY2hhbmdlUmVhc29uOntub1Nlc3Npb25JZDp1LGFjdGl2aXR5VGltZW91dDpjLHNlc3Npb25QYXN0TWF4aW11bUxlbmd0aDphfX0pLHM9aSxsPSEwKTpvfHwobz10aGlzLl93aW5kb3dJZEdlbmVyYXRvcigpLGw9ITApO3ZhciBkPTA9PT1yfHwhZXx8YT9pOnIsaD0wPT09cz8obmV3IERhdGUpLmdldFRpbWUoKTpzO3JldHVybiB0aGlzLl9zZXRXaW5kb3dJZChvKSx0aGlzLl9zZXRTZXNzaW9uSWQobixkLGgpLGV8fHRoaXMucmVzZXRJZGxlVGltZXIoKSxsJiZ0aGlzLl9zZXNzaW9uSWRDaGFuZ2VkSGFuZGxlcnMuZm9yRWFjaCgoZT0+ZShuLG8sbD97bm9TZXNzaW9uSWQ6dSxhY3Rpdml0eVRpbWVvdXQ6YyxzZXNzaW9uUGFzdE1heGltdW1MZW5ndGg6YX06dm9pZCAwKSkpLHtzZXNzaW9uSWQ6bix3aW5kb3dJZDpvLHNlc3Npb25TdGFydFRpbWVzdGFtcDpoLGNoYW5nZVJlYXNvbjpsP3tub1Nlc3Npb25JZDp1LGFjdGl2aXR5VGltZW91dDpjLHNlc3Npb25QYXN0TWF4aW11bUxlbmd0aDphfTp2b2lkIDAsbGFzdEFjdGl2aXR5VGltZXN0YW1wOnJ9fXJlc2V0SWRsZVRpbWVyKCl7Y2xlYXJUaW1lb3V0KHRoaXMuX2VuZm9yY2VJZGxlVGltZW91dCksdGhpcy5fZW5mb3JjZUlkbGVUaW1lb3V0PXNldFRpbWVvdXQoKCgpPT57dGhpcy5yZXNldFNlc3Npb25JZCgpfSksMS4xKnRoaXMuc2Vzc2lvblRpbWVvdXRNcyl9fSFmdW5jdGlvbihlKXtlLlVTPVwidXNcIixlLkVVPVwiZXVcIixlLkNVU1RPTT1cImN1c3RvbVwifShrbnx8KGtuPXt9KSk7dmFyIENuPVwiaS5wb3N0aG9nLmNvbVwiO2NsYXNzIFBue2NvbnN0cnVjdG9yKGUpe1codGhpcyxcIl9yZWdpb25DYWNoZVwiLHt9KSx0aGlzLmluc3RhbmNlPWV9Z2V0IGFwaUhvc3QoKXt2YXIgZT10aGlzLmluc3RhbmNlLmNvbmZpZy5hcGlfaG9zdC50cmltKCkucmVwbGFjZSgvXFwvJC8sXCJcIik7cmV0dXJuXCJodHRwczovL2FwcC5wb3N0aG9nLmNvbVwiPT09ZT9cImh0dHBzOi8vdXMuaS5wb3N0aG9nLmNvbVwiOmV9Z2V0IHVpSG9zdCgpe3ZhciBlLHQ9bnVsbD09PShlPXRoaXMuaW5zdGFuY2UuY29uZmlnLnVpX2hvc3QpfHx2b2lkIDA9PT1lP3ZvaWQgMDplLnJlcGxhY2UoL1xcLyQvLFwiXCIpO3JldHVybiB0fHwodD10aGlzLmFwaUhvc3QucmVwbGFjZShcIi5cIi5jb25jYXQoQ24pLFwiLnBvc3Rob2cuY29tXCIpKSxcImh0dHBzOi8vYXBwLnBvc3Rob2cuY29tXCI9PT10P1wiaHR0cHM6Ly91cy5wb3N0aG9nLmNvbVwiOnR9Z2V0IHJlZ2lvbigpe3JldHVybiB0aGlzLl9yZWdpb25DYWNoZVt0aGlzLmFwaUhvc3RdfHwoL2h0dHBzOlxcL1xcLyhhcHB8dXN8dXMtYXNzZXRzKShcXC5pKT9cXC5wb3N0aG9nXFwuY29tL2kudGVzdCh0aGlzLmFwaUhvc3QpP3RoaXMuX3JlZ2lvbkNhY2hlW3RoaXMuYXBpSG9zdF09a24uVVM6L2h0dHBzOlxcL1xcLyhldXxldS1hc3NldHMpKFxcLmkpP1xcLnBvc3Rob2dcXC5jb20vaS50ZXN0KHRoaXMuYXBpSG9zdCk/dGhpcy5fcmVnaW9uQ2FjaGVbdGhpcy5hcGlIb3N0XT1rbi5FVTp0aGlzLl9yZWdpb25DYWNoZVt0aGlzLmFwaUhvc3RdPWtuLkNVU1RPTSksdGhpcy5fcmVnaW9uQ2FjaGVbdGhpcy5hcGlIb3N0XX1lbmRwb2ludEZvcihlKXt2YXIgdD1hcmd1bWVudHMubGVuZ3RoPjEmJnZvaWQgMCE9PWFyZ3VtZW50c1sxXT9hcmd1bWVudHNbMV06XCJcIjtpZih0JiYodD1cIi9cIj09PXRbMF0/dDpcIi9cIi5jb25jYXQodCkpLFwidWlcIj09PWUpcmV0dXJuIHRoaXMudWlIb3N0K3Q7aWYodGhpcy5yZWdpb249PT1rbi5DVVNUT00pcmV0dXJuIHRoaXMuYXBpSG9zdCt0O3ZhciBpPUNuK3Q7c3dpdGNoKGUpe2Nhc2VcImFzc2V0c1wiOnJldHVyblwiaHR0cHM6Ly9cIi5jb25jYXQodGhpcy5yZWdpb24sXCItYXNzZXRzLlwiKS5jb25jYXQoaSk7Y2FzZVwiYXBpXCI6cmV0dXJuXCJodHRwczovL1wiLmNvbmNhdCh0aGlzLnJlZ2lvbixcIi5cIikuY29uY2F0KGkpfX19dmFyIEZuPVwicG9zdGhvZy1qc1wiO2Z1bmN0aW9uIFJuKGUpe3Zhcntvcmdhbml6YXRpb246dCxwcm9qZWN0SWQ6aSxwcmVmaXg6cixzZXZlcml0eUFsbG93TGlzdDpuPVtcImVycm9yXCJdfT1hcmd1bWVudHMubGVuZ3RoPjEmJnZvaWQgMCE9PWFyZ3VtZW50c1sxXT9hcmd1bWVudHNbMV06e307cmV0dXJuIHM9Pnt2YXIgbyxhLGwsdSxjO2lmKCEoXCIqXCI9PT1ufHxuLmluY2x1ZGVzKHMubGV2ZWwpKXx8IWUuX19sb2FkZWQpcmV0dXJuIHM7cy50YWdzfHwocy50YWdzPXt9KTt2YXIgZD1lLnJlcXVlc3RSb3V0ZXIuZW5kcG9pbnRGb3IoXCJ1aVwiLFwiL3Byb2plY3QvXCIuY29uY2F0KGUuY29uZmlnLnRva2VuLFwiL3BlcnNvbi9cIikuY29uY2F0KGUuZ2V0X2Rpc3RpbmN0X2lkKCkpKTtzLnRhZ3NbXCJQb3N0SG9nIFBlcnNvbiBVUkxcIl09ZCxlLnNlc3Npb25SZWNvcmRpbmdTdGFydGVkKCkmJihzLnRhZ3NbXCJQb3N0SG9nIFJlY29yZGluZyBVUkxcIl09ZS5nZXRfc2Vzc2lvbl9yZXBsYXlfdXJsKHt3aXRoVGltZXN0YW1wOiEwfSkpO3ZhciBoPShudWxsPT09KG89cy5leGNlcHRpb24pfHx2b2lkIDA9PT1vP3ZvaWQgMDpvLnZhbHVlcyl8fFtdO2gubWFwKChlPT57ZS5zdGFja3RyYWNlJiYoZS5zdGFja3RyYWNlLnR5cGU9XCJyYXdcIil9KSk7dmFyIF89eyRleGNlcHRpb25fbWVzc2FnZToobnVsbD09PShhPWhbMF0pfHx2b2lkIDA9PT1hP3ZvaWQgMDphLnZhbHVlKXx8cy5tZXNzYWdlLCRleGNlcHRpb25fdHlwZTpudWxsPT09KGw9aFswXSl8fHZvaWQgMD09PWw/dm9pZCAwOmwudHlwZSwkZXhjZXB0aW9uX3BlcnNvblVSTDpkLCRleGNlcHRpb25fbGV2ZWw6cy5sZXZlbCwkZXhjZXB0aW9uX2xpc3Q6aCwkc2VudHJ5X2V2ZW50X2lkOnMuZXZlbnRfaWQsJHNlbnRyeV9leGNlcHRpb246cy5leGNlcHRpb24sJHNlbnRyeV9leGNlcHRpb25fbWVzc2FnZToobnVsbD09PSh1PWhbMF0pfHx2b2lkIDA9PT11P3ZvaWQgMDp1LnZhbHVlKXx8cy5tZXNzYWdlLCRzZW50cnlfZXhjZXB0aW9uX3R5cGU6bnVsbD09PShjPWhbMF0pfHx2b2lkIDA9PT1jP3ZvaWQgMDpjLnR5cGUsJHNlbnRyeV90YWdzOnMudGFnc307cmV0dXJuIHQmJmkmJihfLiRzZW50cnlfdXJsPShyfHxcImh0dHBzOi8vc2VudHJ5LmlvL29yZ2FuaXphdGlvbnMvXCIpK3QrXCIvaXNzdWVzLz9wcm9qZWN0PVwiK2krXCImcXVlcnk9XCIrcy5ldmVudF9pZCksZS5leGNlcHRpb25zLnNlbmRFeGNlcHRpb25FdmVudChfKSxzfX1jbGFzcyBUbntjb25zdHJ1Y3RvcihlLHQsaSxyLG4pe3RoaXMubmFtZT1Gbix0aGlzLnNldHVwT25jZT1mdW5jdGlvbihzKXtzKFJuKGUse29yZ2FuaXphdGlvbjp0LHByb2plY3RJZDppLHByZWZpeDpyLHNldmVyaXR5QWxsb3dMaXN0Om59KSl9fX12YXIgJG49QihcIltTZWdtZW50SW50ZWdyYXRpb25dXCIpO2Z1bmN0aW9uIE9uKGUsdCl7dmFyIGk9ZS5jb25maWcuc2VnbWVudDtpZighaSlyZXR1cm4gdCgpOyFmdW5jdGlvbihlLHQpe3ZhciBpPWUuY29uZmlnLnNlZ21lbnQ7aWYoIWkpcmV0dXJuIHQoKTt2YXIgcj1pPT57dmFyIHI9KCk9PmkuYW5vbnltb3VzSWQoKXx8ZXQoKTtlLmNvbmZpZy5nZXRfZGV2aWNlX2lkPXIsaS5pZCgpJiYoZS5yZWdpc3Rlcih7ZGlzdGluY3RfaWQ6aS5pZCgpLCRkZXZpY2VfaWQ6cigpfSksZS5wZXJzaXN0ZW5jZS5zZXRfcHJvcGVydHkoJGUsXCJpZGVudGlmaWVkXCIpKSx0KCl9LG49aS51c2VyKCk7XCJ0aGVuXCJpbiBuJiZJKG4udGhlbik/bi50aGVuKChlPT5yKGUpKSk6cihuKX0oZSwoKCk9PntpLnJlZ2lzdGVyKChlPT57UHJvbWlzZSYmUHJvbWlzZS5yZXNvbHZlfHwkbi53YXJuKFwiVGhpcyBicm93c2VyIGRvZXMgbm90IGhhdmUgUHJvbWlzZSBzdXBwb3J0LCBhbmQgY2FuIG5vdCB1c2UgdGhlIHNlZ21lbnQgaW50ZWdyYXRpb25cIik7dmFyIHQ9KHQsaSk9Pnt2YXIgcjtpZighaSlyZXR1cm4gdDt0LmV2ZW50LnVzZXJJZHx8dC5ldmVudC5hbm9ueW1vdXNJZD09PWUuZ2V0X2Rpc3RpbmN0X2lkKCl8fCgkbi5pbmZvKFwiTm8gdXNlcklkIHNldCwgcmVzZXR0aW5nIFBvc3RIb2dcIiksZS5yZXNldCgpKSx0LmV2ZW50LnVzZXJJZCYmdC5ldmVudC51c2VySWQhPT1lLmdldF9kaXN0aW5jdF9pZCgpJiYoJG4uaW5mbyhcIlVzZXJJZCBzZXQsIGlkZW50aWZ5aW5nIHdpdGggUG9zdEhvZ1wiKSxlLmlkZW50aWZ5KHQuZXZlbnQudXNlcklkKSk7dmFyIG49ZS5fY2FsY3VsYXRlX2V2ZW50X3Byb3BlcnRpZXMoaSxudWxsIT09KHI9dC5ldmVudC5wcm9wZXJ0aWVzKSYmdm9pZCAwIT09cj9yOnt9LG5ldyBEYXRlKTtyZXR1cm4gdC5ldmVudC5wcm9wZXJ0aWVzPU9iamVjdC5hc3NpZ24oe30sbix0LmV2ZW50LnByb3BlcnRpZXMpLHR9O3JldHVybntuYW1lOlwiUG9zdEhvZyBKU1wiLHR5cGU6XCJlbnJpY2htZW50XCIsdmVyc2lvbjpcIjEuMC4wXCIsaXNMb2FkZWQ6KCk9PiEwLGxvYWQ6KCk9PlByb21pc2UucmVzb2x2ZSgpLHRyYWNrOmU9PnQoZSxlLmV2ZW50LmV2ZW50KSxwYWdlOmU9PnQoZSxcIiRwYWdldmlld1wiKSxpZGVudGlmeTplPT50KGUsXCIkaWRlbnRpZnlcIiksc2NyZWVuOmU9PnQoZSxcIiRzY3JlZW5cIil9fSkoZSkpLnRoZW4oKCgpPT57dCgpfSkpfSkpfWNsYXNzIE1ue2NvbnN0cnVjdG9yKGUpe3RoaXMuX2luc3RhbmNlPWV9ZG9QYWdlVmlldyhlLGkpe3ZhciByLG49dGhpcy5fcHJldmlvdXNQYWdlVmlld1Byb3BlcnRpZXMoZSxpKTtyZXR1cm4gdGhpcy5fY3VycmVudFBhZ2V2aWV3PXtwYXRobmFtZTpudWxsIT09KHI9bnVsbD09dD92b2lkIDA6dC5sb2NhdGlvbi5wYXRobmFtZSkmJnZvaWQgMCE9PXI/cjpcIlwiLHBhZ2VWaWV3SWQ6aSx0aW1lc3RhbXA6ZX0sdGhpcy5faW5zdGFuY2Uuc2Nyb2xsTWFuYWdlci5yZXNldENvbnRleHQoKSxufWRvUGFnZUxlYXZlKGUpe3ZhciB0O3JldHVybiB0aGlzLl9wcmV2aW91c1BhZ2VWaWV3UHJvcGVydGllcyhlLG51bGw9PT0odD10aGlzLl9jdXJyZW50UGFnZXZpZXcpfHx2b2lkIDA9PT10P3ZvaWQgMDp0LnBhZ2VWaWV3SWQpfWRvRXZlbnQoKXt2YXIgZTtyZXR1cm57JHBhZ2V2aWV3X2lkOm51bGw9PT0oZT10aGlzLl9jdXJyZW50UGFnZXZpZXcpfHx2b2lkIDA9PT1lP3ZvaWQgMDplLnBhZ2VWaWV3SWR9fV9wcmV2aW91c1BhZ2VWaWV3UHJvcGVydGllcyhlLHQpe3ZhciBpPXRoaXMuX2N1cnJlbnRQYWdldmlldztpZighaSlyZXR1cm57JHBhZ2V2aWV3X2lkOnR9O3ZhciByPXskcGFnZXZpZXdfaWQ6dCwkcHJldl9wYWdldmlld19pZDppLnBhZ2VWaWV3SWR9LG49dGhpcy5faW5zdGFuY2Uuc2Nyb2xsTWFuYWdlci5nZXRDb250ZXh0KCk7aWYobiYmIXRoaXMuX2luc3RhbmNlLmNvbmZpZy5kaXNhYmxlX3Njcm9sbF9wcm9wZXJ0aWVzKXt2YXJ7bWF4U2Nyb2xsSGVpZ2h0OnMsbGFzdFNjcm9sbFk6byxtYXhTY3JvbGxZOmEsbWF4Q29udGVudEhlaWdodDpsLGxhc3RDb250ZW50WTp1LG1heENvbnRlbnRZOmN9PW47aWYoIShGKHMpfHxGKG8pfHxGKGEpfHxGKGwpfHxGKHUpfHxGKGMpKSl7cz1NYXRoLmNlaWwocyksbz1NYXRoLmNlaWwobyksYT1NYXRoLmNlaWwoYSksbD1NYXRoLmNlaWwobCksdT1NYXRoLmNlaWwodSksYz1NYXRoLmNlaWwoYyk7dmFyIGQ9czw9MT8xOmRyKG8vcywwLDEpLGg9czw9MT8xOmRyKGEvcywwLDEpLF89bDw9MT8xOmRyKHUvbCwwLDEpLHA9bDw9MT8xOmRyKGMvbCwwLDEpO3I9SyhyLHskcHJldl9wYWdldmlld19sYXN0X3Njcm9sbDpvLCRwcmV2X3BhZ2V2aWV3X2xhc3Rfc2Nyb2xsX3BlcmNlbnRhZ2U6ZCwkcHJldl9wYWdldmlld19tYXhfc2Nyb2xsOmEsJHByZXZfcGFnZXZpZXdfbWF4X3Njcm9sbF9wZXJjZW50YWdlOmgsJHByZXZfcGFnZXZpZXdfbGFzdF9jb250ZW50OnUsJHByZXZfcGFnZXZpZXdfbGFzdF9jb250ZW50X3BlcmNlbnRhZ2U6XywkcHJldl9wYWdldmlld19tYXhfY29udGVudDpjLCRwcmV2X3BhZ2V2aWV3X21heF9jb250ZW50X3BlcmNlbnRhZ2U6cH0pfX1yZXR1cm4gaS5wYXRobmFtZSYmKHIuJHByZXZfcGFnZXZpZXdfcGF0aG5hbWU9aS5wYXRobmFtZSksaS50aW1lc3RhbXAmJihyLiRwcmV2X3BhZ2V2aWV3X2R1cmF0aW9uPShlLmdldFRpbWUoKS1pLnRpbWVzdGFtcC5nZXRUaW1lKCkpLzFlMykscn19dmFyIExuLEFuLERuLE5uLHFuLEJuLEhuLFVuLHpuPXt9LGpuPVtdLFduPS9hY2l0fGV4KD86c3xnfG58cHwkKXxycGh8Z3JpZHxvd3N8bW5jfG50d3xpbmVbY2hdfHpvb3xeb3JkfGl0ZXJhL2ksVm49QXJyYXkuaXNBcnJheTtmdW5jdGlvbiBHbihlLHQpe2Zvcih2YXIgaSBpbiB0KWVbaV09dFtpXTtyZXR1cm4gZX1mdW5jdGlvbiBKbihlKXt2YXIgdD1lLnBhcmVudE5vZGU7dCYmdC5yZW1vdmVDaGlsZChlKX1mdW5jdGlvbiBZbihlLHQsaSxyLG4pe3ZhciBzPXt0eXBlOmUscHJvcHM6dCxrZXk6aSxyZWY6cixfX2s6bnVsbCxfXzpudWxsLF9fYjowLF9fZTpudWxsLF9fZDp2b2lkIDAsX19jOm51bGwsY29uc3RydWN0b3I6dm9pZCAwLF9fdjpudWxsPT1uPysrRG46bixfX2k6LTEsX191OjB9O3JldHVybiBudWxsPT1uJiZudWxsIT1Bbi52bm9kZSYmQW4udm5vZGUocyksc31mdW5jdGlvbiBLbihlKXtyZXR1cm4gZS5jaGlsZHJlbn1mdW5jdGlvbiBYbihlLHQpe3RoaXMucHJvcHM9ZSx0aGlzLmNvbnRleHQ9dH1mdW5jdGlvbiBRbihlLHQpe2lmKG51bGw9PXQpcmV0dXJuIGUuX18/UW4oZS5fXyxlLl9faSsxKTpudWxsO2Zvcih2YXIgaTt0PGUuX19rLmxlbmd0aDt0KyspaWYobnVsbCE9KGk9ZS5fX2tbdF0pJiZudWxsIT1pLl9fZSlyZXR1cm4gaS5fX2U7cmV0dXJuXCJmdW5jdGlvblwiPT10eXBlb2YgZS50eXBlP1FuKGUpOm51bGx9ZnVuY3Rpb24gWm4oZSl7dmFyIHQsaTtpZihudWxsIT0oZT1lLl9fKSYmbnVsbCE9ZS5fX2Mpe2ZvcihlLl9fZT1lLl9fYy5iYXNlPW51bGwsdD0wO3Q8ZS5fX2subGVuZ3RoO3QrKylpZihudWxsIT0oaT1lLl9fa1t0XSkmJm51bGwhPWkuX19lKXtlLl9fZT1lLl9fYy5iYXNlPWkuX19lO2JyZWFrfXJldHVybiBabihlKX19ZnVuY3Rpb24gZXMoZSl7KCFlLl9fZCYmKGUuX19kPSEwKSYmTm4ucHVzaChlKSYmIXRzLl9fcisrfHxxbiE9PUFuLmRlYm91bmNlUmVuZGVyaW5nKSYmKChxbj1Bbi5kZWJvdW5jZVJlbmRlcmluZyl8fEJuKSh0cyl9ZnVuY3Rpb24gdHMoKXt2YXIgZSx0LGkscixuLHMsbyxhLGw7Zm9yKE5uLnNvcnQoSG4pO2U9Tm4uc2hpZnQoKTspZS5fX2QmJih0PU5uLmxlbmd0aCxyPXZvaWQgMCxzPShuPShpPWUpLl9fdikuX19lLGE9W10sbD1bXSwobz1pLl9fUCkmJigocj1Hbih7fSxuKSkuX192PW4uX192KzEsQW4udm5vZGUmJkFuLnZub2RlKHIpLGNzKG8scixuLGkuX19uLHZvaWQgMCE9PW8ub3duZXJTVkdFbGVtZW50LDMyJm4uX191P1tzXTpudWxsLGEsbnVsbD09cz9RbihuKTpzLCEhKDMyJm4uX191KSxsKSxyLl9fLl9fa1tyLl9faV09cixkcyhhLHIsbCksci5fX2UhPXMmJlpuKHIpKSxObi5sZW5ndGg+dCYmTm4uc29ydChIbikpO3RzLl9fcj0wfWZ1bmN0aW9uIGlzKGUsdCxpLHIsbixzLG8sYSxsLHUsYyl7dmFyIGQsaCxfLHAsdixnPXImJnIuX19rfHxqbixmPXQubGVuZ3RoO2ZvcihpLl9fZD1sLHJzKGksdCxnKSxsPWkuX19kLGQ9MDtkPGY7ZCsrKW51bGwhPShfPWkuX19rW2RdKSYmXCJib29sZWFuXCIhPXR5cGVvZiBfJiZcImZ1bmN0aW9uXCIhPXR5cGVvZiBfJiYoaD0tMT09PV8uX19pP3puOmdbXy5fX2ldfHx6bixfLl9faT1kLGNzKGUsXyxoLG4scyxvLGEsbCx1LGMpLHA9Xy5fX2UsXy5yZWYmJmgucmVmIT1fLnJlZiYmKGgucmVmJiZfcyhoLnJlZixudWxsLF8pLGMucHVzaChfLnJlZixfLl9fY3x8cCxfKSksbnVsbD09diYmbnVsbCE9cCYmKHY9cCksNjU1MzYmXy5fX3V8fGguX19rPT09Xy5fX2s/bD1ucyhfLGwsZSk6XCJmdW5jdGlvblwiPT10eXBlb2YgXy50eXBlJiZ2b2lkIDAhPT1fLl9fZD9sPV8uX19kOnAmJihsPXAubmV4dFNpYmxpbmcpLF8uX19kPXZvaWQgMCxfLl9fdSY9LTE5NjYwOSk7aS5fX2Q9bCxpLl9fZT12fWZ1bmN0aW9uIHJzKGUsdCxpKXt2YXIgcixuLHMsbyxhLGw9dC5sZW5ndGgsdT1pLmxlbmd0aCxjPXUsZD0wO2ZvcihlLl9faz1bXSxyPTA7cjxsO3IrKyludWxsIT0obj1lLl9fa1tyXT1udWxsPT0obj10W3JdKXx8XCJib29sZWFuXCI9PXR5cGVvZiBufHxcImZ1bmN0aW9uXCI9PXR5cGVvZiBuP251bGw6XCJzdHJpbmdcIj09dHlwZW9mIG58fFwibnVtYmVyXCI9PXR5cGVvZiBufHxcImJpZ2ludFwiPT10eXBlb2Ygbnx8bi5jb25zdHJ1Y3Rvcj09U3RyaW5nP1luKG51bGwsbixudWxsLG51bGwsbik6Vm4obik/WW4oS24se2NoaWxkcmVuOm59LG51bGwsbnVsbCxudWxsKTp2b2lkIDA9PT1uLmNvbnN0cnVjdG9yJiZuLl9fYj4wP1luKG4udHlwZSxuLnByb3BzLG4ua2V5LG4ucmVmP24ucmVmOm51bGwsbi5fX3YpOm4pPyhuLl9fPWUsbi5fX2I9ZS5fX2IrMSxhPXNzKG4saSxvPXIrZCxjKSxuLl9faT1hLHM9bnVsbCwtMSE9PWEmJihjLS0sKHM9aVthXSkmJihzLl9fdXw9MTMxMDcyKSksbnVsbD09c3x8bnVsbD09PXMuX192PygtMT09YSYmZC0tLFwiZnVuY3Rpb25cIiE9dHlwZW9mIG4udHlwZSYmKG4uX191fD02NTUzNikpOmEhPT1vJiYoYT09PW8rMT9kKys6YT5vP2M+bC1vP2QrPWEtbzpkLS06ZD1hPG8mJmE9PW8tMT9hLW86MCxhIT09citkJiYobi5fX3V8PTY1NTM2KSkpOihzPWlbcl0pJiZudWxsPT1zLmtleSYmcy5fX2UmJihzLl9fZT09ZS5fX2QmJihlLl9fZD1RbihzKSkscHMocyxzLCExKSxpW3JdPW51bGwsYy0tKTtpZihjKWZvcihyPTA7cjx1O3IrKyludWxsIT0ocz1pW3JdKSYmMD09KDEzMTA3MiZzLl9fdSkmJihzLl9fZT09ZS5fX2QmJihlLl9fZD1RbihzKSkscHMocyxzKSl9ZnVuY3Rpb24gbnMoZSx0LGkpe3ZhciByLG47aWYoXCJmdW5jdGlvblwiPT10eXBlb2YgZS50eXBlKXtmb3Iocj1lLl9fayxuPTA7ciYmbjxyLmxlbmd0aDtuKyspcltuXSYmKHJbbl0uX189ZSx0PW5zKHJbbl0sdCxpKSk7cmV0dXJuIHR9cmV0dXJuIGUuX19lIT10JiYoaS5pbnNlcnRCZWZvcmUoZS5fX2UsdHx8bnVsbCksdD1lLl9fZSksdCYmdC5uZXh0U2libGluZ31mdW5jdGlvbiBzcyhlLHQsaSxyKXt2YXIgbj1lLmtleSxzPWUudHlwZSxvPWktMSxhPWkrMSxsPXRbaV07aWYobnVsbD09PWx8fGwmJm49PWwua2V5JiZzPT09bC50eXBlKXJldHVybiBpO2lmKHI+KG51bGwhPWwmJjA9PSgxMzEwNzImbC5fX3UpPzE6MCkpZm9yKDtvPj0wfHxhPHQubGVuZ3RoOyl7aWYobz49MCl7aWYoKGw9dFtvXSkmJjA9PSgxMzEwNzImbC5fX3UpJiZuPT1sLmtleSYmcz09PWwudHlwZSlyZXR1cm4gbztvLS19aWYoYTx0Lmxlbmd0aCl7aWYoKGw9dFthXSkmJjA9PSgxMzEwNzImbC5fX3UpJiZuPT1sLmtleSYmcz09PWwudHlwZSlyZXR1cm4gYTthKyt9fXJldHVybi0xfWZ1bmN0aW9uIG9zKGUsdCxpKXtcIi1cIj09PXRbMF0/ZS5zZXRQcm9wZXJ0eSh0LG51bGw9PWk/XCJcIjppKTplW3RdPW51bGw9PWk/XCJcIjpcIm51bWJlclwiIT10eXBlb2YgaXx8V24udGVzdCh0KT9pOmkrXCJweFwifWZ1bmN0aW9uIGFzKGUsdCxpLHIsbil7dmFyIHM7ZTppZihcInN0eWxlXCI9PT10KWlmKFwic3RyaW5nXCI9PXR5cGVvZiBpKWUuc3R5bGUuY3NzVGV4dD1pO2Vsc2V7aWYoXCJzdHJpbmdcIj09dHlwZW9mIHImJihlLnN0eWxlLmNzc1RleHQ9cj1cIlwiKSxyKWZvcih0IGluIHIpaSYmdCBpbiBpfHxvcyhlLnN0eWxlLHQsXCJcIik7aWYoaSlmb3IodCBpbiBpKXImJmlbdF09PT1yW3RdfHxvcyhlLnN0eWxlLHQsaVt0XSl9ZWxzZSBpZihcIm9cIj09PXRbMF0mJlwiblwiPT09dFsxXSlzPXQhPT0odD10LnJlcGxhY2UoLyhQb2ludGVyQ2FwdHVyZSkkfENhcHR1cmUkLyxcIiQxXCIpKSx0PXQudG9Mb3dlckNhc2UoKWluIGU/dC50b0xvd2VyQ2FzZSgpLnNsaWNlKDIpOnQuc2xpY2UoMiksZS5sfHwoZS5sPXt9KSxlLmxbdCtzXT1pLGk/cj9pLnU9ci51OihpLnU9RGF0ZS5ub3coKSxlLmFkZEV2ZW50TGlzdGVuZXIodCxzP3VzOmxzLHMpKTplLnJlbW92ZUV2ZW50TGlzdGVuZXIodCxzP3VzOmxzLHMpO2Vsc2V7aWYobil0PXQucmVwbGFjZSgveGxpbmsoSHw6aCkvLFwiaFwiKS5yZXBsYWNlKC9zTmFtZSQvLFwic1wiKTtlbHNlIGlmKFwid2lkdGhcIiE9PXQmJlwiaGVpZ2h0XCIhPT10JiZcImhyZWZcIiE9PXQmJlwibGlzdFwiIT09dCYmXCJmb3JtXCIhPT10JiZcInRhYkluZGV4XCIhPT10JiZcImRvd25sb2FkXCIhPT10JiZcInJvd1NwYW5cIiE9PXQmJlwiY29sU3BhblwiIT09dCYmXCJyb2xlXCIhPT10JiZ0IGluIGUpdHJ5e2VbdF09bnVsbD09aT9cIlwiOmk7YnJlYWsgZX1jYXRjaChlKXt9XCJmdW5jdGlvblwiPT10eXBlb2YgaXx8KG51bGw9PWl8fCExPT09aSYmXCItXCIhPT10WzRdP2UucmVtb3ZlQXR0cmlidXRlKHQpOmUuc2V0QXR0cmlidXRlKHQsaSkpfX1mdW5jdGlvbiBscyhlKXt2YXIgdD10aGlzLmxbZS50eXBlKyExXTtpZihlLnQpe2lmKGUudDw9dC51KXJldHVybn1lbHNlIGUudD1EYXRlLm5vdygpO3JldHVybiB0KEFuLmV2ZW50P0FuLmV2ZW50KGUpOmUpfWZ1bmN0aW9uIHVzKGUpe3JldHVybiB0aGlzLmxbZS50eXBlKyEwXShBbi5ldmVudD9Bbi5ldmVudChlKTplKX1mdW5jdGlvbiBjcyhlLHQsaSxyLG4scyxvLGEsbCx1KXt2YXIgYyxkLGgsXyxwLHYsZyxmLG0sYix5LHcsUyxFLGsseD10LnR5cGU7aWYodm9pZCAwIT09dC5jb25zdHJ1Y3RvcilyZXR1cm4gbnVsbDsxMjgmaS5fX3UmJihsPSEhKDMyJmkuX191KSxzPVthPXQuX19lPWkuX19lXSksKGM9QW4uX19iKSYmYyh0KTtlOmlmKFwiZnVuY3Rpb25cIj09dHlwZW9mIHgpdHJ5e2lmKGY9dC5wcm9wcyxtPShjPXguY29udGV4dFR5cGUpJiZyW2MuX19jXSxiPWM/bT9tLnByb3BzLnZhbHVlOmMuX186cixpLl9fYz9nPShkPXQuX19jPWkuX19jKS5fXz1kLl9fRTooXCJwcm90b3R5cGVcImluIHgmJngucHJvdG90eXBlLnJlbmRlcj90Ll9fYz1kPW5ldyB4KGYsYik6KHQuX19jPWQ9bmV3IFhuKGYsYiksZC5jb25zdHJ1Y3Rvcj14LGQucmVuZGVyPXZzKSxtJiZtLnN1YihkKSxkLnByb3BzPWYsZC5zdGF0ZXx8KGQuc3RhdGU9e30pLGQuY29udGV4dD1iLGQuX19uPXIsaD1kLl9fZD0hMCxkLl9faD1bXSxkLl9zYj1bXSksbnVsbD09ZC5fX3MmJihkLl9fcz1kLnN0YXRlKSxudWxsIT14LmdldERlcml2ZWRTdGF0ZUZyb21Qcm9wcyYmKGQuX19zPT1kLnN0YXRlJiYoZC5fX3M9R24oe30sZC5fX3MpKSxHbihkLl9fcyx4LmdldERlcml2ZWRTdGF0ZUZyb21Qcm9wcyhmLGQuX19zKSkpLF89ZC5wcm9wcyxwPWQuc3RhdGUsZC5fX3Y9dCxoKW51bGw9PXguZ2V0RGVyaXZlZFN0YXRlRnJvbVByb3BzJiZudWxsIT1kLmNvbXBvbmVudFdpbGxNb3VudCYmZC5jb21wb25lbnRXaWxsTW91bnQoKSxudWxsIT1kLmNvbXBvbmVudERpZE1vdW50JiZkLl9faC5wdXNoKGQuY29tcG9uZW50RGlkTW91bnQpO2Vsc2V7aWYobnVsbD09eC5nZXREZXJpdmVkU3RhdGVGcm9tUHJvcHMmJmYhPT1fJiZudWxsIT1kLmNvbXBvbmVudFdpbGxSZWNlaXZlUHJvcHMmJmQuY29tcG9uZW50V2lsbFJlY2VpdmVQcm9wcyhmLGIpLCFkLl9fZSYmKG51bGwhPWQuc2hvdWxkQ29tcG9uZW50VXBkYXRlJiYhMT09PWQuc2hvdWxkQ29tcG9uZW50VXBkYXRlKGYsZC5fX3MsYil8fHQuX192PT09aS5fX3YpKXtmb3IodC5fX3YhPT1pLl9fdiYmKGQucHJvcHM9ZixkLnN0YXRlPWQuX19zLGQuX19kPSExKSx0Ll9fZT1pLl9fZSx0Ll9faz1pLl9fayx0Ll9fay5mb3JFYWNoKChmdW5jdGlvbihlKXtlJiYoZS5fXz10KX0pKSx5PTA7eTxkLl9zYi5sZW5ndGg7eSsrKWQuX19oLnB1c2goZC5fc2JbeV0pO2QuX3NiPVtdLGQuX19oLmxlbmd0aCYmby5wdXNoKGQpO2JyZWFrIGV9bnVsbCE9ZC5jb21wb25lbnRXaWxsVXBkYXRlJiZkLmNvbXBvbmVudFdpbGxVcGRhdGUoZixkLl9fcyxiKSxudWxsIT1kLmNvbXBvbmVudERpZFVwZGF0ZSYmZC5fX2gucHVzaCgoZnVuY3Rpb24oKXtkLmNvbXBvbmVudERpZFVwZGF0ZShfLHAsdil9KSl9aWYoZC5jb250ZXh0PWIsZC5wcm9wcz1mLGQuX19QPWUsZC5fX2U9ITEsdz1Bbi5fX3IsUz0wLFwicHJvdG90eXBlXCJpbiB4JiZ4LnByb3RvdHlwZS5yZW5kZXIpe2ZvcihkLnN0YXRlPWQuX19zLGQuX19kPSExLHcmJncodCksYz1kLnJlbmRlcihkLnByb3BzLGQuc3RhdGUsZC5jb250ZXh0KSxFPTA7RTxkLl9zYi5sZW5ndGg7RSsrKWQuX19oLnB1c2goZC5fc2JbRV0pO2QuX3NiPVtdfWVsc2UgZG97ZC5fX2Q9ITEsdyYmdyh0KSxjPWQucmVuZGVyKGQucHJvcHMsZC5zdGF0ZSxkLmNvbnRleHQpLGQuc3RhdGU9ZC5fX3N9d2hpbGUoZC5fX2QmJisrUzwyNSk7ZC5zdGF0ZT1kLl9fcyxudWxsIT1kLmdldENoaWxkQ29udGV4dCYmKHI9R24oR24oe30sciksZC5nZXRDaGlsZENvbnRleHQoKSkpLGh8fG51bGw9PWQuZ2V0U25hcHNob3RCZWZvcmVVcGRhdGV8fCh2PWQuZ2V0U25hcHNob3RCZWZvcmVVcGRhdGUoXyxwKSksaXMoZSxWbihrPW51bGwhPWMmJmMudHlwZT09PUtuJiZudWxsPT1jLmtleT9jLnByb3BzLmNoaWxkcmVuOmMpP2s6W2tdLHQsaSxyLG4scyxvLGEsbCx1KSxkLmJhc2U9dC5fX2UsdC5fX3UmPS0xNjEsZC5fX2gubGVuZ3RoJiZvLnB1c2goZCksZyYmKGQuX19FPWQuX189bnVsbCl9Y2F0Y2goZSl7dC5fX3Y9bnVsbCxsfHxudWxsIT1zPyh0Ll9fZT1hLHQuX191fD1sPzE2MDozMixzW3MuaW5kZXhPZihhKV09bnVsbCk6KHQuX19lPWkuX19lLHQuX19rPWkuX19rKSxBbi5fX2UoZSx0LGkpfWVsc2UgbnVsbD09cyYmdC5fX3Y9PT1pLl9fdj8odC5fX2s9aS5fX2ssdC5fX2U9aS5fX2UpOnQuX19lPWhzKGkuX19lLHQsaSxyLG4scyxvLGwsdSk7KGM9QW4uZGlmZmVkKSYmYyh0KX1mdW5jdGlvbiBkcyhlLHQsaSl7dC5fX2Q9dm9pZCAwO2Zvcih2YXIgcj0wO3I8aS5sZW5ndGg7cisrKV9zKGlbcl0saVsrK3JdLGlbKytyXSk7QW4uX19jJiZBbi5fX2ModCxlKSxlLnNvbWUoKGZ1bmN0aW9uKHQpe3RyeXtlPXQuX19oLHQuX19oPVtdLGUuc29tZSgoZnVuY3Rpb24oZSl7ZS5jYWxsKHQpfSkpfWNhdGNoKGUpe0FuLl9fZShlLHQuX192KX19KSl9ZnVuY3Rpb24gaHMoZSx0LGkscixuLHMsbyxhLGwpe3ZhciB1LGMsZCxoLF8scCx2LGc9aS5wcm9wcyxmPXQucHJvcHMsbT10LnR5cGU7aWYoXCJzdmdcIj09PW0mJihuPSEwKSxudWxsIT1zKWZvcih1PTA7dTxzLmxlbmd0aDt1KyspaWYoKF89c1t1XSkmJlwic2V0QXR0cmlidXRlXCJpbiBfPT0hIW0mJihtP18ubG9jYWxOYW1lPT09bTozPT09Xy5ub2RlVHlwZSkpe2U9XyxzW3VdPW51bGw7YnJlYWt9aWYobnVsbD09ZSl7aWYobnVsbD09PW0pcmV0dXJuIGRvY3VtZW50LmNyZWF0ZVRleHROb2RlKGYpO2U9bj9kb2N1bWVudC5jcmVhdGVFbGVtZW50TlMoXCJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Z1wiLG0pOmRvY3VtZW50LmNyZWF0ZUVsZW1lbnQobSxmLmlzJiZmKSxzPW51bGwsYT0hMX1pZihudWxsPT09bSlnPT09Znx8YSYmZS5kYXRhPT09Znx8KGUuZGF0YT1mKTtlbHNle2lmKHM9cyYmTG4uY2FsbChlLmNoaWxkTm9kZXMpLGc9aS5wcm9wc3x8em4sIWEmJm51bGwhPXMpZm9yKGc9e30sdT0wO3U8ZS5hdHRyaWJ1dGVzLmxlbmd0aDt1KyspZ1soXz1lLmF0dHJpYnV0ZXNbdV0pLm5hbWVdPV8udmFsdWU7Zm9yKHUgaW4gZylfPWdbdV0sXCJjaGlsZHJlblwiPT11fHwoXCJkYW5nZXJvdXNseVNldElubmVySFRNTFwiPT11P2Q9XzpcImtleVwiPT09dXx8dSBpbiBmfHxhcyhlLHUsbnVsbCxfLG4pKTtmb3IodSBpbiBmKV89Zlt1XSxcImNoaWxkcmVuXCI9PXU/aD1fOlwiZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUxcIj09dT9jPV86XCJ2YWx1ZVwiPT11P3A9XzpcImNoZWNrZWRcIj09dT92PV86XCJrZXlcIj09PXV8fGEmJlwiZnVuY3Rpb25cIiE9dHlwZW9mIF98fGdbdV09PT1ffHxhcyhlLHUsXyxnW3VdLG4pO2lmKGMpYXx8ZCYmKGMuX19odG1sPT09ZC5fX2h0bWx8fGMuX19odG1sPT09ZS5pbm5lckhUTUwpfHwoZS5pbm5lckhUTUw9Yy5fX2h0bWwpLHQuX19rPVtdO2Vsc2UgaWYoZCYmKGUuaW5uZXJIVE1MPVwiXCIpLGlzKGUsVm4oaCk/aDpbaF0sdCxpLHIsbiYmXCJmb3JlaWduT2JqZWN0XCIhPT1tLHMsbyxzP3NbMF06aS5fX2smJlFuKGksMCksYSxsKSxudWxsIT1zKWZvcih1PXMubGVuZ3RoO3UtLTspbnVsbCE9c1t1XSYmSm4oc1t1XSk7YXx8KHU9XCJ2YWx1ZVwiLHZvaWQgMCE9PXAmJihwIT09ZVt1XXx8XCJwcm9ncmVzc1wiPT09bSYmIXB8fFwib3B0aW9uXCI9PT1tJiZwIT09Z1t1XSkmJmFzKGUsdSxwLGdbdV0sITEpLHU9XCJjaGVja2VkXCIsdm9pZCAwIT09diYmdiE9PWVbdV0mJmFzKGUsdSx2LGdbdV0sITEpKX1yZXR1cm4gZX1mdW5jdGlvbiBfcyhlLHQsaSl7dHJ5e1wiZnVuY3Rpb25cIj09dHlwZW9mIGU/ZSh0KTplLmN1cnJlbnQ9dH1jYXRjaChlKXtBbi5fX2UoZSxpKX19ZnVuY3Rpb24gcHMoZSx0LGkpe3ZhciByLG47aWYoQW4udW5tb3VudCYmQW4udW5tb3VudChlKSwocj1lLnJlZikmJihyLmN1cnJlbnQmJnIuY3VycmVudCE9PWUuX19lfHxfcyhyLG51bGwsdCkpLG51bGwhPShyPWUuX19jKSl7aWYoci5jb21wb25lbnRXaWxsVW5tb3VudCl0cnl7ci5jb21wb25lbnRXaWxsVW5tb3VudCgpfWNhdGNoKGUpe0FuLl9fZShlLHQpfXIuYmFzZT1yLl9fUD1udWxsLGUuX19jPXZvaWQgMH1pZihyPWUuX19rKWZvcihuPTA7bjxyLmxlbmd0aDtuKyspcltuXSYmcHMocltuXSx0LGl8fFwiZnVuY3Rpb25cIiE9dHlwZW9mIGUudHlwZSk7aXx8bnVsbD09ZS5fX2V8fEpuKGUuX19lKSxlLl9fPWUuX19lPWUuX19kPXZvaWQgMH1mdW5jdGlvbiB2cyhlLHQsaSl7cmV0dXJuIHRoaXMuY29uc3RydWN0b3IoZSxpKX1Mbj1qbi5zbGljZSxBbj17X19lOmZ1bmN0aW9uKGUsdCxpLHIpe2Zvcih2YXIgbixzLG87dD10Ll9fOylpZigobj10Ll9fYykmJiFuLl9fKXRyeXtpZigocz1uLmNvbnN0cnVjdG9yKSYmbnVsbCE9cy5nZXREZXJpdmVkU3RhdGVGcm9tRXJyb3ImJihuLnNldFN0YXRlKHMuZ2V0RGVyaXZlZFN0YXRlRnJvbUVycm9yKGUpKSxvPW4uX19kKSxudWxsIT1uLmNvbXBvbmVudERpZENhdGNoJiYobi5jb21wb25lbnREaWRDYXRjaChlLHJ8fHt9KSxvPW4uX19kKSxvKXJldHVybiBuLl9fRT1ufWNhdGNoKHQpe2U9dH10aHJvdyBlfX0sRG49MCxYbi5wcm90b3R5cGUuc2V0U3RhdGU9ZnVuY3Rpb24oZSx0KXt2YXIgaTtpPW51bGwhPXRoaXMuX19zJiZ0aGlzLl9fcyE9PXRoaXMuc3RhdGU/dGhpcy5fX3M6dGhpcy5fX3M9R24oe30sdGhpcy5zdGF0ZSksXCJmdW5jdGlvblwiPT10eXBlb2YgZSYmKGU9ZShHbih7fSxpKSx0aGlzLnByb3BzKSksZSYmR24oaSxlKSxudWxsIT1lJiZ0aGlzLl9fdiYmKHQmJnRoaXMuX3NiLnB1c2godCksZXModGhpcykpfSxYbi5wcm90b3R5cGUuZm9yY2VVcGRhdGU9ZnVuY3Rpb24oZSl7dGhpcy5fX3YmJih0aGlzLl9fZT0hMCxlJiZ0aGlzLl9faC5wdXNoKGUpLGVzKHRoaXMpKX0sWG4ucHJvdG90eXBlLnJlbmRlcj1LbixObj1bXSxCbj1cImZ1bmN0aW9uXCI9PXR5cGVvZiBQcm9taXNlP1Byb21pc2UucHJvdG90eXBlLnRoZW4uYmluZChQcm9taXNlLnJlc29sdmUoKSk6c2V0VGltZW91dCxIbj1mdW5jdGlvbihlLHQpe3JldHVybiBlLl9fdi5fX2ItdC5fX3YuX19ifSx0cy5fX3I9MCxVbj0wO3ZhciBncyxmcyxtczshZnVuY3Rpb24oZSx0KXt2YXIgaT17X19jOnQ9XCJfX2NDXCIrVW4rKyxfXzplLENvbnN1bWVyOmZ1bmN0aW9uKGUsdCl7cmV0dXJuIGUuY2hpbGRyZW4odCl9LFByb3ZpZGVyOmZ1bmN0aW9uKGUpe3ZhciBpLHI7cmV0dXJuIHRoaXMuZ2V0Q2hpbGRDb250ZXh0fHwoaT1bXSwocj17fSlbdF09dGhpcyx0aGlzLmdldENoaWxkQ29udGV4dD1mdW5jdGlvbigpe3JldHVybiByfSx0aGlzLnNob3VsZENvbXBvbmVudFVwZGF0ZT1mdW5jdGlvbihlKXt0aGlzLnByb3BzLnZhbHVlIT09ZS52YWx1ZSYmaS5zb21lKChmdW5jdGlvbihlKXtlLl9fZT0hMCxlcyhlKX0pKX0sdGhpcy5zdWI9ZnVuY3Rpb24oZSl7aS5wdXNoKGUpO3ZhciB0PWUuY29tcG9uZW50V2lsbFVubW91bnQ7ZS5jb21wb25lbnRXaWxsVW5tb3VudD1mdW5jdGlvbigpe2kuc3BsaWNlKGkuaW5kZXhPZihlKSwxKSx0JiZ0LmNhbGwoZSl9fSksZS5jaGlsZHJlbn19O2kuUHJvdmlkZXIuX189aS5Db25zdW1lci5jb250ZXh0VHlwZT1pfSh7aXNQcmV2aWV3TW9kZTohMSxwcmV2aWV3UGFnZUluZGV4OjAsaGFuZGxlQ2xvc2VTdXJ2ZXlQb3B1cDooKT0+e30saXNQb3B1cDohMCxvblByZXZpZXdTdWJtaXQ6KCk9Pnt9fSksZnVuY3Rpb24oZSl7ZS5Qb3BvdmVyPVwicG9wb3ZlclwiLGUuQVBJPVwiYXBpXCIsZS5XaWRnZXQ9XCJ3aWRnZXRcIn0oZ3N8fChncz17fSkpLGZ1bmN0aW9uKGUpe2UuT3Blbj1cIm9wZW5cIixlLk11bHRpcGxlQ2hvaWNlPVwibXVsdGlwbGVfY2hvaWNlXCIsZS5TaW5nbGVDaG9pY2U9XCJzaW5nbGVfY2hvaWNlXCIsZS5SYXRpbmc9XCJyYXRpbmdcIixlLkxpbms9XCJsaW5rXCJ9KGZzfHwoZnM9e30pKSxmdW5jdGlvbihlKXtlLk5leHRRdWVzdGlvbj1cIm5leHRfcXVlc3Rpb25cIixlLkVuZD1cImVuZFwiLGUuUmVzcG9uc2VCYXNlZD1cInJlc3BvbnNlX2Jhc2VkXCIsZS5TcGVjaWZpY1F1ZXN0aW9uPVwic3BlY2lmaWNfcXVlc3Rpb25cIn0obXN8fChtcz17fSkpO2NsYXNzIGJze2NvbnN0cnVjdG9yKCl7Vyh0aGlzLFwiZXZlbnRzXCIse30pLHRoaXMuZXZlbnRzPXt9fW9uKGUsdCl7cmV0dXJuIHRoaXMuZXZlbnRzW2VdfHwodGhpcy5ldmVudHNbZV09W10pLHRoaXMuZXZlbnRzW2VdLnB1c2godCksKCk9Pnt0aGlzLmV2ZW50c1tlXT10aGlzLmV2ZW50c1tlXS5maWx0ZXIoKGU9PmUhPT10KSl9fWVtaXQoZSx0KXtmb3IodmFyIGkgb2YgdGhpcy5ldmVudHNbZV18fFtdKWkodCk7Zm9yKHZhciByIG9mIHRoaXMuZXZlbnRzW1wiKlwiXXx8W10pcihlLHQpfX1jbGFzcyB5c3tjb25zdHJ1Y3RvcihlKXtXKHRoaXMsXCJfZGVidWdFdmVudEVtaXR0ZXJcIixuZXcgYnMpLFcodGhpcyxcImNoZWNrU3RlcFwiLCgoZSx0KT0+dGhpcy5jaGVja1N0ZXBFdmVudChlLHQpJiZ0aGlzLmNoZWNrU3RlcFVybChlLHQpJiZ0aGlzLmNoZWNrU3RlcEVsZW1lbnQoZSx0KSkpLFcodGhpcyxcImNoZWNrU3RlcEV2ZW50XCIsKChlLHQpPT5udWxsPT10fHwhdC5ldmVudHx8KG51bGw9PWU/dm9pZCAwOmUuZXZlbnQpPT09KG51bGw9PXQ/dm9pZCAwOnQuZXZlbnQpKSksdGhpcy5pbnN0YW5jZT1lLHRoaXMuYWN0aW9uRXZlbnRzPW5ldyBTZXQsdGhpcy5hY3Rpb25SZWdpc3RyeT1uZXcgU2V0fWluaXQoKXt2YXIgZTtpZighRihudWxsPT09KGU9dGhpcy5pbnN0YW5jZSl8fHZvaWQgMD09PWU/dm9pZCAwOmUuX2FkZENhcHR1cmVIb29rKSl7dmFyIHQ7bnVsbD09PSh0PXRoaXMuaW5zdGFuY2UpfHx2b2lkIDA9PT10fHx0Ll9hZGRDYXB0dXJlSG9vaygoKGUsdCk9Pnt0aGlzLm9uKGUsdCl9KSl9fXJlZ2lzdGVyKGUpe3ZhciB0LGk7aWYoIUYobnVsbD09PSh0PXRoaXMuaW5zdGFuY2UpfHx2b2lkIDA9PT10P3ZvaWQgMDp0Ll9hZGRDYXB0dXJlSG9vaykmJihlLmZvckVhY2goKGU9Pnt2YXIgdCxpO251bGw9PT0odD10aGlzLmFjdGlvblJlZ2lzdHJ5KXx8dm9pZCAwPT09dHx8dC5hZGQoZSksbnVsbD09PShpPWUuc3RlcHMpfHx2b2lkIDA9PT1pfHxpLmZvckVhY2goKGU9Pnt2YXIgdDtudWxsPT09KHQ9dGhpcy5hY3Rpb25FdmVudHMpfHx2b2lkIDA9PT10fHx0LmFkZCgobnVsbD09ZT92b2lkIDA6ZS5ldmVudCl8fFwiXCIpfSkpfSkpLG51bGwhPT0oaT10aGlzLmluc3RhbmNlKSYmdm9pZCAwIT09aSYmaS5hdXRvY2FwdHVyZSkpe3ZhciByLG49bmV3IFNldDtlLmZvckVhY2goKGU9Pnt2YXIgdDtudWxsPT09KHQ9ZS5zdGVwcyl8fHZvaWQgMD09PXR8fHQuZm9yRWFjaCgoZT0+e251bGwhPWUmJmUuc2VsZWN0b3ImJm4uYWRkKG51bGw9PWU/dm9pZCAwOmUuc2VsZWN0b3IpfSkpfSkpLG51bGw9PT0ocj10aGlzLmluc3RhbmNlKXx8dm9pZCAwPT09cnx8ci5hdXRvY2FwdHVyZS5zZXRFbGVtZW50U2VsZWN0b3JzKG4pfX1vbihlLHQpe3ZhciBpO251bGwhPXQmJjAhPWUubGVuZ3RoJiYodGhpcy5hY3Rpb25FdmVudHMuaGFzKGUpfHx0aGlzLmFjdGlvbkV2ZW50cy5oYXMobnVsbD09dD92b2lkIDA6dC5ldmVudCkpJiZ0aGlzLmFjdGlvblJlZ2lzdHJ5JiYobnVsbD09PShpPXRoaXMuYWN0aW9uUmVnaXN0cnkpfHx2b2lkIDA9PT1pP3ZvaWQgMDppLnNpemUpPjAmJnRoaXMuYWN0aW9uUmVnaXN0cnkuZm9yRWFjaCgoZT0+e3RoaXMuY2hlY2tBY3Rpb24odCxlKSYmdGhpcy5fZGVidWdFdmVudEVtaXR0ZXIuZW1pdChcImFjdGlvbkNhcHR1cmVkXCIsZS5uYW1lKX0pKX1fYWRkQWN0aW9uSG9vayhlKXt0aGlzLm9uQWN0aW9uKFwiYWN0aW9uQ2FwdHVyZWRcIiwodD0+ZSh0KSkpfWNoZWNrQWN0aW9uKGUsdCl7aWYobnVsbD09KG51bGw9PXQ/dm9pZCAwOnQuc3RlcHMpKXJldHVybiExO2Zvcih2YXIgaSBvZiB0LnN0ZXBzKWlmKHRoaXMuY2hlY2tTdGVwKGUsaSkpcmV0dXJuITA7cmV0dXJuITF9b25BY3Rpb24oZSx0KXtyZXR1cm4gdGhpcy5fZGVidWdFdmVudEVtaXR0ZXIub24oZSx0KX1jaGVja1N0ZXBVcmwoZSx0KXtpZihudWxsIT10JiZ0LnVybCl7dmFyIGkscj1udWxsPT1lfHxudWxsPT09KGk9ZS5wcm9wZXJ0aWVzKXx8dm9pZCAwPT09aT92b2lkIDA6aS4kY3VycmVudF91cmw7aWYoIXJ8fFwic3RyaW5nXCIhPXR5cGVvZiByKXJldHVybiExO2lmKCF5cy5tYXRjaFN0cmluZyhyLG51bGw9PXQ/dm9pZCAwOnQudXJsLChudWxsPT10P3ZvaWQgMDp0LnVybF9tYXRjaGluZyl8fFwiY29udGFpbnNcIikpcmV0dXJuITF9cmV0dXJuITB9c3RhdGljIG1hdGNoU3RyaW5nKGUsaSxyKXtzd2l0Y2gocil7Y2FzZVwicmVnZXhcIjpyZXR1cm4hIXQmJmZ0KGUsaSk7Y2FzZVwiZXhhY3RcIjpyZXR1cm4gaT09PWU7Y2FzZVwiY29udGFpbnNcIjp2YXIgbj15cy5lc2NhcGVTdHJpbmdSZWdleHAoaSkucmVwbGFjZSgvXy9nLFwiLlwiKS5yZXBsYWNlKC8lL2csXCIuKlwiKTtyZXR1cm4gZnQoZSxuKTtkZWZhdWx0OnJldHVybiExfX1zdGF0aWMgZXNjYXBlU3RyaW5nUmVnZXhwKGUpe3JldHVybiBlLnJlcGxhY2UoL1t8XFxcXHt9KClbXFxdXiQrKj8uXS9nLFwiXFxcXCQmXCIpLnJlcGxhY2UoLy0vZyxcIlxcXFx4MmRcIil9Y2hlY2tTdGVwRWxlbWVudChlLHQpe2lmKChudWxsIT10JiZ0LmhyZWZ8fG51bGwhPXQmJnQudGFnX25hbWV8fG51bGwhPXQmJnQudGV4dCkmJiF0aGlzLmdldEVsZW1lbnRzTGlzdChlKS5zb21lKChlPT4hKG51bGwhPXQmJnQuaHJlZiYmIXlzLm1hdGNoU3RyaW5nKGUuaHJlZnx8XCJcIixudWxsPT10P3ZvaWQgMDp0LmhyZWYsKG51bGw9PXQ/dm9pZCAwOnQuaHJlZl9tYXRjaGluZyl8fFwiZXhhY3RcIikpJiYoKG51bGw9PXR8fCF0LnRhZ19uYW1lfHxlLnRhZ19uYW1lPT09KG51bGw9PXQ/dm9pZCAwOnQudGFnX25hbWUpKSYmIShudWxsIT10JiZ0LnRleHQmJiF5cy5tYXRjaFN0cmluZyhlLnRleHR8fFwiXCIsbnVsbD09dD92b2lkIDA6dC50ZXh0LChudWxsPT10P3ZvaWQgMDp0LnRleHRfbWF0Y2hpbmcpfHxcImV4YWN0XCIpJiYheXMubWF0Y2hTdHJpbmcoZS4kZWxfdGV4dHx8XCJcIixudWxsPT10P3ZvaWQgMDp0LnRleHQsKG51bGw9PXQ/dm9pZCAwOnQudGV4dF9tYXRjaGluZyl8fFwiZXhhY3RcIikpKSkpKXJldHVybiExO2lmKG51bGwhPXQmJnQuc2VsZWN0b3Ipe3ZhciBpLHI9bnVsbD09ZXx8bnVsbD09PShpPWUucHJvcGVydGllcyl8fHZvaWQgMD09PWk/dm9pZCAwOmkuJGVsZW1lbnRfc2VsZWN0b3JzO2lmKCFyKXJldHVybiExO2lmKCFyLmluY2x1ZGVzKG51bGw9PXQ/dm9pZCAwOnQuc2VsZWN0b3IpKXJldHVybiExfXJldHVybiEwfWdldEVsZW1lbnRzTGlzdChlKXtyZXR1cm4gbnVsbD09KG51bGw9PWU/dm9pZCAwOmUucHJvcGVydGllcy4kZWxlbWVudHMpP1tdOm51bGw9PWU/dm9pZCAwOmUucHJvcGVydGllcy4kZWxlbWVudHN9fWNsYXNzIHdze2NvbnN0cnVjdG9yKGUpe3RoaXMuaW5zdGFuY2U9ZSx0aGlzLmV2ZW50VG9TdXJ2ZXlzPW5ldyBNYXAsdGhpcy5hY3Rpb25Ub1N1cnZleXM9bmV3IE1hcH1yZWdpc3RlcihlKXt2YXIgdDtGKG51bGw9PT0odD10aGlzLmluc3RhbmNlKXx8dm9pZCAwPT09dD92b2lkIDA6dC5fYWRkQ2FwdHVyZUhvb2spfHwodGhpcy5zZXR1cEV2ZW50QmFzZWRTdXJ2ZXlzKGUpLHRoaXMuc2V0dXBBY3Rpb25CYXNlZFN1cnZleXMoZSkpfXNldHVwQWN0aW9uQmFzZWRTdXJ2ZXlzKGUpe3ZhciB0PWUuZmlsdGVyKChlPT57dmFyIHQsaSxyLG47cmV0dXJuKG51bGw9PT0odD1lLmNvbmRpdGlvbnMpfHx2b2lkIDA9PT10P3ZvaWQgMDp0LmFjdGlvbnMpJiYobnVsbD09PShpPWUuY29uZGl0aW9ucyl8fHZvaWQgMD09PWl8fG51bGw9PT0ocj1pLmFjdGlvbnMpfHx2b2lkIDA9PT1yfHxudWxsPT09KG49ci52YWx1ZXMpfHx2b2lkIDA9PT1uP3ZvaWQgMDpuLmxlbmd0aCk+MH0pKTtpZigwIT09dC5sZW5ndGgpe2lmKG51bGw9PXRoaXMuYWN0aW9uTWF0Y2hlcil7dGhpcy5hY3Rpb25NYXRjaGVyPW5ldyB5cyh0aGlzLmluc3RhbmNlKSx0aGlzLmFjdGlvbk1hdGNoZXIuaW5pdCgpO3RoaXMuYWN0aW9uTWF0Y2hlci5fYWRkQWN0aW9uSG9vaygoZT0+e3RoaXMub25BY3Rpb24oZSl9KSl9dC5mb3JFYWNoKChlPT57dmFyIHQsaSxyLG4scyxvLGEsbCx1LGM7ZS5jb25kaXRpb25zJiZudWxsIT09KHQ9ZS5jb25kaXRpb25zKSYmdm9pZCAwIT09dCYmdC5hY3Rpb25zJiZudWxsIT09KGk9ZS5jb25kaXRpb25zKSYmdm9pZCAwIT09aSYmbnVsbCE9PShyPWkuYWN0aW9ucykmJnZvaWQgMCE9PXImJnIudmFsdWVzJiYobnVsbD09PShuPWUuY29uZGl0aW9ucyl8fHZvaWQgMD09PW58fG51bGw9PT0ocz1uLmFjdGlvbnMpfHx2b2lkIDA9PT1zfHxudWxsPT09KG89cy52YWx1ZXMpfHx2b2lkIDA9PT1vP3ZvaWQgMDpvLmxlbmd0aCk+MCYmKG51bGw9PT0oYT10aGlzLmFjdGlvbk1hdGNoZXIpfHx2b2lkIDA9PT1hfHxhLnJlZ2lzdGVyKGUuY29uZGl0aW9ucy5hY3Rpb25zLnZhbHVlcyksbnVsbD09PShsPWUuY29uZGl0aW9ucyl8fHZvaWQgMD09PWx8fG51bGw9PT0odT1sLmFjdGlvbnMpfHx2b2lkIDA9PT11fHxudWxsPT09KGM9dS52YWx1ZXMpfHx2b2lkIDA9PT1jfHxjLmZvckVhY2goKHQ9PntpZih0JiZ0Lm5hbWUpe3ZhciBpPXRoaXMuYWN0aW9uVG9TdXJ2ZXlzLmdldCh0Lm5hbWUpO2kmJmkucHVzaChlLmlkKSx0aGlzLmFjdGlvblRvU3VydmV5cy5zZXQodC5uYW1lLGl8fFtlLmlkXSl9fSkpKX0pKX19c2V0dXBFdmVudEJhc2VkU3VydmV5cyhlKXt2YXIgdDtpZigwIT09ZS5maWx0ZXIoKGU9Pnt2YXIgdCxpLHIsbjtyZXR1cm4obnVsbD09PSh0PWUuY29uZGl0aW9ucyl8fHZvaWQgMD09PXQ/dm9pZCAwOnQuZXZlbnRzKSYmKG51bGw9PT0oaT1lLmNvbmRpdGlvbnMpfHx2b2lkIDA9PT1pfHxudWxsPT09KHI9aS5ldmVudHMpfHx2b2lkIDA9PT1yfHxudWxsPT09KG49ci52YWx1ZXMpfHx2b2lkIDA9PT1uP3ZvaWQgMDpuLmxlbmd0aCk+MH0pKS5sZW5ndGgpe251bGw9PT0odD10aGlzLmluc3RhbmNlKXx8dm9pZCAwPT09dHx8dC5fYWRkQ2FwdHVyZUhvb2soKChlLHQpPT57dGhpcy5vbkV2ZW50KGUsdCl9KSksZS5mb3JFYWNoKChlPT57dmFyIHQsaSxyO251bGw9PT0odD1lLmNvbmRpdGlvbnMpfHx2b2lkIDA9PT10fHxudWxsPT09KGk9dC5ldmVudHMpfHx2b2lkIDA9PT1pfHxudWxsPT09KHI9aS52YWx1ZXMpfHx2b2lkIDA9PT1yfHxyLmZvckVhY2goKHQ9PntpZih0JiZ0Lm5hbWUpe3ZhciBpPXRoaXMuZXZlbnRUb1N1cnZleXMuZ2V0KHQubmFtZSk7aSYmaS5wdXNoKGUuaWQpLHRoaXMuZXZlbnRUb1N1cnZleXMuc2V0KHQubmFtZSxpfHxbZS5pZF0pfX0pKX0pKX19b25FdmVudChlLHQpe3ZhciBpLHIsbj0obnVsbD09PShpPXRoaXMuaW5zdGFuY2UpfHx2b2lkIDA9PT1pfHxudWxsPT09KHI9aS5wZXJzaXN0ZW5jZSl8fHZvaWQgMD09PXI/dm9pZCAwOnIucHJvcHNbUmVdKXx8W107aWYod3MuU1VSVkVZX1NIT1dOX0VWRU5UX05BTUU9PWUmJnQmJm4ubGVuZ3RoPjApe3ZhciBzLG89bnVsbD09dHx8bnVsbD09PShzPXQucHJvcGVydGllcyl8fHZvaWQgMD09PXM/dm9pZCAwOnMuJHN1cnZleV9pZDtpZihvKXt2YXIgYT1uLmluZGV4T2Yobyk7YT49MCYmKG4uc3BsaWNlKGEsMSksdGhpcy5fdXBkYXRlQWN0aXZhdGVkU3VydmV5cyhuKSl9fWVsc2UgdGhpcy5ldmVudFRvU3VydmV5cy5oYXMoZSkmJnRoaXMuX3VwZGF0ZUFjdGl2YXRlZFN1cnZleXMobi5jb25jYXQodGhpcy5ldmVudFRvU3VydmV5cy5nZXQoZSl8fFtdKSl9b25BY3Rpb24oZSl7dmFyIHQsaSxyPShudWxsPT09KHQ9dGhpcy5pbnN0YW5jZSl8fHZvaWQgMD09PXR8fG51bGw9PT0oaT10LnBlcnNpc3RlbmNlKXx8dm9pZCAwPT09aT92b2lkIDA6aS5wcm9wc1tSZV0pfHxbXTt0aGlzLmFjdGlvblRvU3VydmV5cy5oYXMoZSkmJnRoaXMuX3VwZGF0ZUFjdGl2YXRlZFN1cnZleXMoci5jb25jYXQodGhpcy5hY3Rpb25Ub1N1cnZleXMuZ2V0KGUpfHxbXSkpfV91cGRhdGVBY3RpdmF0ZWRTdXJ2ZXlzKGUpe3ZhciB0LGk7bnVsbD09PSh0PXRoaXMuaW5zdGFuY2UpfHx2b2lkIDA9PT10fHxudWxsPT09KGk9dC5wZXJzaXN0ZW5jZSl8fHZvaWQgMD09PWl8fGkucmVnaXN0ZXIoe1tSZV06Wy4uLm5ldyBTZXQoZSldfSl9Z2V0U3VydmV5cygpe3ZhciBlLHQsaT1udWxsPT09KGU9dGhpcy5pbnN0YW5jZSl8fHZvaWQgMD09PWV8fG51bGw9PT0odD1lLnBlcnNpc3RlbmNlKXx8dm9pZCAwPT09dD92b2lkIDA6dC5wcm9wc1tSZV07cmV0dXJuIGl8fFtdfWdldEV2ZW50VG9TdXJ2ZXlzKCl7cmV0dXJuIHRoaXMuZXZlbnRUb1N1cnZleXN9X2dldEFjdGlvbk1hdGNoZXIoKXtyZXR1cm4gdGhpcy5hY3Rpb25NYXRjaGVyfX1XKHdzLFwiU1VSVkVZX1NIT1dOX0VWRU5UX05BTUVcIixcInN1cnZleSBzaG93blwiKTt2YXIgU3M9QihcIltTdXJ2ZXlzXVwiKSxFcz17aWNvbnRhaW5zOmU9PiEhdCYmdC5sb2NhdGlvbi5ocmVmLnRvTG93ZXJDYXNlKCkuaW5kZXhPZihlLnRvTG93ZXJDYXNlKCkpPi0xLG5vdF9pY29udGFpbnM6ZT0+ISF0JiYtMT09PXQubG9jYXRpb24uaHJlZi50b0xvd2VyQ2FzZSgpLmluZGV4T2YoZS50b0xvd2VyQ2FzZSgpKSxyZWdleDplPT4hIXQmJmZ0KHQubG9jYXRpb24uaHJlZixlKSxub3RfcmVnZXg6ZT0+ISF0JiYhZnQodC5sb2NhdGlvbi5ocmVmLGUpLGV4YWN0OmU9PihudWxsPT10P3ZvaWQgMDp0LmxvY2F0aW9uLmhyZWYpPT09ZSxpc19ub3Q6ZT0+KG51bGw9PXQ/dm9pZCAwOnQubG9jYXRpb24uaHJlZikhPT1lfTtmdW5jdGlvbiBrcyhlLHQsaSl7dmFyIHIsbj1lLnF1ZXN0aW9uc1t0XSxzPXQrMTtpZihudWxsPT09KHI9bi5icmFuY2hpbmcpfHx2b2lkIDA9PT1yfHwhci50eXBlKXJldHVybiB0PT09ZS5xdWVzdGlvbnMubGVuZ3RoLTE/bXMuRW5kOnM7aWYobi5icmFuY2hpbmcudHlwZT09PW1zLkVuZClyZXR1cm4gbXMuRW5kO2lmKG4uYnJhbmNoaW5nLnR5cGU9PT1tcy5TcGVjaWZpY1F1ZXN0aW9uKXtpZihOdW1iZXIuaXNJbnRlZ2VyKG4uYnJhbmNoaW5nLmluZGV4KSlyZXR1cm4gbi5icmFuY2hpbmcuaW5kZXh9ZWxzZSBpZihuLmJyYW5jaGluZy50eXBlPT09bXMuUmVzcG9uc2VCYXNlZCl7aWYobi50eXBlPT09ZnMuU2luZ2xlQ2hvaWNlKXt2YXIgbyxhLGw9bi5jaG9pY2VzLmluZGV4T2YoXCJcIi5jb25jYXQoaSkpO2lmKG51bGwhPT0obz1uLmJyYW5jaGluZykmJnZvaWQgMCE9PW8mJm51bGwhPT0oYT1vLnJlc3BvbnNlVmFsdWVzKSYmdm9pZCAwIT09YSYmYS5oYXNPd25Qcm9wZXJ0eShsKSl7dmFyIHU9bi5icmFuY2hpbmcucmVzcG9uc2VWYWx1ZXNbbF07cmV0dXJuIE51bWJlci5pc0ludGVnZXIodSk/dTp1PT09bXMuRW5kP21zLkVuZDpzfX1lbHNlIGlmKG4udHlwZT09PWZzLlJhdGluZyl7dmFyIGMsZDtpZihcIm51bWJlclwiIT10eXBlb2YgaXx8IU51bWJlci5pc0ludGVnZXIoaSkpdGhyb3cgbmV3IEVycm9yKFwiVGhlIHJlc3BvbnNlIHR5cGUgbXVzdCBiZSBhbiBpbnRlZ2VyXCIpO3ZhciBoPWZ1bmN0aW9uKGUsdCl7aWYoMz09PXQpe2lmKGU8MXx8ZT4zKXRocm93IG5ldyBFcnJvcihcIlRoZSByZXNwb25zZSBtdXN0IGJlIGluIHJhbmdlIDEtM1wiKTtyZXR1cm4gMT09PWU/XCJuZWdhdGl2ZVwiOjI9PT1lP1wibmV1dHJhbFwiOlwicG9zaXRpdmVcIn1pZig1PT09dCl7aWYoZTwxfHxlPjUpdGhyb3cgbmV3IEVycm9yKFwiVGhlIHJlc3BvbnNlIG11c3QgYmUgaW4gcmFuZ2UgMS01XCIpO3JldHVybiBlPD0yP1wibmVnYXRpdmVcIjozPT09ZT9cIm5ldXRyYWxcIjpcInBvc2l0aXZlXCJ9aWYoNz09PXQpe2lmKGU8MXx8ZT43KXRocm93IG5ldyBFcnJvcihcIlRoZSByZXNwb25zZSBtdXN0IGJlIGluIHJhbmdlIDEtN1wiKTtyZXR1cm4gZTw9Mz9cIm5lZ2F0aXZlXCI6ND09PWU/XCJuZXV0cmFsXCI6XCJwb3NpdGl2ZVwifWlmKDEwPT09dCl7aWYoZTwwfHxlPjEwKXRocm93IG5ldyBFcnJvcihcIlRoZSByZXNwb25zZSBtdXN0IGJlIGluIHJhbmdlIDAtMTBcIik7cmV0dXJuIGU8PTY/XCJkZXRyYWN0b3JzXCI6ZTw9OD9cInBhc3NpdmVzXCI6XCJwcm9tb3RlcnNcIn10aHJvdyBuZXcgRXJyb3IoXCJUaGUgc2NhbGUgbXVzdCBiZSBvbmUgb2Y6IDMsIDUsIDcsIDEwXCIpfShpLG4uc2NhbGUpO2lmKG51bGwhPT0oYz1uLmJyYW5jaGluZykmJnZvaWQgMCE9PWMmJm51bGwhPT0oZD1jLnJlc3BvbnNlVmFsdWVzKSYmdm9pZCAwIT09ZCYmZC5oYXNPd25Qcm9wZXJ0eShoKSl7dmFyIF89bi5icmFuY2hpbmcucmVzcG9uc2VWYWx1ZXNbaF07cmV0dXJuIE51bWJlci5pc0ludGVnZXIoXyk/XzpfPT09bXMuRW5kP21zLkVuZDpzfX1yZXR1cm4gc31yZXR1cm4gU3Mud2FybihcIkZhbGxpbmcgYmFjayB0byBuZXh0IHF1ZXN0aW9uIGluZGV4IGR1ZSB0byB1bmV4cGVjdGVkIGJyYW5jaGluZyB0eXBlXCIpLHN9Y2xhc3MgeHN7Y29uc3RydWN0b3IoZSl7Vyh0aGlzLFwiZ2V0TmV4dFN1cnZleVN0ZXBcIixrcyksdGhpcy5pbnN0YW5jZT1lLHRoaXMuX3N1cnZleUV2ZW50UmVjZWl2ZXI9bnVsbH1vblJlbW90ZUNvbmZpZyhlKXt0aGlzLl9kZWNpZGVTZXJ2ZXJSZXNwb25zZT0hIWUuc3VydmV5cyx0aGlzLmxvYWRJZkVuYWJsZWQoKX1yZXNldCgpe2xvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKFwibGFzdFNlZW5TdXJ2ZXlEYXRlXCIpO3ZhciBlPSgoKT0+e2Zvcih2YXIgZT1bXSx0PTA7dDxsb2NhbFN0b3JhZ2UubGVuZ3RoO3QrKyl7dmFyIGk9bG9jYWxTdG9yYWdlLmtleSh0KTtudWxsIT1pJiZpLnN0YXJ0c1dpdGgoXCJzZWVuU3VydmV5X1wiKSYmZS5wdXNoKGkpfXJldHVybiBlfSkoKTtlLmZvckVhY2goKGU9PmxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKGUpKSl9bG9hZElmRW5hYmxlZCgpe3ZhciBlLHQsaSxyPW51bGw9PV98fG51bGw9PT0oZT1fLl9fUG9zdGhvZ0V4dGVuc2lvbnNfXyl8fHZvaWQgMD09PWU/dm9pZCAwOmUuZ2VuZXJhdGVTdXJ2ZXlzO3RoaXMuaW5zdGFuY2UuY29uZmlnLmRpc2FibGVfc3VydmV5c3x8IXRoaXMuX2RlY2lkZVNlcnZlclJlc3BvbnNlfHxyfHwobnVsbD09dGhpcy5fc3VydmV5RXZlbnRSZWNlaXZlciYmKHRoaXMuX3N1cnZleUV2ZW50UmVjZWl2ZXI9bmV3IHdzKHRoaXMuaW5zdGFuY2UpKSxudWxsPT09KHQ9Xy5fX1Bvc3Rob2dFeHRlbnNpb25zX18pfHx2b2lkIDA9PT10fHxudWxsPT09KGk9dC5sb2FkRXh0ZXJuYWxEZXBlbmRlbmN5KXx8dm9pZCAwPT09aXx8aS5jYWxsKHQsdGhpcy5pbnN0YW5jZSxcInN1cnZleXNcIiwoZT0+e3ZhciB0LGk7aWYoZSlyZXR1cm4gU3MuZXJyb3IoXCJDb3VsZCBub3QgbG9hZCBzdXJ2ZXlzIHNjcmlwdFwiLGUpO3RoaXMuX3N1cnZleU1hbmFnZXI9bnVsbD09PSh0PV8uX19Qb3N0aG9nRXh0ZW5zaW9uc19fKXx8dm9pZCAwPT09dHx8bnVsbD09PShpPXQuZ2VuZXJhdGVTdXJ2ZXlzKXx8dm9pZCAwPT09aT92b2lkIDA6aS5jYWxsKHQsdGhpcy5pbnN0YW5jZSl9KSkpfWdldFN1cnZleXMoZSl7dmFyIHQ9YXJndW1lbnRzLmxlbmd0aD4xJiZ2b2lkIDAhPT1hcmd1bWVudHNbMV0mJmFyZ3VtZW50c1sxXTtpZih0aGlzLmluc3RhbmNlLmNvbmZpZy5kaXNhYmxlX3N1cnZleXMpcmV0dXJuIGUoW10pO251bGw9PXRoaXMuX3N1cnZleUV2ZW50UmVjZWl2ZXImJih0aGlzLl9zdXJ2ZXlFdmVudFJlY2VpdmVyPW5ldyB3cyh0aGlzLmluc3RhbmNlKSk7dmFyIGk9dGhpcy5pbnN0YW5jZS5nZXRfcHJvcGVydHkoRmUpO2lmKGkmJiF0KXJldHVybiBlKGkpO3RoaXMuaW5zdGFuY2UuX3NlbmRfcmVxdWVzdCh7dXJsOnRoaXMuaW5zdGFuY2UucmVxdWVzdFJvdXRlci5lbmRwb2ludEZvcihcImFwaVwiLFwiL2FwaS9zdXJ2ZXlzLz90b2tlbj1cIi5jb25jYXQodGhpcy5pbnN0YW5jZS5jb25maWcudG9rZW4pKSxtZXRob2Q6XCJHRVRcIixjYWxsYmFjazp0PT57dmFyIGk7aWYoMjAwIT09dC5zdGF0dXNDb2RlfHwhdC5qc29uKXJldHVybiBlKFtdKTt2YXIgcixuPXQuanNvbi5zdXJ2ZXlzfHxbXSxzPW4uZmlsdGVyKChlPT57dmFyIHQsaSxyLG4scyxvLGEsbCx1LGMsZCxoO3JldHVybihudWxsPT09KHQ9ZS5jb25kaXRpb25zKXx8dm9pZCAwPT09dD92b2lkIDA6dC5ldmVudHMpJiYobnVsbD09PShpPWUuY29uZGl0aW9ucyl8fHZvaWQgMD09PWl8fG51bGw9PT0ocj1pLmV2ZW50cyl8fHZvaWQgMD09PXI/dm9pZCAwOnIudmFsdWVzKSYmKG51bGw9PT0obj1lLmNvbmRpdGlvbnMpfHx2b2lkIDA9PT1ufHxudWxsPT09KHM9bi5ldmVudHMpfHx2b2lkIDA9PT1zfHxudWxsPT09KG89cy52YWx1ZXMpfHx2b2lkIDA9PT1vP3ZvaWQgMDpvLmxlbmd0aCk+MHx8KG51bGw9PT0oYT1lLmNvbmRpdGlvbnMpfHx2b2lkIDA9PT1hP3ZvaWQgMDphLmFjdGlvbnMpJiYobnVsbD09PShsPWUuY29uZGl0aW9ucyl8fHZvaWQgMD09PWx8fG51bGw9PT0odT1sLmFjdGlvbnMpfHx2b2lkIDA9PT11P3ZvaWQgMDp1LnZhbHVlcykmJihudWxsPT09KGM9ZS5jb25kaXRpb25zKXx8dm9pZCAwPT09Y3x8bnVsbD09PShkPWMuYWN0aW9ucyl8fHZvaWQgMD09PWR8fG51bGw9PT0oaD1kLnZhbHVlcyl8fHZvaWQgMD09PWg/dm9pZCAwOmgubGVuZ3RoKT4wfSkpO3MubGVuZ3RoPjAmJihudWxsPT09KHI9dGhpcy5fc3VydmV5RXZlbnRSZWNlaXZlcil8fHZvaWQgMD09PXJ8fHIucmVnaXN0ZXIocykpO3JldHVybiBudWxsPT09KGk9dGhpcy5pbnN0YW5jZS5wZXJzaXN0ZW5jZSl8fHZvaWQgMD09PWl8fGkucmVnaXN0ZXIoe1tGZV06bn0pLGUobil9fSl9Z2V0QWN0aXZlTWF0Y2hpbmdTdXJ2ZXlzKGUpe3ZhciB0PWFyZ3VtZW50cy5sZW5ndGg+MSYmdm9pZCAwIT09YXJndW1lbnRzWzFdJiZhcmd1bWVudHNbMV07dGhpcy5nZXRTdXJ2ZXlzKCh0PT57dmFyIGkscj10LmZpbHRlcigoZT0+ISghZS5zdGFydF9kYXRlfHxlLmVuZF9kYXRlKSkpLmZpbHRlcigoZT0+e3ZhciB0LGkscixuO2lmKCFlLmNvbmRpdGlvbnMpcmV0dXJuITA7dmFyIHM9bnVsbD09PSh0PWUuY29uZGl0aW9ucyl8fHZvaWQgMD09PXR8fCF0LnVybHx8RXNbbnVsbCE9PShpPW51bGw9PT0ocj1lLmNvbmRpdGlvbnMpfHx2b2lkIDA9PT1yP3ZvaWQgMDpyLnVybE1hdGNoVHlwZSkmJnZvaWQgMCE9PWk/aTpcImljb250YWluc1wiXShlLmNvbmRpdGlvbnMudXJsKSxvPW51bGw9PT0obj1lLmNvbmRpdGlvbnMpfHx2b2lkIDA9PT1ufHwhbi5zZWxlY3Rvcnx8KG51bGw9PWE/dm9pZCAwOmEucXVlcnlTZWxlY3RvcihlLmNvbmRpdGlvbnMuc2VsZWN0b3IpKTtyZXR1cm4gcyYmb30pKSxuPW51bGw9PT0oaT10aGlzLl9zdXJ2ZXlFdmVudFJlY2VpdmVyKXx8dm9pZCAwPT09aT92b2lkIDA6aS5nZXRTdXJ2ZXlzKCkscz1yLmZpbHRlcigoZT0+e3ZhciB0LGkscixzLG8sYSxsLHUsYyxkLGg7aWYoIShlLmxpbmtlZF9mbGFnX2tleXx8ZS50YXJnZXRpbmdfZmxhZ19rZXl8fGUuaW50ZXJuYWxfdGFyZ2V0aW5nX2ZsYWdfa2V5fHxudWxsIT09KHQ9ZS5mZWF0dXJlX2ZsYWdfa2V5cykmJnZvaWQgMCE9PXQmJnQubGVuZ3RoKSlyZXR1cm4hMDt2YXIgXz0hZS5saW5rZWRfZmxhZ19rZXl8fHRoaXMuaW5zdGFuY2UuZmVhdHVyZUZsYWdzLmlzRmVhdHVyZUVuYWJsZWQoZS5saW5rZWRfZmxhZ19rZXkpLHA9IWUudGFyZ2V0aW5nX2ZsYWdfa2V5fHx0aGlzLmluc3RhbmNlLmZlYXR1cmVGbGFncy5pc0ZlYXR1cmVFbmFibGVkKGUudGFyZ2V0aW5nX2ZsYWdfa2V5KSx2PShudWxsPT09KGk9ZS5jb25kaXRpb25zKXx8dm9pZCAwPT09aT92b2lkIDA6aS5ldmVudHMpJiYobnVsbD09PShyPWUuY29uZGl0aW9ucyl8fHZvaWQgMD09PXJ8fG51bGw9PT0ocz1yLmV2ZW50cyl8fHZvaWQgMD09PXM/dm9pZCAwOnMudmFsdWVzKSYmKG51bGw9PT0obz1lLmNvbmRpdGlvbnMpfHx2b2lkIDA9PT1vfHxudWxsPT09KGE9by5ldmVudHMpfHx2b2lkIDA9PT1hP3ZvaWQgMDphLnZhbHVlcy5sZW5ndGgpPjAsZz0obnVsbD09PShsPWUuY29uZGl0aW9ucyl8fHZvaWQgMD09PWw/dm9pZCAwOmwuYWN0aW9ucykmJihudWxsPT09KHU9ZS5jb25kaXRpb25zKXx8dm9pZCAwPT09dXx8bnVsbD09PShjPXUuYWN0aW9ucyl8fHZvaWQgMD09PWM/dm9pZCAwOmMudmFsdWVzKSYmKG51bGw9PT0oZD1lLmNvbmRpdGlvbnMpfHx2b2lkIDA9PT1kfHxudWxsPT09KGg9ZC5hY3Rpb25zKXx8dm9pZCAwPT09aD92b2lkIDA6aC52YWx1ZXMubGVuZ3RoKT4wLGY9IXYmJiFnfHwobnVsbD09bj92b2lkIDA6bi5pbmNsdWRlcyhlLmlkKSksbT10aGlzLl9jYW5BY3RpdmF0ZVJlcGVhdGVkbHkoZSksYj0hKGUuaW50ZXJuYWxfdGFyZ2V0aW5nX2ZsYWdfa2V5JiYhbSl8fHRoaXMuaW5zdGFuY2UuZmVhdHVyZUZsYWdzLmlzRmVhdHVyZUVuYWJsZWQoZS5pbnRlcm5hbF90YXJnZXRpbmdfZmxhZ19rZXkpLHk9dGhpcy5jaGVja0ZsYWdzKGUpO3JldHVybiBfJiZwJiZiJiZmJiZ5fSkpO3JldHVybiBlKHMpfSksdCl9Y2hlY2tGbGFncyhlKXt2YXIgdDtyZXR1cm4gbnVsbD09PSh0PWUuZmVhdHVyZV9mbGFnX2tleXMpfHx2b2lkIDA9PT10fHwhdC5sZW5ndGh8fGUuZmVhdHVyZV9mbGFnX2tleXMuZXZlcnkoKGU9Pnt2YXJ7a2V5OnQsdmFsdWU6aX09ZTtyZXR1cm4hdHx8IWl8fHRoaXMuaW5zdGFuY2UuZmVhdHVyZUZsYWdzLmlzRmVhdHVyZUVuYWJsZWQoaSl9KSl9X2NhbkFjdGl2YXRlUmVwZWF0ZWRseShlKXt2YXIgdDtyZXR1cm4gTyhudWxsPT09KHQ9Xy5fX1Bvc3Rob2dFeHRlbnNpb25zX18pfHx2b2lkIDA9PT10P3ZvaWQgMDp0LmNhbkFjdGl2YXRlUmVwZWF0ZWRseSk/KFNzLndhcm4oXCJpbml0IHdhcyBub3QgY2FsbGVkXCIpLCExKTpfLl9fUG9zdGhvZ0V4dGVuc2lvbnNfXy5jYW5BY3RpdmF0ZVJlcGVhdGVkbHkoZSl9Y2FuUmVuZGVyU3VydmV5KGUpe08odGhpcy5fc3VydmV5TWFuYWdlcik/U3Mud2FybihcImluaXQgd2FzIG5vdCBjYWxsZWRcIik6dGhpcy5nZXRTdXJ2ZXlzKCh0PT57dmFyIGk9dC5maWx0ZXIoKHQ9PnQuaWQ9PT1lKSlbMF07dGhpcy5fc3VydmV5TWFuYWdlci5jYW5SZW5kZXJTdXJ2ZXkoaSl9KSl9cmVuZGVyU3VydmV5KGUsdCl7Tyh0aGlzLl9zdXJ2ZXlNYW5hZ2VyKT9Tcy53YXJuKFwiaW5pdCB3YXMgbm90IGNhbGxlZFwiKTp0aGlzLmdldFN1cnZleXMoKGk9Pnt2YXIgcj1pLmZpbHRlcigodD0+dC5pZD09PWUpKVswXTt0aGlzLl9zdXJ2ZXlNYW5hZ2VyLnJlbmRlclN1cnZleShyLG51bGw9PWE/dm9pZCAwOmEucXVlcnlTZWxlY3Rvcih0KSl9KSl9fXZhciBJcz1CKFwiW1JhdGVMaW1pdGVyXVwiKTtjbGFzcyBDc3tjb25zdHJ1Y3RvcihlKXt2YXIgdCxpO1codGhpcyxcInNlcnZlckxpbWl0c1wiLHt9KSxXKHRoaXMsXCJsYXN0RXZlbnRSYXRlTGltaXRlZFwiLCExKSxXKHRoaXMsXCJjaGVja0ZvckxpbWl0aW5nXCIsKGU9Pnt2YXIgdD1lLnRleHQ7aWYodCYmdC5sZW5ndGgpdHJ5eyhKU09OLnBhcnNlKHQpLnF1b3RhX2xpbWl0ZWR8fFtdKS5mb3JFYWNoKChlPT57SXMuaW5mbyhcIlwiLmNvbmNhdChlfHxcImV2ZW50c1wiLFwiIGlzIHF1b3RhIGxpbWl0ZWQuXCIpKSx0aGlzLnNlcnZlckxpbWl0c1tlXT0obmV3IERhdGUpLmdldFRpbWUoKSs2ZTR9KSl9Y2F0Y2goZSl7cmV0dXJuIHZvaWQgSXMud2FybignY291bGQgbm90IHJhdGUgbGltaXQgLSBjb250aW51aW5nLiBFcnJvcjogXCInLmNvbmNhdChudWxsPT1lP3ZvaWQgMDplLm1lc3NhZ2UsJ1wiJykse3RleHQ6dH0pfX0pKSx0aGlzLmluc3RhbmNlPWUsdGhpcy5jYXB0dXJlRXZlbnRzUGVyU2Vjb25kPShudWxsPT09KHQ9ZS5jb25maWcucmF0ZV9saW1pdGluZyl8fHZvaWQgMD09PXQ/dm9pZCAwOnQuZXZlbnRzX3Blcl9zZWNvbmQpfHwxMCx0aGlzLmNhcHR1cmVFdmVudHNCdXJzdExpbWl0PU1hdGgubWF4KChudWxsPT09KGk9ZS5jb25maWcucmF0ZV9saW1pdGluZyl8fHZvaWQgMD09PWk/dm9pZCAwOmkuZXZlbnRzX2J1cnN0X2xpbWl0KXx8MTAqdGhpcy5jYXB0dXJlRXZlbnRzUGVyU2Vjb25kLHRoaXMuY2FwdHVyZUV2ZW50c1BlclNlY29uZCksdGhpcy5sYXN0RXZlbnRSYXRlTGltaXRlZD10aGlzLmNsaWVudFJhdGVMaW1pdENvbnRleHQoITApLmlzUmF0ZUxpbWl0ZWR9Y2xpZW50UmF0ZUxpbWl0Q29udGV4dCgpe3ZhciBlLHQsaSxyPWFyZ3VtZW50cy5sZW5ndGg+MCYmdm9pZCAwIT09YXJndW1lbnRzWzBdJiZhcmd1bWVudHNbMF0sbj0obmV3IERhdGUpLmdldFRpbWUoKSxzPW51bGwhPT0oZT1udWxsPT09KHQ9dGhpcy5pbnN0YW5jZS5wZXJzaXN0ZW5jZSl8fHZvaWQgMD09PXQ/dm9pZCAwOnQuZ2V0X3Byb3BlcnR5KE1lKSkmJnZvaWQgMCE9PWU/ZTp7dG9rZW5zOnRoaXMuY2FwdHVyZUV2ZW50c0J1cnN0TGltaXQsbGFzdDpufTtzLnRva2Vucys9KG4tcy5sYXN0KS8xZTMqdGhpcy5jYXB0dXJlRXZlbnRzUGVyU2Vjb25kLHMubGFzdD1uLHMudG9rZW5zPnRoaXMuY2FwdHVyZUV2ZW50c0J1cnN0TGltaXQmJihzLnRva2Vucz10aGlzLmNhcHR1cmVFdmVudHNCdXJzdExpbWl0KTt2YXIgbz1zLnRva2VuczwxO3JldHVybiBvfHxyfHwocy50b2tlbnM9TWF0aC5tYXgoMCxzLnRva2Vucy0xKSksIW98fHRoaXMubGFzdEV2ZW50UmF0ZUxpbWl0ZWR8fHJ8fHRoaXMuaW5zdGFuY2UuY2FwdHVyZShcIiQkY2xpZW50X2luZ2VzdGlvbl93YXJuaW5nXCIseyQkY2xpZW50X2luZ2VzdGlvbl93YXJuaW5nX21lc3NhZ2U6XCJwb3N0aG9nLWpzIGNsaWVudCByYXRlIGxpbWl0ZWQuIENvbmZpZyBpcyBzZXQgdG8gXCIuY29uY2F0KHRoaXMuY2FwdHVyZUV2ZW50c1BlclNlY29uZCxcIiBldmVudHMgcGVyIHNlY29uZCBhbmQgXCIpLmNvbmNhdCh0aGlzLmNhcHR1cmVFdmVudHNCdXJzdExpbWl0LFwiIGV2ZW50cyBidXJzdCBsaW1pdC5cIil9LHtza2lwX2NsaWVudF9yYXRlX2xpbWl0aW5nOiEwfSksdGhpcy5sYXN0RXZlbnRSYXRlTGltaXRlZD1vLG51bGw9PT0oaT10aGlzLmluc3RhbmNlLnBlcnNpc3RlbmNlKXx8dm9pZCAwPT09aXx8aS5zZXRfcHJvcGVydHkoTWUscykse2lzUmF0ZUxpbWl0ZWQ6byxyZW1haW5pbmdUb2tlbnM6cy50b2tlbnN9fWlzU2VydmVyUmF0ZUxpbWl0ZWQoZSl7dmFyIHQ9dGhpcy5zZXJ2ZXJMaW1pdHNbZXx8XCJldmVudHNcIl18fCExO3JldHVybiExIT09dCYmKG5ldyBEYXRlKS5nZXRUaW1lKCk8dH19dmFyIFBzPWU9Pnt2YXIgdD1udWxsPT1lP3ZvaWQgMDplLmNvbmZpZztyZXR1cm4gaih7aW5pdGlhbFBhdGhOYW1lOihudWxsPT1sP3ZvaWQgMDpsLnBhdGhuYW1lKXx8XCJcIixyZWZlcnJpbmdEb21haW46d2kucmVmZXJyaW5nRG9tYWluKCl9LHdpLmNhbXBhaWduUGFyYW1zKHtjdXN0b21UcmFja2VkUGFyYW1zOm51bGw9PXQ/dm9pZCAwOnQuY3VzdG9tX2NhbXBhaWduX3BhcmFtcyxtYXNrUGVyc29uYWxEYXRhUHJvcGVydGllczpudWxsPT10P3ZvaWQgMDp0Lm1hc2tfcGVyc29uYWxfZGF0YV9wcm9wZXJ0aWVzLGN1c3RvbVBlcnNvbmFsRGF0YVByb3BlcnRpZXM6bnVsbD09dD92b2lkIDA6dC5jdXN0b21fcGVyc29uYWxfZGF0YV9wcm9wZXJ0aWVzfSkpfTtjbGFzcyBGc3tjb25zdHJ1Y3RvcihlLHQsaSxyKXtXKHRoaXMsXCJfb25TZXNzaW9uSWRDYWxsYmFja1wiLChlPT57dmFyIHQ9dGhpcy5fZ2V0U3RvcmVkUHJvcHMoKTtpZighdHx8dC5zZXNzaW9uSWQhPT1lKXt2YXIgaT17c2Vzc2lvbklkOmUscHJvcHM6dGhpcy5fc2Vzc2lvblNvdXJjZVBhcmFtR2VuZXJhdG9yKHRoaXMuaW5zdGFuY2UpfTt0aGlzLl9wZXJzaXN0ZW5jZS5yZWdpc3Rlcih7W09lXTppfSl9fSkpLHRoaXMuaW5zdGFuY2U9ZSx0aGlzLl9zZXNzaW9uSWRNYW5hZ2VyPXQsdGhpcy5fcGVyc2lzdGVuY2U9aSx0aGlzLl9zZXNzaW9uU291cmNlUGFyYW1HZW5lcmF0b3I9cnx8UHMsdGhpcy5fc2Vzc2lvbklkTWFuYWdlci5vblNlc3Npb25JZCh0aGlzLl9vblNlc3Npb25JZENhbGxiYWNrKX1fZ2V0U3RvcmVkUHJvcHMoKXtyZXR1cm4gdGhpcy5fcGVyc2lzdGVuY2UucHJvcHNbT2VdfWdldFNlc3Npb25Qcm9wcygpe3ZhciBlLHQ9bnVsbD09PShlPXRoaXMuX2dldFN0b3JlZFByb3BzKCkpfHx2b2lkIDA9PT1lP3ZvaWQgMDplLnByb3BzO3JldHVybiB0P3skY2xpZW50X3Nlc3Npb25faW5pdGlhbF9yZWZlcnJpbmdfaG9zdDp0LnJlZmVycmluZ0RvbWFpbiwkY2xpZW50X3Nlc3Npb25faW5pdGlhbF9wYXRobmFtZTp0LmluaXRpYWxQYXRoTmFtZSwkY2xpZW50X3Nlc3Npb25faW5pdGlhbF91dG1fc291cmNlOnQudXRtX3NvdXJjZSwkY2xpZW50X3Nlc3Npb25faW5pdGlhbF91dG1fY2FtcGFpZ246dC51dG1fY2FtcGFpZ24sJGNsaWVudF9zZXNzaW9uX2luaXRpYWxfdXRtX21lZGl1bTp0LnV0bV9tZWRpdW0sJGNsaWVudF9zZXNzaW9uX2luaXRpYWxfdXRtX2NvbnRlbnQ6dC51dG1fY29udGVudCwkY2xpZW50X3Nlc3Npb25faW5pdGlhbF91dG1fdGVybTp0LnV0bV90ZXJtfTp7fX19dmFyIFJzPVtcImFocmVmc2JvdFwiLFwiYWhyZWZzc2l0ZWF1ZGl0XCIsXCJhcHBsZWJvdFwiLFwiYmFpZHVzcGlkZXJcIixcImJldHRlciB1cHRpbWUgYm90XCIsXCJiaW5nYm90XCIsXCJiaW5ncHJldmlld1wiLFwiYm90Lmh0bVwiLFwiYm90LnBocFwiLFwiY3Jhd2xlclwiLFwiZGVlcHNjYW5cIixcImR1Y2tkdWNrYm90XCIsXCJmYWNlYm9va2V4dGVybmFsXCIsXCJmYWNlYm9va2NhdGFsb2dcIixcImdwdGJvdFwiLFwiaHR0cDovL3lhbmRleC5jb20vYm90c1wiLFwiaHVic3BvdFwiLFwiaWFfYXJjaGl2ZXJcIixcImxpbmtlZGluYm90XCIsXCJtajEyYm90XCIsXCJtc25ib3RcIixcIm5lc3N1c1wiLFwicGV0YWxib3RcIixcInBpbnRlcmVzdFwiLFwicHJlcmVuZGVyXCIsXCJyb2dlcmJvdFwiLFwic2NyZWFtaW5nIGZyb2dcIixcInNlbXJ1c2hib3RcIixcInNpdGVidWxiXCIsXCJzbHVycFwiLFwidHVybml0aW5cIixcInR3aXR0ZXJib3RcIixcInZlcmNlbGJvdFwiLFwieWFob28hIHNsdXJwXCIsXCJ5YW5kZXhib3RcIixcImhlYWRsZXNzY2hyb21lXCIsXCJjeXByZXNzXCIsXCJHb29nbGUtSG90ZWxBZHNWZXJpZmllclwiLFwiYWRzYm90LWdvb2dsZVwiLFwiYXBpcy1nb29nbGVcIixcImR1cGxleHdlYi1nb29nbGVcIixcImZlZWRmZXRjaGVyLWdvb2dsZVwiLFwiZ29vZ2xlIGZhdmljb25cIixcImdvb2dsZSB3ZWIgcHJldmlld1wiLFwiZ29vZ2xlLXJlYWQtYWxvdWRcIixcImdvb2dsZWJvdFwiLFwiZ29vZ2xld2VibGlnaHRcIixcIm1lZGlhcGFydG5lcnMtZ29vZ2xlXCIsXCJzdG9yZWJvdC1nb29nbGVcIixcIkJ5dGVzcGlkZXI7XCJdLFRzPWZ1bmN0aW9uKGUsdCl7aWYoIWUpcmV0dXJuITE7dmFyIGk9ZS50b0xvd2VyQ2FzZSgpO3JldHVybiBScy5jb25jYXQodHx8W10pLnNvbWUoKGU9Pnt2YXIgdD1lLnRvTG93ZXJDYXNlKCk7cmV0dXJuLTEhPT1pLmluZGV4T2YodCl9KSl9LCRzPWZ1bmN0aW9uKGUsdCl7aWYoIWUpcmV0dXJuITE7dmFyIGk9ZS51c2VyQWdlbnQ7aWYoaSYmVHMoaSx0KSlyZXR1cm4hMDt0cnl7dmFyIHI9bnVsbD09ZT92b2lkIDA6ZS51c2VyQWdlbnREYXRhO2lmKG51bGwhPXImJnIuYnJhbmRzJiZyLmJyYW5kcy5zb21lKChlPT5UcyhudWxsPT1lP3ZvaWQgMDplLmJyYW5kLHQpKSkpcmV0dXJuITB9Y2F0Y2goZSl7fXJldHVybiEhZS53ZWJkcml2ZXJ9O2NsYXNzIE9ze2NvbnN0cnVjdG9yKCl7dGhpcy5jbGlja3M9W119aXNSYWdlQ2xpY2soZSx0LGkpe3ZhciByPXRoaXMuY2xpY2tzW3RoaXMuY2xpY2tzLmxlbmd0aC0xXTtpZihyJiZNYXRoLmFicyhlLXIueCkrTWF0aC5hYnModC1yLnkpPDMwJiZpLXIudGltZXN0YW1wPDFlMyl7aWYodGhpcy5jbGlja3MucHVzaCh7eDplLHk6dCx0aW1lc3RhbXA6aX0pLDM9PT10aGlzLmNsaWNrcy5sZW5ndGgpcmV0dXJuITB9ZWxzZSB0aGlzLmNsaWNrcz1be3g6ZSx5OnQsdGltZXN0YW1wOml9XTtyZXR1cm4hMX19dmFyIE1zPUIoXCJbRGVhZCBDbGlja3NdXCIpLExzPSgpPT4hMCxBcz1lPT57dmFyIHQsaT0hKG51bGw9PT0odD1lLmluc3RhbmNlLnBlcnNpc3RlbmNlKXx8dm9pZCAwPT09dHx8IXQuZ2V0X3Byb3BlcnR5KGhlKSkscj1lLmluc3RhbmNlLmNvbmZpZy5jYXB0dXJlX2RlYWRfY2xpY2tzO3JldHVybiBMKHIpP3I6aX07Y2xhc3MgRHN7Z2V0IGxhenlMb2FkZWREZWFkQ2xpY2tzQXV0b2NhcHR1cmUoKXtyZXR1cm4gdGhpcy5fbGF6eUxvYWRlZERlYWRDbGlja3NBdXRvY2FwdHVyZX1jb25zdHJ1Y3RvcihlLHQsaSl7dGhpcy5pbnN0YW5jZT1lLHRoaXMuaXNFbmFibGVkPXQsdGhpcy5vbkNhcHR1cmU9aSx0aGlzLnN0YXJ0SWZFbmFibGVkKCl9b25SZW1vdGVDb25maWcoZSl7dGhpcy5pbnN0YW5jZS5wZXJzaXN0ZW5jZSYmdGhpcy5pbnN0YW5jZS5wZXJzaXN0ZW5jZS5yZWdpc3Rlcih7W2hlXTpudWxsPT1lP3ZvaWQgMDplLmNhcHR1cmVEZWFkQ2xpY2tzfSksdGhpcy5zdGFydElmRW5hYmxlZCgpfXN0YXJ0SWZFbmFibGVkKCl7dGhpcy5pc0VuYWJsZWQodGhpcykmJnRoaXMubG9hZFNjcmlwdCgoKCk9Pnt0aGlzLnN0YXJ0KCl9KSl9bG9hZFNjcmlwdChlKXt2YXIgdCxpLHI7bnVsbCE9PSh0PV8uX19Qb3N0aG9nRXh0ZW5zaW9uc19fKSYmdm9pZCAwIT09dCYmdC5pbml0RGVhZENsaWNrc0F1dG9jYXB0dXJlJiZlKCksbnVsbD09PShpPV8uX19Qb3N0aG9nRXh0ZW5zaW9uc19fKXx8dm9pZCAwPT09aXx8bnVsbD09PShyPWkubG9hZEV4dGVybmFsRGVwZW5kZW5jeSl8fHZvaWQgMD09PXJ8fHIuY2FsbChpLHRoaXMuaW5zdGFuY2UsXCJkZWFkLWNsaWNrcy1hdXRvY2FwdHVyZVwiLCh0PT57dD9Ncy5lcnJvcihcImZhaWxlZCB0byBsb2FkIHNjcmlwdFwiLHQpOmUoKX0pKX1zdGFydCgpe3ZhciBlO2lmKGEpe2lmKCF0aGlzLl9sYXp5TG9hZGVkRGVhZENsaWNrc0F1dG9jYXB0dXJlJiZudWxsIT09KGU9Xy5fX1Bvc3Rob2dFeHRlbnNpb25zX18pJiZ2b2lkIDAhPT1lJiZlLmluaXREZWFkQ2xpY2tzQXV0b2NhcHR1cmUpe3ZhciB0PUModGhpcy5pbnN0YW5jZS5jb25maWcuY2FwdHVyZV9kZWFkX2NsaWNrcyk/dGhpcy5pbnN0YW5jZS5jb25maWcuY2FwdHVyZV9kZWFkX2NsaWNrczp7fTt0Ll9fb25DYXB0dXJlPXRoaXMub25DYXB0dXJlLHRoaXMuX2xhenlMb2FkZWREZWFkQ2xpY2tzQXV0b2NhcHR1cmU9Xy5fX1Bvc3Rob2dFeHRlbnNpb25zX18uaW5pdERlYWRDbGlja3NBdXRvY2FwdHVyZSh0aGlzLmluc3RhbmNlLHQpLHRoaXMuX2xhenlMb2FkZWREZWFkQ2xpY2tzQXV0b2NhcHR1cmUuc3RhcnQoYSksTXMuaW5mbyhcInN0YXJ0aW5nLi4uXCIpfX1lbHNlIE1zLmVycm9yKFwiYGRvY3VtZW50YCBub3QgZm91bmQuIENhbm5vdCBzdGFydC5cIil9c3RvcCgpe3RoaXMuX2xhenlMb2FkZWREZWFkQ2xpY2tzQXV0b2NhcHR1cmUmJih0aGlzLl9sYXp5TG9hZGVkRGVhZENsaWNrc0F1dG9jYXB0dXJlLnN0b3AoKSx0aGlzLl9sYXp5TG9hZGVkRGVhZENsaWNrc0F1dG9jYXB0dXJlPXZvaWQgMCxNcy5pbmZvKFwic3RvcHBpbmcuLi5cIikpfX12YXIgTnM9QihcIltIZWF0bWFwc11cIik7ZnVuY3Rpb24gcXMoZSl7cmV0dXJuIEMoZSkmJlwiY2xpZW50WFwiaW4gZSYmXCJjbGllbnRZXCJpbiBlJiZNKGUuY2xpZW50WCkmJk0oZS5jbGllbnRZKX1jbGFzcyBCc3tjb25zdHJ1Y3RvcihlKXt2YXIgaTtXKHRoaXMsXCJyYWdlY2xpY2tzXCIsbmV3IE9zKSxXKHRoaXMsXCJfZW5hYmxlZFNlcnZlclNpZGVcIiwhMSksVyh0aGlzLFwiX2luaXRpYWxpemVkXCIsITEpLFcodGhpcyxcIl9mbHVzaEludGVydmFsXCIsbnVsbCksdGhpcy5pbnN0YW5jZT1lLHRoaXMuX2VuYWJsZWRTZXJ2ZXJTaWRlPSEobnVsbD09PShpPXRoaXMuaW5zdGFuY2UucGVyc2lzdGVuY2UpfHx2b2lkIDA9PT1pfHwhaS5wcm9wc1t1ZV0pLG51bGw9PXR8fHQuYWRkRXZlbnRMaXN0ZW5lcihcImJlZm9yZXVubG9hZFwiLCgoKT0+e3RoaXMuZmx1c2goKX0pKX1nZXQgZmx1c2hJbnRlcnZhbE1pbGxpc2Vjb25kcygpe3ZhciBlPTVlMztyZXR1cm4gQyh0aGlzLmluc3RhbmNlLmNvbmZpZy5jYXB0dXJlX2hlYXRtYXBzKSYmdGhpcy5pbnN0YW5jZS5jb25maWcuY2FwdHVyZV9oZWF0bWFwcy5mbHVzaF9pbnRlcnZhbF9taWxsaXNlY29uZHMmJihlPXRoaXMuaW5zdGFuY2UuY29uZmlnLmNhcHR1cmVfaGVhdG1hcHMuZmx1c2hfaW50ZXJ2YWxfbWlsbGlzZWNvbmRzKSxlfWdldCBpc0VuYWJsZWQoKXtyZXR1cm4gRih0aGlzLmluc3RhbmNlLmNvbmZpZy5jYXB0dXJlX2hlYXRtYXBzKT9GKHRoaXMuaW5zdGFuY2UuY29uZmlnLmVuYWJsZV9oZWF0bWFwcyk/dGhpcy5fZW5hYmxlZFNlcnZlclNpZGU6dGhpcy5pbnN0YW5jZS5jb25maWcuZW5hYmxlX2hlYXRtYXBzOiExIT09dGhpcy5pbnN0YW5jZS5jb25maWcuY2FwdHVyZV9oZWF0bWFwc31zdGFydElmRW5hYmxlZCgpe2lmKHRoaXMuaXNFbmFibGVkKXtpZih0aGlzLl9pbml0aWFsaXplZClyZXR1cm47TnMuaW5mbyhcInN0YXJ0aW5nLi4uXCIpLHRoaXMuX3NldHVwTGlzdGVuZXJzKCksdGhpcy5fZmx1c2hJbnRlcnZhbD1zZXRJbnRlcnZhbCh0aGlzLmZsdXNoLmJpbmQodGhpcyksdGhpcy5mbHVzaEludGVydmFsTWlsbGlzZWNvbmRzKX1lbHNle3ZhciBlLHQ7Y2xlYXJJbnRlcnZhbChudWxsIT09KGU9dGhpcy5fZmx1c2hJbnRlcnZhbCkmJnZvaWQgMCE9PWU/ZTp2b2lkIDApLG51bGw9PT0odD10aGlzLmRlYWRDbGlja3NDYXB0dXJlKXx8dm9pZCAwPT09dHx8dC5zdG9wKCksdGhpcy5nZXRBbmRDbGVhckJ1ZmZlcigpfX1vblJlbW90ZUNvbmZpZyhlKXt2YXIgdD0hIWUuaGVhdG1hcHM7dGhpcy5pbnN0YW5jZS5wZXJzaXN0ZW5jZSYmdGhpcy5pbnN0YW5jZS5wZXJzaXN0ZW5jZS5yZWdpc3Rlcih7W3VlXTp0fSksdGhpcy5fZW5hYmxlZFNlcnZlclNpZGU9dCx0aGlzLnN0YXJ0SWZFbmFibGVkKCl9Z2V0QW5kQ2xlYXJCdWZmZXIoKXt2YXIgZT10aGlzLmJ1ZmZlcjtyZXR1cm4gdGhpcy5idWZmZXI9dm9pZCAwLGV9X29uRGVhZENsaWNrKGUpe3RoaXMuX29uQ2xpY2soZS5vcmlnaW5hbEV2ZW50LFwiZGVhZGNsaWNrXCIpfV9zZXR1cExpc3RlbmVycygpe3QmJmEmJihyZShhLFwiY2xpY2tcIiwoZT0+dGhpcy5fb25DbGljayhlfHwobnVsbD09dD92b2lkIDA6dC5ldmVudCkpKSwhMSwhMCkscmUoYSxcIm1vdXNlbW92ZVwiLChlPT50aGlzLl9vbk1vdXNlTW92ZShlfHwobnVsbD09dD92b2lkIDA6dC5ldmVudCkpKSwhMSwhMCksdGhpcy5kZWFkQ2xpY2tzQ2FwdHVyZT1uZXcgRHModGhpcy5pbnN0YW5jZSxMcyx0aGlzLl9vbkRlYWRDbGljay5iaW5kKHRoaXMpKSx0aGlzLmRlYWRDbGlja3NDYXB0dXJlLnN0YXJ0SWZFbmFibGVkKCksdGhpcy5faW5pdGlhbGl6ZWQ9ITApfV9nZXRQcm9wZXJ0aWVzKGUsaSl7dmFyIHI9dGhpcy5pbnN0YW5jZS5zY3JvbGxNYW5hZ2VyLnNjcm9sbFkoKSxuPXRoaXMuaW5zdGFuY2Uuc2Nyb2xsTWFuYWdlci5zY3JvbGxYKCkscz10aGlzLmluc3RhbmNlLnNjcm9sbE1hbmFnZXIuc2Nyb2xsRWxlbWVudCgpLG89ZnVuY3Rpb24oZSxpLHIpe2Zvcih2YXIgbj1lO24mJkZpKG4pJiYhUmkobixcImJvZHlcIik7KXtpZihuPT09cilyZXR1cm4hMTtpZihtKGksbnVsbD09dD92b2lkIDA6dC5nZXRDb21wdXRlZFN0eWxlKG4pLnBvc2l0aW9uKSlyZXR1cm4hMDtuPUJpKG4pfXJldHVybiExfShOaShlKSxbXCJmaXhlZFwiLFwic3RpY2t5XCJdLHMpO3JldHVybnt4OmUuY2xpZW50WCsobz8wOm4pLHk6ZS5jbGllbnRZKyhvPzA6ciksdGFyZ2V0X2ZpeGVkOm8sdHlwZTppfX1fb25DbGljayhlKXt2YXIgdCxpPWFyZ3VtZW50cy5sZW5ndGg+MSYmdm9pZCAwIT09YXJndW1lbnRzWzFdP2FyZ3VtZW50c1sxXTpcImNsaWNrXCI7aWYoIVBpKGUudGFyZ2V0KSYmcXMoZSkpe3ZhciByPXRoaXMuX2dldFByb3BlcnRpZXMoZSxpKTtudWxsIT09KHQ9dGhpcy5yYWdlY2xpY2tzKSYmdm9pZCAwIT09dCYmdC5pc1JhZ2VDbGljayhlLmNsaWVudFgsZS5jbGllbnRZLChuZXcgRGF0ZSkuZ2V0VGltZSgpKSYmdGhpcy5fY2FwdHVyZShqKGooe30scikse30se3R5cGU6XCJyYWdlY2xpY2tcIn0pKSx0aGlzLl9jYXB0dXJlKHIpfX1fb25Nb3VzZU1vdmUoZSl7IVBpKGUudGFyZ2V0KSYmcXMoZSkmJihjbGVhclRpbWVvdXQodGhpcy5fbW91c2VNb3ZlVGltZW91dCksdGhpcy5fbW91c2VNb3ZlVGltZW91dD1zZXRUaW1lb3V0KCgoKT0+e3RoaXMuX2NhcHR1cmUodGhpcy5fZ2V0UHJvcGVydGllcyhlLFwibW91c2Vtb3ZlXCIpKX0pLDUwMCkpfV9jYXB0dXJlKGUpe2lmKHQpe3ZhciBpPXQubG9jYXRpb24uaHJlZjt0aGlzLmJ1ZmZlcj10aGlzLmJ1ZmZlcnx8e30sdGhpcy5idWZmZXJbaV18fCh0aGlzLmJ1ZmZlcltpXT1bXSksdGhpcy5idWZmZXJbaV0ucHVzaChlKX19Zmx1c2goKXt0aGlzLmJ1ZmZlciYmIVAodGhpcy5idWZmZXIpJiZ0aGlzLmluc3RhbmNlLmNhcHR1cmUoXCIkJGhlYXRtYXBcIix7JGhlYXRtYXBfZGF0YTp0aGlzLmdldEFuZENsZWFyQnVmZmVyKCl9KX19Y2xhc3MgSHN7Y29uc3RydWN0b3IoZSl7Vyh0aGlzLFwiX3VwZGF0ZVNjcm9sbERhdGFcIiwoKCk9Pnt2YXIgZSx0LGkscjt0aGlzLmNvbnRleHR8fCh0aGlzLmNvbnRleHQ9e30pO3ZhciBuPXRoaXMuc2Nyb2xsRWxlbWVudCgpLHM9dGhpcy5zY3JvbGxZKCksbz1uP01hdGgubWF4KDAsbi5zY3JvbGxIZWlnaHQtbi5jbGllbnRIZWlnaHQpOjAsYT1zKygobnVsbD09bj92b2lkIDA6bi5jbGllbnRIZWlnaHQpfHwwKSxsPShudWxsPT1uP3ZvaWQgMDpuLnNjcm9sbEhlaWdodCl8fDA7dGhpcy5jb250ZXh0Lmxhc3RTY3JvbGxZPU1hdGguY2VpbChzKSx0aGlzLmNvbnRleHQubWF4U2Nyb2xsWT1NYXRoLm1heChzLG51bGwhPT0oZT10aGlzLmNvbnRleHQubWF4U2Nyb2xsWSkmJnZvaWQgMCE9PWU/ZTowKSx0aGlzLmNvbnRleHQubWF4U2Nyb2xsSGVpZ2h0PU1hdGgubWF4KG8sbnVsbCE9PSh0PXRoaXMuY29udGV4dC5tYXhTY3JvbGxIZWlnaHQpJiZ2b2lkIDAhPT10P3Q6MCksdGhpcy5jb250ZXh0Lmxhc3RDb250ZW50WT1hLHRoaXMuY29udGV4dC5tYXhDb250ZW50WT1NYXRoLm1heChhLG51bGwhPT0oaT10aGlzLmNvbnRleHQubWF4Q29udGVudFkpJiZ2b2lkIDAhPT1pP2k6MCksdGhpcy5jb250ZXh0Lm1heENvbnRlbnRIZWlnaHQ9TWF0aC5tYXgobCxudWxsIT09KHI9dGhpcy5jb250ZXh0Lm1heENvbnRlbnRIZWlnaHQpJiZ2b2lkIDAhPT1yP3I6MCl9KSksdGhpcy5pbnN0YW5jZT1lfWdldENvbnRleHQoKXtyZXR1cm4gdGhpcy5jb250ZXh0fXJlc2V0Q29udGV4dCgpe3ZhciBlPXRoaXMuY29udGV4dDtyZXR1cm4gc2V0VGltZW91dCh0aGlzLl91cGRhdGVTY3JvbGxEYXRhLDApLGV9c3RhcnRNZWFzdXJpbmdTY3JvbGxQb3NpdGlvbigpe251bGw9PXR8fHQuYWRkRXZlbnRMaXN0ZW5lcihcInNjcm9sbFwiLHRoaXMuX3VwZGF0ZVNjcm9sbERhdGEsITApLG51bGw9PXR8fHQuYWRkRXZlbnRMaXN0ZW5lcihcInNjcm9sbGVuZFwiLHRoaXMuX3VwZGF0ZVNjcm9sbERhdGEsITApLG51bGw9PXR8fHQuYWRkRXZlbnRMaXN0ZW5lcihcInJlc2l6ZVwiLHRoaXMuX3VwZGF0ZVNjcm9sbERhdGEpfXNjcm9sbEVsZW1lbnQoKXtpZighdGhpcy5pbnN0YW5jZS5jb25maWcuc2Nyb2xsX3Jvb3Rfc2VsZWN0b3IpcmV0dXJuIG51bGw9PXQ/dm9pZCAwOnQuZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50O3ZhciBlPXgodGhpcy5pbnN0YW5jZS5jb25maWcuc2Nyb2xsX3Jvb3Rfc2VsZWN0b3IpP3RoaXMuaW5zdGFuY2UuY29uZmlnLnNjcm9sbF9yb290X3NlbGVjdG9yOlt0aGlzLmluc3RhbmNlLmNvbmZpZy5zY3JvbGxfcm9vdF9zZWxlY3Rvcl07Zm9yKHZhciBpIG9mIGUpe3ZhciByPW51bGw9PXQ/dm9pZCAwOnQuZG9jdW1lbnQucXVlcnlTZWxlY3RvcihpKTtpZihyKXJldHVybiByfX1zY3JvbGxZKCl7aWYodGhpcy5pbnN0YW5jZS5jb25maWcuc2Nyb2xsX3Jvb3Rfc2VsZWN0b3Ipe3ZhciBlPXRoaXMuc2Nyb2xsRWxlbWVudCgpO3JldHVybiBlJiZlLnNjcm9sbFRvcHx8MH1yZXR1cm4gdCYmKHQuc2Nyb2xsWXx8dC5wYWdlWU9mZnNldHx8dC5kb2N1bWVudC5kb2N1bWVudEVsZW1lbnQuc2Nyb2xsVG9wKXx8MH1zY3JvbGxYKCl7aWYodGhpcy5pbnN0YW5jZS5jb25maWcuc2Nyb2xsX3Jvb3Rfc2VsZWN0b3Ipe3ZhciBlPXRoaXMuc2Nyb2xsRWxlbWVudCgpO3JldHVybiBlJiZlLnNjcm9sbExlZnR8fDB9cmV0dXJuIHQmJih0LnNjcm9sbFh8fHQucGFnZVhPZmZzZXR8fHQuZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LnNjcm9sbExlZnQpfHwwfX12YXIgVXM9QihcIltBdXRvQ2FwdHVyZV1cIik7ZnVuY3Rpb24genMoZSx0KXtyZXR1cm4gdC5sZW5ndGg+ZT90LnNsaWNlKDAsZSkrXCIuLi5cIjp0fWZ1bmN0aW9uIGpzKGUpe2lmKGUucHJldmlvdXNFbGVtZW50U2libGluZylyZXR1cm4gZS5wcmV2aW91c0VsZW1lbnRTaWJsaW5nO3ZhciB0PWU7ZG97dD10LnByZXZpb3VzU2libGluZ313aGlsZSh0JiYhRmkodCkpO3JldHVybiB0fWZ1bmN0aW9uIFdzKGUsdCxpLHIpe3ZhciBuPWUudGFnTmFtZS50b0xvd2VyQ2FzZSgpLHM9e3RhZ19uYW1lOm59O3FpLmluZGV4T2Yobik+LTEmJiFpJiYoXCJhXCI9PT1uLnRvTG93ZXJDYXNlKCl8fFwiYnV0dG9uXCI9PT1uLnRvTG93ZXJDYXNlKCk/cy4kZWxfdGV4dD16cygxMDI0LFhpKGUpKTpzLiRlbF90ZXh0PXpzKDEwMjQsRGkoZSkpKTt2YXIgbz1MaShlKTtvLmxlbmd0aD4wJiYocy5jbGFzc2VzPW8uZmlsdGVyKChmdW5jdGlvbihlKXtyZXR1cm5cIlwiIT09ZX0pKSksWShlLmF0dHJpYnV0ZXMsKGZ1bmN0aW9uKGkpe3ZhciBuO2lmKCghemkoZSl8fC0xIT09W1wibmFtZVwiLFwiaWRcIixcImNsYXNzXCIsXCJhcmlhLWxhYmVsXCJdLmluZGV4T2YoaS5uYW1lKSkmJigobnVsbD09cnx8IXIuaW5jbHVkZXMoaS5uYW1lKSkmJiF0JiZLaShpLnZhbHVlKSYmKG49aS5uYW1lLCFSKG4pfHxcIl9uZ2NvbnRlbnRcIiE9PW4uc3Vic3RyaW5nKDAsMTApJiZcIl9uZ2hvc3RcIiE9PW4uc3Vic3RyaW5nKDAsNykpKSl7dmFyIG89aS52YWx1ZTtcImNsYXNzXCI9PT1pLm5hbWUmJihvPU9pKG8pLmpvaW4oXCIgXCIpKSxzW1wiYXR0cl9fXCIraS5uYW1lXT16cygxMDI0LG8pfX0pKTtmb3IodmFyIGE9MSxsPTEsdT1lO3U9anModSk7KWErKyx1LnRhZ05hbWU9PT1lLnRhZ05hbWUmJmwrKztyZXR1cm4gcy5udGhfY2hpbGQ9YSxzLm50aF9vZl90eXBlPWwsc31mdW5jdGlvbiBWcyhlLGkpe2Zvcih2YXIgcixuLHtlOnMsbWFza0FsbEVsZW1lbnRBdHRyaWJ1dGVzOm8sbWFza0FsbFRleHQ6YSxlbGVtZW50QXR0cmlidXRlSWdub3JlTGlzdDpsLGVsZW1lbnRzQ2hhaW5Bc1N0cmluZzp1fT1pLGM9W2VdLGQ9ZTtkLnBhcmVudE5vZGUmJiFSaShkLFwiYm9keVwiKTspJGkoZC5wYXJlbnROb2RlKT8oYy5wdXNoKGQucGFyZW50Tm9kZS5ob3N0KSxkPWQucGFyZW50Tm9kZS5ob3N0KTooYy5wdXNoKGQucGFyZW50Tm9kZSksZD1kLnBhcmVudE5vZGUpO3ZhciBoLF89W10scD17fSx2PSExLGc9ITE7aWYoWShjLChlPT57dmFyIHQ9VWkoZSk7XCJhXCI9PT1lLnRhZ05hbWUudG9Mb3dlckNhc2UoKSYmKHY9ZS5nZXRBdHRyaWJ1dGUoXCJocmVmXCIpLHY9dCYmdiYmS2kodikmJnYpLG0oTGkoZSksXCJwaC1uby1jYXB0dXJlXCIpJiYoZz0hMCksXy5wdXNoKFdzKGUsbyxhLGwpKTt2YXIgaT1mdW5jdGlvbihlKXtpZighVWkoZSkpcmV0dXJue307dmFyIHQ9e307cmV0dXJuIFkoZS5hdHRyaWJ1dGVzLChmdW5jdGlvbihlKXtpZihlLm5hbWUmJjA9PT1lLm5hbWUuaW5kZXhPZihcImRhdGEtcGgtY2FwdHVyZS1hdHRyaWJ1dGVcIikpe3ZhciBpPWUubmFtZS5yZXBsYWNlKFwiZGF0YS1waC1jYXB0dXJlLWF0dHJpYnV0ZS1cIixcIlwiKSxyPWUudmFsdWU7aSYmciYmS2kocikmJih0W2ldPXIpfX0pKSx0fShlKTtLKHAsaSl9KSksZylyZXR1cm57cHJvcHM6e30sZXhwbGljaXROb0NhcHR1cmU6Z307aWYoYXx8KFwiYVwiPT09ZS50YWdOYW1lLnRvTG93ZXJDYXNlKCl8fFwiYnV0dG9uXCI9PT1lLnRhZ05hbWUudG9Mb3dlckNhc2UoKT9fWzBdLiRlbF90ZXh0PVhpKGUpOl9bMF0uJGVsX3RleHQ9RGkoZSkpLHYpe3ZhciBmLGI7X1swXS5hdHRyX19ocmVmPXY7dmFyIHk9bnVsbD09PShmPWd0KHYpKXx8dm9pZCAwPT09Zj92b2lkIDA6Zi5ob3N0LHc9bnVsbD09dHx8bnVsbD09PShiPXQubG9jYXRpb24pfHx2b2lkIDA9PT1iP3ZvaWQgMDpiLmhvc3Q7eSYmdyYmeSE9PXcmJihoPXYpfXJldHVybntwcm9wczpLKHskZXZlbnRfdHlwZTpzLnR5cGUsJGNlX3ZlcnNpb246MX0sdT97fTp7JGVsZW1lbnRzOl99LHskZWxlbWVudHNfY2hhaW46WmkoXyl9LG51bGwhPT0ocj1fWzBdKSYmdm9pZCAwIT09ciYmci4kZWxfdGV4dD97JGVsX3RleHQ6bnVsbD09PShuPV9bMF0pfHx2b2lkIDA9PT1uP3ZvaWQgMDpuLiRlbF90ZXh0fTp7fSxoJiZcImNsaWNrXCI9PT1zLnR5cGU/eyRleHRlcm5hbF9jbGlja191cmw6aH06e30scCl9fWNsYXNzIEdze2NvbnN0cnVjdG9yKGUpe1codGhpcyxcIl9pbml0aWFsaXplZFwiLCExKSxXKHRoaXMsXCJfaXNEaXNhYmxlZFNlcnZlclNpZGVcIixudWxsKSxXKHRoaXMsXCJyYWdlY2xpY2tzXCIsbmV3IE9zKSxXKHRoaXMsXCJfZWxlbWVudHNDaGFpbkFzU3RyaW5nXCIsITEpLHRoaXMuaW5zdGFuY2U9ZSx0aGlzLl9lbGVtZW50U2VsZWN0b3JzPW51bGx9Z2V0IGNvbmZpZygpe3ZhciBlLHQsaT1DKHRoaXMuaW5zdGFuY2UuY29uZmlnLmF1dG9jYXB0dXJlKT90aGlzLmluc3RhbmNlLmNvbmZpZy5hdXRvY2FwdHVyZTp7fTtyZXR1cm4gaS51cmxfYWxsb3dsaXN0PW51bGw9PT0oZT1pLnVybF9hbGxvd2xpc3QpfHx2b2lkIDA9PT1lP3ZvaWQgMDplLm1hcCgoZT0+bmV3IFJlZ0V4cChlKSkpLGkudXJsX2lnbm9yZWxpc3Q9bnVsbD09PSh0PWkudXJsX2lnbm9yZWxpc3QpfHx2b2lkIDA9PT10P3ZvaWQgMDp0Lm1hcCgoZT0+bmV3IFJlZ0V4cChlKSkpLGl9X2FkZERvbUV2ZW50SGFuZGxlcnMoKXtpZih0aGlzLmlzQnJvd3NlclN1cHBvcnRlZCgpKXtpZih0JiZhKXt2YXIgZT1lPT57ZT1lfHwobnVsbD09dD92b2lkIDA6dC5ldmVudCk7dHJ5e3RoaXMuX2NhcHR1cmVFdmVudChlKX1jYXRjaChlKXtVcy5lcnJvcihcIkZhaWxlZCB0byBjYXB0dXJlIGV2ZW50XCIsZSl9fSxpPWU9PntlPWV8fChudWxsPT10P3ZvaWQgMDp0LmV2ZW50KSx0aGlzLl9jYXB0dXJlRXZlbnQoZSx2KX07cmUoYSxcInN1Ym1pdFwiLGUsITEsITApLHJlKGEsXCJjaGFuZ2VcIixlLCExLCEwKSxyZShhLFwiY2xpY2tcIixlLCExLCEwKSx0aGlzLmNvbmZpZy5jYXB0dXJlX2NvcGllZF90ZXh0JiYocmUoYSxcImNvcHlcIixpLCExLCEwKSxyZShhLFwiY3V0XCIsaSwhMSwhMCkpfX1lbHNlIFVzLmluZm8oXCJEaXNhYmxpbmcgQXV0b21hdGljIEV2ZW50IENvbGxlY3Rpb24gYmVjYXVzZSB0aGlzIGJyb3dzZXIgaXMgbm90IHN1cHBvcnRlZFwiKX1zdGFydElmRW5hYmxlZCgpe3RoaXMuaXNFbmFibGVkJiYhdGhpcy5faW5pdGlhbGl6ZWQmJih0aGlzLl9hZGREb21FdmVudEhhbmRsZXJzKCksdGhpcy5faW5pdGlhbGl6ZWQ9ITApfW9uUmVtb3RlQ29uZmlnKGUpe2UuZWxlbWVudHNDaGFpbkFzU3RyaW5nJiYodGhpcy5fZWxlbWVudHNDaGFpbkFzU3RyaW5nPWUuZWxlbWVudHNDaGFpbkFzU3RyaW5nKSx0aGlzLmluc3RhbmNlLnBlcnNpc3RlbmNlJiZ0aGlzLmluc3RhbmNlLnBlcnNpc3RlbmNlLnJlZ2lzdGVyKHtbbGVdOiEhZS5hdXRvY2FwdHVyZV9vcHRfb3V0fSksdGhpcy5faXNEaXNhYmxlZFNlcnZlclNpZGU9ISFlLmF1dG9jYXB0dXJlX29wdF9vdXQsdGhpcy5zdGFydElmRW5hYmxlZCgpfXNldEVsZW1lbnRTZWxlY3RvcnMoZSl7dGhpcy5fZWxlbWVudFNlbGVjdG9ycz1lfWdldEVsZW1lbnRTZWxlY3RvcnMoZSl7dmFyIHQsaT1bXTtyZXR1cm4gbnVsbD09PSh0PXRoaXMuX2VsZW1lbnRTZWxlY3RvcnMpfHx2b2lkIDA9PT10fHx0LmZvckVhY2goKHQ9Pnt2YXIgcj1udWxsPT1hP3ZvaWQgMDphLnF1ZXJ5U2VsZWN0b3JBbGwodCk7bnVsbD09cnx8ci5mb3JFYWNoKChyPT57ZT09PXImJmkucHVzaCh0KX0pKX0pKSxpfWdldCBpc0VuYWJsZWQoKXt2YXIgZSx0LGk9bnVsbD09PShlPXRoaXMuaW5zdGFuY2UucGVyc2lzdGVuY2UpfHx2b2lkIDA9PT1lP3ZvaWQgMDplLnByb3BzW2xlXSxyPXRoaXMuX2lzRGlzYWJsZWRTZXJ2ZXJTaWRlO2lmKCQocikmJiFMKGkpJiYhdGhpcy5pbnN0YW5jZS5jb25maWcuYWR2YW5jZWRfZGlzYWJsZV9kZWNpZGUpcmV0dXJuITE7dmFyIG49bnVsbCE9PSh0PXRoaXMuX2lzRGlzYWJsZWRTZXJ2ZXJTaWRlKSYmdm9pZCAwIT09dD90OiEhaTtyZXR1cm4hIXRoaXMuaW5zdGFuY2UuY29uZmlnLmF1dG9jYXB0dXJlJiYhbn1fY2FwdHVyZUV2ZW50KGUpe3ZhciBpPWFyZ3VtZW50cy5sZW5ndGg+MSYmdm9pZCAwIT09YXJndW1lbnRzWzFdP2FyZ3VtZW50c1sxXTpcIiRhdXRvY2FwdHVyZVwiO2lmKHRoaXMuaXNFbmFibGVkKXt2YXIgcixuPU5pKGUpO2lmKFRpKG4pJiYobj1uLnBhcmVudE5vZGV8fG51bGwpLFwiJGF1dG9jYXB0dXJlXCI9PT1pJiZcImNsaWNrXCI9PT1lLnR5cGUmJmUgaW5zdGFuY2VvZiBNb3VzZUV2ZW50KXRoaXMuaW5zdGFuY2UuY29uZmlnLnJhZ2VjbGljayYmbnVsbCE9PShyPXRoaXMucmFnZWNsaWNrcykmJnZvaWQgMCE9PXImJnIuaXNSYWdlQ2xpY2soZS5jbGllbnRYLGUuY2xpZW50WSwobmV3IERhdGUpLmdldFRpbWUoKSkmJnRoaXMuX2NhcHR1cmVFdmVudChlLFwiJHJhZ2VjbGlja1wiKTt2YXIgcz1pPT09djtpZihuJiZIaShuLGUsdGhpcy5jb25maWcscyxzP1tcImNvcHlcIixcImN1dFwiXTp2b2lkIDApKXt2YXJ7cHJvcHM6byxleHBsaWNpdE5vQ2FwdHVyZTphfT1WcyhuLHtlOmUsbWFza0FsbEVsZW1lbnRBdHRyaWJ1dGVzOnRoaXMuaW5zdGFuY2UuY29uZmlnLm1hc2tfYWxsX2VsZW1lbnRfYXR0cmlidXRlcyxtYXNrQWxsVGV4dDp0aGlzLmluc3RhbmNlLmNvbmZpZy5tYXNrX2FsbF90ZXh0LGVsZW1lbnRBdHRyaWJ1dGVJZ25vcmVMaXN0OnRoaXMuY29uZmlnLmVsZW1lbnRfYXR0cmlidXRlX2lnbm9yZWxpc3QsZWxlbWVudHNDaGFpbkFzU3RyaW5nOnRoaXMuX2VsZW1lbnRzQ2hhaW5Bc1N0cmluZ30pO2lmKGEpcmV0dXJuITE7dmFyIGw9dGhpcy5nZXRFbGVtZW50U2VsZWN0b3JzKG4pO2lmKGwmJmwubGVuZ3RoPjAmJihvLiRlbGVtZW50X3NlbGVjdG9ycz1sKSxpPT09dil7dmFyIHUsYz1BaShudWxsPT10fHxudWxsPT09KHU9dC5nZXRTZWxlY3Rpb24oKSl8fHZvaWQgMD09PXU/dm9pZCAwOnUudG9TdHJpbmcoKSksZD1lLnR5cGV8fFwiY2xpcGJvYXJkXCI7aWYoIWMpcmV0dXJuITE7by4kc2VsZWN0ZWRfY29udGVudD1jLG8uJGNvcHlfdHlwZT1kfXJldHVybiB0aGlzLmluc3RhbmNlLmNhcHR1cmUoaSxvKSwhMH19fWlzQnJvd3NlclN1cHBvcnRlZCgpe3JldHVybiBJKG51bGw9PWE/dm9pZCAwOmEucXVlcnlTZWxlY3RvckFsbCl9fXZhciBKcz1CKFwiW1RyYWNpbmdIZWFkZXJzXVwiKTtjbGFzcyBZc3tjb25zdHJ1Y3RvcihlKXtXKHRoaXMsXCJfcmVzdG9yZVhIUlBhdGNoXCIsdm9pZCAwKSxXKHRoaXMsXCJfcmVzdG9yZUZldGNoUGF0Y2hcIix2b2lkIDApLFcodGhpcyxcIl9zdGFydENhcHR1cmluZ1wiLCgoKT0+e3ZhciBlLHQsaSxyO0YodGhpcy5fcmVzdG9yZVhIUlBhdGNoKSYmKG51bGw9PT0oZT1fLl9fUG9zdGhvZ0V4dGVuc2lvbnNfXyl8fHZvaWQgMD09PWV8fG51bGw9PT0odD1lLnRyYWNpbmdIZWFkZXJzUGF0Y2hGbnMpfHx2b2lkIDA9PT10fHx0Ll9wYXRjaFhIUih0aGlzLmluc3RhbmNlLnNlc3Npb25NYW5hZ2VyKSk7Rih0aGlzLl9yZXN0b3JlRmV0Y2hQYXRjaCkmJihudWxsPT09KGk9Xy5fX1Bvc3Rob2dFeHRlbnNpb25zX18pfHx2b2lkIDA9PT1pfHxudWxsPT09KHI9aS50cmFjaW5nSGVhZGVyc1BhdGNoRm5zKXx8dm9pZCAwPT09cnx8ci5fcGF0Y2hGZXRjaCh0aGlzLmluc3RhbmNlLnNlc3Npb25NYW5hZ2VyKSl9KSksdGhpcy5pbnN0YW5jZT1lfV9sb2FkU2NyaXB0KGUpe3ZhciB0LGkscjtudWxsIT09KHQ9Xy5fX1Bvc3Rob2dFeHRlbnNpb25zX18pJiZ2b2lkIDAhPT10JiZ0LnRyYWNpbmdIZWFkZXJzUGF0Y2hGbnMmJmUoKSxudWxsPT09KGk9Xy5fX1Bvc3Rob2dFeHRlbnNpb25zX18pfHx2b2lkIDA9PT1pfHxudWxsPT09KHI9aS5sb2FkRXh0ZXJuYWxEZXBlbmRlbmN5KXx8dm9pZCAwPT09cnx8ci5jYWxsKGksdGhpcy5pbnN0YW5jZSxcInRyYWNpbmctaGVhZGVyc1wiLCh0PT57aWYodClyZXR1cm4gSnMuZXJyb3IoXCJmYWlsZWQgdG8gbG9hZCBzY3JpcHRcIix0KTtlKCl9KSl9c3RhcnRJZkVuYWJsZWRPclN0b3AoKXt2YXIgZSx0O3RoaXMuaW5zdGFuY2UuY29uZmlnLl9fYWRkX3RyYWNpbmdfaGVhZGVycz90aGlzLl9sb2FkU2NyaXB0KHRoaXMuX3N0YXJ0Q2FwdHVyaW5nKToobnVsbD09PShlPXRoaXMuX3Jlc3RvcmVYSFJQYXRjaCl8fHZvaWQgMD09PWV8fGUuY2FsbCh0aGlzKSxudWxsPT09KHQ9dGhpcy5fcmVzdG9yZUZldGNoUGF0Y2gpfHx2b2lkIDA9PT10fHx0LmNhbGwodGhpcyksdGhpcy5fcmVzdG9yZVhIUlBhdGNoPXZvaWQgMCx0aGlzLl9yZXN0b3JlRmV0Y2hQYXRjaD12b2lkIDApfX12YXIgS3M7IWZ1bmN0aW9uKGUpe2VbZS5QRU5ESU5HPS0xXT1cIlBFTkRJTkdcIixlW2UuREVOSUVEPTBdPVwiREVOSUVEXCIsZVtlLkdSQU5URUQ9MV09XCJHUkFOVEVEXCJ9KEtzfHwoS3M9e30pKTtjbGFzcyBYc3tjb25zdHJ1Y3RvcihlKXt0aGlzLmluc3RhbmNlPWV9Z2V0IGNvbmZpZygpe3JldHVybiB0aGlzLmluc3RhbmNlLmNvbmZpZ31nZXQgY29uc2VudCgpe3JldHVybiB0aGlzLmdldERudCgpP0tzLkRFTklFRDp0aGlzLnN0b3JlZENvbnNlbnR9aXNPcHRlZE91dCgpe3JldHVybiB0aGlzLmNvbnNlbnQ9PT1Lcy5ERU5JRUR8fHRoaXMuY29uc2VudD09PUtzLlBFTkRJTkcmJnRoaXMuY29uZmlnLm9wdF9vdXRfY2FwdHVyaW5nX2J5X2RlZmF1bHR9aXNPcHRlZEluKCl7cmV0dXJuIXRoaXMuaXNPcHRlZE91dCgpfW9wdEluT3V0KGUpe3RoaXMuc3RvcmFnZS5zZXQodGhpcy5zdG9yYWdlS2V5LGU/MTowLHRoaXMuY29uZmlnLmNvb2tpZV9leHBpcmF0aW9uLHRoaXMuY29uZmlnLmNyb3NzX3N1YmRvbWFpbl9jb29raWUsdGhpcy5jb25maWcuc2VjdXJlX2Nvb2tpZSl9cmVzZXQoKXt0aGlzLnN0b3JhZ2UucmVtb3ZlKHRoaXMuc3RvcmFnZUtleSx0aGlzLmNvbmZpZy5jcm9zc19zdWJkb21haW5fY29va2llKX1nZXQgc3RvcmFnZUtleSgpe3Zhcnt0b2tlbjplLG9wdF9vdXRfY2FwdHVyaW5nX2Nvb2tpZV9wcmVmaXg6dH09dGhpcy5pbnN0YW5jZS5jb25maWc7cmV0dXJuKHR8fFwiX19waF9vcHRfaW5fb3V0X1wiKStlfWdldCBzdG9yZWRDb25zZW50KCl7dmFyIGU9dGhpcy5zdG9yYWdlLmdldCh0aGlzLnN0b3JhZ2VLZXkpO3JldHVyblwiMVwiPT09ZT9Lcy5HUkFOVEVEOlwiMFwiPT09ZT9Lcy5ERU5JRUQ6S3MuUEVORElOR31nZXQgc3RvcmFnZSgpe2lmKCF0aGlzLl9zdG9yYWdlKXt2YXIgZT10aGlzLmNvbmZpZy5vcHRfb3V0X2NhcHR1cmluZ19wZXJzaXN0ZW5jZV90eXBlO3RoaXMuX3N0b3JhZ2U9XCJsb2NhbFN0b3JhZ2VcIj09PWU/bHQ6b3Q7dmFyIHQ9XCJsb2NhbFN0b3JhZ2VcIj09PWU/b3Q6bHQ7dC5nZXQodGhpcy5zdG9yYWdlS2V5KSYmKHRoaXMuX3N0b3JhZ2UuZ2V0KHRoaXMuc3RvcmFnZUtleSl8fHRoaXMub3B0SW5PdXQoXCIxXCI9PT10LmdldCh0aGlzLnN0b3JhZ2VLZXkpKSx0LnJlbW92ZSh0aGlzLnN0b3JhZ2VLZXksdGhpcy5jb25maWcuY3Jvc3Nfc3ViZG9tYWluX2Nvb2tpZSkpfXJldHVybiB0aGlzLl9zdG9yYWdlfWdldERudCgpe3JldHVybiEhdGhpcy5jb25maWcucmVzcGVjdF9kbnQmJiEhbmUoW251bGw9PW8/dm9pZCAwOm8uZG9Ob3RUcmFjayxudWxsPT1vP3ZvaWQgMDpvLm1zRG9Ob3RUcmFjayxfLmRvTm90VHJhY2tdLChlPT5tKFshMCwxLFwiMVwiLFwieWVzXCJdLGUpKSl9fXZhciBRcz1CKFwiW0V4Y2VwdGlvbkF1dG9jYXB0dXJlXVwiKTtjbGFzcyBac3tjb25zdHJ1Y3RvcihlKXt2YXIgaTtXKHRoaXMsXCJvcmlnaW5hbE9uVW5oYW5kbGVkUmVqZWN0aW9uSGFuZGxlclwiLHZvaWQgMCksVyh0aGlzLFwic3RhcnRDYXB0dXJpbmdcIiwoKCk9Pnt2YXIgZSxpLHIsbjtpZih0JiZ0aGlzLmlzRW5hYmxlZCYmIXRoaXMuaGFzSGFuZGxlcnMmJiF0aGlzLmlzQ2FwdHVyaW5nKXt2YXIgcz1udWxsPT09KGU9Xy5fX1Bvc3Rob2dFeHRlbnNpb25zX18pfHx2b2lkIDA9PT1lfHxudWxsPT09KGk9ZS5lcnJvcldyYXBwaW5nRnVuY3Rpb25zKXx8dm9pZCAwPT09aT92b2lkIDA6aS53cmFwT25FcnJvcixvPW51bGw9PT0ocj1fLl9fUG9zdGhvZ0V4dGVuc2lvbnNfXyl8fHZvaWQgMD09PXJ8fG51bGw9PT0obj1yLmVycm9yV3JhcHBpbmdGdW5jdGlvbnMpfHx2b2lkIDA9PT1uP3ZvaWQgMDpuLndyYXBVbmhhbmRsZWRSZWplY3Rpb247aWYocyYmbyl0cnl7dGhpcy51bndyYXBPbkVycm9yPXModGhpcy5jYXB0dXJlRXhjZXB0aW9uLmJpbmQodGhpcykpLHRoaXMudW53cmFwVW5oYW5kbGVkUmVqZWN0aW9uPW8odGhpcy5jYXB0dXJlRXhjZXB0aW9uLmJpbmQodGhpcykpfWNhdGNoKGUpe1FzLmVycm9yKFwiZmFpbGVkIHRvIHN0YXJ0XCIsZSksdGhpcy5zdG9wQ2FwdHVyaW5nKCl9ZWxzZSBRcy5lcnJvcihcImZhaWxlZCB0byBsb2FkIGVycm9yIHdyYXBwaW5nIGZ1bmN0aW9ucyAtIGNhbm5vdCBzdGFydFwiKX19KSksdGhpcy5pbnN0YW5jZT1lLHRoaXMucmVtb3RlRW5hYmxlZD0hKG51bGw9PT0oaT10aGlzLmluc3RhbmNlLnBlcnNpc3RlbmNlKXx8dm9pZCAwPT09aXx8IWkucHJvcHNbY2VdKSx0aGlzLnN0YXJ0SWZFbmFibGVkKCl9Z2V0IGlzRW5hYmxlZCgpe3ZhciBlO3JldHVybiBudWxsIT09KGU9dGhpcy5yZW1vdGVFbmFibGVkKSYmdm9pZCAwIT09ZSYmZX1nZXQgaXNDYXB0dXJpbmcoKXt2YXIgZTtyZXR1cm4hKG51bGw9PXR8fG51bGw9PT0oZT10Lm9uZXJyb3IpfHx2b2lkIDA9PT1lfHwhZS5fX1BPU1RIT0dfSU5TVFJVTUVOVEVEX18pfWdldCBoYXNIYW5kbGVycygpe3JldHVybiB0aGlzLm9yaWdpbmFsT25VbmhhbmRsZWRSZWplY3Rpb25IYW5kbGVyfHx0aGlzLnVud3JhcE9uRXJyb3J9c3RhcnRJZkVuYWJsZWQoKXt0aGlzLmlzRW5hYmxlZCYmIXRoaXMuaXNDYXB0dXJpbmcmJihRcy5pbmZvKFwiZW5hYmxlZCwgc3RhcnRpbmcuLi5cIiksdGhpcy5sb2FkU2NyaXB0KHRoaXMuc3RhcnRDYXB0dXJpbmcpKX1sb2FkU2NyaXB0KGUpe3ZhciB0LGk7dGhpcy5oYXNIYW5kbGVycyYmZSgpLG51bGw9PT0odD1fLl9fUG9zdGhvZ0V4dGVuc2lvbnNfXyl8fHZvaWQgMD09PXR8fG51bGw9PT0oaT10LmxvYWRFeHRlcm5hbERlcGVuZGVuY3kpfHx2b2lkIDA9PT1pfHxpLmNhbGwodCx0aGlzLmluc3RhbmNlLFwiZXhjZXB0aW9uLWF1dG9jYXB0dXJlXCIsKHQ9PntpZih0KXJldHVybiBRcy5lcnJvcihcImZhaWxlZCB0byBsb2FkIHNjcmlwdFwiLHQpO2UoKX0pKX1zdG9wQ2FwdHVyaW5nKCl7dmFyIGUsdDtudWxsPT09KGU9dGhpcy51bndyYXBPbkVycm9yKXx8dm9pZCAwPT09ZXx8ZS5jYWxsKHRoaXMpLG51bGw9PT0odD10aGlzLnVud3JhcFVuaGFuZGxlZFJlamVjdGlvbil8fHZvaWQgMD09PXR8fHQuY2FsbCh0aGlzKX1vblJlbW90ZUNvbmZpZyhlKXt2YXIgdD1lLmF1dG9jYXB0dXJlRXhjZXB0aW9uczt0aGlzLnJlbW90ZUVuYWJsZWQ9ISF0fHwhMSx0aGlzLmluc3RhbmNlLnBlcnNpc3RlbmNlJiZ0aGlzLmluc3RhbmNlLnBlcnNpc3RlbmNlLnJlZ2lzdGVyKHtbY2VdOnRoaXMucmVtb3RlRW5hYmxlZH0pLHRoaXMuc3RhcnRJZkVuYWJsZWQoKX1jYXB0dXJlRXhjZXB0aW9uKGUpe3ZhciB0PXRoaXMuaW5zdGFuY2UucmVxdWVzdFJvdXRlci5lbmRwb2ludEZvcihcInVpXCIpO2UuJGV4Y2VwdGlvbl9wZXJzb25VUkw9XCJcIi5jb25jYXQodCxcIi9wcm9qZWN0L1wiKS5jb25jYXQodGhpcy5pbnN0YW5jZS5jb25maWcudG9rZW4sXCIvcGVyc29uL1wiKS5jb25jYXQodGhpcy5pbnN0YW5jZS5nZXRfZGlzdGluY3RfaWQoKSksdGhpcy5pbnN0YW5jZS5leGNlcHRpb25zLnNlbmRFeGNlcHRpb25FdmVudChlKX19dmFyIGVvPUIoXCJbV2ViIFZpdGFsc11cIiksdG89OWU1O2NsYXNzIGlve2NvbnN0cnVjdG9yKGUpe3ZhciB0O1codGhpcyxcIl9lbmFibGVkU2VydmVyU2lkZVwiLCExKSxXKHRoaXMsXCJfaW5pdGlhbGl6ZWRcIiwhMSksVyh0aGlzLFwiYnVmZmVyXCIse3VybDp2b2lkIDAsbWV0cmljczpbXSxmaXJzdE1ldHJpY1RpbWVzdGFtcDp2b2lkIDB9KSxXKHRoaXMsXCJfZmx1c2hUb0NhcHR1cmVcIiwoKCk9PntjbGVhclRpbWVvdXQodGhpcy5fZGVsYXllZEZsdXNoVGltZXIpLDAhPT10aGlzLmJ1ZmZlci5tZXRyaWNzLmxlbmd0aCYmKHRoaXMuaW5zdGFuY2UuY2FwdHVyZShcIiR3ZWJfdml0YWxzXCIsdGhpcy5idWZmZXIubWV0cmljcy5yZWR1Y2UoKChlLHQpPT5qKGooe30sZSkse30se1tcIiR3ZWJfdml0YWxzX1wiLmNvbmNhdCh0Lm5hbWUsXCJfZXZlbnRcIildOmooe30sdCksW1wiJHdlYl92aXRhbHNfXCIuY29uY2F0KHQubmFtZSxcIl92YWx1ZVwiKV06dC52YWx1ZX0pKSx7fSkpLHRoaXMuYnVmZmVyPXt1cmw6dm9pZCAwLG1ldHJpY3M6W10sZmlyc3RNZXRyaWNUaW1lc3RhbXA6dm9pZCAwfSl9KSksVyh0aGlzLFwiX2FkZFRvQnVmZmVyXCIsKGU9Pnt2YXIgdCxpPW51bGw9PT0odD10aGlzLmluc3RhbmNlLnNlc3Npb25NYW5hZ2VyKXx8dm9pZCAwPT09dD92b2lkIDA6dC5jaGVja0FuZEdldFNlc3Npb25BbmRXaW5kb3dJZCghMCk7aWYoRihpKSllby5lcnJvcihcIkNvdWxkIG5vdCByZWFkIHNlc3Npb24gSUQuIERyb3BwaW5nIG1ldHJpY3MhXCIpO2Vsc2V7dGhpcy5idWZmZXI9dGhpcy5idWZmZXJ8fHt1cmw6dm9pZCAwLG1ldHJpY3M6W10sZmlyc3RNZXRyaWNUaW1lc3RhbXA6dm9pZCAwfTt2YXIgcj10aGlzLl9jdXJyZW50VVJMKCk7aWYoIUYocikpaWYoTyhudWxsPT1lP3ZvaWQgMDplLm5hbWUpfHxPKG51bGw9PWU/dm9pZCAwOmUudmFsdWUpKWVvLmVycm9yKFwiSW52YWxpZCBtZXRyaWMgcmVjZWl2ZWRcIixlKTtlbHNlIGlmKHRoaXMuX21heEFsbG93ZWRWYWx1ZSYmZS52YWx1ZT49dGhpcy5fbWF4QWxsb3dlZFZhbHVlKWVvLmVycm9yKFwiSWdub3JpbmcgbWV0cmljIHdpdGggdmFsdWUgPj0gXCIrdGhpcy5fbWF4QWxsb3dlZFZhbHVlLGUpO2Vsc2UgdGhpcy5idWZmZXIudXJsIT09ciYmKHRoaXMuX2ZsdXNoVG9DYXB0dXJlKCksdGhpcy5fZGVsYXllZEZsdXNoVGltZXI9c2V0VGltZW91dCh0aGlzLl9mbHVzaFRvQ2FwdHVyZSx0aGlzLmZsdXNoVG9DYXB0dXJlVGltZW91dE1zKSksRih0aGlzLmJ1ZmZlci51cmwpJiYodGhpcy5idWZmZXIudXJsPXIpLHRoaXMuYnVmZmVyLmZpcnN0TWV0cmljVGltZXN0YW1wPUYodGhpcy5idWZmZXIuZmlyc3RNZXRyaWNUaW1lc3RhbXApP0RhdGUubm93KCk6dGhpcy5idWZmZXIuZmlyc3RNZXRyaWNUaW1lc3RhbXAsZS5hdHRyaWJ1dGlvbiYmZS5hdHRyaWJ1dGlvbi5pbnRlcmFjdGlvblRhcmdldEVsZW1lbnQmJihlLmF0dHJpYnV0aW9uLmludGVyYWN0aW9uVGFyZ2V0RWxlbWVudD12b2lkIDApLHRoaXMuYnVmZmVyLm1ldHJpY3MucHVzaChqKGooe30sZSkse30seyRjdXJyZW50X3VybDpyLCRzZXNzaW9uX2lkOmkuc2Vzc2lvbklkLCR3aW5kb3dfaWQ6aS53aW5kb3dJZCx0aW1lc3RhbXA6RGF0ZS5ub3coKX0pKSx0aGlzLmJ1ZmZlci5tZXRyaWNzLmxlbmd0aD09PXRoaXMuYWxsb3dlZE1ldHJpY3MubGVuZ3RoJiZ0aGlzLl9mbHVzaFRvQ2FwdHVyZSgpfX0pKSxXKHRoaXMsXCJfc3RhcnRDYXB0dXJpbmdcIiwoKCk9Pnt2YXIgZSx0LGkscixuPV8uX19Qb3N0aG9nRXh0ZW5zaW9uc19fO0Yobil8fEYobi5wb3N0SG9nV2ViVml0YWxzQ2FsbGJhY2tzKXx8KHtvbkxDUDplLG9uQ0xTOnQsb25GQ1A6aSxvbklOUDpyfT1uLnBvc3RIb2dXZWJWaXRhbHNDYWxsYmFja3MpLGUmJnQmJmkmJnI/KHRoaXMuYWxsb3dlZE1ldHJpY3MuaW5kZXhPZihcIkxDUFwiKT4tMSYmZSh0aGlzLl9hZGRUb0J1ZmZlci5iaW5kKHRoaXMpKSx0aGlzLmFsbG93ZWRNZXRyaWNzLmluZGV4T2YoXCJDTFNcIik+LTEmJnQodGhpcy5fYWRkVG9CdWZmZXIuYmluZCh0aGlzKSksdGhpcy5hbGxvd2VkTWV0cmljcy5pbmRleE9mKFwiRkNQXCIpPi0xJiZpKHRoaXMuX2FkZFRvQnVmZmVyLmJpbmQodGhpcykpLHRoaXMuYWxsb3dlZE1ldHJpY3MuaW5kZXhPZihcIklOUFwiKT4tMSYmcih0aGlzLl9hZGRUb0J1ZmZlci5iaW5kKHRoaXMpKSx0aGlzLl9pbml0aWFsaXplZD0hMCk6ZW8uZXJyb3IoXCJ3ZWIgdml0YWxzIGNhbGxiYWNrcyBub3QgbG9hZGVkIC0gbm90IHN0YXJ0aW5nXCIpfSkpLHRoaXMuaW5zdGFuY2U9ZSx0aGlzLl9lbmFibGVkU2VydmVyU2lkZT0hKG51bGw9PT0odD10aGlzLmluc3RhbmNlLnBlcnNpc3RlbmNlKXx8dm9pZCAwPT09dHx8IXQucHJvcHNbZGVdKSx0aGlzLnN0YXJ0SWZFbmFibGVkKCl9Z2V0IGFsbG93ZWRNZXRyaWNzKCl7dmFyIGUsdCxpPUModGhpcy5pbnN0YW5jZS5jb25maWcuY2FwdHVyZV9wZXJmb3JtYW5jZSk/bnVsbD09PShlPXRoaXMuaW5zdGFuY2UuY29uZmlnLmNhcHR1cmVfcGVyZm9ybWFuY2UpfHx2b2lkIDA9PT1lP3ZvaWQgMDplLndlYl92aXRhbHNfYWxsb3dlZF9tZXRyaWNzOnZvaWQgMDtyZXR1cm4gRihpKT8obnVsbD09PSh0PXRoaXMuaW5zdGFuY2UucGVyc2lzdGVuY2UpfHx2b2lkIDA9PT10P3ZvaWQgMDp0LnByb3BzW19lXSl8fFtcIkNMU1wiLFwiRkNQXCIsXCJJTlBcIixcIkxDUFwiXTppfWdldCBmbHVzaFRvQ2FwdHVyZVRpbWVvdXRNcygpe3JldHVybihDKHRoaXMuaW5zdGFuY2UuY29uZmlnLmNhcHR1cmVfcGVyZm9ybWFuY2UpP3RoaXMuaW5zdGFuY2UuY29uZmlnLmNhcHR1cmVfcGVyZm9ybWFuY2Uud2ViX3ZpdGFsc19kZWxheWVkX2ZsdXNoX21zOnZvaWQgMCl8fDVlM31nZXQgX21heEFsbG93ZWRWYWx1ZSgpe3ZhciBlPUModGhpcy5pbnN0YW5jZS5jb25maWcuY2FwdHVyZV9wZXJmb3JtYW5jZSkmJk0odGhpcy5pbnN0YW5jZS5jb25maWcuY2FwdHVyZV9wZXJmb3JtYW5jZS5fX3dlYl92aXRhbHNfbWF4X3ZhbHVlKT90aGlzLmluc3RhbmNlLmNvbmZpZy5jYXB0dXJlX3BlcmZvcm1hbmNlLl9fd2ViX3ZpdGFsc19tYXhfdmFsdWU6dG87cmV0dXJuIDA8ZSYmZTw9NmU0P3RvOmV9Z2V0IGlzRW5hYmxlZCgpe3ZhciBlPUModGhpcy5pbnN0YW5jZS5jb25maWcuY2FwdHVyZV9wZXJmb3JtYW5jZSk/dGhpcy5pbnN0YW5jZS5jb25maWcuY2FwdHVyZV9wZXJmb3JtYW5jZS53ZWJfdml0YWxzOnZvaWQgMDtyZXR1cm4gTChlKT9lOnRoaXMuX2VuYWJsZWRTZXJ2ZXJTaWRlfXN0YXJ0SWZFbmFibGVkKCl7dGhpcy5pc0VuYWJsZWQmJiF0aGlzLl9pbml0aWFsaXplZCYmKGVvLmluZm8oXCJlbmFibGVkLCBzdGFydGluZy4uLlwiKSx0aGlzLmxvYWRTY3JpcHQodGhpcy5fc3RhcnRDYXB0dXJpbmcpKX1vblJlbW90ZUNvbmZpZyhlKXt2YXIgdD1DKGUuY2FwdHVyZVBlcmZvcm1hbmNlKSYmISFlLmNhcHR1cmVQZXJmb3JtYW5jZS53ZWJfdml0YWxzLGk9QyhlLmNhcHR1cmVQZXJmb3JtYW5jZSk/ZS5jYXB0dXJlUGVyZm9ybWFuY2Uud2ViX3ZpdGFsc19hbGxvd2VkX21ldHJpY3M6dm9pZCAwO3RoaXMuaW5zdGFuY2UucGVyc2lzdGVuY2UmJih0aGlzLmluc3RhbmNlLnBlcnNpc3RlbmNlLnJlZ2lzdGVyKHtbZGVdOnR9KSx0aGlzLmluc3RhbmNlLnBlcnNpc3RlbmNlLnJlZ2lzdGVyKHtbX2VdOml9KSksdGhpcy5fZW5hYmxlZFNlcnZlclNpZGU9dCx0aGlzLnN0YXJ0SWZFbmFibGVkKCl9bG9hZFNjcmlwdChlKXt2YXIgdCxpLHI7bnVsbCE9PSh0PV8uX19Qb3N0aG9nRXh0ZW5zaW9uc19fKSYmdm9pZCAwIT09dCYmdC5wb3N0SG9nV2ViVml0YWxzQ2FsbGJhY2tzJiZlKCksbnVsbD09PShpPV8uX19Qb3N0aG9nRXh0ZW5zaW9uc19fKXx8dm9pZCAwPT09aXx8bnVsbD09PShyPWkubG9hZEV4dGVybmFsRGVwZW5kZW5jeSl8fHZvaWQgMD09PXJ8fHIuY2FsbChpLHRoaXMuaW5zdGFuY2UsXCJ3ZWItdml0YWxzXCIsKHQ9Pnt0P2VvLmVycm9yKFwiZmFpbGVkIHRvIGxvYWQgc2NyaXB0XCIsdCk6ZSgpfSkpfV9jdXJyZW50VVJMKCl7dmFyIGU9dD90LmxvY2F0aW9uLmhyZWY6dm9pZCAwO3JldHVybiBlfHxlby5lcnJvcihcIkNvdWxkIG5vdCBkZXRlcm1pbmUgY3VycmVudCBVUkxcIiksZX19dmFyIHJvPXtpY29udGFpbnM6KGUsaSk9PiEhdCYmaS5ocmVmLnRvTG93ZXJDYXNlKCkuaW5kZXhPZihlLnRvTG93ZXJDYXNlKCkpPi0xLG5vdF9pY29udGFpbnM6KGUsaSk9PiEhdCYmLTE9PT1pLmhyZWYudG9Mb3dlckNhc2UoKS5pbmRleE9mKGUudG9Mb3dlckNhc2UoKSkscmVnZXg6KGUsaSk9PiEhdCYmZnQoaS5ocmVmLGUpLG5vdF9yZWdleDooZSxpKT0+ISF0JiYhZnQoaS5ocmVmLGUpLGV4YWN0OihlLHQpPT50LmhyZWY9PT1lLGlzX25vdDooZSx0KT0+dC5ocmVmIT09ZX07Y2xhc3Mgbm97Y29uc3RydWN0b3IoZSl7dmFyIHQ9dGhpcztXKHRoaXMsXCJnZXRXZWJFeHBlcmltZW50c0FuZEV2YWx1YXRlRGlzcGxheUxvZ2ljXCIsKGZ1bmN0aW9uKCl7dmFyIGU9YXJndW1lbnRzLmxlbmd0aD4wJiZ2b2lkIDAhPT1hcmd1bWVudHNbMF0mJmFyZ3VtZW50c1swXTt0LmdldFdlYkV4cGVyaW1lbnRzKChlPT57bm8ubG9nSW5mbyhcInJldHJpZXZlZCB3ZWIgZXhwZXJpbWVudHMgZnJvbSB0aGUgc2VydmVyXCIpLHQuX2ZsYWdUb0V4cGVyaW1lbnRzPW5ldyBNYXAsZS5mb3JFYWNoKChlPT57aWYoZS5mZWF0dXJlX2ZsYWdfa2V5KXt2YXIgaTtpZih0Ll9mbGFnVG9FeHBlcmltZW50cyluby5sb2dJbmZvKFwic2V0dGluZyBmbGFnIGtleSBcIixlLmZlYXR1cmVfZmxhZ19rZXksXCIgdG8gd2ViIGV4cGVyaW1lbnQgXCIsZSksbnVsbD09PShpPXQuX2ZsYWdUb0V4cGVyaW1lbnRzKXx8dm9pZCAwPT09aXx8aS5zZXQoZS5mZWF0dXJlX2ZsYWdfa2V5LGUpO3ZhciByPXQuaW5zdGFuY2UuZ2V0RmVhdHVyZUZsYWcoZS5mZWF0dXJlX2ZsYWdfa2V5KTtSKHIpJiZlLnZhcmlhbnRzW3JdJiZ0LmFwcGx5VHJhbnNmb3JtcyhlLm5hbWUscixlLnZhcmlhbnRzW3JdLnRyYW5zZm9ybXMpfWVsc2UgaWYoZS52YXJpYW50cylmb3IodmFyIG4gaW4gZS52YXJpYW50cyl7dmFyIHM9ZS52YXJpYW50c1tuXTtuby5tYXRjaGVzVGVzdFZhcmlhbnQocykmJnQuYXBwbHlUcmFuc2Zvcm1zKGUubmFtZSxuLHMudHJhbnNmb3Jtcyl9fSkpfSksZSl9KSksdGhpcy5pbnN0YW5jZT1lLHRoaXMuaW5zdGFuY2Uub25GZWF0dXJlRmxhZ3MoKGU9Pnt0aGlzLm9uRmVhdHVyZUZsYWdzKGUpfSkpfW9uRmVhdHVyZUZsYWdzKGUpe2lmKHRoaXMuX2lzX2JvdCgpKW5vLmxvZ0luZm8oXCJSZWZ1c2luZyB0byByZW5kZXIgd2ViIGV4cGVyaW1lbnQgc2luY2UgdGhlIHZpZXdlciBpcyBhIGxpa2VseSBib3RcIik7ZWxzZSBpZighdGhpcy5pbnN0YW5jZS5jb25maWcuZGlzYWJsZV93ZWJfZXhwZXJpbWVudHMpe2lmKE8odGhpcy5fZmxhZ1RvRXhwZXJpbWVudHMpKXJldHVybiB0aGlzLl9mbGFnVG9FeHBlcmltZW50cz1uZXcgTWFwLHRoaXMubG9hZElmRW5hYmxlZCgpLHZvaWQgdGhpcy5wcmV2aWV3V2ViRXhwZXJpbWVudCgpO25vLmxvZ0luZm8oXCJhcHBseWluZyBmZWF0dXJlIGZsYWdzXCIsZSksZS5mb3JFYWNoKChlPT57dmFyIHQ7aWYodGhpcy5fZmxhZ1RvRXhwZXJpbWVudHMmJm51bGwhPT0odD10aGlzLl9mbGFnVG9FeHBlcmltZW50cykmJnZvaWQgMCE9PXQmJnQuaGFzKGUpKXt2YXIgaSxyPXRoaXMuaW5zdGFuY2UuZ2V0RmVhdHVyZUZsYWcoZSksbj1udWxsPT09KGk9dGhpcy5fZmxhZ1RvRXhwZXJpbWVudHMpfHx2b2lkIDA9PT1pP3ZvaWQgMDppLmdldChlKTtyJiZudWxsIT1uJiZuLnZhcmlhbnRzW3JdJiZ0aGlzLmFwcGx5VHJhbnNmb3JtcyhuLm5hbWUscixuLnZhcmlhbnRzW3JdLnRyYW5zZm9ybXMpfX0pKX19cHJldmlld1dlYkV4cGVyaW1lbnQoKXt2YXIgZT1uby5nZXRXaW5kb3dMb2NhdGlvbigpO2lmKG51bGwhPWUmJmUuc2VhcmNoKXt2YXIgdD1idChudWxsPT1lP3ZvaWQgMDplLnNlYXJjaCxcIl9fZXhwZXJpbWVudF9pZFwiKSxpPWJ0KG51bGw9PWU/dm9pZCAwOmUuc2VhcmNoLFwiX19leHBlcmltZW50X3ZhcmlhbnRcIik7dCYmaSYmKG5vLmxvZ0luZm8oXCJwcmV2aWV3aW5nIHdlYiBleHBlcmltZW50cyBcIi5jb25jYXQodCxcIiAmJiBcIikuY29uY2F0KGkpKSx0aGlzLmdldFdlYkV4cGVyaW1lbnRzKChlPT57dGhpcy5zaG93UHJldmlld1dlYkV4cGVyaW1lbnQocGFyc2VJbnQodCksaSxlKX0pLCExLCEwKSl9fWxvYWRJZkVuYWJsZWQoKXt0aGlzLmluc3RhbmNlLmNvbmZpZy5kaXNhYmxlX3dlYl9leHBlcmltZW50c3x8dGhpcy5nZXRXZWJFeHBlcmltZW50c0FuZEV2YWx1YXRlRGlzcGxheUxvZ2ljKCl9Z2V0V2ViRXhwZXJpbWVudHMoZSx0LGkpe2lmKHRoaXMuaW5zdGFuY2UuY29uZmlnLmRpc2FibGVfd2ViX2V4cGVyaW1lbnRzJiYhaSlyZXR1cm4gZShbXSk7dmFyIHI9dGhpcy5pbnN0YW5jZS5nZXRfcHJvcGVydHkoXCIkd2ViX2V4cGVyaW1lbnRzXCIpO2lmKHImJiF0KXJldHVybiBlKHIpO3RoaXMuaW5zdGFuY2UuX3NlbmRfcmVxdWVzdCh7dXJsOnRoaXMuaW5zdGFuY2UucmVxdWVzdFJvdXRlci5lbmRwb2ludEZvcihcImFwaVwiLFwiL2FwaS93ZWJfZXhwZXJpbWVudHMvP3Rva2VuPVwiLmNvbmNhdCh0aGlzLmluc3RhbmNlLmNvbmZpZy50b2tlbikpLG1ldGhvZDpcIkdFVFwiLGNhbGxiYWNrOnQ9PntpZigyMDAhPT10LnN0YXR1c0NvZGV8fCF0Lmpzb24pcmV0dXJuIGUoW10pO3ZhciBpPXQuanNvbi5leHBlcmltZW50c3x8W107cmV0dXJuIGUoaSl9fSl9c2hvd1ByZXZpZXdXZWJFeHBlcmltZW50KGUsdCxpKXt2YXIgcj1pLmZpbHRlcigodD0+dC5pZD09PWUpKTtyJiZyLmxlbmd0aD4wJiYobm8ubG9nSW5mbyhcIlByZXZpZXdpbmcgd2ViIGV4cGVyaW1lbnQgW1wiLmNvbmNhdChyWzBdLm5hbWUsXCJdIHdpdGggdmFyaWFudCBbXCIpLmNvbmNhdCh0LFwiXVwiKSksdGhpcy5hcHBseVRyYW5zZm9ybXMoclswXS5uYW1lLHQsclswXS52YXJpYW50c1t0XS50cmFuc2Zvcm1zKSl9c3RhdGljIG1hdGNoZXNUZXN0VmFyaWFudChlKXtyZXR1cm4hTyhlLmNvbmRpdGlvbnMpJiYobm8ubWF0Y2hVcmxDb25kaXRpb25zKGUpJiZuby5tYXRjaFVUTUNvbmRpdGlvbnMoZSkpfXN0YXRpYyBtYXRjaFVybENvbmRpdGlvbnMoZSl7dmFyIHQ7aWYoTyhlLmNvbmRpdGlvbnMpfHxPKG51bGw9PT0odD1lLmNvbmRpdGlvbnMpfHx2b2lkIDA9PT10P3ZvaWQgMDp0LnVybCkpcmV0dXJuITA7dmFyIGkscixuLHM9bm8uZ2V0V2luZG93TG9jYXRpb24oKTtyZXR1cm4hIXMmJihudWxsPT09KGk9ZS5jb25kaXRpb25zKXx8dm9pZCAwPT09aXx8IWkudXJsfHxyb1tudWxsIT09KHI9bnVsbD09PShuPWUuY29uZGl0aW9ucyl8fHZvaWQgMD09PW4/dm9pZCAwOm4udXJsTWF0Y2hUeXBlKSYmdm9pZCAwIT09cj9yOlwiaWNvbnRhaW5zXCJdKGUuY29uZGl0aW9ucy51cmwscykpfXN0YXRpYyBnZXRXaW5kb3dMb2NhdGlvbigpe3JldHVybiBudWxsPT10P3ZvaWQgMDp0LmxvY2F0aW9ufXN0YXRpYyBtYXRjaFVUTUNvbmRpdGlvbnMoZSl7dmFyIHQ7aWYoTyhlLmNvbmRpdGlvbnMpfHxPKG51bGw9PT0odD1lLmNvbmRpdGlvbnMpfHx2b2lkIDA9PT10P3ZvaWQgMDp0LnV0bSkpcmV0dXJuITA7dmFyIGk9d2kuY2FtcGFpZ25QYXJhbXMoKTtpZihpLnV0bV9zb3VyY2Upe3ZhciByLG4scyxvLGEsbCx1LGMsZCxoLF8scCx2LGcsZixtLGI9bnVsbD09PShyPWUuY29uZGl0aW9ucyl8fHZvaWQgMD09PXJ8fG51bGw9PT0obj1yLnV0bSl8fHZvaWQgMD09PW58fCFuLnV0bV9jYW1wYWlnbnx8KG51bGw9PT0ocz1lLmNvbmRpdGlvbnMpfHx2b2lkIDA9PT1zfHxudWxsPT09KG89cy51dG0pfHx2b2lkIDA9PT1vP3ZvaWQgMDpvLnV0bV9jYW1wYWlnbik9PWkudXRtX2NhbXBhaWduLHk9bnVsbD09PShhPWUuY29uZGl0aW9ucyl8fHZvaWQgMD09PWF8fG51bGw9PT0obD1hLnV0bSl8fHZvaWQgMD09PWx8fCFsLnV0bV9zb3VyY2V8fChudWxsPT09KHU9ZS5jb25kaXRpb25zKXx8dm9pZCAwPT09dXx8bnVsbD09PShjPXUudXRtKXx8dm9pZCAwPT09Yz92b2lkIDA6Yy51dG1fc291cmNlKT09aS51dG1fc291cmNlLHc9bnVsbD09PShkPWUuY29uZGl0aW9ucyl8fHZvaWQgMD09PWR8fG51bGw9PT0oaD1kLnV0bSl8fHZvaWQgMD09PWh8fCFoLnV0bV9tZWRpdW18fChudWxsPT09KF89ZS5jb25kaXRpb25zKXx8dm9pZCAwPT09X3x8bnVsbD09PShwPV8udXRtKXx8dm9pZCAwPT09cD92b2lkIDA6cC51dG1fbWVkaXVtKT09aS51dG1fbWVkaXVtLFM9bnVsbD09PSh2PWUuY29uZGl0aW9ucyl8fHZvaWQgMD09PXZ8fG51bGw9PT0oZz12LnV0bSl8fHZvaWQgMD09PWd8fCFnLnV0bV90ZXJtfHwobnVsbD09PShmPWUuY29uZGl0aW9ucyl8fHZvaWQgMD09PWZ8fG51bGw9PT0obT1mLnV0bSl8fHZvaWQgMD09PW0/dm9pZCAwOm0udXRtX3Rlcm0pPT1pLnV0bV90ZXJtO3JldHVybiBiJiZ3JiZTJiZ5fXJldHVybiExfXN0YXRpYyBsb2dJbmZvKGUpe2Zvcih2YXIgdD1hcmd1bWVudHMubGVuZ3RoLGk9bmV3IEFycmF5KHQ+MT90LTE6MCkscj0xO3I8dDtyKyspaVtyLTFdPWFyZ3VtZW50c1tyXTtxLmluZm8oXCJbV2ViRXhwZXJpbWVudHNdIFwiLmNvbmNhdChlKSxpKX1hcHBseVRyYW5zZm9ybXMoZSx0LGkpe3RoaXMuX2lzX2JvdCgpP25vLmxvZ0luZm8oXCJSZWZ1c2luZyB0byByZW5kZXIgd2ViIGV4cGVyaW1lbnQgc2luY2UgdGhlIHZpZXdlciBpcyBhIGxpa2VseSBib3RcIik6XCJjb250cm9sXCIhPT10P2kuZm9yRWFjaCgoaT0+e2lmKGkuc2VsZWN0b3Ipe3ZhciByO25vLmxvZ0luZm8oXCJhcHBseWluZyB0cmFuc2Zvcm0gb2YgdmFyaWFudCBcIi5jb25jYXQodCxcIiBmb3IgZXhwZXJpbWVudCBcIikuY29uY2F0KGUsXCIgXCIpLGkpO3ZhciBuPW51bGw9PT0ocj1kb2N1bWVudCl8fHZvaWQgMD09PXI/dm9pZCAwOnIucXVlcnlTZWxlY3RvckFsbChpLnNlbGVjdG9yKTtudWxsPT1ufHxuLmZvckVhY2goKGU9Pnt2YXIgdD1lO2kuYXR0cmlidXRlcyYmaS5hdHRyaWJ1dGVzLmZvckVhY2goKGU9Pntzd2l0Y2goZS5uYW1lKXtjYXNlXCJ0ZXh0XCI6dC5pbm5lclRleHQ9ZS52YWx1ZTticmVhaztjYXNlXCJodG1sXCI6dC5pbm5lckhUTUw9ZS52YWx1ZTticmVhaztjYXNlXCJjc3NDbGFzc1wiOnQuY2xhc3NOYW1lPWUudmFsdWU7YnJlYWs7ZGVmYXVsdDp0LnNldEF0dHJpYnV0ZShlLm5hbWUsZS52YWx1ZSl9fSkpLGkudGV4dCYmKHQuaW5uZXJUZXh0PWkudGV4dCksaS5odG1sJiYodC5wYXJlbnRFbGVtZW50P3QucGFyZW50RWxlbWVudC5pbm5lckhUTUw9aS5odG1sOnQuaW5uZXJIVE1MPWkuaHRtbCksaS5jc3MmJnQuc2V0QXR0cmlidXRlKFwic3R5bGVcIixpLmNzcyl9KSl9fSkpOm5vLmxvZ0luZm8oXCJDb250cm9sIHZhcmlhbnRzIGxlYXZlIHRoZSBwYWdlIHVubW9kaWZpZWQuXCIpfV9pc19ib3QoKXtyZXR1cm4gbyYmdGhpcy5pbnN0YW5jZT8kcyhvLHRoaXMuaW5zdGFuY2UuY29uZmlnLmN1c3RvbV9ibG9ja2VkX3VzZXJhZ2VudHMpOnZvaWQgMH19Y2xhc3Mgc297Y29uc3RydWN0b3IoZSl7dGhpcy5pbnN0YW5jZT1lfXNlbmRFeGNlcHRpb25FdmVudChlKXt0aGlzLmluc3RhbmNlLmNhcHR1cmUoXCIkZXhjZXB0aW9uXCIsZSx7X25vVHJ1bmNhdGU6ITAsX2JhdGNoS2V5OlwiZXhjZXB0aW9uRXZlbnRcIn0pfX12YXIgb289W1wiJHNldF9vbmNlXCIsXCIkc2V0XCJdLGFvPUIoXCJbU2l0ZUFwcHNdXCIpO2NsYXNzIGxve2NvbnN0cnVjdG9yKGUpe3RoaXMuaW5zdGFuY2U9ZSx0aGlzLmJ1ZmZlcmVkSW52b2NhdGlvbnM9W10sdGhpcy5hcHBzPXt9fWdldCBpc0VuYWJsZWQoKXtyZXR1cm4hIXRoaXMuaW5zdGFuY2UuY29uZmlnLm9wdF9pbl9zaXRlX2FwcHN9ZXZlbnRDb2xsZWN0b3IoZSx0KXtpZih0KXt2YXIgaT10aGlzLmdsb2JhbHNGb3JFdmVudCh0KTt0aGlzLmJ1ZmZlcmVkSW52b2NhdGlvbnMucHVzaChpKSx0aGlzLmJ1ZmZlcmVkSW52b2NhdGlvbnMubGVuZ3RoPjFlMyYmKHRoaXMuYnVmZmVyZWRJbnZvY2F0aW9ucz10aGlzLmJ1ZmZlcmVkSW52b2NhdGlvbnMuc2xpY2UoMTApKX19Z2V0IHNpdGVBcHBMb2FkZXJzKCl7dmFyIGUsdDtyZXR1cm4gbnVsbD09PShlPV8uX1BPU1RIT0dfUkVNT1RFX0NPTkZJRyl8fHZvaWQgMD09PWV8fG51bGw9PT0odD1lW3RoaXMuaW5zdGFuY2UuY29uZmlnLnRva2VuXSl8fHZvaWQgMD09PXQ/dm9pZCAwOnQuc2l0ZUFwcHN9aW5pdCgpe2lmKHRoaXMuaXNFbmFibGVkKXt2YXIgZT10aGlzLmluc3RhbmNlLl9hZGRDYXB0dXJlSG9vayh0aGlzLmV2ZW50Q29sbGVjdG9yLmJpbmQodGhpcykpO3RoaXMuc3RvcEJ1ZmZlcmluZz0oKT0+e2UoKSx0aGlzLmJ1ZmZlcmVkSW52b2NhdGlvbnM9W10sdGhpcy5zdG9wQnVmZmVyaW5nPXZvaWQgMH19fWdsb2JhbHNGb3JFdmVudChlKXt2YXIgdCxpLHIsbixzLG8sYTtpZighZSl0aHJvdyBuZXcgRXJyb3IoXCJFdmVudCBwYXlsb2FkIGlzIHJlcXVpcmVkXCIpO3ZhciBsPXt9LHU9dGhpcy5pbnN0YW5jZS5nZXRfcHJvcGVydHkoXCIkZ3JvdXBzXCIpfHxbXSxjPXRoaXMuaW5zdGFuY2UuZ2V0X3Byb3BlcnR5KFwiJHN0b3JlZF9ncm91cF9wcm9wZXJ0aWVzXCIpfHx7fTtmb3IodmFyW2QsaF1vZiBPYmplY3QuZW50cmllcyhjKSlsW2RdPXtpZDp1W2RdLHR5cGU6ZCxwcm9wZXJ0aWVzOmh9O3Zhcnskc2V0X29uY2U6Xywkc2V0OnB9PWU7cmV0dXJue2V2ZW50Omooaih7fSxWKGUsb28pKSx7fSx7cHJvcGVydGllczpqKGooaih7fSxlLnByb3BlcnRpZXMpLHA/eyRzZXQ6aihqKHt9LG51bGwhPT0odD1udWxsPT09KGk9ZS5wcm9wZXJ0aWVzKXx8dm9pZCAwPT09aT92b2lkIDA6aS4kc2V0KSYmdm9pZCAwIT09dD90Ont9KSxwKX06e30pLF8/eyRzZXRfb25jZTpqKGooe30sbnVsbCE9PShyPW51bGw9PT0obj1lLnByb3BlcnRpZXMpfHx2b2lkIDA9PT1uP3ZvaWQgMDpuLiRzZXRfb25jZSkmJnZvaWQgMCE9PXI/cjp7fSksXyl9Ont9KSxlbGVtZW50c19jaGFpbjpudWxsIT09KHM9bnVsbD09PShvPWUucHJvcGVydGllcyl8fHZvaWQgMD09PW8/dm9pZCAwOm8uJGVsZW1lbnRzX2NoYWluKSYmdm9pZCAwIT09cz9zOlwiXCIsZGlzdGluY3RfaWQ6bnVsbD09PShhPWUucHJvcGVydGllcyl8fHZvaWQgMD09PWE/dm9pZCAwOmEuZGlzdGluY3RfaWR9KSxwZXJzb246e3Byb3BlcnRpZXM6dGhpcy5pbnN0YW5jZS5nZXRfcHJvcGVydHkoXCIkc3RvcmVkX3BlcnNvbl9wcm9wZXJ0aWVzXCIpfSxncm91cHM6bH19c2V0dXBTaXRlQXBwKGUpe3ZhciB0PXtpZDplLmlkLGxvYWRlZDohMSxlcnJvcmVkOiExfTt0aGlzLmFwcHNbZS5pZF09dDt2YXIgaT1pPT57dmFyIHI7Zm9yKHZhciBuIG9mKHRoaXMuYXBwc1tlLmlkXS5lcnJvcmVkPSFpLHRoaXMuYXBwc1tlLmlkXS5sb2FkZWQ9ITAsYW8uaW5mbyhcIlNpdGUgYXBwIHdpdGggaWQgXCIuY29uY2F0KGUuaWQsXCIgXCIpLmNvbmNhdChpP1wibG9hZGVkXCI6XCJlcnJvcmVkXCIpKSxpJiZ0aGlzLmJ1ZmZlcmVkSW52b2NhdGlvbnMubGVuZ3RoJiYoYW8uaW5mbyhcIlByb2Nlc3NpbmcgXCIuY29uY2F0KHRoaXMuYnVmZmVyZWRJbnZvY2F0aW9ucy5sZW5ndGgsXCIgZXZlbnRzIGZvciBzaXRlIGFwcCB3aXRoIGlkIFwiKS5jb25jYXQoZS5pZCkpLHRoaXMuYnVmZmVyZWRJbnZvY2F0aW9ucy5mb3JFYWNoKChlPT57dmFyIGk7cmV0dXJuIG51bGw9PT0oaT10LnByb2Nlc3NFdmVudCl8fHZvaWQgMD09PWk/dm9pZCAwOmkuY2FsbCh0LGUpfSkpKSxPYmplY3QudmFsdWVzKHRoaXMuYXBwcykpKWlmKCFuLmxvYWRlZClyZXR1cm47bnVsbD09PShyPXRoaXMuc3RvcEJ1ZmZlcmluZyl8fHZvaWQgMD09PXJ8fHIuY2FsbCh0aGlzKX07dHJ5e3Zhcntwcm9jZXNzRXZlbnQ6cn09ZS5pbml0KHtwb3N0aG9nOnRoaXMuaW5zdGFuY2UsY2FsbGJhY2s6ZT0+e2koZSl9fSk7ciYmKHQucHJvY2Vzc0V2ZW50PXIpfWNhdGNoKHQpe2FvLmVycm9yKFwiRXJyb3Igd2hpbGUgaW5pdGlhbGl6aW5nIFBvc3RIb2cgYXBwIHdpdGggY29uZmlnIGlkIFwiLmNvbmNhdChlLmlkKSx0KSxpKCExKX19b25DYXB0dXJlZEV2ZW50KGUpe2lmKDAhPT1PYmplY3Qua2V5cyh0aGlzLmFwcHMpLmxlbmd0aCl7dmFyIHQ9dGhpcy5nbG9iYWxzRm9yRXZlbnQoZSk7Zm9yKHZhciBpIG9mIE9iamVjdC52YWx1ZXModGhpcy5hcHBzKSl0cnl7dmFyIHI7bnVsbD09PShyPWkucHJvY2Vzc0V2ZW50KXx8dm9pZCAwPT09cnx8ci5jYWxsKGksdCl9Y2F0Y2godCl7YW8uZXJyb3IoXCJFcnJvciB3aGlsZSBwcm9jZXNzaW5nIGV2ZW50IFwiLmNvbmNhdChlLmV2ZW50LFwiIGZvciBzaXRlIGFwcCBcIikuY29uY2F0KGkuaWQpLHQpfX19b25SZW1vdGVDb25maWcoZSl7dmFyIHQsaSxyLG49dGhpcztpZihudWxsIT09KHQ9dGhpcy5zaXRlQXBwTG9hZGVycykmJnZvaWQgMCE9PXQmJnQubGVuZ3RoKXtpZighdGhpcy5pc0VuYWJsZWQpcmV0dXJuIHZvaWQgYW8uZXJyb3IoJ1Bvc3RIb2cgc2l0ZSBhcHBzIGFyZSBkaXNhYmxlZC4gRW5hYmxlIHRoZSBcIm9wdF9pbl9zaXRlX2FwcHNcIiBjb25maWcgdG8gcHJvY2VlZC4nKTtmb3IodmFyIHMgb2YgdGhpcy5zaXRlQXBwTG9hZGVycyl0aGlzLnNldHVwU2l0ZUFwcChzKTt0aGlzLmluc3RhbmNlLm9uKFwiZXZlbnRDYXB0dXJlZFwiLChlPT50aGlzLm9uQ2FwdHVyZWRFdmVudChlKSkpfWVsc2UgaWYobnVsbD09PShpPXRoaXMuc3RvcEJ1ZmZlcmluZyl8fHZvaWQgMD09PWl8fGkuY2FsbCh0aGlzKSxudWxsIT09KHI9ZS5zaXRlQXBwcykmJnZvaWQgMCE9PXImJnIubGVuZ3RoKWlmKHRoaXMuaXNFbmFibGVkKXt2YXIgbz1mdW5jdGlvbihlLHQpe3ZhciBpLHI7X1tcIl9fJCRwaF9zaXRlX2FwcF9cIi5jb25jYXQoZSldPW4uaW5zdGFuY2UsbnVsbD09PShpPV8uX19Qb3N0aG9nRXh0ZW5zaW9uc19fKXx8dm9pZCAwPT09aXx8bnVsbD09PShyPWkubG9hZFNpdGVBcHApfHx2b2lkIDA9PT1yfHxyLmNhbGwoaSxuLmluc3RhbmNlLHQsKHQ9PntpZih0KXJldHVybiBhby5lcnJvcihcIkVycm9yIHdoaWxlIGluaXRpYWxpemluZyBQb3N0SG9nIGFwcCB3aXRoIGNvbmZpZyBpZCBcIi5jb25jYXQoZSksdCl9KSl9O2Zvcih2YXJ7aWQ6YSx1cmw6bH1vZiBlLnNpdGVBcHBzKW8oYSxsKX1lbHNlIGFvLmVycm9yKCdQb3N0SG9nIHNpdGUgYXBwcyBhcmUgZGlzYWJsZWQuIEVuYWJsZSB0aGUgXCJvcHRfaW5fc2l0ZV9hcHBzXCIgY29uZmlnIHRvIHByb2NlZWQuJyl9fWZ1bmN0aW9uIHVvKGUsdCxpKXtyZXR1cm4gYm4oe2Rpc3RpbmN0X2lkOmUsdXNlclByb3BlcnRpZXNUb1NldDp0LHVzZXJQcm9wZXJ0aWVzVG9TZXRPbmNlOml9KX12YXIgY289e30saG89KCk9Pnt9LF9vPVwicG9zdGhvZ1wiLHBvPSFnbiYmLTE9PT0obnVsbD09aD92b2lkIDA6aC5pbmRleE9mKFwiTVNJRVwiKSkmJi0xPT09KG51bGw9PWg/dm9pZCAwOmguaW5kZXhPZihcIk1vemlsbGFcIikpLHZvPSgpPT57dmFyIGUsaSxyO3JldHVybnthcGlfaG9zdDpcImh0dHBzOi8vdXMuaS5wb3N0aG9nLmNvbVwiLHVpX2hvc3Q6bnVsbCx0b2tlbjpcIlwiLGF1dG9jYXB0dXJlOiEwLHJhZ2VjbGljazohMCxjcm9zc19zdWJkb21haW5fY29va2llOihpPW51bGw9PWE/dm9pZCAwOmEubG9jYXRpb24scj1udWxsPT1pP3ZvaWQgMDppLmhvc3RuYW1lLCEhUihyKSYmXCJoZXJva3VhcHAuY29tXCIhPT1yLnNwbGl0KFwiLlwiKS5zbGljZSgtMikuam9pbihcIi5cIikpLHBlcnNpc3RlbmNlOlwibG9jYWxTdG9yYWdlK2Nvb2tpZVwiLHBlcnNpc3RlbmNlX25hbWU6XCJcIixsb2FkZWQ6aG8sc3RvcmVfZ29vZ2xlOiEwLGN1c3RvbV9jYW1wYWlnbl9wYXJhbXM6W10sY3VzdG9tX2Jsb2NrZWRfdXNlcmFnZW50czpbXSxzYXZlX3JlZmVycmVyOiEwLGNhcHR1cmVfcGFnZXZpZXc6ITAsY2FwdHVyZV9wYWdlbGVhdmU6XCJpZl9jYXB0dXJlX3BhZ2V2aWV3XCIsZGVidWc6bCYmUihudWxsPT1sP3ZvaWQgMDpsLnNlYXJjaCkmJi0xIT09bC5zZWFyY2guaW5kZXhPZihcIl9fcG9zdGhvZ19kZWJ1Zz10cnVlXCIpfHwhMSx2ZXJib3NlOiExLGNvb2tpZV9leHBpcmF0aW9uOjM2NSx1cGdyYWRlOiExLGRpc2FibGVfc2Vzc2lvbl9yZWNvcmRpbmc6ITEsZGlzYWJsZV9wZXJzaXN0ZW5jZTohMSxkaXNhYmxlX3dlYl9leHBlcmltZW50czohMCxkaXNhYmxlX3N1cnZleXM6ITEsZW5hYmxlX3JlY29yZGluZ19jb25zb2xlX2xvZzp2b2lkIDAsc2VjdXJlX2Nvb2tpZTpcImh0dHBzOlwiPT09KG51bGw9PXR8fG51bGw9PT0oZT10LmxvY2F0aW9uKXx8dm9pZCAwPT09ZT92b2lkIDA6ZS5wcm90b2NvbCksaXA6ITAsb3B0X291dF9jYXB0dXJpbmdfYnlfZGVmYXVsdDohMSxvcHRfb3V0X3BlcnNpc3RlbmNlX2J5X2RlZmF1bHQ6ITEsb3B0X291dF91c2VyYWdlbnRfZmlsdGVyOiExLG9wdF9vdXRfY2FwdHVyaW5nX3BlcnNpc3RlbmNlX3R5cGU6XCJsb2NhbFN0b3JhZ2VcIixvcHRfb3V0X2NhcHR1cmluZ19jb29raWVfcHJlZml4Om51bGwsb3B0X2luX3NpdGVfYXBwczohMSxwcm9wZXJ0eV9kZW55bGlzdDpbXSxyZXNwZWN0X2RudDohMSxzYW5pdGl6ZV9wcm9wZXJ0aWVzOm51bGwscmVxdWVzdF9oZWFkZXJzOnt9LGluYXBwX3Byb3RvY29sOlwiLy9cIixpbmFwcF9saW5rX25ld193aW5kb3c6ITEscmVxdWVzdF9iYXRjaGluZzohMCxwcm9wZXJ0aWVzX3N0cmluZ19tYXhfbGVuZ3RoOjY1NTM1LHNlc3Npb25fcmVjb3JkaW5nOnt9LG1hc2tfYWxsX2VsZW1lbnRfYXR0cmlidXRlczohMSxtYXNrX2FsbF90ZXh0OiExLG1hc2tfcGVyc29uYWxfZGF0YV9wcm9wZXJ0aWVzOiExLGN1c3RvbV9wZXJzb25hbF9kYXRhX3Byb3BlcnRpZXM6W10sYWR2YW5jZWRfZGlzYWJsZV9kZWNpZGU6ITEsYWR2YW5jZWRfZGlzYWJsZV9mZWF0dXJlX2ZsYWdzOiExLGFkdmFuY2VkX2Rpc2FibGVfZmVhdHVyZV9mbGFnc19vbl9maXJzdF9sb2FkOiExLGFkdmFuY2VkX2Rpc2FibGVfdG9vbGJhcl9tZXRyaWNzOiExLGZlYXR1cmVfZmxhZ19yZXF1ZXN0X3RpbWVvdXRfbXM6M2UzLG9uX3JlcXVlc3RfZXJyb3I6ZT0+e3ZhciB0PVwiQmFkIEhUVFAgc3RhdHVzOiBcIitlLnN0YXR1c0NvZGUrXCIgXCIrZS50ZXh0O3EuZXJyb3IodCl9LGdldF9kZXZpY2VfaWQ6ZT0+ZSxfb25DYXB0dXJlOmhvLGNhcHR1cmVfcGVyZm9ybWFuY2U6dm9pZCAwLG5hbWU6XCJwb3N0aG9nXCIsYm9vdHN0cmFwOnt9LGRpc2FibGVfY29tcHJlc3Npb246ITEsc2Vzc2lvbl9pZGxlX3RpbWVvdXRfc2Vjb25kczoxODAwLHBlcnNvbl9wcm9maWxlczpcImlkZW50aWZpZWRfb25seVwiLF9fYWRkX3RyYWNpbmdfaGVhZGVyczohMSxiZWZvcmVfc2VuZDp2b2lkIDB9fSxnbz1lPT57dmFyIHQ9e307RihlLnByb2Nlc3NfcGVyc29uKXx8KHQucGVyc29uX3Byb2ZpbGVzPWUucHJvY2Vzc19wZXJzb24pLEYoZS54aHJfaGVhZGVycyl8fCh0LnJlcXVlc3RfaGVhZGVycz1lLnhocl9oZWFkZXJzKSxGKGUuY29va2llX25hbWUpfHwodC5wZXJzaXN0ZW5jZV9uYW1lPWUuY29va2llX25hbWUpLEYoZS5kaXNhYmxlX2Nvb2tpZSl8fCh0LmRpc2FibGVfcGVyc2lzdGVuY2U9ZS5kaXNhYmxlX2Nvb2tpZSk7dmFyIGk9Syh7fSx0LGUpO3JldHVybiB4KGUucHJvcGVydHlfYmxhY2tsaXN0KSYmKEYoZS5wcm9wZXJ0eV9kZW55bGlzdCk/aS5wcm9wZXJ0eV9kZW55bGlzdD1lLnByb3BlcnR5X2JsYWNrbGlzdDp4KGUucHJvcGVydHlfZGVueWxpc3QpP2kucHJvcGVydHlfZGVueWxpc3Q9Wy4uLmUucHJvcGVydHlfYmxhY2tsaXN0LC4uLmUucHJvcGVydHlfZGVueWxpc3RdOnEuZXJyb3IoXCJJbnZhbGlkIHZhbHVlIGZvciBwcm9wZXJ0eV9kZW55bGlzdCBjb25maWc6IFwiK2UucHJvcGVydHlfZGVueWxpc3QpKSxpfTtjbGFzcyBmb3tjb25zdHJ1Y3Rvcigpe1codGhpcyxcIl9fZm9yY2VBbGxvd0xvY2FsaG9zdFwiLCExKX1nZXQgX2ZvcmNlQWxsb3dMb2NhbGhvc3QoKXtyZXR1cm4gdGhpcy5fX2ZvcmNlQWxsb3dMb2NhbGhvc3R9c2V0IF9mb3JjZUFsbG93TG9jYWxob3N0KGUpe3EuZXJyb3IoXCJXZWJQZXJmb3JtYW5jZU9ic2VydmVyIGlzIGRlcHJlY2F0ZWQgYW5kIGhhcyBubyBpbXBhY3Qgb24gbmV0d29yayBjYXB0dXJlLiBVc2UgYF9mb3JjZUFsbG93TG9jYWxob3N0TmV0d29ya0NhcHR1cmVgIG9uIGBwb3N0aG9nLnNlc3Npb25SZWNvcmRpbmdgXCIpLHRoaXMuX19mb3JjZUFsbG93TG9jYWxob3N0PWV9fWNsYXNzIG1ve2dldCBkZWNpZGVFbmRwb2ludFdhc0hpdCgpe3ZhciBlLHQ7cmV0dXJuIG51bGwhPT0oZT1udWxsPT09KHQ9dGhpcy5mZWF0dXJlRmxhZ3MpfHx2b2lkIDA9PT10P3ZvaWQgMDp0Lmhhc0xvYWRlZEZsYWdzKSYmdm9pZCAwIT09ZSYmZX1jb25zdHJ1Y3Rvcigpe1codGhpcyxcIndlYlBlcmZvcm1hbmNlXCIsbmV3IGZvKSxXKHRoaXMsXCJ2ZXJzaW9uXCIscC5MSUJfVkVSU0lPTiksVyh0aGlzLFwiX2ludGVybmFsRXZlbnRFbWl0dGVyXCIsbmV3IGJzKSx0aGlzLmNvbmZpZz12bygpLHRoaXMuU2VudHJ5SW50ZWdyYXRpb249VG4sdGhpcy5zZW50cnlJbnRlZ3JhdGlvbj1lPT5mdW5jdGlvbihlLHQpe3ZhciBpPVJuKGUsdCk7cmV0dXJue25hbWU6Rm4scHJvY2Vzc0V2ZW50OmU9PmkoZSl9fSh0aGlzLGUpLHRoaXMuX19yZXF1ZXN0X3F1ZXVlPVtdLHRoaXMuX19sb2FkZWQ9ITEsdGhpcy5hbmFseXRpY3NEZWZhdWx0RW5kcG9pbnQ9XCIvZS9cIix0aGlzLl9pbml0aWFsUGFnZXZpZXdDYXB0dXJlZD0hMSx0aGlzLl9pbml0aWFsUGVyc29uUHJvZmlsZXNDb25maWc9bnVsbCx0aGlzLl9jYWNoZWRJZGVudGlmeT1udWxsLHRoaXMuZmVhdHVyZUZsYWdzPW5ldyBHZSh0aGlzKSx0aGlzLnRvb2xiYXI9bmV3IF9uKHRoaXMpLHRoaXMuc2Nyb2xsTWFuYWdlcj1uZXcgSHModGhpcyksdGhpcy5wYWdlVmlld01hbmFnZXI9bmV3IE1uKHRoaXMpLHRoaXMuc3VydmV5cz1uZXcgeHModGhpcyksdGhpcy5leHBlcmltZW50cz1uZXcgbm8odGhpcyksdGhpcy5leGNlcHRpb25zPW5ldyBzbyh0aGlzKSx0aGlzLnJhdGVMaW1pdGVyPW5ldyBDcyh0aGlzKSx0aGlzLnJlcXVlc3RSb3V0ZXI9bmV3IFBuKHRoaXMpLHRoaXMuY29uc2VudD1uZXcgWHModGhpcyksdGhpcy5wZW9wbGU9e3NldDooZSx0LGkpPT57dmFyIHI9UihlKT97W2VdOnR9OmU7dGhpcy5zZXRQZXJzb25Qcm9wZXJ0aWVzKHIpLG51bGw9PWl8fGkoe30pfSxzZXRfb25jZTooZSx0LGkpPT57dmFyIHI9UihlKT97W2VdOnR9OmU7dGhpcy5zZXRQZXJzb25Qcm9wZXJ0aWVzKHZvaWQgMCxyKSxudWxsPT1pfHxpKHt9KX19LHRoaXMub24oXCJldmVudENhcHR1cmVkXCIsKGU9PnEuaW5mbygnc2VuZCBcIicuY29uY2F0KG51bGw9PWU/dm9pZCAwOmUuZXZlbnQsJ1wiJyksZSkpKX1pbml0KGUsdCxpKXtpZihpJiZpIT09X28pe3ZhciByLG49bnVsbCE9PShyPWNvW2ldKSYmdm9pZCAwIT09cj9yOm5ldyBtbztyZXR1cm4gbi5faW5pdChlLHQsaSksY29baV09bixjb1tfb11baV09bixufXJldHVybiB0aGlzLl9pbml0KGUsdCxpKX1faW5pdChpKXt2YXIgcixuLHMsbz1hcmd1bWVudHMubGVuZ3RoPjEmJnZvaWQgMCE9PWFyZ3VtZW50c1sxXT9hcmd1bWVudHNbMV06e30sYT1hcmd1bWVudHMubGVuZ3RoPjI/YXJndW1lbnRzWzJdOnZvaWQgMDtpZihGKGkpfHxUKGkpKXJldHVybiBxLmNyaXRpY2FsKFwiUG9zdEhvZyB3YXMgaW5pdGlhbGl6ZWQgd2l0aG91dCBhIHRva2VuLiBUaGlzIGxpa2VseSBpbmRpY2F0ZXMgYSBtaXNjb25maWd1cmF0aW9uLiBQbGVhc2UgY2hlY2sgdGhlIGZpcnN0IGFyZ3VtZW50IHBhc3NlZCB0byBwb3N0aG9nLmluaXQoKVwiKSx0aGlzO2lmKHRoaXMuX19sb2FkZWQpcmV0dXJuIHEud2FybihcIllvdSBoYXZlIGFscmVhZHkgaW5pdGlhbGl6ZWQgUG9zdEhvZyEgUmUtaW5pdGlhbGl6aW5nIGlzIGEgbm8tb3BcIiksdGhpczt0aGlzLl9fbG9hZGVkPSEwLHRoaXMuY29uZmlnPXt9LHRoaXMuX3RyaWdnZXJlZF9ub3RpZnM9W10sby5wZXJzb25fcHJvZmlsZXMmJih0aGlzLl9pbml0aWFsUGVyc29uUHJvZmlsZXNDb25maWc9by5wZXJzb25fcHJvZmlsZXMpLHRoaXMuc2V0X2NvbmZpZyhLKHt9LHZvKCksZ28obykse25hbWU6YSx0b2tlbjppfSkpLHRoaXMuY29uZmlnLm9uX3hocl9lcnJvciYmcS5lcnJvcihcIm9uX3hocl9lcnJvciBpcyBkZXByZWNhdGVkLiBVc2Ugb25fcmVxdWVzdF9lcnJvciBpbnN0ZWFkXCIpLHRoaXMuY29tcHJlc3Npb249by5kaXNhYmxlX2NvbXByZXNzaW9uP3ZvaWQgMDplLkdaaXBKUyx0aGlzLnBlcnNpc3RlbmNlPW5ldyBFaSh0aGlzLmNvbmZpZyksdGhpcy5zZXNzaW9uUGVyc2lzdGVuY2U9XCJzZXNzaW9uU3RvcmFnZVwiPT09dGhpcy5jb25maWcucGVyc2lzdGVuY2V8fFwibWVtb3J5XCI9PT10aGlzLmNvbmZpZy5wZXJzaXN0ZW5jZT90aGlzLnBlcnNpc3RlbmNlOm5ldyBFaShqKGooe30sdGhpcy5jb25maWcpLHt9LHtwZXJzaXN0ZW5jZTpcInNlc3Npb25TdG9yYWdlXCJ9KSk7dmFyIGw9aih7fSx0aGlzLnBlcnNpc3RlbmNlLnByb3BzKSx1PWooe30sdGhpcy5zZXNzaW9uUGVyc2lzdGVuY2UucHJvcHMpO2lmKHRoaXMuX3JlcXVlc3RRdWV1ZT1uZXcgcG4oKGU9PnRoaXMuX3NlbmRfcmV0cmlhYmxlX3JlcXVlc3QoZSkpKSx0aGlzLl9yZXRyeVF1ZXVlPW5ldyBFbih0aGlzKSx0aGlzLl9fcmVxdWVzdF9xdWV1ZT1bXSx0aGlzLmNvbmZpZy5fX3ByZXZpZXdfZXhwZXJpbWVudGFsX2Nvb2tpZWxlc3NfbW9kZXx8KHRoaXMuc2Vzc2lvbk1hbmFnZXI9bmV3IEluKHRoaXMpLHRoaXMuc2Vzc2lvblByb3BzTWFuYWdlcj1uZXcgRnModGhpcyx0aGlzLnNlc3Npb25NYW5hZ2VyLHRoaXMucGVyc2lzdGVuY2UpKSxuZXcgWXModGhpcykuc3RhcnRJZkVuYWJsZWRPclN0b3AoKSx0aGlzLnNpdGVBcHBzPW5ldyBsbyh0aGlzKSxudWxsPT09KHI9dGhpcy5zaXRlQXBwcyl8fHZvaWQgMD09PXJ8fHIuaW5pdCgpLHRoaXMuY29uZmlnLl9fcHJldmlld19leHBlcmltZW50YWxfY29va2llbGVzc19tb2RlfHwodGhpcy5zZXNzaW9uUmVjb3JkaW5nPW5ldyBvbih0aGlzKSx0aGlzLnNlc3Npb25SZWNvcmRpbmcuc3RhcnRJZkVuYWJsZWRPclN0b3AoKSksdGhpcy5jb25maWcuZGlzYWJsZV9zY3JvbGxfcHJvcGVydGllc3x8dGhpcy5zY3JvbGxNYW5hZ2VyLnN0YXJ0TWVhc3VyaW5nU2Nyb2xsUG9zaXRpb24oKSx0aGlzLmF1dG9jYXB0dXJlPW5ldyBHcyh0aGlzKSx0aGlzLmF1dG9jYXB0dXJlLnN0YXJ0SWZFbmFibGVkKCksdGhpcy5zdXJ2ZXlzLmxvYWRJZkVuYWJsZWQoKSx0aGlzLmhlYXRtYXBzPW5ldyBCcyh0aGlzKSx0aGlzLmhlYXRtYXBzLnN0YXJ0SWZFbmFibGVkKCksdGhpcy53ZWJWaXRhbHNBdXRvY2FwdHVyZT1uZXcgaW8odGhpcyksdGhpcy5leGNlcHRpb25PYnNlcnZlcj1uZXcgWnModGhpcyksdGhpcy5leGNlcHRpb25PYnNlcnZlci5zdGFydElmRW5hYmxlZCgpLHRoaXMuZGVhZENsaWNrc0F1dG9jYXB0dXJlPW5ldyBEcyh0aGlzLEFzKSx0aGlzLmRlYWRDbGlja3NBdXRvY2FwdHVyZS5zdGFydElmRW5hYmxlZCgpLHAuREVCVUc9cC5ERUJVR3x8dGhpcy5jb25maWcuZGVidWcscC5ERUJVRyYmcS5pbmZvKFwiU3RhcnRpbmcgaW4gZGVidWcgbW9kZVwiLHt0aGlzOnRoaXMsY29uZmlnOm8sdGhpc0M6aih7fSx0aGlzLmNvbmZpZykscDpsLHM6dX0pLHRoaXMuX3N5bmNfb3B0X291dF93aXRoX3BlcnNpc3RlbmNlKCksdm9pZCAwIT09KG51bGw9PT0obj1vLmJvb3RzdHJhcCl8fHZvaWQgMD09PW4/dm9pZCAwOm4uZGlzdGluY3RJRCkpe3ZhciBjLGQsaD10aGlzLmNvbmZpZy5nZXRfZGV2aWNlX2lkKGV0KCkpLF89bnVsbCE9PShjPW8uYm9vdHN0cmFwKSYmdm9pZCAwIT09YyYmYy5pc0lkZW50aWZpZWRJRD9oOm8uYm9vdHN0cmFwLmRpc3RpbmN0SUQ7dGhpcy5wZXJzaXN0ZW5jZS5zZXRfcHJvcGVydHkoJGUsbnVsbCE9PShkPW8uYm9vdHN0cmFwKSYmdm9pZCAwIT09ZCYmZC5pc0lkZW50aWZpZWRJRD9cImlkZW50aWZpZWRcIjpcImFub255bW91c1wiKSx0aGlzLnJlZ2lzdGVyKHtkaXN0aW5jdF9pZDpvLmJvb3RzdHJhcC5kaXN0aW5jdElELCRkZXZpY2VfaWQ6X30pfWlmKHRoaXMuX2hhc0Jvb3RzdHJhcHBlZEZlYXR1cmVGbGFncygpKXt2YXIgdixnLGY9T2JqZWN0LmtleXMoKG51bGw9PT0odj1vLmJvb3RzdHJhcCl8fHZvaWQgMD09PXY/dm9pZCAwOnYuZmVhdHVyZUZsYWdzKXx8e30pLmZpbHRlcigoZT0+e3ZhciB0LGk7cmV0dXJuIShudWxsPT09KHQ9by5ib290c3RyYXApfHx2b2lkIDA9PT10fHxudWxsPT09KGk9dC5mZWF0dXJlRmxhZ3MpfHx2b2lkIDA9PT1pfHwhaVtlXSl9KSkucmVkdWNlKCgoZSx0KT0+e3ZhciBpLHI7cmV0dXJuIGVbdF09KG51bGw9PT0oaT1vLmJvb3RzdHJhcCl8fHZvaWQgMD09PWl8fG51bGw9PT0ocj1pLmZlYXR1cmVGbGFncyl8fHZvaWQgMD09PXI/dm9pZCAwOnJbdF0pfHwhMSxlfSkse30pLG09T2JqZWN0LmtleXMoKG51bGw9PT0oZz1vLmJvb3RzdHJhcCl8fHZvaWQgMD09PWc/dm9pZCAwOmcuZmVhdHVyZUZsYWdQYXlsb2Fkcyl8fHt9KS5maWx0ZXIoKGU9PmZbZV0pKS5yZWR1Y2UoKChlLHQpPT57dmFyIGkscixuLHM7bnVsbCE9PShpPW8uYm9vdHN0cmFwKSYmdm9pZCAwIT09aSYmbnVsbCE9PShyPWkuZmVhdHVyZUZsYWdQYXlsb2FkcykmJnZvaWQgMCE9PXImJnJbdF0mJihlW3RdPW51bGw9PT0obj1vLmJvb3RzdHJhcCl8fHZvaWQgMD09PW58fG51bGw9PT0ocz1uLmZlYXR1cmVGbGFnUGF5bG9hZHMpfHx2b2lkIDA9PT1zP3ZvaWQgMDpzW3RdKTtyZXR1cm4gZX0pLHt9KTt0aGlzLmZlYXR1cmVGbGFncy5yZWNlaXZlZEZlYXR1cmVGbGFncyh7ZmVhdHVyZUZsYWdzOmYsZmVhdHVyZUZsYWdQYXlsb2FkczptfSl9aWYodGhpcy5jb25maWcuX19wcmV2aWV3X2V4cGVyaW1lbnRhbF9jb29raWVsZXNzX21vZGUpdGhpcy5yZWdpc3Rlcl9vbmNlKHtkaXN0aW5jdF9pZDpCZSwkZGV2aWNlX2lkOm51bGx9LFwiXCIpO2Vsc2UgaWYoIXRoaXMuZ2V0X2Rpc3RpbmN0X2lkKCkpe3ZhciBiPXRoaXMuY29uZmlnLmdldF9kZXZpY2VfaWQoZXQoKSk7dGhpcy5yZWdpc3Rlcl9vbmNlKHtkaXN0aW5jdF9pZDpiLCRkZXZpY2VfaWQ6Yn0sXCJcIiksdGhpcy5wZXJzaXN0ZW5jZS5zZXRfcHJvcGVydHkoJGUsXCJhbm9ueW1vdXNcIil9cmV0dXJuIG51bGw9PXR8fG51bGw9PT0ocz10LmFkZEV2ZW50TGlzdGVuZXIpfHx2b2lkIDA9PT1zfHxzLmNhbGwodCxcIm9ucGFnZWhpZGVcImluIHNlbGY/XCJwYWdlaGlkZVwiOlwidW5sb2FkXCIsdGhpcy5faGFuZGxlX3VubG9hZC5iaW5kKHRoaXMpKSx0aGlzLnRvb2xiYXIubWF5YmVMb2FkVG9vbGJhcigpLG8uc2VnbWVudD9Pbih0aGlzLCgoKT0+dGhpcy5fbG9hZGVkKCkpKTp0aGlzLl9sb2FkZWQoKSxJKHRoaXMuY29uZmlnLl9vbkNhcHR1cmUpJiZ0aGlzLmNvbmZpZy5fb25DYXB0dXJlIT09aG8mJihxLndhcm4oXCJvbkNhcHR1cmUgaXMgZGVwcmVjYXRlZC4gUGxlYXNlIHVzZSBgYmVmb3JlX3NlbmRgIGluc3RlYWRcIiksdGhpcy5vbihcImV2ZW50Q2FwdHVyZWRcIiwoZT0+dGhpcy5jb25maWcuX29uQ2FwdHVyZShlLmV2ZW50LGUpKSkpLHRoaXN9X29uUmVtb3RlQ29uZmlnKHQpe3ZhciBpLHIsbixzLG8sbCx1LGMsZDtpZighYXx8IWEuYm9keSlyZXR1cm4gcS5pbmZvKFwiZG9jdW1lbnQgbm90IHJlYWR5IHlldCwgdHJ5aW5nIGFnYWluIGluIDUwMCBtaWxsaXNlY29uZHMuLi5cIiksdm9pZCBzZXRUaW1lb3V0KCgoKT0+e3RoaXMuX29uUmVtb3RlQ29uZmlnKHQpfSksNTAwKTt0aGlzLmNvbXByZXNzaW9uPXZvaWQgMCx0LnN1cHBvcnRlZENvbXByZXNzaW9uJiYhdGhpcy5jb25maWcuZGlzYWJsZV9jb21wcmVzc2lvbiYmKHRoaXMuY29tcHJlc3Npb249bSh0LnN1cHBvcnRlZENvbXByZXNzaW9uLGUuR1ppcEpTKT9lLkdaaXBKUzptKHQuc3VwcG9ydGVkQ29tcHJlc3Npb24sZS5CYXNlNjQpP2UuQmFzZTY0OnZvaWQgMCksbnVsbCE9PShpPXQuYW5hbHl0aWNzKSYmdm9pZCAwIT09aSYmaS5lbmRwb2ludCYmKHRoaXMuYW5hbHl0aWNzRGVmYXVsdEVuZHBvaW50PXQuYW5hbHl0aWNzLmVuZHBvaW50KSx0aGlzLnNldF9jb25maWcoe3BlcnNvbl9wcm9maWxlczp0aGlzLl9pbml0aWFsUGVyc29uUHJvZmlsZXNDb25maWc/dGhpcy5faW5pdGlhbFBlcnNvblByb2ZpbGVzQ29uZmlnOlwiaWRlbnRpZmllZF9vbmx5XCJ9KSxudWxsPT09KHI9dGhpcy5zaXRlQXBwcyl8fHZvaWQgMD09PXJ8fHIub25SZW1vdGVDb25maWcodCksbnVsbD09PShuPXRoaXMuc2Vzc2lvblJlY29yZGluZyl8fHZvaWQgMD09PW58fG4ub25SZW1vdGVDb25maWcodCksbnVsbD09PShzPXRoaXMuYXV0b2NhcHR1cmUpfHx2b2lkIDA9PT1zfHxzLm9uUmVtb3RlQ29uZmlnKHQpLG51bGw9PT0obz10aGlzLmhlYXRtYXBzKXx8dm9pZCAwPT09b3x8by5vblJlbW90ZUNvbmZpZyh0KSxudWxsPT09KGw9dGhpcy5zdXJ2ZXlzKXx8dm9pZCAwPT09bHx8bC5vblJlbW90ZUNvbmZpZyh0KSxudWxsPT09KHU9dGhpcy53ZWJWaXRhbHNBdXRvY2FwdHVyZSl8fHZvaWQgMD09PXV8fHUub25SZW1vdGVDb25maWcodCksbnVsbD09PShjPXRoaXMuZXhjZXB0aW9uT2JzZXJ2ZXIpfHx2b2lkIDA9PT1jfHxjLm9uUmVtb3RlQ29uZmlnKHQpLG51bGw9PT0oZD10aGlzLmRlYWRDbGlja3NBdXRvY2FwdHVyZSl8fHZvaWQgMD09PWR8fGQub25SZW1vdGVDb25maWcodCl9X2xvYWRlZCgpe3RyeXt0aGlzLmNvbmZpZy5sb2FkZWQodGhpcyl9Y2F0Y2goZSl7cS5jcml0aWNhbChcImBsb2FkZWRgIGZ1bmN0aW9uIGZhaWxlZFwiLGUpfXRoaXMuX3N0YXJ0X3F1ZXVlX2lmX29wdGVkX2luKCksdGhpcy5jb25maWcuY2FwdHVyZV9wYWdldmlldyYmc2V0VGltZW91dCgoKCk9Pnt0aGlzLmNvbnNlbnQuaXNPcHRlZEluKCkmJnRoaXMuX2NhcHR1cmVJbml0aWFsUGFnZXZpZXcoKX0pLDEpLG5ldyBsbih0aGlzKS5sb2FkKCksdGhpcy5mZWF0dXJlRmxhZ3MuZGVjaWRlKCl9X3N0YXJ0X3F1ZXVlX2lmX29wdGVkX2luKCl7dmFyIGU7dGhpcy5oYXNfb3B0ZWRfb3V0X2NhcHR1cmluZygpfHx0aGlzLmNvbmZpZy5yZXF1ZXN0X2JhdGNoaW5nJiYobnVsbD09PShlPXRoaXMuX3JlcXVlc3RRdWV1ZSl8fHZvaWQgMD09PWV8fGUuZW5hYmxlKCkpfV9kb21fbG9hZGVkKCl7dGhpcy5oYXNfb3B0ZWRfb3V0X2NhcHR1cmluZygpfHxKKHRoaXMuX19yZXF1ZXN0X3F1ZXVlLChlPT50aGlzLl9zZW5kX3JldHJpYWJsZV9yZXF1ZXN0KGUpKSksdGhpcy5fX3JlcXVlc3RfcXVldWU9W10sdGhpcy5fc3RhcnRfcXVldWVfaWZfb3B0ZWRfaW4oKX1faGFuZGxlX3VubG9hZCgpe3ZhciBlLHQ7dGhpcy5jb25maWcucmVxdWVzdF9iYXRjaGluZz8odGhpcy5fc2hvdWxkQ2FwdHVyZVBhZ2VsZWF2ZSgpJiZ0aGlzLmNhcHR1cmUoXCIkcGFnZWxlYXZlXCIpLG51bGw9PT0oZT10aGlzLl9yZXF1ZXN0UXVldWUpfHx2b2lkIDA9PT1lfHxlLnVubG9hZCgpLG51bGw9PT0odD10aGlzLl9yZXRyeVF1ZXVlKXx8dm9pZCAwPT09dHx8dC51bmxvYWQoKSk6dGhpcy5fc2hvdWxkQ2FwdHVyZVBhZ2VsZWF2ZSgpJiZ0aGlzLmNhcHR1cmUoXCIkcGFnZWxlYXZlXCIsbnVsbCx7dHJhbnNwb3J0Olwic2VuZEJlYWNvblwifSl9X3NlbmRfcmVxdWVzdChlKXt0aGlzLl9fbG9hZGVkJiYocG8/dGhpcy5fX3JlcXVlc3RfcXVldWUucHVzaChlKTp0aGlzLnJhdGVMaW1pdGVyLmlzU2VydmVyUmF0ZUxpbWl0ZWQoZS5iYXRjaEtleSl8fChlLnRyYW5zcG9ydD1lLnRyYW5zcG9ydHx8dGhpcy5jb25maWcuYXBpX3RyYW5zcG9ydCxlLnVybD1tbihlLnVybCx7aXA6dGhpcy5jb25maWcuaXA/MTowfSksZS5oZWFkZXJzPWooe30sdGhpcy5jb25maWcucmVxdWVzdF9oZWFkZXJzKSxlLmNvbXByZXNzaW9uPVwiYmVzdC1hdmFpbGFibGVcIj09PWUuY29tcHJlc3Npb24/dGhpcy5jb21wcmVzc2lvbjplLmNvbXByZXNzaW9uLGUuZmV0Y2hPcHRpb25zPWUuZmV0Y2hPcHRpb25zfHx0aGlzLmNvbmZpZy5mZXRjaF9vcHRpb25zLChlPT57dmFyIHQsaSxyLG49aih7fSxlKTtuLnRpbWVvdXQ9bi50aW1lb3V0fHw2ZTQsbi51cmw9bW4obi51cmwse186KG5ldyBEYXRlKS5nZXRUaW1lKCkudG9TdHJpbmcoKSx2ZXI6cC5MSUJfVkVSU0lPTixjb21wcmVzc2lvbjpuLmNvbXByZXNzaW9ufSk7dmFyIHM9bnVsbCE9PSh0PW4udHJhbnNwb3J0KSYmdm9pZCAwIT09dD90OlwiZmV0Y2hcIixvPW51bGwhPT0oaT1udWxsPT09KHI9bmUod24sKGU9PmUudHJhbnNwb3J0PT09cykpKXx8dm9pZCAwPT09cj92b2lkIDA6ci5tZXRob2QpJiZ2b2lkIDAhPT1pP2k6d25bMF0ubWV0aG9kO2lmKCFvKXRocm93IG5ldyBFcnJvcihcIk5vIGF2YWlsYWJsZSB0cmFuc3BvcnQgbWV0aG9kXCIpO28obil9KShqKGooe30sZSkse30se2NhbGxiYWNrOnQ9Pnt2YXIgaSxyLG47KHRoaXMucmF0ZUxpbWl0ZXIuY2hlY2tGb3JMaW1pdGluZyh0KSx0LnN0YXR1c0NvZGU+PTQwMCkmJihudWxsPT09KHI9KG49dGhpcy5jb25maWcpLm9uX3JlcXVlc3RfZXJyb3IpfHx2b2lkIDA9PT1yfHxyLmNhbGwobix0KSk7bnVsbD09PShpPWUuY2FsbGJhY2spfHx2b2lkIDA9PT1pfHxpLmNhbGwoZSx0KX19KSkpKX1fc2VuZF9yZXRyaWFibGVfcmVxdWVzdChlKXt0aGlzLl9yZXRyeVF1ZXVlP3RoaXMuX3JldHJ5UXVldWUucmV0cmlhYmxlUmVxdWVzdChlKTp0aGlzLl9zZW5kX3JlcXVlc3QoZSl9X2V4ZWN1dGVfYXJyYXkoZSl7dmFyIHQsaT1bXSxyPVtdLG49W107SihlLChlPT57ZSYmKHQ9ZVswXSx4KHQpP24ucHVzaChlKTpJKGUpP2UuY2FsbCh0aGlzKTp4KGUpJiZcImFsaWFzXCI9PT10P2kucHVzaChlKTp4KGUpJiYtMSE9PXQuaW5kZXhPZihcImNhcHR1cmVcIikmJkkodGhpc1t0XSk/bi5wdXNoKGUpOnIucHVzaChlKSl9KSk7dmFyIHM9ZnVuY3Rpb24oZSx0KXtKKGUsKGZ1bmN0aW9uKGUpe2lmKHgoZVswXSkpe3ZhciBpPXQ7WShlLChmdW5jdGlvbihlKXtpPWlbZVswXV0uYXBwbHkoaSxlLnNsaWNlKDEpKX0pKX1lbHNlIHRoaXNbZVswXV0uYXBwbHkodGhpcyxlLnNsaWNlKDEpKX0pLHQpfTtzKGksdGhpcykscyhyLHRoaXMpLHMobix0aGlzKX1faGFzQm9vdHN0cmFwcGVkRmVhdHVyZUZsYWdzKCl7dmFyIGUsdDtyZXR1cm4obnVsbD09PShlPXRoaXMuY29uZmlnLmJvb3RzdHJhcCl8fHZvaWQgMD09PWU/dm9pZCAwOmUuZmVhdHVyZUZsYWdzKSYmT2JqZWN0LmtleXMobnVsbD09PSh0PXRoaXMuY29uZmlnLmJvb3RzdHJhcCl8fHZvaWQgMD09PXQ/dm9pZCAwOnQuZmVhdHVyZUZsYWdzKS5sZW5ndGg+MHx8ITF9cHVzaChlKXt0aGlzLl9leGVjdXRlX2FycmF5KFtlXSl9Y2FwdHVyZShlLHQsaSl7dmFyIHI7aWYodGhpcy5fX2xvYWRlZCYmdGhpcy5wZXJzaXN0ZW5jZSYmdGhpcy5zZXNzaW9uUGVyc2lzdGVuY2UmJnRoaXMuX3JlcXVlc3RRdWV1ZSl7aWYoIXRoaXMuY29uc2VudC5pc09wdGVkT3V0KCkpaWYoIUYoZSkmJlIoZSkpe2lmKHRoaXMuY29uZmlnLm9wdF9vdXRfdXNlcmFnZW50X2ZpbHRlcnx8IXRoaXMuX2lzX2JvdCgpKXt2YXIgbj1udWxsIT1pJiZpLnNraXBfY2xpZW50X3JhdGVfbGltaXRpbmc/dm9pZCAwOnRoaXMucmF0ZUxpbWl0ZXIuY2xpZW50UmF0ZUxpbWl0Q29udGV4dCgpO2lmKG51bGw9PW58fCFuLmlzUmF0ZUxpbWl0ZWQpe3RoaXMuc2Vzc2lvblBlcnNpc3RlbmNlLnVwZGF0ZV9zZWFyY2hfa2V5d29yZCgpLHRoaXMuY29uZmlnLnN0b3JlX2dvb2dsZSYmdGhpcy5zZXNzaW9uUGVyc2lzdGVuY2UudXBkYXRlX2NhbXBhaWduX3BhcmFtcygpLHRoaXMuY29uZmlnLnNhdmVfcmVmZXJyZXImJnRoaXMuc2Vzc2lvblBlcnNpc3RlbmNlLnVwZGF0ZV9yZWZlcnJlcl9pbmZvKCksKHRoaXMuY29uZmlnLnN0b3JlX2dvb2dsZXx8dGhpcy5jb25maWcuc2F2ZV9yZWZlcnJlcikmJnRoaXMucGVyc2lzdGVuY2Uuc2V0X2luaXRpYWxfcGVyc29uX2luZm8oKTt2YXIgcz1uZXcgRGF0ZSxvPShudWxsPT1pP3ZvaWQgMDppLnRpbWVzdGFtcCl8fHMsYT1ldCgpLGw9e3V1aWQ6YSxldmVudDplLHByb3BlcnRpZXM6dGhpcy5fY2FsY3VsYXRlX2V2ZW50X3Byb3BlcnRpZXMoZSx0fHx7fSxvLGEpfTtuJiYobC5wcm9wZXJ0aWVzLiRsaWJfcmF0ZV9saW1pdF9yZW1haW5pbmdfdG9rZW5zPW4ucmVtYWluaW5nVG9rZW5zKSwobnVsbD09aT92b2lkIDA6aS4kc2V0KSYmKGwuJHNldD1udWxsPT1pP3ZvaWQgMDppLiRzZXQpO3ZhciB1PXRoaXMuX2NhbGN1bGF0ZV9zZXRfb25jZV9wcm9wZXJ0aWVzKG51bGw9PWk/dm9pZCAwOmkuJHNldF9vbmNlKTt1JiYobC4kc2V0X29uY2U9dSksKGw9aWUobCxudWxsIT1pJiZpLl9ub1RydW5jYXRlP251bGw6dGhpcy5jb25maWcucHJvcGVydGllc19zdHJpbmdfbWF4X2xlbmd0aCkpLnRpbWVzdGFtcD1vLEYobnVsbD09aT92b2lkIDA6aS50aW1lc3RhbXApfHwobC5wcm9wZXJ0aWVzLiRldmVudF90aW1lX292ZXJyaWRlX3Byb3ZpZGVkPSEwLGwucHJvcGVydGllcy4kZXZlbnRfdGltZV9vdmVycmlkZV9zeXN0ZW1fdGltZT1zKTt2YXIgYz1qKGooe30sbC5wcm9wZXJ0aWVzLiRzZXQpLGwuJHNldCk7aWYoUChjKXx8dGhpcy5zZXRQZXJzb25Qcm9wZXJ0aWVzRm9yRmxhZ3MoYyksIU8odGhpcy5jb25maWcuYmVmb3JlX3NlbmQpKXt2YXIgZD10aGlzLl9ydW5CZWZvcmVTZW5kKGwpO2lmKCFkKXJldHVybjtsPWR9dGhpcy5faW50ZXJuYWxFdmVudEVtaXR0ZXIuZW1pdChcImV2ZW50Q2FwdHVyZWRcIixsKTt2YXIgaD17bWV0aG9kOlwiUE9TVFwiLHVybDpudWxsIT09KHI9bnVsbD09aT92b2lkIDA6aS5fdXJsKSYmdm9pZCAwIT09cj9yOnRoaXMucmVxdWVzdFJvdXRlci5lbmRwb2ludEZvcihcImFwaVwiLHRoaXMuYW5hbHl0aWNzRGVmYXVsdEVuZHBvaW50KSxkYXRhOmwsY29tcHJlc3Npb246XCJiZXN0LWF2YWlsYWJsZVwiLGJhdGNoS2V5Om51bGw9PWk/dm9pZCAwOmkuX2JhdGNoS2V5fTtyZXR1cm4hdGhpcy5jb25maWcucmVxdWVzdF9iYXRjaGluZ3x8aSYmKG51bGw9PWl8fCFpLl9iYXRjaEtleSl8fG51bGwhPWkmJmkuc2VuZF9pbnN0YW50bHk/dGhpcy5fc2VuZF9yZXRyaWFibGVfcmVxdWVzdChoKTp0aGlzLl9yZXF1ZXN0UXVldWUuZW5xdWV1ZShoKSxsfXEuY3JpdGljYWwoXCJUaGlzIGNhcHR1cmUgY2FsbCBpcyBpZ25vcmVkIGR1ZSB0byBjbGllbnQgcmF0ZSBsaW1pdGluZy5cIil9fWVsc2UgcS5lcnJvcihcIk5vIGV2ZW50IG5hbWUgcHJvdmlkZWQgdG8gcG9zdGhvZy5jYXB0dXJlXCIpfWVsc2UgcS51bmluaXRpYWxpemVkV2FybmluZyhcInBvc3Rob2cuY2FwdHVyZVwiKX1fYWRkQ2FwdHVyZUhvb2soZSl7cmV0dXJuIHRoaXMub24oXCJldmVudENhcHR1cmVkXCIsKHQ9PmUodC5ldmVudCx0KSkpfV9jYWxjdWxhdGVfZXZlbnRfcHJvcGVydGllcyhlLHQsaSxyKXtpZihpPWl8fG5ldyBEYXRlLCF0aGlzLnBlcnNpc3RlbmNlfHwhdGhpcy5zZXNzaW9uUGVyc2lzdGVuY2UpcmV0dXJuIHQ7dmFyIG49dGhpcy5wZXJzaXN0ZW5jZS5yZW1vdmVfZXZlbnRfdGltZXIoZSkscz1qKHt9LHQpO2lmKHMudG9rZW49dGhpcy5jb25maWcudG9rZW4sdGhpcy5jb25maWcuX19wcmV2aWV3X2V4cGVyaW1lbnRhbF9jb29raWVsZXNzX21vZGUmJihzLiRjb29raWVsZXNzX21vZGU9ITApLFwiJHNuYXBzaG90XCI9PT1lKXt2YXIgbz1qKGooe30sdGhpcy5wZXJzaXN0ZW5jZS5wcm9wZXJ0aWVzKCkpLHRoaXMuc2Vzc2lvblBlcnNpc3RlbmNlLnByb3BlcnRpZXMoKSk7cmV0dXJuIHMuZGlzdGluY3RfaWQ9by5kaXN0aW5jdF9pZCwoIVIocy5kaXN0aW5jdF9pZCkmJiFNKHMuZGlzdGluY3RfaWQpfHxUKHMuZGlzdGluY3RfaWQpKSYmcS5lcnJvcihcIkludmFsaWQgZGlzdGluY3RfaWQgZm9yIHJlcGxheSBldmVudC4gVGhpcyBpbmRpY2F0ZXMgYSBidWcgaW4geW91ciBpbXBsZW1lbnRhdGlvblwiKSxzfXZhciBsLHU9d2kucHJvcGVydGllcyh7bWFza1BlcnNvbmFsRGF0YVByb3BlcnRpZXM6dGhpcy5jb25maWcubWFza19wZXJzb25hbF9kYXRhX3Byb3BlcnRpZXMsY3VzdG9tUGVyc29uYWxEYXRhUHJvcGVydGllczp0aGlzLmNvbmZpZy5jdXN0b21fcGVyc29uYWxfZGF0YV9wcm9wZXJ0aWVzfSk7aWYodGhpcy5zZXNzaW9uTWFuYWdlcil7dmFye3Nlc3Npb25JZDpjLHdpbmRvd0lkOmR9PXRoaXMuc2Vzc2lvbk1hbmFnZXIuY2hlY2tBbmRHZXRTZXNzaW9uQW5kV2luZG93SWQoKTtzLiRzZXNzaW9uX2lkPWMscy4kd2luZG93X2lkPWR9aWYodGhpcy5zZXNzaW9uUmVjb3JkaW5nJiYocy4kcmVjb3JkaW5nX3N0YXR1cz10aGlzLnNlc3Npb25SZWNvcmRpbmcuc3RhdHVzKSx0aGlzLnJlcXVlc3RSb3V0ZXIucmVnaW9uPT09a24uQ1VTVE9NJiYocy4kbGliX2N1c3RvbV9hcGlfaG9zdD10aGlzLmNvbmZpZy5hcGlfaG9zdCksdGhpcy5zZXNzaW9uUHJvcHNNYW5hZ2VyJiZ0aGlzLmNvbmZpZy5fX3ByZXZpZXdfc2VuZF9jbGllbnRfc2Vzc2lvbl9wYXJhbXMmJihcIiRwYWdldmlld1wiPT09ZXx8XCIkcGFnZWxlYXZlXCI9PT1lfHxcIiRhdXRvY2FwdHVyZVwiPT09ZSkpe3ZhciBfPXRoaXMuc2Vzc2lvblByb3BzTWFuYWdlci5nZXRTZXNzaW9uUHJvcHMoKTtzPUsocyxfKX1pZihsPVwiJHBhZ2V2aWV3XCI9PT1lP3RoaXMucGFnZVZpZXdNYW5hZ2VyLmRvUGFnZVZpZXcoaSxyKTpcIiRwYWdlbGVhdmVcIj09PWU/dGhpcy5wYWdlVmlld01hbmFnZXIuZG9QYWdlTGVhdmUoaSk6dGhpcy5wYWdlVmlld01hbmFnZXIuZG9FdmVudCgpLHM9SyhzLGwpLFwiJHBhZ2V2aWV3XCI9PT1lJiZhJiYocy50aXRsZT1hLnRpdGxlKSwhRihuKSl7dmFyIHA9aS5nZXRUaW1lKCktbjtzLiRkdXJhdGlvbj1wYXJzZUZsb2F0KChwLzFlMykudG9GaXhlZCgzKSl9aCYmdGhpcy5jb25maWcub3B0X291dF91c2VyYWdlbnRfZmlsdGVyJiYocy4kYnJvd3Nlcl90eXBlPXRoaXMuX2lzX2JvdCgpP1wiYm90XCI6XCJicm93c2VyXCIpLChzPUsoe30sdSx0aGlzLnBlcnNpc3RlbmNlLnByb3BlcnRpZXMoKSx0aGlzLnNlc3Npb25QZXJzaXN0ZW5jZS5wcm9wZXJ0aWVzKCkscykpLiRpc19pZGVudGlmaWVkPXRoaXMuX2lzSWRlbnRpZmllZCgpLHgodGhpcy5jb25maWcucHJvcGVydHlfZGVueWxpc3QpP1kodGhpcy5jb25maWcucHJvcGVydHlfZGVueWxpc3QsKGZ1bmN0aW9uKGUpe2RlbGV0ZSBzW2VdfSkpOnEuZXJyb3IoXCJJbnZhbGlkIHZhbHVlIGZvciBwcm9wZXJ0eV9kZW55bGlzdCBjb25maWc6IFwiK3RoaXMuY29uZmlnLnByb3BlcnR5X2RlbnlsaXN0K1wiIG9yIHByb3BlcnR5X2JsYWNrbGlzdCBjb25maWc6IFwiK3RoaXMuY29uZmlnLnByb3BlcnR5X2JsYWNrbGlzdCk7dmFyIHY9dGhpcy5jb25maWcuc2FuaXRpemVfcHJvcGVydGllczt2JiYocS5lcnJvcihcInNhbml0aXplX3Byb3BlcnRpZXMgaXMgZGVwcmVjYXRlZC4gVXNlIGJlZm9yZV9zZW5kIGluc3RlYWRcIikscz12KHMsZSkpO3ZhciBnPXRoaXMuX2hhc1BlcnNvblByb2Nlc3NpbmcoKTtyZXR1cm4gcy4kcHJvY2Vzc19wZXJzb25fcHJvZmlsZT1nLGcmJnRoaXMuX3JlcXVpcmVQZXJzb25Qcm9jZXNzaW5nKFwiX2NhbGN1bGF0ZV9ldmVudF9wcm9wZXJ0aWVzXCIpLHN9X2NhbGN1bGF0ZV9zZXRfb25jZV9wcm9wZXJ0aWVzKGUpe2lmKCF0aGlzLnBlcnNpc3RlbmNlfHwhdGhpcy5faGFzUGVyc29uUHJvY2Vzc2luZygpKXJldHVybiBlO3ZhciB0PUsoe30sdGhpcy5wZXJzaXN0ZW5jZS5nZXRfaW5pdGlhbF9wcm9wcygpLGV8fHt9KSxpPXRoaXMuY29uZmlnLnNhbml0aXplX3Byb3BlcnRpZXM7cmV0dXJuIGkmJihxLmVycm9yKFwic2FuaXRpemVfcHJvcGVydGllcyBpcyBkZXByZWNhdGVkLiBVc2UgYmVmb3JlX3NlbmQgaW5zdGVhZFwiKSx0PWkodCxcIiRzZXRfb25jZVwiKSksUCh0KT92b2lkIDA6dH1yZWdpc3RlcihlLHQpe3ZhciBpO251bGw9PT0oaT10aGlzLnBlcnNpc3RlbmNlKXx8dm9pZCAwPT09aXx8aS5yZWdpc3RlcihlLHQpfXJlZ2lzdGVyX29uY2UoZSx0LGkpe3ZhciByO251bGw9PT0ocj10aGlzLnBlcnNpc3RlbmNlKXx8dm9pZCAwPT09cnx8ci5yZWdpc3Rlcl9vbmNlKGUsdCxpKX1yZWdpc3Rlcl9mb3Jfc2Vzc2lvbihlKXt2YXIgdDtudWxsPT09KHQ9dGhpcy5zZXNzaW9uUGVyc2lzdGVuY2UpfHx2b2lkIDA9PT10fHx0LnJlZ2lzdGVyKGUpfXVucmVnaXN0ZXIoZSl7dmFyIHQ7bnVsbD09PSh0PXRoaXMucGVyc2lzdGVuY2UpfHx2b2lkIDA9PT10fHx0LnVucmVnaXN0ZXIoZSl9dW5yZWdpc3Rlcl9mb3Jfc2Vzc2lvbihlKXt2YXIgdDtudWxsPT09KHQ9dGhpcy5zZXNzaW9uUGVyc2lzdGVuY2UpfHx2b2lkIDA9PT10fHx0LnVucmVnaXN0ZXIoZSl9X3JlZ2lzdGVyX3NpbmdsZShlLHQpe3RoaXMucmVnaXN0ZXIoe1tlXTp0fSl9Z2V0RmVhdHVyZUZsYWcoZSx0KXtyZXR1cm4gdGhpcy5mZWF0dXJlRmxhZ3MuZ2V0RmVhdHVyZUZsYWcoZSx0KX1nZXRGZWF0dXJlRmxhZ1BheWxvYWQoZSl7dmFyIHQ9dGhpcy5mZWF0dXJlRmxhZ3MuZ2V0RmVhdHVyZUZsYWdQYXlsb2FkKGUpO3RyeXtyZXR1cm4gSlNPTi5wYXJzZSh0KX1jYXRjaChlKXtyZXR1cm4gdH19aXNGZWF0dXJlRW5hYmxlZChlLHQpe3JldHVybiB0aGlzLmZlYXR1cmVGbGFncy5pc0ZlYXR1cmVFbmFibGVkKGUsdCl9cmVsb2FkRmVhdHVyZUZsYWdzKCl7dGhpcy5mZWF0dXJlRmxhZ3MucmVsb2FkRmVhdHVyZUZsYWdzKCl9dXBkYXRlRWFybHlBY2Nlc3NGZWF0dXJlRW5yb2xsbWVudChlLHQpe3RoaXMuZmVhdHVyZUZsYWdzLnVwZGF0ZUVhcmx5QWNjZXNzRmVhdHVyZUVucm9sbG1lbnQoZSx0KX1nZXRFYXJseUFjY2Vzc0ZlYXR1cmVzKGUpe3ZhciB0PWFyZ3VtZW50cy5sZW5ndGg+MSYmdm9pZCAwIT09YXJndW1lbnRzWzFdJiZhcmd1bWVudHNbMV07cmV0dXJuIHRoaXMuZmVhdHVyZUZsYWdzLmdldEVhcmx5QWNjZXNzRmVhdHVyZXMoZSx0KX1vbihlLHQpe3JldHVybiB0aGlzLl9pbnRlcm5hbEV2ZW50RW1pdHRlci5vbihlLHQpfW9uRmVhdHVyZUZsYWdzKGUpe3JldHVybiB0aGlzLmZlYXR1cmVGbGFncy5vbkZlYXR1cmVGbGFncyhlKX1vblNlc3Npb25JZChlKXt2YXIgdCxpO3JldHVybiBudWxsIT09KHQ9bnVsbD09PShpPXRoaXMuc2Vzc2lvbk1hbmFnZXIpfHx2b2lkIDA9PT1pP3ZvaWQgMDppLm9uU2Vzc2lvbklkKGUpKSYmdm9pZCAwIT09dD90OigpPT57fX1nZXRTdXJ2ZXlzKGUpe3ZhciB0PWFyZ3VtZW50cy5sZW5ndGg+MSYmdm9pZCAwIT09YXJndW1lbnRzWzFdJiZhcmd1bWVudHNbMV07dGhpcy5zdXJ2ZXlzLmdldFN1cnZleXMoZSx0KX1nZXRBY3RpdmVNYXRjaGluZ1N1cnZleXMoZSl7dmFyIHQ9YXJndW1lbnRzLmxlbmd0aD4xJiZ2b2lkIDAhPT1hcmd1bWVudHNbMV0mJmFyZ3VtZW50c1sxXTt0aGlzLnN1cnZleXMuZ2V0QWN0aXZlTWF0Y2hpbmdTdXJ2ZXlzKGUsdCl9cmVuZGVyU3VydmV5KGUsdCl7dGhpcy5zdXJ2ZXlzLnJlbmRlclN1cnZleShlLHQpfWNhblJlbmRlclN1cnZleShlKXt0aGlzLnN1cnZleXMuY2FuUmVuZGVyU3VydmV5KGUpfWdldE5leHRTdXJ2ZXlTdGVwKGUsdCxpKXtyZXR1cm4gdGhpcy5zdXJ2ZXlzLmdldE5leHRTdXJ2ZXlTdGVwKGUsdCxpKX1pZGVudGlmeShlLHQsaSl7aWYoIXRoaXMuX19sb2FkZWR8fCF0aGlzLnBlcnNpc3RlbmNlKXJldHVybiBxLnVuaW5pdGlhbGl6ZWRXYXJuaW5nKFwicG9zdGhvZy5pZGVudGlmeVwiKTtpZihNKGUpJiYoZT1lLnRvU3RyaW5nKCkscS53YXJuKFwiVGhlIGZpcnN0IGFyZ3VtZW50IHRvIHBvc3Rob2cuaWRlbnRpZnkgd2FzIGEgbnVtYmVyLCBidXQgaXQgc2hvdWxkIGJlIGEgc3RyaW5nLiBJdCBoYXMgYmVlbiBjb252ZXJ0ZWQgdG8gYSBzdHJpbmcuXCIpKSxlKXtpZihbXCJkaXN0aW5jdF9pZFwiLFwiZGlzdGluY3RpZFwiXS5pbmNsdWRlcyhlLnRvTG93ZXJDYXNlKCkpKXEuY3JpdGljYWwoJ1RoZSBzdHJpbmcgXCInLmNvbmNhdChlLCdcIiB3YXMgc2V0IGluIHBvc3Rob2cuaWRlbnRpZnkgd2hpY2ggaW5kaWNhdGVzIGFuIGVycm9yLiBUaGlzIElEIHNob3VsZCBiZSB1bmlxdWUgdG8gdGhlIHVzZXIgYW5kIG5vdCBhIGhhcmRjb2RlZCBzdHJpbmcuJykpO2Vsc2UgaWYodGhpcy5fcmVxdWlyZVBlcnNvblByb2Nlc3NpbmcoXCJwb3N0aG9nLmlkZW50aWZ5XCIpKXt2YXIgcj10aGlzLmdldF9kaXN0aW5jdF9pZCgpO2lmKHRoaXMucmVnaXN0ZXIoeyR1c2VyX2lkOmV9KSwhdGhpcy5nZXRfcHJvcGVydHkoXCIkZGV2aWNlX2lkXCIpKXt2YXIgbj1yO3RoaXMucmVnaXN0ZXJfb25jZSh7JGhhZF9wZXJzaXN0ZWRfZGlzdGluY3RfaWQ6ITAsJGRldmljZV9pZDpufSxcIlwiKX1lIT09ciYmZSE9PXRoaXMuZ2V0X3Byb3BlcnR5KG9lKSYmKHRoaXMudW5yZWdpc3RlcihvZSksdGhpcy5yZWdpc3Rlcih7ZGlzdGluY3RfaWQ6ZX0pKTt2YXIgcz1cImFub255bW91c1wiPT09KHRoaXMucGVyc2lzdGVuY2UuZ2V0X3Byb3BlcnR5KCRlKXx8XCJhbm9ueW1vdXNcIik7ZSE9PXImJnM/KHRoaXMucGVyc2lzdGVuY2Uuc2V0X3Byb3BlcnR5KCRlLFwiaWRlbnRpZmllZFwiKSx0aGlzLnNldFBlcnNvblByb3BlcnRpZXNGb3JGbGFncyh0fHx7fSwhMSksdGhpcy5jYXB0dXJlKFwiJGlkZW50aWZ5XCIse2Rpc3RpbmN0X2lkOmUsJGFub25fZGlzdGluY3RfaWQ6cn0seyRzZXQ6dHx8e30sJHNldF9vbmNlOml8fHt9fSksdGhpcy5mZWF0dXJlRmxhZ3Muc2V0QW5vbnltb3VzRGlzdGluY3RJZChyKSx0aGlzLl9jYWNoZWRJZGVudGlmeT11byhlLHQsaSkpOih0fHxpKSYmKHRoaXMuX2NhY2hlZElkZW50aWZ5IT09dW8oZSx0LGkpPyh0aGlzLnNldFBlcnNvblByb3BlcnRpZXModCxpKSx0aGlzLl9jYWNoZWRJZGVudGlmeT11byhlLHQsaSkpOnEuaW5mbyhcIkEgZHVwbGljYXRlIHBvc3Rob2cuaWRlbnRpZnkgY2FsbCB3YXMgbWFkZSB3aXRoIHRoZSBzYW1lIHByb3BlcnRpZXMuIEl0IGhhcyBiZWVuIGlnbm9yZWQuXCIpKSxlIT09ciYmKHRoaXMucmVsb2FkRmVhdHVyZUZsYWdzKCksdGhpcy51bnJlZ2lzdGVyKFRlKSl9fWVsc2UgcS5lcnJvcihcIlVuaXF1ZSB1c2VyIGlkIGhhcyBub3QgYmVlbiBzZXQgaW4gcG9zdGhvZy5pZGVudGlmeVwiKX1zZXRQZXJzb25Qcm9wZXJ0aWVzKGUsdCl7KGV8fHQpJiZ0aGlzLl9yZXF1aXJlUGVyc29uUHJvY2Vzc2luZyhcInBvc3Rob2cuc2V0UGVyc29uUHJvcGVydGllc1wiKSYmKHRoaXMuc2V0UGVyc29uUHJvcGVydGllc0ZvckZsYWdzKGV8fHt9KSx0aGlzLmNhcHR1cmUoXCIkc2V0XCIseyRzZXQ6ZXx8e30sJHNldF9vbmNlOnR8fHt9fSkpfWdyb3VwKGUsdCxpKXtpZihlJiZ0KXtpZih0aGlzLl9yZXF1aXJlUGVyc29uUHJvY2Vzc2luZyhcInBvc3Rob2cuZ3JvdXBcIikpe3ZhciByPXRoaXMuZ2V0R3JvdXBzKCk7cltlXSE9PXQmJnRoaXMucmVzZXRHcm91cFByb3BlcnRpZXNGb3JGbGFncyhlKSx0aGlzLnJlZ2lzdGVyKHskZ3JvdXBzOmooaih7fSxyKSx7fSx7W2VdOnR9KX0pLGkmJih0aGlzLmNhcHR1cmUoXCIkZ3JvdXBpZGVudGlmeVwiLHskZ3JvdXBfdHlwZTplLCRncm91cF9rZXk6dCwkZ3JvdXBfc2V0Oml9KSx0aGlzLnNldEdyb3VwUHJvcGVydGllc0ZvckZsYWdzKHtbZV06aX0pKSxyW2VdPT09dHx8aXx8dGhpcy5yZWxvYWRGZWF0dXJlRmxhZ3MoKX19ZWxzZSBxLmVycm9yKFwicG9zdGhvZy5ncm91cCByZXF1aXJlcyBhIGdyb3VwIHR5cGUgYW5kIGdyb3VwIGtleVwiKX1yZXNldEdyb3Vwcygpe3RoaXMucmVnaXN0ZXIoeyRncm91cHM6e319KSx0aGlzLnJlc2V0R3JvdXBQcm9wZXJ0aWVzRm9yRmxhZ3MoKSx0aGlzLnJlbG9hZEZlYXR1cmVGbGFncygpfXNldFBlcnNvblByb3BlcnRpZXNGb3JGbGFncyhlKXt2YXIgdD0hKGFyZ3VtZW50cy5sZW5ndGg+MSYmdm9pZCAwIT09YXJndW1lbnRzWzFdKXx8YXJndW1lbnRzWzFdO3RoaXMuZmVhdHVyZUZsYWdzLnNldFBlcnNvblByb3BlcnRpZXNGb3JGbGFncyhlLHQpfXJlc2V0UGVyc29uUHJvcGVydGllc0ZvckZsYWdzKCl7dGhpcy5mZWF0dXJlRmxhZ3MucmVzZXRQZXJzb25Qcm9wZXJ0aWVzRm9yRmxhZ3MoKX1zZXRHcm91cFByb3BlcnRpZXNGb3JGbGFncyhlKXt2YXIgdD0hKGFyZ3VtZW50cy5sZW5ndGg+MSYmdm9pZCAwIT09YXJndW1lbnRzWzFdKXx8YXJndW1lbnRzWzFdO3RoaXMuX3JlcXVpcmVQZXJzb25Qcm9jZXNzaW5nKFwicG9zdGhvZy5zZXRHcm91cFByb3BlcnRpZXNGb3JGbGFnc1wiKSYmdGhpcy5mZWF0dXJlRmxhZ3Muc2V0R3JvdXBQcm9wZXJ0aWVzRm9yRmxhZ3MoZSx0KX1yZXNldEdyb3VwUHJvcGVydGllc0ZvckZsYWdzKGUpe3RoaXMuZmVhdHVyZUZsYWdzLnJlc2V0R3JvdXBQcm9wZXJ0aWVzRm9yRmxhZ3MoZSl9cmVzZXQoZSl7dmFyIHQsaSxyLG4scztpZihxLmluZm8oXCJyZXNldFwiKSwhdGhpcy5fX2xvYWRlZClyZXR1cm4gcS51bmluaXRpYWxpemVkV2FybmluZyhcInBvc3Rob2cucmVzZXRcIik7dmFyIG89dGhpcy5nZXRfcHJvcGVydHkoXCIkZGV2aWNlX2lkXCIpO2lmKHRoaXMuY29uc2VudC5yZXNldCgpLG51bGw9PT0odD10aGlzLnBlcnNpc3RlbmNlKXx8dm9pZCAwPT09dHx8dC5jbGVhcigpLG51bGw9PT0oaT10aGlzLnNlc3Npb25QZXJzaXN0ZW5jZSl8fHZvaWQgMD09PWl8fGkuY2xlYXIoKSxudWxsPT09KHI9dGhpcy5zdXJ2ZXlzKXx8dm9pZCAwPT09cnx8ci5yZXNldCgpLG51bGw9PT0obj10aGlzLnBlcnNpc3RlbmNlKXx8dm9pZCAwPT09bnx8bi5zZXRfcHJvcGVydHkoJGUsXCJhbm9ueW1vdXNcIiksbnVsbD09PShzPXRoaXMuc2Vzc2lvbk1hbmFnZXIpfHx2b2lkIDA9PT1zfHxzLnJlc2V0U2Vzc2lvbklkKCksdGhpcy5fY2FjaGVkSWRlbnRpZnk9bnVsbCx0aGlzLmNvbmZpZy5fX3ByZXZpZXdfZXhwZXJpbWVudGFsX2Nvb2tpZWxlc3NfbW9kZSl0aGlzLnJlZ2lzdGVyX29uY2Uoe2Rpc3RpbmN0X2lkOkJlLCRkZXZpY2VfaWQ6bnVsbH0sXCJcIik7ZWxzZXt2YXIgYT10aGlzLmNvbmZpZy5nZXRfZGV2aWNlX2lkKGV0KCkpO3RoaXMucmVnaXN0ZXJfb25jZSh7ZGlzdGluY3RfaWQ6YSwkZGV2aWNlX2lkOmU/YTpvfSxcIlwiKX19Z2V0X2Rpc3RpbmN0X2lkKCl7cmV0dXJuIHRoaXMuZ2V0X3Byb3BlcnR5KFwiZGlzdGluY3RfaWRcIil9Z2V0R3JvdXBzKCl7cmV0dXJuIHRoaXMuZ2V0X3Byb3BlcnR5KFwiJGdyb3Vwc1wiKXx8e319Z2V0X3Nlc3Npb25faWQoKXt2YXIgZSx0O3JldHVybiBudWxsIT09KGU9bnVsbD09PSh0PXRoaXMuc2Vzc2lvbk1hbmFnZXIpfHx2b2lkIDA9PT10P3ZvaWQgMDp0LmNoZWNrQW5kR2V0U2Vzc2lvbkFuZFdpbmRvd0lkKCEwKS5zZXNzaW9uSWQpJiZ2b2lkIDAhPT1lP2U6XCJcIn1nZXRfc2Vzc2lvbl9yZXBsYXlfdXJsKGUpe2lmKCF0aGlzLnNlc3Npb25NYW5hZ2VyKXJldHVyblwiXCI7dmFye3Nlc3Npb25JZDp0LHNlc3Npb25TdGFydFRpbWVzdGFtcDppfT10aGlzLnNlc3Npb25NYW5hZ2VyLmNoZWNrQW5kR2V0U2Vzc2lvbkFuZFdpbmRvd0lkKCEwKSxyPXRoaXMucmVxdWVzdFJvdXRlci5lbmRwb2ludEZvcihcInVpXCIsXCIvcHJvamVjdC9cIi5jb25jYXQodGhpcy5jb25maWcudG9rZW4sXCIvcmVwbGF5L1wiKS5jb25jYXQodCkpO2lmKG51bGwhPWUmJmUud2l0aFRpbWVzdGFtcCYmaSl7dmFyIG4scz1udWxsIT09KG49ZS50aW1lc3RhbXBMb29rQmFjaykmJnZvaWQgMCE9PW4/bjoxMDtpZighaSlyZXR1cm4gcjt2YXIgbz1NYXRoLm1heChNYXRoLmZsb29yKCgobmV3IERhdGUpLmdldFRpbWUoKS1pKS8xZTMpLXMsMCk7cis9XCI/dD1cIi5jb25jYXQobyl9cmV0dXJuIHJ9YWxpYXMoZSx0KXtyZXR1cm4gZT09PXRoaXMuZ2V0X3Byb3BlcnR5KHNlKT8ocS5jcml0aWNhbChcIkF0dGVtcHRpbmcgdG8gY3JlYXRlIGFsaWFzIGZvciBleGlzdGluZyBQZW9wbGUgdXNlciAtIGFib3J0aW5nLlwiKSwtMik6dGhpcy5fcmVxdWlyZVBlcnNvblByb2Nlc3NpbmcoXCJwb3N0aG9nLmFsaWFzXCIpPyhGKHQpJiYodD10aGlzLmdldF9kaXN0aW5jdF9pZCgpKSxlIT09dD8odGhpcy5fcmVnaXN0ZXJfc2luZ2xlKG9lLGUpLHRoaXMuY2FwdHVyZShcIiRjcmVhdGVfYWxpYXNcIix7YWxpYXM6ZSxkaXN0aW5jdF9pZDp0fSkpOihxLndhcm4oXCJhbGlhcyBtYXRjaGVzIGN1cnJlbnQgZGlzdGluY3RfaWQgLSBza2lwcGluZyBhcGkgY2FsbC5cIiksdGhpcy5pZGVudGlmeShlKSwtMSkpOnZvaWQgMH1zZXRfY29uZmlnKGUpe3ZhciB0LGkscixuLHM9aih7fSx0aGlzLmNvbmZpZyk7QyhlKSYmKEsodGhpcy5jb25maWcsZ28oZSkpLG51bGw9PT0odD10aGlzLnBlcnNpc3RlbmNlKXx8dm9pZCAwPT09dHx8dC51cGRhdGVfY29uZmlnKHRoaXMuY29uZmlnLHMpLHRoaXMuc2Vzc2lvblBlcnNpc3RlbmNlPVwic2Vzc2lvblN0b3JhZ2VcIj09PXRoaXMuY29uZmlnLnBlcnNpc3RlbmNlfHxcIm1lbW9yeVwiPT09dGhpcy5jb25maWcucGVyc2lzdGVuY2U/dGhpcy5wZXJzaXN0ZW5jZTpuZXcgRWkoaihqKHt9LHRoaXMuY29uZmlnKSx7fSx7cGVyc2lzdGVuY2U6XCJzZXNzaW9uU3RvcmFnZVwifSkpLGx0LmlzX3N1cHBvcnRlZCgpJiZcInRydWVcIj09PWx0LmdldChcInBoX2RlYnVnXCIpJiYodGhpcy5jb25maWcuZGVidWc9ITApLHRoaXMuY29uZmlnLmRlYnVnJiYocC5ERUJVRz0hMCxxLmluZm8oXCJzZXRfY29uZmlnXCIse2NvbmZpZzplLG9sZENvbmZpZzpzLG5ld0NvbmZpZzpqKHt9LHRoaXMuY29uZmlnKX0pKSxudWxsPT09KGk9dGhpcy5zZXNzaW9uUmVjb3JkaW5nKXx8dm9pZCAwPT09aXx8aS5zdGFydElmRW5hYmxlZE9yU3RvcCgpLG51bGw9PT0ocj10aGlzLmF1dG9jYXB0dXJlKXx8dm9pZCAwPT09cnx8ci5zdGFydElmRW5hYmxlZCgpLG51bGw9PT0obj10aGlzLmhlYXRtYXBzKXx8dm9pZCAwPT09bnx8bi5zdGFydElmRW5hYmxlZCgpLHRoaXMuc3VydmV5cy5sb2FkSWZFbmFibGVkKCksdGhpcy5fc3luY19vcHRfb3V0X3dpdGhfcGVyc2lzdGVuY2UoKSl9c3RhcnRTZXNzaW9uUmVjb3JkaW5nKGUpe3ZhciB0PSEwPT09ZSxpPXtzYW1wbGluZzp0fHwhKG51bGw9PWV8fCFlLnNhbXBsaW5nKSxsaW5rZWRfZmxhZzp0fHwhKG51bGw9PWV8fCFlLmxpbmtlZF9mbGFnKSx1cmxfdHJpZ2dlcjp0fHwhKG51bGw9PWV8fCFlLnVybF90cmlnZ2VyKSxldmVudF90cmlnZ2VyOnR8fCEobnVsbD09ZXx8IWUuZXZlbnRfdHJpZ2dlcil9O2lmKE9iamVjdC52YWx1ZXMoaSkuc29tZShCb29sZWFuKSl7dmFyIHIsbixzLG8sYTtpZihudWxsPT09KHI9dGhpcy5zZXNzaW9uTWFuYWdlcil8fHZvaWQgMD09PXJ8fHIuY2hlY2tBbmRHZXRTZXNzaW9uQW5kV2luZG93SWQoKSxpLnNhbXBsaW5nKW51bGw9PT0obj10aGlzLnNlc3Npb25SZWNvcmRpbmcpfHx2b2lkIDA9PT1ufHxuLm92ZXJyaWRlU2FtcGxpbmcoKTtpZihpLmxpbmtlZF9mbGFnKW51bGw9PT0ocz10aGlzLnNlc3Npb25SZWNvcmRpbmcpfHx2b2lkIDA9PT1zfHxzLm92ZXJyaWRlTGlua2VkRmxhZygpO2lmKGkudXJsX3RyaWdnZXIpbnVsbD09PShvPXRoaXMuc2Vzc2lvblJlY29yZGluZyl8fHZvaWQgMD09PW98fG8ub3ZlcnJpZGVUcmlnZ2VyKFwidXJsXCIpO2lmKGkuZXZlbnRfdHJpZ2dlciludWxsPT09KGE9dGhpcy5zZXNzaW9uUmVjb3JkaW5nKXx8dm9pZCAwPT09YXx8YS5vdmVycmlkZVRyaWdnZXIoXCJldmVudFwiKX10aGlzLnNldF9jb25maWcoe2Rpc2FibGVfc2Vzc2lvbl9yZWNvcmRpbmc6ITF9KX1zdG9wU2Vzc2lvblJlY29yZGluZygpe3RoaXMuc2V0X2NvbmZpZyh7ZGlzYWJsZV9zZXNzaW9uX3JlY29yZGluZzohMH0pfXNlc3Npb25SZWNvcmRpbmdTdGFydGVkKCl7dmFyIGU7cmV0dXJuIShudWxsPT09KGU9dGhpcy5zZXNzaW9uUmVjb3JkaW5nKXx8dm9pZCAwPT09ZXx8IWUuc3RhcnRlZCl9Y2FwdHVyZUV4Y2VwdGlvbihlLHQpe3ZhciBpLHI9bmV3IEVycm9yKFwiUG9zdEhvZyBzeW50aGV0aWNFeGNlcHRpb25cIiksbj1JKG51bGw9PT0oaT1fLl9fUG9zdGhvZ0V4dGVuc2lvbnNfXyl8fHZvaWQgMD09PWk/dm9pZCAwOmkucGFyc2VFcnJvckFzUHJvcGVydGllcyk/Xy5fX1Bvc3Rob2dFeHRlbnNpb25zX18ucGFyc2VFcnJvckFzUHJvcGVydGllcyhbZS5tZXNzYWdlLHZvaWQgMCx2b2lkIDAsdm9pZCAwLGVdLHtzeW50aGV0aWNFeGNlcHRpb246cn0pOmooeyRleGNlcHRpb25fbGV2ZWw6XCJlcnJvclwiLCRleGNlcHRpb25fbGlzdDpbe3R5cGU6ZS5uYW1lLHZhbHVlOmUubWVzc2FnZSxtZWNoYW5pc206e2hhbmRsZWQ6ITAsc3ludGhldGljOiExfX1dfSx0KTt0aGlzLmV4Y2VwdGlvbnMuc2VuZEV4Y2VwdGlvbkV2ZW50KG4pfWxvYWRUb29sYmFyKGUpe3JldHVybiB0aGlzLnRvb2xiYXIubG9hZFRvb2xiYXIoZSl9Z2V0X3Byb3BlcnR5KGUpe3ZhciB0O3JldHVybiBudWxsPT09KHQ9dGhpcy5wZXJzaXN0ZW5jZSl8fHZvaWQgMD09PXQ/dm9pZCAwOnQucHJvcHNbZV19Z2V0U2Vzc2lvblByb3BlcnR5KGUpe3ZhciB0O3JldHVybiBudWxsPT09KHQ9dGhpcy5zZXNzaW9uUGVyc2lzdGVuY2UpfHx2b2lkIDA9PT10P3ZvaWQgMDp0LnByb3BzW2VdfXRvU3RyaW5nKCl7dmFyIGUsdD1udWxsIT09KGU9dGhpcy5jb25maWcubmFtZSkmJnZvaWQgMCE9PWU/ZTpfbztyZXR1cm4gdCE9PV9vJiYodD1fbytcIi5cIit0KSx0fV9pc0lkZW50aWZpZWQoKXt2YXIgZSx0O3JldHVyblwiaWRlbnRpZmllZFwiPT09KG51bGw9PT0oZT10aGlzLnBlcnNpc3RlbmNlKXx8dm9pZCAwPT09ZT92b2lkIDA6ZS5nZXRfcHJvcGVydHkoJGUpKXx8XCJpZGVudGlmaWVkXCI9PT0obnVsbD09PSh0PXRoaXMuc2Vzc2lvblBlcnNpc3RlbmNlKXx8dm9pZCAwPT09dD92b2lkIDA6dC5nZXRfcHJvcGVydHkoJGUpKX1faGFzUGVyc29uUHJvY2Vzc2luZygpe3ZhciBlLHQsaSxyO3JldHVybiEoXCJuZXZlclwiPT09dGhpcy5jb25maWcucGVyc29uX3Byb2ZpbGVzfHxcImlkZW50aWZpZWRfb25seVwiPT09dGhpcy5jb25maWcucGVyc29uX3Byb2ZpbGVzJiYhdGhpcy5faXNJZGVudGlmaWVkKCkmJlAodGhpcy5nZXRHcm91cHMoKSkmJihudWxsPT09KGU9dGhpcy5wZXJzaXN0ZW5jZSl8fHZvaWQgMD09PWV8fG51bGw9PT0odD1lLnByb3BzKXx8dm9pZCAwPT09dHx8IXRbb2VdKSYmKG51bGw9PT0oaT10aGlzLnBlcnNpc3RlbmNlKXx8dm9pZCAwPT09aXx8bnVsbD09PShyPWkucHJvcHMpfHx2b2lkIDA9PT1yfHwhcltOZV0pKX1fc2hvdWxkQ2FwdHVyZVBhZ2VsZWF2ZSgpe3JldHVybiEwPT09dGhpcy5jb25maWcuY2FwdHVyZV9wYWdlbGVhdmV8fFwiaWZfY2FwdHVyZV9wYWdldmlld1wiPT09dGhpcy5jb25maWcuY2FwdHVyZV9wYWdlbGVhdmUmJnRoaXMuY29uZmlnLmNhcHR1cmVfcGFnZXZpZXd9Y3JlYXRlUGVyc29uUHJvZmlsZSgpe3RoaXMuX2hhc1BlcnNvblByb2Nlc3NpbmcoKXx8dGhpcy5fcmVxdWlyZVBlcnNvblByb2Nlc3NpbmcoXCJwb3N0aG9nLmNyZWF0ZVBlcnNvblByb2ZpbGVcIikmJnRoaXMuc2V0UGVyc29uUHJvcGVydGllcyh7fSx7fSl9X3JlcXVpcmVQZXJzb25Qcm9jZXNzaW5nKGUpe3JldHVyblwibmV2ZXJcIj09PXRoaXMuY29uZmlnLnBlcnNvbl9wcm9maWxlcz8ocS5lcnJvcihlKycgd2FzIGNhbGxlZCwgYnV0IHByb2Nlc3NfcGVyc29uIGlzIHNldCB0byBcIm5ldmVyXCIuIFRoaXMgY2FsbCB3aWxsIGJlIGlnbm9yZWQuJyksITEpOih0aGlzLl9yZWdpc3Rlcl9zaW5nbGUoTmUsITApLCEwKX1fc3luY19vcHRfb3V0X3dpdGhfcGVyc2lzdGVuY2UoKXt2YXIgZSx0LGkscixuPXRoaXMuY29uc2VudC5pc09wdGVkT3V0KCkscz10aGlzLmNvbmZpZy5vcHRfb3V0X3BlcnNpc3RlbmNlX2J5X2RlZmF1bHQsbz10aGlzLmNvbmZpZy5kaXNhYmxlX3BlcnNpc3RlbmNlfHxuJiYhIXM7KG51bGw9PT0oZT10aGlzLnBlcnNpc3RlbmNlKXx8dm9pZCAwPT09ZT92b2lkIDA6ZS5kaXNhYmxlZCkhPT1vJiYobnVsbD09PShpPXRoaXMucGVyc2lzdGVuY2UpfHx2b2lkIDA9PT1pfHxpLnNldF9kaXNhYmxlZChvKSk7KG51bGw9PT0odD10aGlzLnNlc3Npb25QZXJzaXN0ZW5jZSl8fHZvaWQgMD09PXQ/dm9pZCAwOnQuZGlzYWJsZWQpIT09byYmKG51bGw9PT0ocj10aGlzLnNlc3Npb25QZXJzaXN0ZW5jZSl8fHZvaWQgMD09PXJ8fHIuc2V0X2Rpc2FibGVkKG8pKX1vcHRfaW5fY2FwdHVyaW5nKGUpe3ZhciB0Oyh0aGlzLmNvbnNlbnQub3B0SW5PdXQoITApLHRoaXMuX3N5bmNfb3B0X291dF93aXRoX3BlcnNpc3RlbmNlKCksRihudWxsPT1lP3ZvaWQgMDplLmNhcHR1cmVFdmVudE5hbWUpfHxudWxsIT1lJiZlLmNhcHR1cmVFdmVudE5hbWUpJiZ0aGlzLmNhcHR1cmUobnVsbCE9PSh0PW51bGw9PWU/dm9pZCAwOmUuY2FwdHVyZUV2ZW50TmFtZSkmJnZvaWQgMCE9PXQ/dDpcIiRvcHRfaW5cIixudWxsPT1lP3ZvaWQgMDplLmNhcHR1cmVQcm9wZXJ0aWVzLHtzZW5kX2luc3RhbnRseTohMH0pO3RoaXMuY29uZmlnLmNhcHR1cmVfcGFnZXZpZXcmJnRoaXMuX2NhcHR1cmVJbml0aWFsUGFnZXZpZXcoKX1vcHRfb3V0X2NhcHR1cmluZygpe3RoaXMuY29uc2VudC5vcHRJbk91dCghMSksdGhpcy5fc3luY19vcHRfb3V0X3dpdGhfcGVyc2lzdGVuY2UoKX1oYXNfb3B0ZWRfaW5fY2FwdHVyaW5nKCl7cmV0dXJuIHRoaXMuY29uc2VudC5pc09wdGVkSW4oKX1oYXNfb3B0ZWRfb3V0X2NhcHR1cmluZygpe3JldHVybiB0aGlzLmNvbnNlbnQuaXNPcHRlZE91dCgpfWNsZWFyX29wdF9pbl9vdXRfY2FwdHVyaW5nKCl7dGhpcy5jb25zZW50LnJlc2V0KCksdGhpcy5fc3luY19vcHRfb3V0X3dpdGhfcGVyc2lzdGVuY2UoKX1faXNfYm90KCl7cmV0dXJuIG8/JHMobyx0aGlzLmNvbmZpZy5jdXN0b21fYmxvY2tlZF91c2VyYWdlbnRzKTp2b2lkIDB9X2NhcHR1cmVJbml0aWFsUGFnZXZpZXcoKXthJiYhdGhpcy5faW5pdGlhbFBhZ2V2aWV3Q2FwdHVyZWQmJih0aGlzLl9pbml0aWFsUGFnZXZpZXdDYXB0dXJlZD0hMCx0aGlzLmNhcHR1cmUoXCIkcGFnZXZpZXdcIix7dGl0bGU6YS50aXRsZX0se3NlbmRfaW5zdGFudGx5OiEwfSkpfWRlYnVnKGUpeyExPT09ZT8obnVsbD09dHx8dC5jb25zb2xlLmxvZyhcIllvdSd2ZSBkaXNhYmxlZCBkZWJ1ZyBtb2RlLlwiKSxsb2NhbFN0b3JhZ2UmJmxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKFwicGhfZGVidWdcIiksdGhpcy5zZXRfY29uZmlnKHtkZWJ1ZzohMX0pKToobnVsbD09dHx8dC5jb25zb2xlLmxvZyhcIllvdSdyZSBub3cgaW4gZGVidWcgbW9kZS4gQWxsIGNhbGxzIHRvIFBvc3RIb2cgd2lsbCBiZSBsb2dnZWQgaW4geW91ciBjb25zb2xlLlxcbllvdSBjYW4gZGlzYWJsZSB0aGlzIHdpdGggYHBvc3Rob2cuZGVidWcoZmFsc2UpYC5cIiksbG9jYWxTdG9yYWdlJiZsb2NhbFN0b3JhZ2Uuc2V0SXRlbShcInBoX2RlYnVnXCIsXCJ0cnVlXCIpLHRoaXMuc2V0X2NvbmZpZyh7ZGVidWc6ITB9KSl9X3J1bkJlZm9yZVNlbmQoZSl7aWYoTyh0aGlzLmNvbmZpZy5iZWZvcmVfc2VuZCkpcmV0dXJuIGU7dmFyIHQ9eCh0aGlzLmNvbmZpZy5iZWZvcmVfc2VuZCk/dGhpcy5jb25maWcuYmVmb3JlX3NlbmQ6W3RoaXMuY29uZmlnLmJlZm9yZV9zZW5kXSxpPWU7Zm9yKHZhciByIG9mIHQpe2lmKGk9cihpKSxPKGkpKXt2YXIgbj1cIkV2ZW50ICdcIi5jb25jYXQoZS5ldmVudCxcIicgd2FzIHJlamVjdGVkIGluIGJlZm9yZVNlbmQgZnVuY3Rpb25cIik7cmV0dXJuIEQoZS5ldmVudCk/cS53YXJuKFwiXCIuY29uY2F0KG4sXCIuIFRoaXMgY2FuIGNhdXNlIHVuZXhwZWN0ZWQgYmVoYXZpb3IuXCIpKTpxLmluZm8obiksbnVsbH1pLnByb3BlcnRpZXMmJiFQKGkucHJvcGVydGllcyl8fHEud2FybihcIkV2ZW50ICdcIi5jb25jYXQoZS5ldmVudCxcIicgaGFzIG5vIHByb3BlcnRpZXMgYWZ0ZXIgYmVmb3JlU2VuZCBmdW5jdGlvbiwgdGhpcyBpcyBsaWtlbHkgYW4gZXJyb3IuXCIpKX1yZXR1cm4gaX1nZXRQYWdlVmlld0lkKCl7dmFyIGU7cmV0dXJuIG51bGw9PT0oZT10aGlzLnBhZ2VWaWV3TWFuYWdlci5fY3VycmVudFBhZ2V2aWV3KXx8dm9pZCAwPT09ZT92b2lkIDA6ZS5wYWdlVmlld0lkfX0hZnVuY3Rpb24oZSx0KXtmb3IodmFyIGk9MDtpPHQubGVuZ3RoO2krKyllLnByb3RvdHlwZVt0W2ldXT1lZShlLnByb3RvdHlwZVt0W2ldXSl9KG1vLFtcImlkZW50aWZ5XCJdKTt2YXIgYm8seW89KGJvPWNvW19vXT1uZXcgbW8sZnVuY3Rpb24oKXtmdW5jdGlvbiBlKCl7ZS5kb25lfHwoZS5kb25lPSEwLHBvPSExLFkoY28sKGZ1bmN0aW9uKGUpe2UuX2RvbV9sb2FkZWQoKX0pKSl9bnVsbCE9YSYmYS5hZGRFdmVudExpc3RlbmVyJiYoXCJjb21wbGV0ZVwiPT09YS5yZWFkeVN0YXRlP2UoKTphLmFkZEV2ZW50TGlzdGVuZXIoXCJET01Db250ZW50TG9hZGVkXCIsZSwhMSkpLHQmJnJlKHQsXCJsb2FkXCIsZSwhMCl9KCksYm8pO2V4cG9ydHt2IGFzIENPUFlfQVVUT0NBUFRVUkVfRVZFTlQsZSBhcyBDb21wcmVzc2lvbixtbyBhcyBQb3N0SG9nLG1zIGFzIFN1cnZleVF1ZXN0aW9uQnJhbmNoaW5nVHlwZSxmcyBhcyBTdXJ2ZXlRdWVzdGlvblR5cGUsZ3MgYXMgU3VydmV5VHlwZSx5byBhcyBkZWZhdWx0LGcgYXMga25vd25VbnNhZmVFZGl0YWJsZUV2ZW50LHlvIGFzIHBvc3Rob2csZiBhcyBzZXZlcml0eUxldmVsc307XG4vLyMgc291cmNlTWFwcGluZ1VSTD1tb2R1bGUuanMubWFwXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/posthog-js/dist/module.js\n");

/***/ })

};
;