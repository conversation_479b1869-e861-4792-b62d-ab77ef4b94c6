### **New Features:**
- **Added sub-command for path persistence:** Introduced a new sub-command `screenpipe add-to-path` for managing path persistence efficiently.

### **Improvements:**
- **Enhanced auto destruct behavior:** Ensured that all pipes stop correctly when auto destruct is initiated.

### **Fixes:**
- No specific fixes were noted in the provided commits.

#### **Full Changelog:** [7031c..c5e17](https://github.com/mediar-ai/screenpipe/compare/7031c..c5e17)

