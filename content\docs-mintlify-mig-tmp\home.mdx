---
title: "Welcome to screenpipe"
icon: "house"
---

screenpipe empowers developers to build context-aware AI tools by:
- capturing screen & audio 24/7
- processing everything locally for privacy
- providing clean APIs for AI integration
- supporting all major platforms

built in rust for reliability.

## key features

- **24/7 media capture**: captures screen and audio data continuously, storing it locally.
- **personalized ai**: enables ai models to be powered by your captured data.
- **open source & secure**: your data stays private, 100% local, with complete control over storage and processing.
- **cross-platform**: works on windows, macos, and linux.
- **multi-device support**: supports multiple monitors & audio devices for comprehensive data capture.
- **plugins (pipes)**: allows the creation and use of plugins (pipes) in NextJS, running within a sandboxed runtime to extend functionality.


## target audience

screenpipe is suitable for developers, AI businesses, and anyone interested in automating data capture and creating desktop context-aware ai agents. 

## what's next?

- [getting started](/getting-started)
- [plugins](/plugins)

