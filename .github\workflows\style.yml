name: Code Quality & Optimization

on: [push]

env:
  CARGO_TERM_COLOR: always

jobs:
  optimize:
    name: Dependency & Performance Checks
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
    runs-on: ${{ matrix.os }}

    steps:
      - uses: actions/checkout@v4

      - name: Check for unused dependencies
        uses: bnjbvr/cargo-machete@main

      - name: Install dependencies
        if: matrix.os == 'ubuntu-latest'
        run: .github/scripts/install_dependencies.sh

      - name: Install cargo-udeps
        run: |
          rustup install nightly
          cargo +nightly install cargo-udeps --locked

      - name: Run cargo-udeps
        run: cargo +nightly udeps

      - name: Install cargo-bloat
        run: cargo install cargo-bloat

      - name: Analyze binary size
        run: cargo bloat --release

      - name: Install cargo-audit
        run: cargo install cargo-audit

      - name: Security audit
        run: cargo audit

      - name: Install cargo-deny
        run: cargo install cargo-deny

      - name: Check duplicate dependencies
        run: cargo deny check duplicates
