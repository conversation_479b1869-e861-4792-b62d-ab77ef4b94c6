Based on the provided commits, here is the changelog for the new screenpipe update:

### **New Features:**
- **Released Screenpipe MCP setup CLI:** Introduced a command-line interface for easier setup of the Claude MCP.
- **Brought MCP server to limelight:** Enhanced visibility and functionality of the MCP server within the application.

### **Improvements:**
- **Improved benchmarks and platform-specific handling:** Enhanced performance benchmarks and refined management of platform-specific code for better compatibility.
- **Control computer functionality:** Implemented improved functionalities to allow users to control their computer more efficiently through the app.

### **Fixes:**
- **Removed development code:** Cleaned up the codebase by removing unnecessary development code to improve overall performance and maintainability.

#### **Full Changelog:** [adb69..8800b](https://github.com/mediar-ai/screenpipe/compare/adb69..8800b)

