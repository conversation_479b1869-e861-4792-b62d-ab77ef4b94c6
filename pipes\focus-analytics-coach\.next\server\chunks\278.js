exports.id=278,exports.ids=[278],exports.modules={509:(e,t,i)=>{"use strict";i.d(t,{F:()=>a});var r=i(802),n=i(12119),o=i(94351);class a{constructor(){this.fuse=new n.A([],{keys:["text","appName","windowName"],threshold:.3})}async analyzeTaskContent(e){try{let t=await r.F.queryScreenpipe({startTime:e.start.toISOString(),endTime:e.end.toISOString(),contentType:"ocr",limit:500,includeFrames:!1});if(!t?.data)return{detectedTasks:[],insights:[]};let i=[],n=[];for(let e of t.data)if("OCR"===e.type){let t=e.content.text||"",r=e.content.appName||"",n=e.content.windowName||"",a=this.analyzeOCRForTasks(t,r,n);a.confidence>.6&&i.push({id:(0,o.A)(),timestamp:new Date(e.content.timestamp),taskType:a.taskType,description:a.description,confidence:a.confidence,appName:r,windowName:n,ocrText:t.substring(0,200)})}let a=this.generateTaskInsights(i);return n.push(...a),{detectedTasks:i,insights:n}}catch(e){return console.error("Error analyzing task content:",e),{detectedTasks:[],insights:[]}}}analyzeOCRForTasks(e,t,i){let r=e.toLowerCase(),n=t.toLowerCase();i.toLowerCase();let o={taskType:"unknown",confidence:0,description:"Unknown activity"};for(let[e,i]of Object.entries({"deep-work":{keywords:["function","class","import","export","const","let","var","def","public","private"],apps:["vscode","intellij","xcode","sublime","vim"],confidence:.9},communication:{keywords:["message","chat","email","meeting","call","video","send","reply"],apps:["slack","teams","discord","zoom","mail"],confidence:.8},research:{keywords:["search","google","stackoverflow","documentation","tutorial","how to"],apps:["chrome","firefox","safari","edge"],confidence:.7},creative:{keywords:["design","layer","brush","color","font","canvas","artboard"],apps:["photoshop","illustrator","figma","sketch"],confidence:.8},administrative:{keywords:["spreadsheet","document","presentation","table","chart","formula"],apps:["excel","word","powerpoint","sheets","docs"],confidence:.7},learning:{keywords:["course","lesson","tutorial","learn","study","education"],apps:["coursera","udemy","khan"],confidence:.8},entertainment:{keywords:["watch","video","movie","music","game","play"],apps:["netflix","youtube","spotify","steam"],confidence:.9},social:{keywords:["post","like","share","comment","follow","friend"],apps:["facebook","twitter","instagram","linkedin"],confidence:.8}})){let a=0,s=[];i.apps.some(e=>n.includes(e))&&(a+=.4);let c=i.keywords.filter(e=>r.includes(e));c.length>0&&(a+=c.length/i.keywords.length*.6,s=c),(a*=i.confidence)>o.confidence&&(o={taskType:e,confidence:a,description:this.generateTaskDescription(e,s,t)})}return o}generateTaskDescription(e,t,i){let r={"deep-work":`Coding/development work in ${i}`,communication:`Communication activity in ${i}`,research:`Research and information gathering in ${i}`,creative:`Creative/design work in ${i}`,administrative:`Administrative tasks in ${i}`,learning:`Learning activity in ${i}`,entertainment:`Entertainment/leisure in ${i}`,social:`Social media activity in ${i}`,distraction:`Potentially distracting activity in ${i}`,unknown:`Activity in ${i}`};return r[e]||r.unknown}generateTaskInsights(e){let t=[],i=e.reduce((e,t)=>(e[t.taskType]=(e[t.taskType]||0)+1,e),{}),r=e.length;if(0===r)return t;let n=Object.entries(i).sort(([,e],[,t])=>t-e)[0];if(n){let[e,i]=n,o=Math.round(i/r*100);t.push(`${o}% of detected activities were ${e.replace("-"," ")}`)}let o=Math.round(e.filter(e=>["deep-work","creative","learning","research"].includes(e.taskType)).length/r*100);o>=70?t.push("High productivity detected - great focus on meaningful work!"):o>=40?t.push("Moderate productivity - consider reducing distractions"):t.push("Low productivity detected - focus on high-value tasks");let a=this.analyzeAppSwitching(e);return a>10&&t.push(`High app switching detected (${a} switches) - consider batching similar tasks`),t}analyzeAppSwitching(e){let t=0,i="";for(let r of e.sort((e,t)=>e.timestamp.getTime()-t.timestamp.getTime()))i&&i!==r.appName&&t++,i=r.appName;return t}async detectProductivityPatterns(e){let t=[];for(let[i,r]of Object.entries(this.groupSessionsByTimePattern(e))){if(r.length<3)continue;let n=r.reduce((e,t)=>e+t.focusScore,0)/r.length,a=Math.min(100,r.length/e.length*100),[s,c]=i.split("|"),[l,d]=s.split("-").map(Number);t.push({id:(0,o.A)(),name:this.generatePatternName(l,d,c),description:this.generatePatternDescription(n,r.length),timePattern:{startHour:l,endHour:d,daysOfWeek:c.split(",").map(Number)},averageProductivity:n,confidence:a,recommendations:this.generatePatternRecommendations(n,l,d)})}return t.sort((e,t)=>t.confidence-e.confidence)}groupSessionsByTimePattern(e){let t={};return e.forEach(e=>{let i=e.startTime.getHours(),r=e.startTime.getDay(),n=2*Math.floor(i/2),o=`${n}-${n+2}`,a=`${o}|${0===r||6===r?"0,6":"1,2,3,4,5"}`;t[a]||(t[a]=[]),t[a].push(e)}),t}generatePatternName(e,t,i){let r=`${e}:00-${t}:00`;return`${"0,6"===i?"Weekends":"Weekdays"} ${r}`}generatePatternDescription(e,t){return`${e>=80?"high":e>=60?"moderate":"low"} productivity pattern with ${t} sessions`}generatePatternRecommendations(e,t,i){let r=[];return e>=80?(r.push("Excellent productivity window - schedule important tasks here"),r.push("Consider extending this time block if possible")):e>=60?(r.push("Good productivity window - optimize environment for better focus"),r.push("Minimize distractions during this time")):(r.push("Low productivity window - consider rescheduling demanding tasks"),r.push("Use this time for lighter activities or breaks")),t>=6&&t<=10?r.push("Morning energy - great for creative and analytical work"):t>=14&&t<=16?r.push("Post-lunch dip - consider light exercise or break"):t>=20&&r.push("Evening hours - wind down with lighter tasks"),r}async generateBreakRecommendation(e,t){if(!e)return null;let i=(Date.now()-e.startTime.getTime())/6e4,r=0,n="micro",a=5,s="";return(i>=120?(r=90,n="long",a=15,s="Extended focus session - time for a longer break"):i>=60?(r=70,n="short",a=10,s="Good focus session - take a short break to recharge"):i>=25&&(r=50,n="micro",a=5,s="Pomodoro break - quick refresh recommended"),this.analyzeRecentBreaks(t).timeSinceLastBreak>180&&(r=Math.min(100,r+30),s="Long period without breaks - rest is important"),r<40)?null:{id:(0,o.A)(),timestamp:new Date,reason:s,type:n,suggestedDuration:a,activities:this.getBreakActivities(n),urgency:r}}analyzeRecentBreaks(e){let t=Date.now(),i=e[e.length-1];return{timeSinceLastBreak:i?(t-i.endTime.getTime())/6e4:0,averageBreakInterval:60}}getBreakActivities(e){return({micro:["Look away from screen (20-20-20 rule)","Deep breathing exercises","Stretch your neck and shoulders","Drink water"],short:["Walk around the room","Light stretching","Grab a healthy snack","Step outside for fresh air","Chat with a colleague"],long:["Take a walk outside","Have a proper meal","Do some exercise","Meditate or relax","Call a friend or family member"]})[e]}}},47625:(e,t,i)=>{"use strict";i.d(t,{x:()=>s});var r=i(802),n=i(27831),o=i(631),a=i(94351);class s{constructor(e){this.config=e}async analyzeActivity(e){try{let t=await r.F.queryScreenpipe({startTime:e.start.toISOString(),endTime:e.end.toISOString(),contentType:"all",limit:1e3,includeFrames:!1});if(!t?.data)throw Error("No data received from Screenpipe");let i=this.processActivityData(t.data),n=this.calculateProductivityMetrics(i.focusSessions,i.contextSwitches,e.start);return{focusSessions:i.focusSessions,contextSwitches:i.contextSwitches,metrics:n}}catch(e){throw console.error("Error analyzing activity:",e),e}}processActivityData(e){let t=[],i=[],r=null,o=null;for(let s of e.sort((e,t)=>new Date(e.content.timestamp).getTime()-new Date(t.content.timestamp).getTime())){let e=new Date(s.content.timestamp),c="",l="";if("OCR"===s.type?(c=s.content.appName||"",l=s.content.windowName||""):"UI"===s.type&&(c=s.content.appName||"",l=s.content.windowName||""),c){if(o&&(o.app!==c||o.window!==l)){let s=(0,n.O)(e,o.timestamp);s>=this.config.contextSwitchWindow&&(i.push({id:(0,a.A)(),timestamp:e,fromApp:o.app,toApp:c,fromWindow:o.window,toWindow:l,duration:s,switchType:o.app!==c?"app":"window"}),r&&this.isDistractingSwitch(o.app,c)&&(this.finalizeSession(r,o.timestamp,t),r=null))}(!r||this.shouldStartNewSession(r,c,l,e))&&(r&&this.finalizeSession(r,e,t),r={id:(0,a.A)(),startTime:e,appName:c,windowName:l,taskCategory:this.categorizeTask(c,l),distractionCount:0,contextSwitches:0}),r&&(r.endTime=e,i.length>0&&r.startTime&&i[i.length-1].timestamp>=r.startTime&&(r.contextSwitches=(r.contextSwitches||0)+1)),o={app:c,window:l,timestamp:e}}}return r&&o&&this.finalizeSession(r,o.timestamp,t),{focusSessions:t,contextSwitches:i}}finalizeSession(e,t,i){if(!e.startTime||!e.appName)return;let r=(0,o.o)(t,e.startTime);if(r>=this.config.focusThreshold){let n=this.calculateFocusScore(r,e.contextSwitches||0,e.distractionCount||0);i.push({id:e.id,startTime:e.startTime,endTime:t,duration:r,appName:e.appName,windowName:e.windowName||"",taskCategory:e.taskCategory||"unknown",focusScore:n,distractionCount:e.distractionCount||0,contextSwitches:e.contextSwitches||0,productivity:this.getProductivityLevel(n)})}}calculateFocusScore(e,t,i){let r;return r=100-Math.min(5*t,30)-Math.min(10*i,40),e>=this.config.deepWorkMinimum&&(r+=10),Math.max(0,Math.min(100,r))}categorizeTask(e,t){let i=`${e} ${t}`.toLowerCase();for(let[e,t]of Object.entries({"deep-work":["vscode","intellij","xcode","sublime","vim","emacs","figma","sketch"],communication:["slack","teams","discord","zoom","meet","skype","mail","outlook"],research:["chrome","firefox","safari","edge","browser","wikipedia","stackoverflow"],creative:["photoshop","illustrator","premiere","after effects","blender","canva"],administrative:["excel","sheets","word","docs","powerpoint","slides","notion"],learning:["coursera","udemy","youtube","khan academy","duolingo","anki"],entertainment:["netflix","youtube","spotify","twitch","gaming","steam"],social:["facebook","twitter","instagram","linkedin","reddit","tiktok"],distraction:["news","shopping","amazon","ebay","social media"],unknown:[]}))if(t.some(e=>i.includes(e)))return e;return"unknown"}isDistractingSwitch(e,t){return["vscode","intellij","figma","notion","excel"].some(t=>e.toLowerCase().includes(t))&&["chrome","firefox","safari","social","entertainment"].some(e=>t.toLowerCase().includes(e))}shouldStartNewSession(e,t,i,r){return!e.startTime||e.appName!==t&&(0,o.o)(r,e.startTime)>=this.config.focusThreshold}getProductivityLevel(e){return e>=90?"very-high":e>=75?"high":e>=50?"medium":e>=25?"low":"very-low"}calculateProductivityMetrics(e,t,i){let r=e.reduce((e,t)=>e+t.duration,0),n=e.length>0?e.reduce((e,t)=>e+t.focusScore,0)/e.length:0,o=e.filter(e=>e.duration>=this.config.deepWorkMinimum).length,a=e.length>0?r/e.length:0,s=this.calculateHourlyProductivity(e),c=this.getMostProductiveHour(s),l=this.getLeastProductiveHour(s),d=this.calculateAppUsage(e),u=d.filter(e=>e.productivityScore>=70).sort((e,t)=>t.focusTime-e.focusTime).slice(0,5),m=d.filter(e=>e.productivityScore<50).sort((e,t)=>t.totalTime-e.totalTime).slice(0,5);return{date:i,totalFocusTime:r,totalDistractionTime:0,focusScore:n,contextSwitches:t.length,deepWorkSessions:o,averageSessionLength:a,mostProductiveHour:c,leastProductiveHour:l,topProductiveApps:u,topDistractingApps:m}}calculateHourlyProductivity(e){let t={};for(let e=0;e<24;e++)t[e]={totalTime:0,totalScore:0,count:0};e.forEach(e=>{let i=e.startTime.getHours();t[i].totalTime+=e.duration,t[i].totalScore+=e.focusScore,t[i].count+=1});let i={};for(let e=0;e<24;e++){let r=t[e];i[e]=r.count>0?r.totalScore/r.count:0}return i}getMostProductiveHour(e){return Object.entries(e).reduce((t,[i,r])=>r>e[t]?parseInt(i):t,0)}getLeastProductiveHour(e){return Object.entries(e).reduce((t,[i,r])=>r<e[t]?parseInt(i):t,0)}calculateAppUsage(e){let t={};return e.forEach(e=>{t[e.appName]||(t[e.appName]={totalTime:0,focusTime:0,sessionCount:0,totalScore:0});let i=t[e.appName];i.totalTime+=e.duration,i.sessionCount+=1,i.totalScore+=e.focusScore,e.focusScore>=70&&(i.focusTime+=e.duration)}),Object.entries(t).map(([e,t])=>({appName:e,totalTime:t.totalTime,focusTime:t.focusTime,distractionTime:t.totalTime-t.focusTime,sessionCount:t.sessionCount,averageSessionLength:t.totalTime/t.sessionCount,productivityScore:t.totalScore/t.sessionCount}))}}},75006:(e,t,i)=>{"use strict";i.d(t,{S:()=>a});var r=i(51707),n=i(21022),o=i(73741);class a{constructor(){this.patterns=[],this.energyLevels=[],this.model=null,this.initializeModel()}async initializeModel(){try{this.model=o.ilg({layers:[o.ZFI.dense({inputShape:[7],units:16,activation:"relu"}),o.ZFI.dropout({rate:.2}),o.ZFI.dense({units:8,activation:"relu"}),o.ZFI.dense({units:1,activation:"sigmoid"})]}),this.model.compile({optimizer:"adam",loss:"meanSquaredError",metrics:["mae"]}),console.log("Pattern recognition model initialized")}catch(e){console.error("Error initializing ML model:",e)}}async analyzeProductivityPatterns(e,t){if(e.length<10)return console.warn("Insufficient data for pattern analysis"),[];let i=this.groupSessionsByTimePatterns(e),r=[];for(let[e,t]of Object.entries(i)){if(t.length<3)continue;let i=await this.analyzeTimePattern(e,t);i&&r.push(i)}let n=this.analyzeWeeklyPatterns(e);r.push(...n);let o=this.analyzeTaskPatterns(e);return r.push(...o),r.sort((e,t)=>t.confidence-e.confidence)}groupSessionsByTimePatterns(e){let t={};return e.forEach(e=>{let i=(0,r.q)(e.startTime),o=(0,n.P)(e.startTime),a=2*Math.floor(i/2),s=`${a}-${a+2}_${0===o||6===o?"weekend":"weekday"}`;t[s]||(t[s]=[]),t[s].push(e)}),t}async analyzeTimePattern(e,t){let[i,r]=e.split("_"),[n,o]=i.split("-").map(Number),a=t.reduce((e,t)=>e+t.focusScore,0)/t.length,s=t.reduce((e,t)=>e+t.duration,0)/t.length,c=t.length,l=(Math.max(0,100-this.calculateVariance(t.map(e=>e.focusScore)))+Math.min(100,c/10*100))/2;if(l<30)return null;let d=this.generatePatternInsights(t,a,s),u=this.generatePatternRecommendations(a,n,o,r);return{id:`pattern_${e}`,name:this.generatePatternName(n,o,r),description:`${r} productivity pattern (${c} sessions)`,timePattern:{startHour:n,endHour:o,daysOfWeek:"weekend"===r?[0,6]:[1,2,3,4,5]},averageProductivity:a,confidence:l,recommendations:[...d,...u]}}analyzeWeeklyPatterns(e){let t={};e.forEach(e=>{let i=(0,n.P)(e.startTime);t[i]||(t[i]=[]),t[i].push(e)});let i=[],r=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"];return Object.entries(t).forEach(([t,n])=>{if(n.length<3)return;let o=parseInt(t),a=n.reduce((e,t)=>e+t.focusScore,0)/n.length,s=Math.min(100,n.length/e.length*700);s>=20&&i.push({id:`weekly_${t}`,name:`${r[o]} Pattern`,description:`Productivity pattern for ${r[o]}s`,timePattern:{startHour:0,endHour:24,daysOfWeek:[o]},averageProductivity:a,confidence:s,recommendations:this.generateDaySpecificRecommendations(o,a)})}),i}analyzeTaskPatterns(e){let t={};e.forEach(e=>{t[e.taskCategory]||(t[e.taskCategory]=[]),t[e.taskCategory].push(e)});let i=[];return Object.entries(t).forEach(([t,r])=>{if(r.length<5)return;let n=r.reduce((e,t)=>e+t.focusScore,0)/r.length,o=Math.min(100,r.length/e.length*500),a=this.analyzeHourlyPerformance(r),s=this.findBestPerformanceHours(a);o>=30&&s.length>0&&i.push({id:`task_${t}`,name:`${t.replace("-"," ")} Optimization`,description:`Best times for ${t.replace("-"," ")} tasks`,timePattern:{startHour:Math.min(...s),endHour:Math.max(...s)+1,daysOfWeek:[1,2,3,4,5]},averageProductivity:n,confidence:o,recommendations:this.generateTaskSpecificRecommendations(t,s)})}),i}async predictOptimalSchedule(e,t){let i=[];if(!this.model)return console.warn("ML model not available for predictions"),this.generateRuleBasedSchedule(e,t);try{let r=(0,n.P)(e),a=+(0===r||6===r);for(let n=8;n<18;n++)for(let s of t){let t=[n/24,r/7,a,this.encodeTaskType(s),this.getHistoricalPerformance(n,r,s),this.getEnergyLevel(n),this.getContextualFactors(e,n)],c=this.model.predict(o.KtR([t])),l=100*(await c.data())[0];c.dispose();let d=this.calculatePredictionConfidence(n,r,s);i.push({hour:n,taskType:s,predictedProductivity:l,confidence:d,reasoning:this.generatePredictionReasoning(n,s,l)})}return i.sort((e,t)=>t.predictedProductivity-e.predictedProductivity).slice(0,8)}catch(i){return console.error("Error in ML prediction:",i),this.generateRuleBasedSchedule(e,t)}}generateRuleBasedSchedule(e,t){let i=[],r=(0,n.P)(e),o=0===r||6===r,a={"deep-work":o?[10,11,14,15]:[9,10,14,15],creative:[10,11,16,17],communication:[11,12,14,16],administrative:[13,14,15,16],learning:[9,10,11,15],research:[9,10,14,15],entertainment:[18,19,20,21],social:[12,13,17,18],distraction:[16,17,18,19],unknown:[10,11,14,15]};return t.forEach(e=>{(a[e]||[10,11,14,15]).forEach(t=>{i.push({hour:t,taskType:e,predictedProductivity:this.getRuleBasedProductivity(t,e,o),confidence:70,reasoning:`Based on general productivity patterns for ${e.replace("-"," ")}`})})}),i.sort((e,t)=>t.predictedProductivity-e.predictedProductivity)}calculateVariance(e){let t=e.reduce((e,t)=>e+t,0)/e.length;return e.map(e=>Math.pow(e-t,2)).reduce((e,t)=>e+t,0)/e.length}generatePatternName(e,t,i){let r=`${e}:00-${t}:00`;return`${"weekend"===i?"Weekend":"Weekday"} ${r}`}generatePatternInsights(e,t,i){let r=[];return t>=80?r.push("High-performance time window - excellent for important tasks"):t>=60?r.push("Good productivity window - suitable for focused work"):r.push("Lower productivity period - consider lighter tasks"),i>=60?r.push("Long focus sessions typical - good for deep work"):i>=30?r.push("Moderate session length - good for regular tasks"):r.push("Short sessions typical - better for quick tasks"),r}generatePatternRecommendations(e,t,i,r){let n=[];return e>=80?(n.push("Schedule your most important work during this time"),n.push("Minimize interruptions and distractions")):e>=60?(n.push("Good time for regular focused work"),n.push("Consider optimizing environment for better focus")):(n.push("Use for lighter tasks or administrative work"),n.push("Consider taking breaks or doing physical activity")),t<=10?n.push("Morning energy - great for analytical tasks"):t>=14&&t<=16&&n.push("Post-lunch period - may need energy boost"),n}generateDaySpecificRecommendations(e,t){let i=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"][e],r=[`Optimize ${i} productivity`];return 1===e?r.push("Start week with planning and goal setting"):5===e?r.push("Wrap up weekly tasks and plan for next week"):(0===e||6===e)&&r.push("Focus on personal projects and learning"),r}generateTaskSpecificRecommendations(e,t){let i=[],r=t.map(e=>`${e}:00`).join(", ");switch(i.push(`Best performance for ${e.replace("-"," ")} at ${r}`),e){case"deep-work":i.push("Minimize distractions and notifications"),i.push("Ensure comfortable environment and good lighting");break;case"creative":i.push("Allow for inspiration and experimentation"),i.push("Consider background music or change of scenery");break;case"communication":i.push("Batch similar communication tasks together"),i.push("Set specific times for checking messages")}return i}analyzeHourlyPerformance(e){let t={};e.forEach(e=>{let i=(0,r.q)(e.startTime);t[i]||(t[i]={total:0,count:0}),t[i].total+=e.focusScore,t[i].count+=1});let i={};return Object.entries(t).forEach(([e,t])=>{i[parseInt(e)]=t.total/t.count}),i}findBestPerformanceHours(e){let t=Object.values(e).reduce((e,t)=>e+t,0)/Object.values(e).length;return Object.entries(e).filter(([,e])=>e>1.1*t).map(([e])=>parseInt(e))}encodeTaskType(e){return({"deep-work":.9,creative:.8,research:.7,learning:.6,administrative:.5,communication:.4,social:.3,entertainment:.2,distraction:.1,unknown:0})[e]||0}getHistoricalPerformance(e,t,i){return .7}getEnergyLevel(e){return e>=9&&e<=11?.9:e>=14&&e<=16?.8:e>=20||e<=6?.3:.6}getContextualFactors(e,t){return .5}calculatePredictionConfidence(e,t,i){return 75}generatePredictionReasoning(e,t,i){return i>=80?`Optimal time for ${t.replace("-"," ")} based on historical performance`:i>=60?`Good time for ${t.replace("-"," ")} with moderate productivity expected`:`Lower productivity expected for ${t.replace("-"," ")} at this time`}getRuleBasedProductivity(e,t,i){let r=70;return e>=9&&e<=11&&(r+=15),e>=14&&e<=16&&(r+=10),(e<=8||e>=18)&&(r-=20),"deep-work"===t&&e>=9&&e<=11&&(r+=10),"creative"===t&&e>=10&&e<=12&&(r+=10),"communication"===t&&e>=11&&e<=16&&(r+=5),i&&(r-=10),Math.max(0,Math.min(100,r))}}},78335:()=>{},96487:()=>{}};