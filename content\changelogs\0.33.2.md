Based on the provided commits, here is the changelog for the new Screenpipe update:

### **New Features:**
- **Added production configuration file:** Implemented a new production configuration file for improved deployment setup.
- **Added permissions guard:** Introduced a permissions guard in the sidecar to enhance security before starting the application.

### **Improvements:**
- **Reenabled event API:** Restored functionality of the event API for better interaction and integration.

### **Fixes:**
- **Fixed CLI issue:** Addressed a small fix in the command-line interface for improved usability.

#### **Full Changelog:** [d0f98..ae40f](https://github.com/mediar-ai/screenpipe/compare/d0f98..ae40f)

