openapi: 3.0.3
info:
  title: 'Screenpipe API'
  version: '1.0.0'
  description: |
    # Screenpipe API
    
    Screenpipe is the context layer for AGI - a bridge between context-free AI and context-aware super intelligence.
    
    ## Core Functionality
    
    - **Context Retrieval**: Search through screen recordings and audio transcriptions
    - **Computer Automation**: Control applications and automate desktop interactions
    - **Real-time Streams**: Stream OCR, UI elements, and audio transcriptions
    - **Device Management**: Control audio and video recording devices
    - **Pipes System**: Extend functionality with pluggable pipes
    
    ## Getting Started
    
    The most commonly used endpoints are:
    
    1. `/search` - Main endpoint for retrieving contextual data
    2. `/experimental/operator` - Desktop automation endpoints for controlling applications

paths:
  /search:
    get:
      x-order: 1
      x-typescript-method: 'queryScreenpipe'
      tags: ['Context Retrieval']
      summary: 'Search screen and audio content with various filters'
      description: |
        Query Screenpipe for content based on various filters.
        
        ## Use Cases
        
        - Search for specific text across all applications
        - Find content from a specific application or window
        - Get screenshots from a particular time period
        - Retrieve all visits to a specific website
        
        ## Examples
        
        ### Basic search for recent browser activity:
        ```js
        const githubActivity = await pipe.queryScreenpipe({
          browserUrl: "github.com",
          contentType: "ocr",
          limit: 20,
          includeFrames: true
        });
        ```
        
        ### Search for specific text with date filters:
        ```js
        const searchResults = await pipe.queryScreenpipe({
          q: "authentication",
          browserUrl: "auth0.com",
          appName: "Chrome",
          contentType: "ocr",
          startTime: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          endTime: new Date().toISOString(),
          limit: 50
        });
        ```
      operationId: server_search
      parameters:
      - name: q
        schema:
          nullable: true
          type: string
        in: query
        style: form
        examples:
          browserSearch:
            summary: 'Find recent GitHub activity'
            value: null
          specificTextSearch: 
            summary: 'Search for authentication-related content'
            value: "authentication"
      - name: limit
        schema:
          type: integer
        in: query
        style: form
      - name: offset
        schema:
          type: integer
        in: query
        style: form
      - name: content_type
        schema:
          $ref: '#/components/schemas/ContentType'
        in: query
        style: form
      - name: start_time
        schema:
          nullable: true
          type: string
          format: date-time
        in: query
        style: form
      - name: end_time
        schema:
          nullable: true
          type: string
          format: date-time
        in: query
        style: form
      - name: app_name
        schema:
          nullable: true
          type: string
        in: query
        style: form
      - name: window_name
        schema:
          nullable: true
          type: string
        in: query
        style: form
      - name: frame_name
        schema:
          nullable: true
          type: string
        in: query
        style: form
      - name: include_frames
        schema:
          type: boolean
        in: query
        style: form
      - name: min_length
        schema:
          nullable: true
          type: integer
        in: query
        style: form
      - name: max_length
        schema:
          nullable: true
          type: integer
        in: query
        style: form
      - name: speaker_ids
        schema:
          nullable: true
          type: array
          items:
            type: integer
        in: query
        style: form
      - name: focused
        schema:
          nullable: true
          type: boolean
        in: query
        style: form
      - name: browser_url
        schema:
          nullable: true
          type: string
        in: query
        style: form
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SearchResponse'

  /experimental/operator:
    post:
      x-order: 2
      x-typescript-method: 'findElements'
      tags: ['Computer Automation']
      summary: 'Find UI elements in applications for automation'
      description: |
        Find UI elements in applications by role to enable desktop automation.
        
        ## Use Cases
        
        - Find buttons, text fields, and other UI elements for automation
        - Build workflows that interact with desktop applications
        - Create AI-powered desktop automation agents
        
        ## Examples
        
        ### Find all buttons in Chrome:
        ```js
        const buttons = await pipe.operator
          .getByRole("button", {
            app: "Chrome",
            activateApp: true
          })
          .all(5);
        ```
        
        ### Click a button:
        ```js
        const button = await pipe.operator
          .getByRole("button", { app: "Chrome" })
          .first();
          
        await pipe.operator
          .getById(button.id, { app: "Chrome" })
          .click();
        ```
      operationId: server_find_elements_handler
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FindElementsRequest'
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FindElementsResponse'

  /experimental/operator/open-application:
    post:
      tags: ['Computer Automation']
      summary: 'Open an application'
      operationId: server_open_application_handler
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OpenApplicationRequest'
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OpenApplicationResponse'

  /experimental/operator/open-url:
    post:
      tags: ['Computer Automation']
      summary: 'Open a URL in a browser'
      operationId: server_open_url_handler
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OpenUrlRequest'
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OpenUrlResponse'

  /experimental/operator/click:
    post:
      tags: ['Computer Automation']
      summary: 'Click on a UI element'
      operationId: server_click_element_handler
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ClickElementRequest'
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ActionResponse'

  /experimental/operator/type:
    post:
      tags: ['Computer Automation']
      summary: 'Type text into a UI element'
      operationId: server_type_text_handler
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TypeTextRequest'
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ActionResponse'

  /experimental/operator/scroll:
    post:
      tags: ['Computer Automation']
      operationId: server_scroll_element_handler
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ScrollElementRequest'
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ScrollElementResponse'

  /audio/list:
    get:
      operationId: server_api_list_audio_devices
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ListDeviceResponse'
  /vision/list:
    get:
      operationId: server_api_list_monitors
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/MonitorInfo'
  /tags/{content_type}/{id}:
    post:
      operationId: server_add_tags
      parameters:
      - $ref: '#/components/parameters/ContentTypeParam'
      - $ref: '#/components/parameters/IdParam'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddTagsRequest'
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AddTagsResponse'
    delete:
      operationId: server_remove_tags
      parameters:
      - $ref: '#/components/parameters/ContentTypeParam'
      - $ref: '#/components/parameters/IdParam'
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RemoveTagsResponse'

  /experimental/operator/press-key:
    post:
      tags: ['Computer Automation']
      operationId: server_press_key_handler
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PressKeyRequest'
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PressKeyResponse'
                
  /experimental/operator/get_text:
    post:
      tags: ['Computer Automation']
      operationId: server_get_text_handler
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetTextRequest'
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetTextResponse'
                
  /experimental/operator/list-interactable-elements:
    post:
      tags: ['Computer Automation']
      operationId: server_list_interactable_elements_handler
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ListInteractableElementsRequest'
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ListInteractableElementsResponse'
                
  /experimental/operator/click-by-index:
    post:
      tags: ['Computer Automation']
      operationId: server_click_by_index_handler
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ClickByIndexRequest'
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ClickByIndexResponse'
                
  /experimental/operator/type-by-index:
    post:
      tags: ['Computer Automation']
      operationId: server_type_by_index_handler
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TypeByIndexRequest'
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TypeByIndexResponse'
                
  /experimental/operator/press-key-by-index:
    post:
      tags: ['Computer Automation']
      operationId: server_press_key_by_index_handler
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PressKeyByIndexRequest'
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PressKeyByIndexResponse'
                
  /pipes/info/{pipe_id}:
    get:
      operationId: server_get_pipe_info_handler
      parameters:
      - $ref: '#/components/parameters/PipeIdParam'
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
  /pipes/list:
    get:
      operationId: server_list_pipes_handler
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
  /pipes/download:
    post:
      operationId: server_download_pipe_handler
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DownloadPipeRequest'
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
  /pipes/download-private:
    post:
      operationId: server_download_pipe_private_handler
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DownloadPipePrivateRequest'
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
  /pipes/enable:
    post:
      operationId: server_run_pipe_handler
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RunPipeRequest'
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
  /pipes/disable:
    post:
      operationId: server_stop_pipe_handler
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RunPipeRequest'
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
  /pipes/update:
    post:
      operationId: server_update_pipe_config_handler
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdatePipeConfigRequest'
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
  /pipes/update-version:
    post:
      operationId: server_update_pipe_version_handler
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdatePipeVersionRequest'
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
  /pipes/delete:
    post:
      operationId: server_delete_pipe_handler
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeletePipeRequest'
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
  /pipes/purge:
    post:
      operationId: server_purge_pipe_handler
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PurgePipeRequest'
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
  /frames/{frame_id}:
    get:
      operationId: server_get_frame_data
      parameters:
      - $ref: '#/components/parameters/FrameIdParam'
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema: {}
  /health:
    get:
      operationId: server_health_check
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthCheckResponse'
  /raw_sql:
    post:
      operationId: server_execute_raw_sql
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RawSqlQuery'
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
  /add:
    post:
      operationId: server_add_to_database
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddContentRequest'
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AddContentResponse'

  # Speaker management endpoints
  /speakers/unnamed:
    get:
      tags: ['Speaker Management']
      operationId: server_get_unnamed_speakers_handler
      parameters:
      - name: limit
        schema:
          type: integer
        in: query
        style: form
      - name: offset
        schema:
          type: integer
        in: query
        style: form
      - name: speaker_ids
        schema:
          nullable: true
          type: array
          items:
            type: integer
        in: query
        style: form
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Speaker'
  /speakers/update:
    post:
      tags: ['Speaker Management']
      operationId: server_update_speaker_handler
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateSpeakerRequest'
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Speaker'
  /speakers/search:
    get:
      tags: ['Speaker Management']
      operationId: server_search_speakers_handler
      parameters:
      - name: name
        schema:
          nullable: true
          type: string
        in: query
        style: form
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Speaker'
  /speakers/delete:
    post:
      tags: ['Speaker Management']
      operationId: server_delete_speaker_handler
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeleteSpeakerRequest'
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
  /speakers/hallucination:
    post:
      tags: ['Speaker Management']
      operationId: server_mark_as_hallucination_handler
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MarkAsHallucinationRequest'
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
  /speakers/merge:
    post:
      tags: ['Speaker Management']
      operationId: server_merge_speakers_handler
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MergeSpeakersRequest'
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
  /speakers/similar:
    get:
      tags: ['Speaker Management']
      operationId: server_get_similar_speakers_handler
      parameters:
      - name: speaker_id
        schema:
          type: integer
        in: query
        style: form
      - name: limit
        schema:
          type: integer
        in: query
        style: form
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Speaker'

  # Experimental features
  /experimental/frames/merge:
    post:
      operationId: server_merge_frames_handler
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MergeVideosRequest'
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MergeVideosResponse'
  /experimental/validate/media:
    get:
      operationId: server_validate_media_handler
      parameters:
      - name: file_path
        schema:
          type: string
        in: query
        style: form
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                
  /experimental/input_control:
    post:
      tags: ['Computer Automation']
      operationId: server_input_control_handler
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InputControlRequest'
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InputControlResponse'

  # Audio control endpoints
  /audio/start:
    post:
      tags: ['Audio Control']
      operationId: server_start_audio
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema: {}
  /audio/stop:
    post:
      tags: ['Audio Control']
      operationId: server_stop_audio
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema: {}
  /audio/device/start:
    post:
      tags: ['Audio Control']
      operationId: server_start_audio_device
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AudioDeviceControlRequest'
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AudioDeviceControlResponse'
  /audio/device/stop:
    post:
      tags: ['Audio Control']
      operationId: server_stop_audio_device
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AudioDeviceControlRequest'
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AudioDeviceControlResponse'

  /pipes/build-status/{pipe_id}:
    get:
      operationId: server_get_pipe_build_status
      parameters:
      - $ref: '#/components/parameters/PipeIdParam'
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
  /search/keyword:
    get:
      tags: ['Context Retrieval']
      summary: 'Keyword-based search'
      operationId: server_keyword_search_handler
      parameters:
      - name: query
        schema:
          type: string
        in: query
        style: form
      - name: limit
        schema:
          type: integer
        in: query
        style: form
      - name: offset
        schema:
          type: integer
        in: query
        style: form
      - name: start_time
        schema:
          nullable: true
          type: string
          format: date-time
        in: query
        style: form
      - name: end_time
        schema:
          nullable: true
          type: string
          format: date-time
        in: query
        style: form
      - name: fuzzy_match
        schema:
          type: boolean
        in: query
        style: form
      - name: order
        schema:
          $ref: '#/components/schemas/Order'
        in: query
        style: form
      - name: app_names
        schema:
          nullable: true
          type: array
          items:
            type: string
        in: query
        style: form
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SearchMatch'
  /v1/embeddings:
    post:
      x-typescript-method: 'deduplicateText'
      operationId: embedding_embedding_endpoint_create_embeddings
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EmbeddingRequest'
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EmbeddingResponse'

components:
  schemas:
    ActionResponse:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
      required:
      - success
      - message
    AddContentData:
      type: object
      properties:
        content_type:
          type: string
        data:
          $ref: '#/components/schemas/ContentData'
      required:
      - content_type
      - data
    AddContentRequest:
      type: object
      properties:
        device_name:
          type: string
        content:
          $ref: '#/components/schemas/AddContentData'
      required:
      - device_name
      - content
    AddContentResponse:
      type: object
      properties:
        success:
          type: boolean
        message:
          nullable: true
          type: string
      required:
      - success
      - message
    AddTagsRequest:
      type: object
      properties:
        tags:
          type: array
          items:
            type: string
      required:
      - tags
    AddTagsResponse:
      type: object
      properties:
        success:
          type: boolean
      required:
      - success
    AudioChunk:
      type: object
      properties:
        id:
          type: integer
        file_path:
          type: string
        timestamp:
          type: string
          format: date-time
      required:
      - id
      - file_path
      - timestamp
    AudioChunksResponse:
      type: object
      properties:
        audio_chunk_id:
          type: integer
        start_time:
          nullable: true
          type: number
        end_time:
          nullable: true
          type: number
        file_path:
          type: string
        timestamp:
          type: string
          format: date-time
      required:
      - audio_chunk_id
      - start_time
      - end_time
      - file_path
      - timestamp
    AudioContent:
      type: object
      properties:
        chunk_id:
          type: integer
        transcription:
          type: string
        timestamp:
          type: string
          format: date-time
        file_path:
          type: string
        offset_index:
          type: integer
        tags:
          type: array
          items:
            type: string
        device_name:
          type: string
        device_type:
          $ref: '#/components/schemas/DeviceType'
        speaker:
          $ref: '#/components/schemas/Speaker'
        start_time:
          nullable: true
          type: number
        end_time:
          nullable: true
          type: number
      required:
      - chunk_id
      - transcription
      - timestamp
      - file_path
      - offset_index
      - tags
      - device_name
      - device_type
      - speaker
      - start_time
      - end_time
    AudioDevice:
      type: object
      properties:
        name:
          type: string
        device_type:
          $ref: '#/components/schemas/DeviceType'
      required:
      - name
      - device_type
    AudioDeviceControlRequest:
      type: object
      properties:
        device_name:
          type: string
      required:
      - device_name
    AudioDeviceControlResponse:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
      required:
      - success
      - message
    AudioEntry:
      type: object
      properties:
        transcription:
          type: string
        device_name:
          type: string
        is_input:
          type: boolean
        audio_file_path:
          type: string
        duration_secs:
          type: number
      required:
      - transcription
      - device_name
      - is_input
      - audio_file_path
      - duration_secs
    AudioResult:
      type: object
      properties:
        audio_chunk_id:
          type: integer
        transcription:
          type: string
        timestamp:
          type: string
          format: date-time
        file_path:
          type: string
        offset_index:
          type: integer
        transcription_engine:
          type: string
        tags:
          type: array
          items:
            type: string
        device_name:
          type: string
        device_type:
          $ref: '#/components/schemas/DeviceType'
        speaker:
          $ref: '#/components/schemas/Speaker'
        start_time:
          nullable: true
          type: number
        end_time:
          nullable: true
          type: number
      required:
      - audio_chunk_id
      - transcription
      - timestamp
      - file_path
      - offset_index
      - transcription_engine
      - tags
      - device_name
      - device_type
      - speaker
      - start_time
      - end_time
    AudioTranscription:
      type: object
      properties:
        transcription:
          type: string
        transcription_engine:
          type: string
      required:
      - transcription
      - transcription_engine
    ClickByIndexRequest:
      type: object
      properties:
        element_index:
          type: integer
      required:
      - element_index
    ClickByIndexResponse:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
      required:
      - success
      - message
    ClickElementRequest:
      type: object
      properties:
        selector:
          $ref: '#/components/schemas/ElementSelector'
      required:
      - selector
    ContentData:
      oneOf:
      - type: array
        items:
          type: object
          properties:
            file_path:
              type: string
            timestamp:
              nullable: true
              type: string
              format: date-time
            app_name:
              nullable: true
              type: string
            window_name:
              nullable: true
              type: string
            ocr_results:
              nullable: true
              type: array
              items:
                $ref: '#/components/schemas/OCRResult'
            tags:
              nullable: true
              type: array
              items:
                type: string
          required:
          - file_path
          - timestamp
          - app_name
          - window_name
          - ocr_results
          - tags
      - type: object
        properties:
          transcription:
            type: string
          transcription_engine:
            type: string
        required:
        - transcription
        - transcription_engine
    ContentItem:
      oneOf:
      - type: object
        properties:
          type:
            type: string
            enum:
            - OCR
          content:
            type: object
            properties:
              frame_id:
                type: integer
              text:
                type: string
              timestamp:
                type: string
                format: date-time
              file_path:
                type: string
              offset_index:
                type: integer
              app_name:
                type: string
              window_name:
                type: string
              tags:
                type: array
                items:
                  type: string
              frame:
                nullable: true
                type: string
              frame_name:
                nullable: true
                type: string
              browser_url:
                nullable: true
                type: string
              focused:
                nullable: true
                type: boolean
            required:
            - frame_id
            - text
            - timestamp
            - file_path
            - offset_index
            - app_name
            - window_name
            - tags
            - frame
            - frame_name
            - browser_url
            - focused
        required:
        - type
        - content
      - type: object
        properties:
          type:
            type: string
            enum:
            - Audio
          content:
            type: object
            properties:
              chunk_id:
                type: integer
              transcription:
                type: string
              timestamp:
                type: string
                format: date-time
              file_path:
                type: string
              offset_index:
                type: integer
              tags:
                type: array
                items:
                  type: string
              device_name:
                type: string
              device_type:
                $ref: '#/components/schemas/DeviceType'
              speaker:
                $ref: '#/components/schemas/Speaker'
              start_time:
                nullable: true
                type: number
              end_time:
                nullable: true
                type: number
            required:
            - chunk_id
            - transcription
            - timestamp
            - file_path
            - offset_index
            - tags
            - device_name
            - device_type
            - speaker
            - start_time
            - end_time
        required:
        - type
        - content
      - type: object
        properties:
          type:
            type: string
            enum:
            - UI
          content:
            type: object
            properties:
              id:
                type: integer
              text:
                type: string
              timestamp:
                type: string
                format: date-time
              app_name:
                type: string
              window_name:
                type: string
              initial_traversal_at:
                nullable: true
                type: string
                format: date-time
              file_path:
                type: string
              offset_index:
                type: integer
              frame_name:
                nullable: true
                type: string
              browser_url:
                nullable: true
                type: string
            required:
            - id
            - text
            - timestamp
            - app_name
            - window_name
            - initial_traversal_at
            - file_path
            - offset_index
            - frame_name
            - browser_url
        required:
        - type
        - content
    ContentSource:
      type: string
      enum:
      - Screen
      - Audio
    ContentType:
      type: string
      description: 'Type of content to search for. "all" returns all content types.'
      enum:
      - all
      - ocr
      - audio
      - ui
      - audio+ui
      - ocr+ui
      - audio+ocr
    CustomOcrConfig:
      type: object
      properties:
        api_url:
          type: string
        api_key:
          type: string
        timeout_ms:
          type: integer
      required:
      - api_url
      - api_key
      - timeout_ms
    DeletePipeRequest:
      type: object
      properties:
        pipe_id:
          type: string
      required:
      - pipe_id
    DeleteSpeakerRequest:
      type: object
      properties:
        id:
          type: integer
      required:
      - id
    DeviceControl:
      type: object
      properties:
        is_running:
          type: boolean
        is_paused:
          type: boolean
      required:
      - is_running
      - is_paused
    DeviceType:
      type: string
      enum:
      - Input
      - Output
    DownloadPipePrivateRequest:
      type: object
      properties:
        url:
          type: string
        pipe_name:
          type: string
        pipe_id:
          type: string
      required:
      - url
      - pipe_name
      - pipe_id
    DownloadPipeRequest:
      type: object
      properties:
        url:
          type: string
      required:
      - url
    ElementCacheInfo:
      type: object
      properties:
        cache_id:
          type: string
        timestamp:
          type: string
        expires_at:
          type: string
        element_count:
          type: integer
        ttl_seconds:
          type: integer
      required:
      - cache_id
      - timestamp
      - expires_at
      - element_count
      - ttl_seconds
    ElementInfo:
      type: object
      properties:
        id:
          type: string
        title:
          type: string
        role:
          type: string
        enabled:
          type: boolean
        attributes:
          nullable: true
          type: object
          additionalProperties: true
      required:
      - id
      - title
      - role
      - enabled
    ElementSelector:
      type: object
      properties:
        id:
          type: string
        app:
          nullable: true
          type: string
        activateApp:
          nullable: true
          type: boolean
      required:
      - id
    EmbeddingRequest:
      type: object
      properties:
        input:
          type: array
          items:
            type: string
      required:
      - input
    EmbeddingResponse:
      type: object
      properties:
        data:
          type: array
          items:
            type: object
            properties:
              embedding:
                type: array
                items:
                  type: number
              index:
                type: integer
              object:
                type: string
            required:
            - embedding
            - index
            - object
        model:
          type: string
        object:
          type: string
        usage:
          type: object
          properties:
            prompt_tokens:
              type: integer
            total_tokens:
              type: integer
          required:
          - prompt_tokens
          - total_tokens
      required:
      - data
      - model
      - object
      - usage
    FindElementsRequest:
      type: object
      properties:
        role:
          type: string
        app:
          nullable: true
          type: string
        strict:
          nullable: true
          type: boolean
        activateApp:
          nullable: true
          type: boolean
      required:
      - role
    FindElementsResponse:
      type: object
      properties:
        elements:
          type: array
          items:
            $ref: '#/components/schemas/ElementInfo'
        cache:
          $ref: '#/components/schemas/ElementCacheInfo'
      required:
      - elements
      - cache
    GetTextRequest:
      type: object
      properties:
        selector:
          $ref: '#/components/schemas/ElementSelector'
      required:
      - selector
    GetTextResponse:
      type: object
      properties:
        text:
          type: string
      required:
      - text
    HealthCheckResponse:
      type: object
      properties:
        status:
          type: string
      required:
      - status
    InputControlRequest:
      type: object
      properties:
        type:
          type: string
        key:
          nullable: true
          type: string
        modifiers:
          nullable: true
          type: array
          items:
            type: string
        position:
          nullable: true
          $ref: '#/components/schemas/Position'
        text:
          nullable: true
          type: string
      required:
      - type
    InputControlResponse:
      type: object
      properties:
        status:
          type: string
      required:
      - status
    ListDeviceResponse:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
        device_type:
          $ref: '#/components/schemas/DeviceType'
        device_control:
          $ref: '#/components/schemas/DeviceControl'
      required:
      - id
      - name
      - device_type
      - device_control
    ListInteractableElementsRequest:
      type: object
      properties:
        app:
          nullable: true
          type: string
    ListInteractableElementsResponse:
      type: object
      properties:
        elements:
          type: array
          items:
            type: object
            properties:
              index:
                type: integer
              role:
                type: string
              title:
                type: string
            required:
            - index
            - role
            - title
      required:
      - elements
    MarkAsHallucinationRequest:
      type: object
      properties:
        id:
          type: integer
      required:
      - id
    MergeSpeakersRequest:
      type: object
      properties:
        source_id:
          type: integer
        target_id:
          type: integer
      required:
      - source_id
      - target_id
    MergeVideosRequest:
      type: object
      properties:
        frame_ids:
          type: array
          items:
            type: integer
        output_path:
          nullable: true
          type: string
      required:
      - frame_ids
    MergeVideosResponse:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
        output_path:
          type: string
      required:
      - success
      - message
      - output_path
    MonitorInfo:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
        width:
          type: integer
        height:
          type: integer
        recording:
          type: boolean
      required:
      - id
      - name
      - width
      - height
      - recording
    OCRResult:
      type: object
      properties:
        text:
          type: string
        confidence:
          type: number
      required:
      - text
      - confidence
    OCRSearchMatch:
      type: object
      properties:
        frame_id:
          type: integer
        text:
          type: string
        timestamp:
          type: string
          format: date-time
        file_path:
          type: string
        offset_index:
          type: integer
        app_name:
          type: string
        window_name:
          type: string
        tags:
          type: array
          items:
            type: string
        frame:
          nullable: true
          type: string
        frame_name:
          nullable: true
          type: string
        browser_url:
          nullable: true
          type: string
        focused:
          nullable: true
          type: boolean
      required:
      - frame_id
      - text
      - timestamp
      - file_path
      - offset_index
      - app_name
      - window_name
      - tags
      - frame
      - frame_name
      - browser_url
      - focused
    OpenApplicationRequest:
      type: object
      properties:
        appName:
          type: string
      required:
      - appName
    OpenApplicationResponse:
      type: object
      properties:
        success:
          type: boolean
      required:
      - success
    OpenUrlRequest:
      type: object
      properties:
        url:
          type: string
        browser:
          nullable: true
          type: string
      required:
      - url
    OpenUrlResponse:
      type: object
      properties:
        success:
          type: boolean
      required:
      - success
    Order:
      type: string
      enum:
      - timestamp_asc
      - timestamp_desc
      - relevance
    Position:
      type: object
      properties:
        x:
          type: integer
        y:
          type: integer
      required:
      - x
      - y
    PressKeyByIndexRequest:
      type: object
      properties:
        element_index:
          type: integer
        key:
          type: string
        modifiers:
          nullable: true
          type: array
          items:
            type: string
      required:
      - element_index
      - key
    PressKeyByIndexResponse:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
      required:
      - success
      - message
    PressKeyRequest:
      type: object
      properties:
        selector:
          $ref: '#/components/schemas/ElementSelector'
        key:
          type: string
        modifiers:
          nullable: true
          type: array
          items:
            type: string
      required:
      - selector
      - key
    PressKeyResponse:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
      required:
      - success
      - message
    PurgePipeRequest:
      type: object
      properties:
        pipe_id:
          type: string
      required:
      - pipe_id
    RawSqlQuery:
      type: object
      properties:
        query:
          type: string
      required:
      - query
    RemoveTagsRequest:
      type: object
      properties:
        tags:
          type: array
          items:
            type: string
      required:
      - tags
    RemoveTagsResponse:
      type: object
      properties:
        success:
          type: boolean
      required:
      - success
    RunPipeRequest:
      type: object
      properties:
        pipe_id:
          type: string
      required:
      - pipe_id
    ScrollElementRequest:
      type: object
      properties:
        selector:
          $ref: '#/components/schemas/ElementSelector'
        direction:
          type: string
        amount:
          type: integer
      required:
      - selector
      - direction
      - amount
    ScrollElementResponse:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
      required:
      - success
      - message
    SearchMatch:
      type: object
      properties:
        id:
          type: integer
        timestamp:
          type: string
          format: date-time
        snippet:
          type: string
        app_name:
          nullable: true
          type: string
        score:
          type: number
      required:
      - id
      - timestamp
      - snippet
      - app_name
      - score
    SearchRequest:
      type: object
      properties:
        q:
          nullable: true
          type: string
        limit:
          type: integer
        offset:
          type: integer
        content_type:
          $ref: '#/components/schemas/ContentType'
        start_time:
          nullable: true
          type: string
          format: date-time
        end_time:
          nullable: true
          type: string
          format: date-time
        app_name:
          nullable: true
          type: string
        window_name:
          nullable: true
          type: string
        frame_name:
          nullable: true
          type: string
        include_frames:
          type: boolean
        min_length:
          nullable: true
          type: integer
        max_length:
          nullable: true
          type: integer
        speaker_ids:
          nullable: true
          type: array
          items:
            type: integer
        focused:
          nullable: true
          type: boolean
      required:
      - limit
      - offset
      - content_type
      - include_frames
    SearchResponse:
      type: object
      properties:
        results:
          type: array
          items:
            $ref: '#/components/schemas/ContentItem'
        total:
          type: integer
      required:
      - results
      - total
    Speaker:
      type: object
      properties:
        id:
          type: integer
        name:
          nullable: true
          type: string
      required:
      - id
      - name
    TypeByIndexRequest:
      type: object
      properties:
        element_index:
          type: integer
        text:
          type: string
      required:
      - element_index
      - text
    TypeByIndexResponse:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
      required:
      - success
      - message
    TypeTextRequest:
      type: object
      properties:
        selector:
          $ref: '#/components/schemas/ElementSelector'
        text:
          type: string
      required:
      - selector
      - text
    UIResult:
      type: object
      properties:
        id:
          type: integer
        text:
          type: string
        timestamp:
          type: string
          format: date-time
        app_name:
          type: string
        window_name:
          type: string
        initial_traversal_at:
          nullable: true
          type: string
          format: date-time
        file_path:
          type: string
        offset_index:
          type: integer
        frame_name:
          nullable: true
          type: string
        browser_url:
          nullable: true
          type: string
      required:
      - id
      - text
      - timestamp
      - app_name
      - window_name
      - initial_traversal_at
      - file_path
      - offset_index
      - frame_name
      - browser_url
    UpdatePipeConfigRequest:
      type: object
      properties:
        pipe_id:
          type: string
        config:
          type: object
      required:
      - pipe_id
      - config
    UpdatePipeVersionRequest:
      type: object
      properties:
        pipe_id:
          type: string
        version:
          type: string
      required:
      - pipe_id
      - version
    UpdateSpeakerRequest:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
      required:
      - id
      - name

  parameters:
    ContentTypeParam:
      name: content_type
      description: Type of content
      schema:
        type: string
        enum:
        - ocr
        - audio
        - ui
      in: path
      required: true
    FrameIdParam:
      name: frame_id
      description: ID of the frame
      schema:
        type: integer
      in: path
      required: true
    IdParam:
      name: id
      description: ID of the content item
      schema:
        type: integer
      in: path
      required: true
    PipeIdParam:
      name: pipe_id
      description: ID of the pipe
      schema:
        type: string
      in: path
      required: true

