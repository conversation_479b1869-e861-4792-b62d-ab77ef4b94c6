name: E2E Tests

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  windows-e2e-test:
    name: Windows E2E Tests
    runs-on: windows-latest
    steps:
    - uses: actions/checkout@v4

    - name: Setup Bun
      uses: oven-sh/setup-bun@v2
      with:
        bun-version: 1.1.43

    - name: Setup Node
      uses: actions/setup-node@v4
      with:
        node-version: 22

    - name: Install Chocolatey
      run: |
        [Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12
        Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))

    - name: Install dependencies
      shell: pwsh
      run: |
        choco install ffmpeg openssl
        echo "C:\ProgramData\chocolatey\bin" | Out-File -FilePath $env:GITHUB_PATH -Encoding utf8 -Append

    - name: Install Scream on Windows
      shell: powershell
      run: |
        Invoke-WebRequest https://github.com/duncanthrax/scream/releases/download/4.0/Scream4.0.zip -OutFile Scream4.0.zip
        Expand-Archive -Path Scream4.0.zip -DestinationPath Scream
        openssl req -batch -verbose -x509 -newkey rsa -keyout ScreamCertificate.pvk -out ScreamCertificate.cer -nodes -extensions v3_req
        openssl pkcs12 -export -nodes -in ScreamCertificate.cer -inkey ScreamCertificate.pvk -out ScreamCertificate.pfx -passout pass:

    - name: Set up MSVC
      uses: ilammy/msvc-dev-cmd@v1

    - name: Sign and Install Scream Driver on Windows
      shell: powershell
      run: |
        signtool sign /v /fd SHA256 /f ScreamCertificate.pfx Scream\Install\driver\x64\Scream.cat
        Import-Certificate -FilePath ScreamCertificate.cer -CertStoreLocation Cert:\LocalMachine\root
        Import-Certificate -FilePath ScreamCertificate.cer -CertStoreLocation Cert:\LocalMachine\TrustedPublisher
        Scream\Install\helpers\devcon-x64.exe install Scream\Install\driver\x64\Scream.inf *Scream
      timeout-minutes: 5

    - name: Start Windows Audio Service
      shell: powershell
      run: net start audiosrv

    - name: Install Rust
      run: |
        Invoke-WebRequest https://static.rust-lang.org/rustup/dist/x86_64-pc-windows-gnu/rustup-init.exe -OutFile rustup-init.exe
        .\rustup-init.exe -y

    - name: Set up Rust
      uses: actions-rust-lang/setup-rust-toolchain@v1
      with:
        toolchain: stable
        override: true
        cache: true
        target: x86_64-pc-windows-msvc
        rustflags: ""

    - name: Install vcpkg
      uses: lukka/run-vcpkg@v11
      with:
        vcpkgGitCommitId: "7adc2e4d49e8d0efc07a369079faa6bc3dbb90f3"

    - name: Install 7zip
      shell: powershell
      run: |
        $7zipUrl = "https://7-zip.org/a/7z2301-x64.exe"
        $7zipInstaller = "7z-installer.exe"
        Invoke-WebRequest -Uri $7zipUrl -OutFile $7zipInstaller
        Start-Process -FilePath .\$7zipInstaller -Args "/S" -Wait
        Remove-Item $7zipInstaller
        echo "C:\Program Files\7-Zip" | Out-File -FilePath $env:GITHUB_PATH -Encoding utf8 -Append
        & "C:\Program Files\7-Zip\7z.exe" i

    - name: Install LLVM and Clang
      uses: KyleMayes/install-llvm-action@v2
      with:
        version: "10.0"
        directory: "C:\\Program Files\\LLVM"

    - name: Install pip
      uses: actions/setup-python@v5
      with:
        python-version: "3.13"

    - name: Copy library for MKL
      shell: pwsh
      run: |
        $mkl_dir = "screenpipe-app-tauri/src-tauri/mkl"
        New-Item -ItemType Directory -Force -Path $mkl_dir | Out-Null
        python -m pip install --upgrade pip
        $temp_dir = "temp_omp"
        New-Item -ItemType Directory -Force -Path $temp_dir | Out-Null
        Write-Host "Installing Intel OpenMP..."
        python -m pip install intel-openmp --target $temp_dir
        Write-Host "Copying DLL files..."
        Get-ChildItem -Path $temp_dir -Recurse -Filter "*.dll" | ForEach-Object {
          Write-Host "Copying $_"
          Copy-Item $_.FullName -Destination $mkl_dir -Force
        }
        $dll_count = (Get-ChildItem -Path $mkl_dir -Filter "*.dll").Count
        Write-Host "Found $dll_count DLLs in target directory"
        if ($dll_count -eq 0) {
          throw "No DLLs found in target directory!"
        }
        Remove-Item -Path $temp_dir -Recurse -Force

    - name: Copy library for vcredist
      shell: powershell
      run: |
        $vcredist_dir = "screenpipe-app-tauri/src-tauri/vcredist"
        New-Item -ItemType Directory -Force -Path $vcredist_dir | Out-Null
        Set-ExecutionPolicy Bypass -Scope Process -Force
        [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
        iex ((New-Object System.Net.WebClient).DownloadString('https://vcredist.com/install.ps1'))
        Write-Host "Copying DLL files..."
        Copy-Item C:\Windows\System32\vcruntime140.dll -Destination $vcredist_dir -Force
        $dll_count = (Get-ChildItem -Path $vcredist_dir -Filter "*.dll").Count
        Write-Host "Found $dll_count DLLs in target directory"
        if ($dll_count -eq 0) {
          throw "No DLLs found in target directory!"
        }

    - name: Build CLI on Windows
      shell: pwsh
      env:
        CARGO_PROFILE_RELEASE_STRIP: symbols
        CARGO_PROFILE_RELEASE_PANIC: abort
        CARGO_PROFILE_RELEASE_INCREMENTAL: false
      run: cargo build --release -p screenpipe-server --target x86_64-pc-windows-msvc

    - name: Install frontend dependencies
      working-directory: ./screenpipe-app-tauri
      run: bun install

    - name: Run pre_build.js
      run: |
        bun ./scripts/pre_build.js
      working-directory: ./screenpipe-app-tauri

    - name: Build
      uses: tauri-apps/tauri-action@v0.5.17
      env:
        CI: true
        CARGO_PROFILE_RELEASE_LTO: thin
        CARGO_PROFILE_RELEASE_OPT_LEVEL: 2
        CARGO_PROFILE_RELEASE_CODEGEN_UNITS: 16
        CARGO_INCREMENTAL: true
        CARGO_PROFILE_RELEASE_STRIP: symbols
        CARGO_PROFILE_RELEASE_PANIC: abort
        CARGO_PROFILE_RELEASE_INCREMENTAL: false
        RUSTFLAGS: "-C target-feature=+crt-static -C link-arg=/LTCG"
        VCPKG_STATIC_LINKAGE: "true"
        KNF_STATIC_CRT: "1"
      with:
        args: "--target x86_64-pc-windows-msvc --no-bundle"
        projectPath: "./screenpipe-app-tauri"
        tauriScript: bunx tauri -v

    - name: Install tauri-driver
      shell: pwsh
      run: |
        if (-not (Test-Path "$env:USERPROFILE\.cargo\bin\tauri-driver.exe")) {
          cargo install tauri-driver
        }

    - name: Remove previous Edge WebDriver
      shell: pwsh
      run: |
        $folderPath = "$($env:SystemDrive)\SeleniumWebDrivers\EdgeDriver"

        if (Test-Path $folderPath) {
          Remove-Item -Path $folderPath -Recurse -Force
        }

    - name: Download matching Edge WebDriver
      shell: pwsh
      run: |
        cd ./screenpipe-app-tauri
        $edgeVersion = (bun tauri info  | Select-String 'WebView2').ToString().Split(':')[1].Trim()
        Write-Output "Detected Edge version: $edgeVersion"
        cd ..
        $driverUrl = "https://msedgedriver.azureedge.net/$edgeVersion/edgedriver_win64.zip"
        Write-Output "Downloading driver from: $driverUrl"

        Invoke-WebRequest -Uri $driverUrl -OutFile edgedriver.zip
        Expand-Archive edgedriver.zip -DestinationPath edgedriver
        $edgedriverPath = Join-Path $PWD 'edgedriver'
        echo $edgedriverPath | Out-File -FilePath $env:GITHUB_PATH -Encoding utf8 -Append

    - name: Run E2E Tests
      shell: pwsh
      working-directory: ./screenpipe-app-tauri
      run: |
        bun run test:e2e *> e2e_output.log

    - name: Upload E2E Test Artifacts
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: windows-e2e-test-artifacts
        path: |
          ./screenpipe-app-tauri/e2e/videos/*
          ./screenpipe-app-tauri/e2e_output.log

    - name: Upload Screenpipe data
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: windows-screenpipe-data
        include-hidden-files: true
        path: |
          ~/.screenpipe/*
  linux-e2e-test:
    name: Linux E2E Tests
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Setup Bun
      uses: oven-sh/setup-bun@v2
      with:
        bun-version: 1.1.43

    - name: Setup Node
      uses: actions/setup-node@v4
      with:
        node-version: 22
    - name: Install Linux dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y \
            g++ \
            ffmpeg \
            tesseract-ocr \
            cmake \
            clang \
            libavformat-dev \
            libavfilter-dev \
            libavdevice-dev \
            libssl-dev \
            libsoup-3.0-0 \
            libtesseract-dev \
            libxdo-dev \
            libsdl2-dev \
            libclang-dev \
            libxtst-dev \
            libx11-dev \
            libxext-dev \
            libxrandr-dev \
            libxinerama-dev \
            libxcursor-dev \
            libxi-dev \
            libgl1-mesa-dev \
            libasound2-dev \
            libpulse-dev \
            curl \
            desktop-file-utils \
            xdg-utils \
            dbus-x11 \
            xvfb \
            x11-xserver-utils \
            pkg-config \
            libsqlite3-dev \
            libbz2-dev \
            zlib1g-dev \
            libonig-dev \
            libayatana-appindicator3-dev \
            libsamplerate-dev \
            libwebrtc-audio-processing-dev \
            libgtk-3-dev \
            libwebkit2gtk-4.1-dev \
            webkit2gtk-driver \
            librsvg2-dev \
            patchelf \
            x11-utils \
            x11-apps \
            xdotool \
            openbox \
            xterm \
            fonts-liberation \
            imagemagick \
            fonts-dejavu \
            alsa-utils \
            pulseaudio \
            pulseaudio-utils

    - name: Set up Rust
      uses: actions-rust-lang/setup-rust-toolchain@v1
      with:
        toolchain: stable
        override: true
        cache: true
        target: x86_64-unknown-linux-gnu
        rustflags: ""

    - name: Build CLI on Linux
      run: cargo build --release -p screenpipe-server --target x86_64-unknown-linux-gnu

    - name: Install frontend dependencies
      working-directory: ./screenpipe-app-tauri
      run: bun install

    - name: Run pre_build.js
      run: |
        bun ./scripts/pre_build.js
      working-directory: ./screenpipe-app-tauri

    - name: Build
      uses: tauri-apps/tauri-action@v0.5.17
      env:
        CI: true
        CARGO_PROFILE_RELEASE_LTO: false
        CARGO_PROFILE_RELEASE_OPT_LEVEL: 3
        CARGO_PROFILE_RELEASE_CODEGEN_UNITS: 16
        CARGO_INCREMENTAL: false
        CARGO_PROFILE_RELEASE_STRIP: none
        CARGO_PROFILE_RELEASE_PANIC: unwind
        CARGO_PROFILE_RELEASE_INCREMENTAL: true
        VCPKG_STATIC_LINKAGE: "true"
      with:
        args: "--target x86_64-unknown-linux-gnu --no-bundle"
        projectPath: "./screenpipe-app-tauri"
        tauriScript: bunx tauri -v

    - name: Install tauri-driver
      run: |
        if [ ! -f "$HOME/.cargo/bin/tauri-driver" ]; then
          cargo install tauri-driver
        fi
    
    - name: Set up virtual display with window manager
      run: .github/scripts/setup_display.sh

    - name: Setup audio
      run: .github/scripts/setup_audio.sh

    - name: Run E2E Tests
      shell: bash
      working-directory: ./screenpipe-app-tauri
      run: |
        export XDG_RUNTIME_DIR=/run/user/$(id -u)
        export XDG_CONFIG_HOME="$HOME/.config"
        export XDG_DATA_HOME="$HOME/.local/share"
        export XDG_CACHE_HOME="$HOME/.cache"
        export PULSE_SERVER=unix:${XDG_RUNTIME_DIR}/pulse/native
        export DISPLAY=:99

        bun run test:e2e > e2e_output.log

    - name: Upload E2E Test Artifacts
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: linux-e2e-test-artifacts
        path: |
          ./screenpipe-app-tauri/e2e/videos/*
          ./screenpipe-app-tauri/e2e_output.log

    - name: Upload Screenpipe data
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: linux-screenpipe-data
        include-hidden-files: true
        path: |
          ~/.screenpipe/*
