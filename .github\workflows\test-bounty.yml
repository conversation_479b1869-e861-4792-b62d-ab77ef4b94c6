name: Create Test Bounty

on:
  pull_request_target:
    types: [opened, reopened, closed]

jobs:
  create-test-bounty:
    if: github.event.action != 'closed'
    runs-on: ubuntu-latest
    permissions:
      issues: write
      pull-requests: write
      contents: read
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
      
      - name: Create test bounty issue
        id: create-issue
        uses: actions/github-script@v6
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const fs = require('fs');
            const prNumber = context.payload.pull_request.number;
            const prTitle = context.payload.pull_request.title;
            const prAuthor = context.payload.pull_request.user.login;
            const prUrl = context.payload.pull_request.html_url;
            
            // Read template file
            let templateContent = fs.readFileSync('.github/ISSUE_TEMPLATE/test-bounty-template.md', 'utf8');
            
            // Skip YAML frontmatter
            const frontmatterEnd = templateContent.indexOf('---', 3) + 3;
            templateContent = templateContent.substring(frontmatterEnd);
            
            // Replace variables manually
            const replacedContent = templateContent
              .replace(/\$\{\{ env\.PR_NUMBER \}\}/g, prNumber)
              .replace(/\$\{\{ env\.PR_TITLE \}\}/g, prTitle)
              .replace(/\$\{\{ env\.PR_AUTHOR \}\}/g, prAuthor)
              .replace(/\$\{\{ env\.PR_URL \}\}/g, prUrl);
            
            // Create the issue
            const issue = await github.rest.issues.create({
              owner: context.repo.owner,
              repo: context.repo.repo,
              title: `🧪 Testing Bounty: PR #${prNumber} - ${prTitle}`,
              body: replacedContent,
              labels: ['testing', 'bounty', 'algora']
            });
            
            console.log(`Created issue #${issue.data.number}: ${issue.data.html_url}`);
            return issue.data.html_url;
        
      - name: Comment on PR with issue link
        uses: actions/github-script@v6
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const issueUrl = ${{ steps.create-issue.outputs.result }};
            const prNumber = context.payload.pull_request.number;
            
            const comment = `
            ## 🧪 testing bounty created!
            
            a testing bounty has been created for this PR: [view testing issue](${issueUrl})
            
            testers will be awarded $20 each for providing quality test reports. please check the issue for testing requirements.
            `;
            
            await github.rest.issues.createComment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: prNumber,
              body: comment
            });

  close-test-bounty:
    if: github.event.action == 'closed'
    runs-on: ubuntu-latest
    permissions:
      issues: write
      pull-requests: read
      contents: read
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
      
      - name: Find and close test bounty issue
        uses: actions/github-script@v6
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const prNumber = context.payload.pull_request.number;
            
            // Search for issues with the bounty label and PR number in title
            const issues = await github.rest.issues.listForRepo({
              owner: context.repo.owner,
              repo: context.repo.repo,
              labels: 'bounty',
              state: 'open'
            });
            
            const bountyIssue = issues.data.find(issue => 
              issue.title.includes(`PR #${prNumber}`)
            );
            
            if (bountyIssue) {
              await github.rest.issues.update({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: bountyIssue.number,
                state: 'closed'
              });
              
              console.log(`Closed test bounty issue #${bountyIssue.number}`);
            } else {
              console.log('No matching test bounty issue found');
            } 
