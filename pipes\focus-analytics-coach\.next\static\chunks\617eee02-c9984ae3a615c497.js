"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[272],{13221:(i,t,e)=>{e.d(t,{A1:()=>C,Ds:()=>N,M5:()=>F,RK:()=>m,V5:()=>S,al:()=>k,k1:()=>I,kO:()=>z,p2:()=>v,so:()=>y,tw:()=>D});var s=e(97102),n=e(82492),r=e(73187),a=e(90556),l=e(15034),h=e(8785),o=e(14811),d=e(97344),p=e(96902),u=e(65650),g=e(62041),c=e(82692),f=e(1560);function m(i,t){return(0,s.DZQ)(()=>((0,l.uM)(t),"channelsFirst"===t)?s.mgz(i,[0,2,3,1]):i)}function k(i,t){return(0,s.DZQ)(()=>((0,l.uM)(t),"channelsFirst"===t)?s.mgz(i,[0,2,3,4,1]):i)}function b(i,t,e,n=[1,1],a="valid",h,o,p=null){return(0,s.DZQ)(()=>{if(null==h&&(h=(0,r.VI)()),(0,l.uM)(h),3!==i.rank&&4!==i.rank)throw new d.Qp(`conv2dWithBiasActivation expects input to be of rank 3 or 4, but received ${i.rank}.`);if(3!==t.rank&&4!==t.rank)throw new d.Qp(`conv2dWithBiasActivation expects kernel to be of rank 3 or 4, but received ${i.rank}.`);let u=m(i,h);if("causal"===a)throw new d.EH("The support for CAUSAL padding mode in conv1dWithBias is not implemented yet.");return u=s.cZk.conv2d({x:u,filter:t,strides:n,pad:"same"===a?"same":"valid",dilations:o,dataFormat:"NHWC",bias:e,activation:p}),"channelsFirst"===h&&(u=s.mgz(u,[0,3,1,2])),u})}class z extends o.Wd{constructor(i,t){if(super(t),this.bias=null,this.DEFAULT_KERNEL_INITIALIZER="glorotNormal",this.DEFAULT_BIAS_INITIALIZER="zeros",z.verifyArgs(t),this.rank=i,c.oo(this.rank,"rank"),1!==this.rank&&2!==this.rank&&3!==this.rank)throw new d.EH(`Convolution layer for rank other than 1, 2, or 3 (${this.rank}) is not implemented yet.`);if(this.kernelSize=(0,g.J)(t.kernelSize,i,"kernelSize"),this.strides=(0,g.J)(null==t.strides?1:t.strides,i,"strides"),this.padding=null==t.padding?"valid":t.padding,(0,l.tB)(this.padding),this.dataFormat=null==t.dataFormat?"channelsLast":t.dataFormat,(0,l.uM)(this.dataFormat),this.activation=(0,n.b_)(t.activation),this.useBias=null==t.useBias||t.useBias,this.biasInitializer=(0,p.Fe)(t.biasInitializer||this.DEFAULT_BIAS_INITIALIZER),this.biasConstraint=(0,h.YZ)(t.biasConstraint),this.biasRegularizer=(0,u.Bm)(t.biasRegularizer),this.activityRegularizer=(0,u.Bm)(t.activityRegularizer),this.dilationRate=(0,g.J)(null==t.dilationRate?1:t.dilationRate,i,"dilationRate"),1===this.rank&&Array.isArray(this.dilationRate)&&1!==this.dilationRate.length)throw new d.Qp(`dilationRate must be a number or an array of a single number for 1D convolution, but received ${JSON.stringify(this.dilationRate)}`);if(2===this.rank){if("number"==typeof this.dilationRate)this.dilationRate=[this.dilationRate,this.dilationRate];else if(2!==this.dilationRate.length)throw new d.Qp(`dilationRate must be a number or array of two numbers for 2D convolution, but received ${JSON.stringify(this.dilationRate)}`)}else if(3===this.rank){if("number"==typeof this.dilationRate)this.dilationRate=[this.dilationRate,this.dilationRate,this.dilationRate];else if(3!==this.dilationRate.length)throw new d.Qp(`dilationRate must be a number or array of three numbers for 3D convolution, but received ${JSON.stringify(this.dilationRate)}`)}}static verifyArgs(i){if(c.vA("kernelSize"in i,"required key 'kernelSize' not in config"),"number"!=typeof i.kernelSize&&!c.HP(i.kernelSize,"number",1,3))throw new d.Qp(`BaseConv expects config.kernelSize to be number or number[] with length 1, 2, or 3, but received ${JSON.stringify(i.kernelSize)}.`)}getConfig(){let i={kernelSize:this.kernelSize,strides:this.strides,padding:this.padding,dataFormat:this.dataFormat,dilationRate:this.dilationRate,activation:(0,n.Bu)(this.activation),useBias:this.useBias,biasInitializer:(0,p.zo)(this.biasInitializer),biasRegularizer:(0,u.R9)(this.biasRegularizer),activityRegularizer:(0,u.R9)(this.activityRegularizer),biasConstraint:(0,h.uH)(this.biasConstraint)};return Object.assign(i,super.getConfig()),i}}class w extends z{constructor(i,t){super(i,t),this.kernel=null,w.verifyArgs(t),this.filters=t.filters,c.oo(this.filters,"filters"),this.kernelInitializer=(0,p.Fe)(t.kernelInitializer||this.DEFAULT_KERNEL_INITIALIZER),this.kernelConstraint=(0,h.YZ)(t.kernelConstraint),this.kernelRegularizer=(0,u.Bm)(t.kernelRegularizer)}build(i){i=(0,f.U$)(i);let t="channelsFirst"===this.dataFormat?1:i.length-1;if(null==i[t])throw new d.Qp(`The channel dimension of the input should be defined. Found ${i[t]}`);let e=i[t],s=this.kernelSize.concat([e,this.filters]);this.kernel=this.addWeight("kernel",s,null,this.kernelInitializer,this.kernelRegularizer,!0,this.kernelConstraint),this.useBias&&(this.bias=this.addWeight("bias",[this.filters],null,this.biasInitializer,this.biasRegularizer,!0,this.biasConstraint)),this.inputSpec=[{ndim:this.rank+2,axes:{[t]:e}}],this.built=!0}call(i,t){return(0,s.DZQ)(()=>{let t;i=(0,f.un)(i);let e=null==this.bias?null:this.bias.read(),n=c.Cd(this.activation.getClassName());if(null!=n&&2===this.rank)t=b(i,this.kernel.read(),e,this.strides,this.padding,this.dataFormat,this.dilationRate,n);else{if(1===this.rank)t=function(i,t,e,n=1,h="valid",o,p=1){return(0,s.DZQ)(()=>{if(null==o&&(o=(0,r.VI)()),(0,l.uM)(o),3!==i.shape.length)throw new d.Qp(`The input of a conv1dWithBias operation should be 3, but is ${i.shape.length} instead.`);if(3!==t.shape.length)throw new d.Qp(`The kernel for a conv1dWithBias operation should be 3, but is ${t.shape.length} instead`);if(null!=e&&1!==e.shape.length)throw new d.Qp(`The bias for a conv1dWithBias operation should be 1, but is ${e.shape.length} instead`);if("channelsFirst"===o&&(i=s.mgz(i,[0,2,1])),"causal"===h)throw new d.EH("The support for CAUSAL padding mode in conv1dWithBias is not implemented yet.");let u=s.kA9(i,t,n,"same"===h?"same":"valid","NWC",p);return null!=e&&(u=a.ni(u,e)),u})}(i,this.kernel.read(),e,this.strides[0],this.padding,this.dataFormat,this.dilationRate[0]);else if(2===this.rank)t=b(i,this.kernel.read(),e,this.strides,this.padding,this.dataFormat,this.dilationRate);else if(3===this.rank)t=function(i,t,e,n=[1,1,1],h="valid",o,p){return(0,s.DZQ)(()=>{if(null==o&&(o=(0,r.VI)()),(0,l.uM)(o),4!==i.rank&&5!==i.rank)throw new d.Qp(`conv3dWithBias expects input to be of rank 4 or 5, but received ${i.rank}.`);if(4!==t.rank&&5!==t.rank)throw new d.Qp(`conv3dWithBias expects kernel to be of rank 4 or 5, but received ${i.rank}.`);let u=k(i,o);if("causal"===h)throw new d.EH("The support for CAUSAL padding mode in conv3dWithBias is not implemented yet.");return u=s.IPL(u,t,n,"same"===h?"same":"valid","NDHWC",p),null!=e&&(u=a.ni(u,e)),"channelsFirst"===o&&(u=s.mgz(u,[0,4,1,2,3])),u})}(i,this.kernel.read(),e,this.strides,this.padding,this.dataFormat,this.dilationRate);else throw new d.EH("convolutions greater than 3D are not implemented yet.");null!=this.activation&&(t=this.activation.apply(t))}return t})}computeOutputShape(i){i=(0,f.U$)(i);let t=[],e="channelsLast"===this.dataFormat?i.slice(1,i.length-1):i.slice(2);for(let i=0;i<e.length;++i){let s=(0,g.Ol)(e[i],this.kernelSize[i],this.padding,this.strides[i],"number"==typeof this.dilationRate?this.dilationRate:this.dilationRate[i]);t.push(s)}let s=[i[0]];return"channelsLast"===this.dataFormat?(s=s.concat(t)).push(this.filters):(s.push(this.filters),s=s.concat(t)),s}getConfig(){let i={filters:this.filters,kernelInitializer:(0,p.zo)(this.kernelInitializer),kernelRegularizer:(0,u.R9)(this.kernelRegularizer),kernelConstraint:(0,h.uH)(this.kernelConstraint)};return Object.assign(i,super.getConfig()),i}static verifyArgs(i){if(!("filters"in i)||"number"!=typeof i.filters||i.filters<1)throw new d.Qp(`Convolution layer expected config.filters to be a 'number' > 0 but got ${JSON.stringify(i.filters)}`)}}class v extends w{constructor(i){super(2,i),v.verifyArgs(i)}getConfig(){let i=super.getConfig();return delete i.rank,i}static verifyArgs(i){if("number"!=typeof i.kernelSize&&!c.HP(i.kernelSize,"number",1,2))throw new d.Qp(`Conv2D expects config.kernelSize to be number or number[] with length 1 or 2, but received ${JSON.stringify(i.kernelSize)}.`)}}v.className="Conv2D",s.JFn.registerClass(v);class C extends w{constructor(i){super(3,i),C.verifyArgs(i)}getConfig(){let i=super.getConfig();return delete i.rank,i}static verifyArgs(i){if("number"!=typeof i.kernelSize&&!(Array.isArray(i.kernelSize)&&(1===i.kernelSize.length||3===i.kernelSize.length)))throw new d.Qp(`Conv3D expects config.kernelSize to be number or [number, number, number], but received ${JSON.stringify(i.kernelSize)}.`)}}C.className="Conv3D",s.JFn.registerClass(C);class F extends v{constructor(i){if(super(i),this.inputSpec=[new o.eO({ndim:4})],"same"!==this.padding&&"valid"!==this.padding)throw new d.Qp(`Conv2DTranspose currently supports only padding modes 'same' and 'valid', but received padding mode ${this.padding}`)}build(i){if(4!==(i=(0,f.U$)(i)).length)throw new d.Qp("Input should have rank 4; Received input shape: "+JSON.stringify(i));let t="channelsFirst"===this.dataFormat?1:i.length-1;if(null==i[t])throw new d.Qp("The channel dimension of the inputs should be defined. Found `None`.");let e=i[t],s=this.kernelSize.concat([this.filters,e]);this.kernel=this.addWeight("kernel",s,"float32",this.kernelInitializer,this.kernelRegularizer,!0,this.kernelConstraint),this.useBias&&(this.bias=this.addWeight("bias",[this.filters],"float32",this.biasInitializer,this.biasRegularizer,!0,this.biasConstraint)),this.inputSpec=[new o.eO({ndim:4,axes:{[t]:e}})],this.built=!0}call(i,t){return s.DZQ(()=>{let t,e,n=(0,f.un)(i);if(4!==n.shape.length)throw new d.Qp(`Conv2DTranspose.call() expects input tensor to be rank-4, but received a tensor of rank-${n.shape.length}`);let r=n.shape,l=r[0];"channelsFirst"===this.dataFormat?(t=2,e=3):(t=1,e=2);let h=r[t],o=r[e],p=this.kernelSize[0],u=this.kernelSize[1],c=this.strides[0],m=this.strides[1],k=[l,(0,g.mW)(h,c,p,this.padding),(0,g.mW)(o,m,u,this.padding),this.filters];"channelsLast"!==this.dataFormat&&(n=s.mgz(n,[0,2,3,1]));let b=s.wX9(n,this.kernel.read(),k,this.strides,this.padding);return"channelsLast"!==this.dataFormat&&(b=s.mgz(b,[0,3,1,2])),null!=this.bias&&(b=a.ni(b,this.bias.read(),this.dataFormat)),null!=this.activation&&(b=this.activation.apply(b)),b})}computeOutputShape(i){let t,e,s;let n=(i=(0,f.U$)(i)).slice();"channelsFirst"===this.dataFormat?(t=1,e=2,s=3):(t=3,e=1,s=2);let r=this.kernelSize[0],a=this.kernelSize[1],l=this.strides[0],h=this.strides[1];return n[t]=this.filters,n[e]=(0,g.mW)(n[e],l,r,this.padding),n[s]=(0,g.mW)(n[s],h,a,this.padding),n}getConfig(){let i=super.getConfig();return delete i.dilationRate,i}}F.className="Conv2DTranspose",s.JFn.registerClass(F);class S extends C{constructor(i){if(super(i),this.inputSpec=[new o.eO({ndim:5})],"same"!==this.padding&&"valid"!==this.padding)throw new d.Qp(`Conv3DTranspose currently supports only padding modes 'same' and 'valid', but received padding mode ${this.padding}`)}build(i){if(5!==(i=(0,f.U$)(i)).length)throw new d.Qp("Input should have rank 5; Received input shape: "+JSON.stringify(i));let t="channelsFirst"===this.dataFormat?1:i.length-1;if(null==i[t])throw new d.Qp("The channel dimension of the inputs should be defined. Found `None`.");let e=i[t],s=this.kernelSize.concat([this.filters,e]);this.kernel=this.addWeight("kernel",s,"float32",this.kernelInitializer,this.kernelRegularizer,!0,this.kernelConstraint),this.useBias&&(this.bias=this.addWeight("bias",[this.filters],"float32",this.biasInitializer,this.biasRegularizer,!0,this.biasConstraint)),this.inputSpec=[new o.eO({ndim:5,axes:{[t]:e}})],this.built=!0}call(i,t){return s.DZQ(()=>{let t,e,n,r=(0,f.un)(i);if(5!==r.shape.length)throw new d.Qp(`Conv3DTranspose.call() expects input tensor to be rank-4, but received a tensor of rank-${r.shape.length}`);let l=r.shape,h=l[0];"channelsFirst"===this.dataFormat?(n=2,t=3,e=4):(n=1,t=2,e=3);let o=l[n],p=l[t],u=l[e],c=this.kernelSize[0],m=this.kernelSize[1],k=this.kernelSize[2],b=this.strides[0],z=this.strides[1],w=this.strides[2],v=(0,g.mW)(o,b,c,this.padding),C=[h,v,(0,g.mW)(p,z,m,this.padding),(0,g.mW)(u,w,k,this.padding),this.filters];"channelsLast"!==this.dataFormat&&(r=s.mgz(r,[0,2,3,4,1]));let F=s.jIJ(r,this.kernel.read(),C,this.strides,this.padding);return"channelsLast"!==this.dataFormat&&(F=s.mgz(F,[0,4,1,2,3])),null!==this.bias&&(F=a.ni(F,this.bias.read(),this.dataFormat)),null!==this.activation&&(F=this.activation.apply(F)),F})}computeOutputShape(i){let t,e,s,n;let r=(i=(0,f.U$)(i)).slice();"channelsFirst"===this.dataFormat?(t=1,e=2,s=3,n=4):(t=4,e=1,s=2,n=3);let a=this.kernelSize[0],l=this.kernelSize[1],h=this.kernelSize[2],o=this.strides[0],d=this.strides[1],p=this.strides[2];return r[t]=this.filters,r[e]=(0,g.mW)(r[e],o,a,this.padding),r[s]=(0,g.mW)(r[s],d,l,this.padding),r[n]=(0,g.mW)(r[n],p,h,this.padding),r}getConfig(){let i=super.getConfig();return delete i.dilationRate,i}}S.className="Conv3DTranspose",s.JFn.registerClass(S);class R extends w{constructor(i,t){if(super(i,t),this.DEFAULT_DEPTHWISE_INITIALIZER="glorotUniform",this.DEFAULT_POINTWISE_INITIALIZER="glorotUniform",this.depthwiseKernel=null,this.pointwiseKernel=null,null==t.filters)throw new d.Qp("The `filters` configuration field is required by SeparableConv, but is unspecified.");if(null!=t.kernelInitializer||null!=t.kernelRegularizer||null!=t.kernelConstraint)throw new d.Qp("Fields kernelInitializer, kernelRegularizer and kernelConstraint are invalid for SeparableConv2D. Use depthwiseInitializer, depthwiseRegularizer, depthwiseConstraint, pointwiseInitializer, pointwiseRegularizer and pointwiseConstraint instead.");if(null!=t.padding&&"same"!==t.padding&&"valid"!==t.padding)throw new d.Qp(`SeparableConv${this.rank}D supports only padding modes: 'same' and 'valid', but received ${JSON.stringify(t.padding)}`);this.depthMultiplier=null==t.depthMultiplier?1:t.depthMultiplier,this.depthwiseInitializer=(0,p.Fe)(t.depthwiseInitializer||this.DEFAULT_DEPTHWISE_INITIALIZER),this.depthwiseRegularizer=(0,u.Bm)(t.depthwiseRegularizer),this.depthwiseConstraint=(0,h.YZ)(t.depthwiseConstraint),this.pointwiseInitializer=(0,p.Fe)(t.depthwiseInitializer||this.DEFAULT_POINTWISE_INITIALIZER),this.pointwiseRegularizer=(0,u.Bm)(t.pointwiseRegularizer),this.pointwiseConstraint=(0,h.YZ)(t.pointwiseConstraint)}build(i){if((i=(0,f.U$)(i)).length<this.rank+2)throw new d.Qp(`Inputs to SeparableConv${this.rank}D should have rank ${this.rank+2}, but received input shape: ${JSON.stringify(i)}`);let t="channelsFirst"===this.dataFormat?1:i.length-1;if(null==i[t]||i[t]<0)throw new d.Qp(`The channel dimension of the inputs should be defined, but found ${JSON.stringify(i[t])}`);let e=i[t],s=this.kernelSize.concat([e,this.depthMultiplier]),n=[];for(let i=0;i<this.rank;++i)n.push(1);n.push(e*this.depthMultiplier,this.filters),this.depthwiseKernel=this.addWeight("depthwise_kernel",s,"float32",this.depthwiseInitializer,this.depthwiseRegularizer,!0,this.depthwiseConstraint),this.pointwiseKernel=this.addWeight("pointwise_kernel",n,"float32",this.pointwiseInitializer,this.pointwiseRegularizer,!0,this.pointwiseConstraint),this.useBias?this.bias=this.addWeight("bias",[this.filters],"float32",this.biasInitializer,this.biasRegularizer,!0,this.biasConstraint):this.bias=null,this.inputSpec=[new o.eO({ndim:this.rank+2,axes:{[t]:e}})],this.built=!0}call(i,t){return(0,s.DZQ)(()=>{let t;if(i=(0,f.un)(i),1===this.rank)throw new d.EH("1D separable convolution is not implemented yet.");return 2===this.rank&&("channelsFirst"===this.dataFormat&&(i=s.mgz(i,[0,2,3,1])),t=s.wdz(i,this.depthwiseKernel.read(),this.pointwiseKernel.read(),this.strides,this.padding,this.dilationRate,"NHWC")),this.useBias&&(t=a.ni(t,this.bias.read(),this.dataFormat)),null!=this.activation&&(t=this.activation.apply(t)),"channelsFirst"===this.dataFormat&&(t=s.mgz(t,[0,3,1,2])),t})}getConfig(){let i=super.getConfig();return delete i.rank,delete i.kernelInitializer,delete i.kernelRegularizer,delete i.kernelConstraint,i.depthwiseInitializer=(0,p.zo)(this.depthwiseInitializer),i.pointwiseInitializer=(0,p.zo)(this.pointwiseInitializer),i.depthwiseRegularizer=(0,u.R9)(this.depthwiseRegularizer),i.pointwiseRegularizer=(0,u.R9)(this.pointwiseRegularizer),i.depthwiseConstraint=(0,h.uH)(this.depthwiseConstraint),i.pointwiseConstraint=(0,h.uH)(this.pointwiseConstraint),i}}R.className="SeparableConv";class I extends R{constructor(i){super(2,i)}}I.className="SeparableConv2D",s.JFn.registerClass(I);class y extends w{constructor(i){super(1,i),y.verifyArgs(i),this.inputSpec=[{ndim:3}]}getConfig(){let i=super.getConfig();return delete i.rank,delete i.dataFormat,i}static verifyArgs(i){if("number"!=typeof i.kernelSize&&!c.HP(i.kernelSize,"number",1,1))throw new d.Qp(`Conv1D expects config.kernelSize to be number or number[] with length 1, but received ${JSON.stringify(i.kernelSize)}.`)}}y.className="Conv1D",s.JFn.registerClass(y);class D extends o.Wd{constructor(i){super(i),"number"==typeof i.cropping?this.cropping=[[i.cropping,i.cropping],[i.cropping,i.cropping]]:"number"==typeof i.cropping[0]?this.cropping=[[i.cropping[0],i.cropping[0]],[i.cropping[1],i.cropping[1]]]:this.cropping=i.cropping,this.dataFormat=void 0===i.dataFormat?"channelsLast":i.dataFormat,this.inputSpec=[{ndim:4}]}computeOutputShape(i){return"channelsFirst"===this.dataFormat?[i[0],i[1],i[2]-this.cropping[0][0]-this.cropping[0][1],i[3]-this.cropping[1][0]-this.cropping[1][1]]:[i[0],i[1]-this.cropping[0][0]-this.cropping[0][1],i[2]-this.cropping[1][0]-this.cropping[1][1],i[3]]}call(i,t){return(0,s.DZQ)(()=>{if(i=(0,f.un)(i),"channelsLast"===this.dataFormat){let t=a.r0(i,this.cropping[0][0],i.shape[1]-this.cropping[0][0]-this.cropping[0][1],2);return a.r0(t,this.cropping[1][0],i.shape[2]-this.cropping[1][1]-this.cropping[1][0],3)}{let t=a.r0(i,this.cropping[0][0],i.shape[2]-this.cropping[0][0]-this.cropping[0][1],3);return a.r0(t,this.cropping[1][0],i.shape[3]-this.cropping[1][1]-this.cropping[1][0],4)}})}getConfig(){let i={cropping:this.cropping,dataFormat:this.dataFormat};return Object.assign(i,super.getConfig()),i}}D.className="Cropping2D",s.JFn.registerClass(D);class N extends o.Wd{constructor(i){super(i),this.DEFAULT_SIZE=[2,2],this.inputSpec=[{ndim:4}],this.size=null==i.size?this.DEFAULT_SIZE:i.size,this.dataFormat=null==i.dataFormat?"channelsLast":i.dataFormat,(0,l.uM)(this.dataFormat),this.interpolation=null==i.interpolation?"nearest":i.interpolation,(0,l.uU)(this.interpolation)}computeOutputShape(i){if("channelsFirst"===this.dataFormat){let t=null==i[2]?null:this.size[0]*i[2],e=null==i[3]?null:this.size[1]*i[3];return[i[0],i[1],t,e]}{let t=null==i[1]?null:this.size[0]*i[1],e=null==i[2]?null:this.size[1]*i[2];return[i[0],t,e,i[3]]}}call(i,t){return s.DZQ(()=>{let t=(0,f.un)(i),e=t.shape;if("channelsFirst"===this.dataFormat){t=s.mgz(t,[0,2,3,1]);let i=this.size[0]*e[2],n=this.size[1]*e[3],r="nearest"===this.interpolation?s.Slp.resizeNearestNeighbor(t,[i,n]):s.Slp.resizeBilinear(t,[i,n]);return s.mgz(r,[0,3,1,2])}{let i=this.size[0]*e[1],n=this.size[1]*e[2];return"nearest"===this.interpolation?s.Slp.resizeNearestNeighbor(t,[i,n]):s.Slp.resizeBilinear(t,[i,n])}})}getConfig(){let i={size:this.size,dataFormat:this.dataFormat,interpolation:this.interpolation};return Object.assign(i,super.getConfig()),i}}N.className="UpSampling2D",s.JFn.registerClass(N)}}]);