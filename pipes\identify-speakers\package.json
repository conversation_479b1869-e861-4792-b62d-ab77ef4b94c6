{"name": "identify-speakers", "version": "0.1.3", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --no-lint", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-dialog": "^1.1.3", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.3", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-select": "^2.1.3", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-toast": "^1.2.3", "@radix-ui/react-tooltip": "^1.1.5", "@screenpipe/browser": "^0.1.28", "@screenpipe/js": "^1.0.12", "@shadcn/ui": "^0.0.4", "@tanstack/react-query": "^5.62.7", "@types/js-levenshtein": "^1.1.3", "@types/lodash": "^4.17.13", "@types/react-syntax-highlighter": "^15.5.13", "ai": "^4.0.18", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.0", "date-fns": "^4.1.0", "framer-motion": "^11.14.4", "install": "^0.13.0", "js-levenshtein": "^1.1.6", "localforage": "^1.10.0", "lodash": "^4.17.21", "lucide-react": "^0.468.0", "magic-ui": "^0.1.0", "next": "15.1.0", "npm": "^10.9.2", "openai": "^4.76.3", "react": "^19.0.0", "react-day-picker": "8.10.1", "react-dom": "^19.0.0", "react-markdown": "^9.0.1", "react-syntax-highlighter": "^15.6.1", "remark-gfm": "^4.0.0", "remark-math": "^6.0.0", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^22.10.2", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.20", "eslint": "^9", "eslint-config-next": "15.1.0", "postcss": "^8.4.49", "tailwindcss": "^3.4.16", "typescript": "^5"}}