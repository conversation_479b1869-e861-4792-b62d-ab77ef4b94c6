"use client";

import React, { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  Brain, 
  Target, 
  TrendingUp, 
  Clock, 
  Zap, 
  Coffee, 
  Settings,
  Play,
  Pause,
  BarChart3,
  Calendar,
  Bell
} from "lucide-react";
import { FocusAnalyticsEngine } from "@/lib/analytics-engine";
import { RealTimeCoachingSystem } from "@/lib/coaching-system";
import { AIAnalysisEngine } from "@/lib/ai-analysis";
import { PatternRecognitionEngine } from "@/lib/pattern-recognition";
import { 
  FocusSession, 
  ProductivityMetrics, 
  CoachingNotification, 
  ProductivityGoal,
  AnalyticsConfig,
  TaskCategory
} from "@/lib/types";
import { format, startOfDay, endOfDay, subDays } from "date-fns";
import { toast } from "sonner";
import { generateDemoData, generateDemoNotifications, generateDemoGoals } from "@/lib/demo-data";
import { ScreenpipeStatus, ScreenpipeStatusBanner } from "@/components/screenpipe-status";

// Import dashboard components
import { ProductivityOverview } from "./dashboard/productivity-overview";

export function FocusAnalyticsDashboard() {
  // State management
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [currentTab, setCurrentTab] = useState("overview");
  const [timeRange, setTimeRange] = useState("today");
  const [loading, setLoading] = useState(true);
  
  // Analytics data
  const [productivityMetrics, setProductivityMetrics] = useState<ProductivityMetrics | null>(null);
  const [focusSessions, setFocusSessions] = useState<FocusSession[]>([]);
  const [notifications, setNotifications] = useState<CoachingNotification[]>([]);
  const [goals, setGoals] = useState<ProductivityGoal[]>([]);
  
  // Analytics engines
  const [analyticsEngine, setAnalyticsEngine] = useState<FocusAnalyticsEngine | null>(null);
  const [coachingSystem, setCoachingSystem] = useState<RealTimeCoachingSystem | null>(null);
  const [aiAnalysis, setAiAnalysis] = useState<AIAnalysisEngine | null>(null);
  const [patternEngine, setPatternEngine] = useState<PatternRecognitionEngine | null>(null);

  // Configuration
  const [config, setConfig] = useState<AnalyticsConfig>({
    focusThreshold: 15, // minimum 15 minutes for focus session
    distractionThreshold: 30, // 30 seconds before considering distraction
    contextSwitchWindow: 5, // 5 seconds to group rapid switches
    breakReminderInterval: 60, // remind every 60 minutes
    deepWorkMinimum: 45, // 45 minutes minimum for deep work
    productivityCategories: [], // Will be populated
    enableRealTimeCoaching: true,
    enableBreakReminders: true,
    enableGoalTracking: true
  });

  // Initialize analytics engines
  useEffect(() => {
    const initializeEngines = async () => {
      try {
        const analytics = new FocusAnalyticsEngine(config);
        const coaching = new RealTimeCoachingSystem(config);
        const ai = new AIAnalysisEngine();
        const patterns = new PatternRecognitionEngine();

        setAnalyticsEngine(analytics);
        setCoachingSystem(coaching);
        setAiAnalysis(ai);
        setPatternEngine(patterns);

        // Subscribe to coaching notifications
        coaching.onNotification((notification) => {
          setNotifications(prev => [notification, ...prev.slice(0, 9)]); // Keep last 10
          toast(notification.title, {
            description: notification.message,
            action: notification.actionable ? {
              label: notification.action?.label || "Action",
              onClick: notification.action?.callback || (() => {})
            } : undefined
          });
        });

        setLoading(false);
      } catch (error) {
        console.error("Error initializing analytics engines:", error);
        toast.error("Failed to initialize analytics engines");
        setLoading(false);
      }
    };

    initializeEngines();
  }, [config]);

  // Load analytics data
  useEffect(() => {
    if (loading) return;

    const loadAnalyticsData = async () => {
      try {
        if (analyticsEngine) {
          // Try to load real data
          const timeRangeMap = {
            today: { start: startOfDay(new Date()), end: endOfDay(new Date()) },
            yesterday: { start: startOfDay(subDays(new Date(), 1)), end: endOfDay(subDays(new Date(), 1)) },
            week: { start: startOfDay(subDays(new Date(), 7)), end: endOfDay(new Date()) },
            month: { start: startOfDay(subDays(new Date(), 30)), end: endOfDay(new Date()) }
          };

          const range = timeRangeMap[timeRange as keyof typeof timeRangeMap];
          const analysisResult = await analyticsEngine.analyzeActivity(range);

          if (analysisResult.focusSessions.length > 0) {
            setFocusSessions(analysisResult.focusSessions);
            setProductivityMetrics(analysisResult.metrics);
          } else {
            // Fallback to demo data if no real data available
            const demoData = generateDemoData();
            setFocusSessions(demoData.focusSessions);
            setProductivityMetrics(demoData.productivityMetrics);
            toast.info("Using demo data - start Screenpipe to see real analytics");
          }
        } else {
          // Use demo data if analytics engine not available
          const demoData = generateDemoData();
          setFocusSessions(demoData.focusSessions);
          setProductivityMetrics(demoData.productivityMetrics);

          // Load demo notifications and goals
          setNotifications(generateDemoNotifications());
          setGoals(generateDemoGoals());

          toast.info("Demo mode - install and run Screenpipe for real analytics");
        }

      } catch (error) {
        console.error("Error loading analytics data:", error);
        // Fallback to demo data on error
        const demoData = generateDemoData();
        setFocusSessions(demoData.focusSessions);
        setProductivityMetrics(demoData.productivityMetrics);
        setNotifications(generateDemoNotifications());
        setGoals(generateDemoGoals());
        toast.error("Failed to load real data, showing demo data");
      }
    };

    loadAnalyticsData();
  }, [analyticsEngine, timeRange, loading]);

  // Handle monitoring toggle
  const toggleMonitoring = async () => {
    if (!coachingSystem) return;

    try {
      if (isMonitoring) {
        coachingSystem.stopMonitoring();
        setIsMonitoring(false);
        toast.success("Stopped productivity monitoring");
      } else {
        await coachingSystem.startMonitoring();
        setIsMonitoring(true);
        toast.success("Started productivity monitoring");
      }
    } catch (error) {
      console.error("Error toggling monitoring:", error);
      toast.error("Failed to toggle monitoring");
    }
  };

  // Handle goal creation
  const createGoal = (goal: Omit<ProductivityGoal, 'id' | 'createdAt'>) => {
    if (!coachingSystem) return;

    coachingSystem.addGoal(goal);
    setGoals(coachingSystem.getGoals());
    toast.success("Goal created successfully");
  };

  // Handle settings update
  const updateSettings = (newConfig: Partial<AnalyticsConfig>) => {
    setConfig(prev => ({ ...prev, ...newConfig }));
    toast.success("Settings updated");
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Initializing Focus Analytics...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Focus Analytics & Productivity Coach</h1>
          <p className="text-gray-600 mt-1">
            AI-powered insights to optimize your productivity and focus
          </p>
        </div>
        
        <div className="flex items-center gap-4">
          {/* Screenpipe Status */}
          <ScreenpipeStatus />

          {/* Time Range Selector */}
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="today">Today</option>
            <option value="yesterday">Yesterday</option>
            <option value="week">Last 7 Days</option>
            <option value="month">Last 30 Days</option>
          </select>

          {/* Monitoring Toggle */}
          <Button
            onClick={toggleMonitoring}
            variant={isMonitoring ? "destructive" : "default"}
            className="flex items-center gap-2"
          >
            {isMonitoring ? (
              <>
                <Pause className="h-4 w-4" />
                Stop Monitoring
              </>
            ) : (
              <>
                <Play className="h-4 w-4" />
                Start Monitoring
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Screenpipe Connection Status Banner */}
      <ScreenpipeStatusBanner />

      {/* Status Bar */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <div className={`w-3 h-3 rounded-full ${isMonitoring ? 'bg-green-500' : 'bg-gray-400'}`}></div>
                <span className="text-sm font-medium">
                  {isMonitoring ? 'Monitoring Active' : 'Monitoring Inactive'}
                </span>
              </div>
              
              {productivityMetrics && (
                <>
                  <Badge variant="outline" className="flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    {Math.round(productivityMetrics.totalFocusTime)} min focused
                  </Badge>
                  
                  <Badge variant="outline" className="flex items-center gap-1">
                    <TrendingUp className="h-3 w-3" />
                    {Math.round(productivityMetrics.focusScore)}% focus score
                  </Badge>
                  
                  <Badge variant="outline" className="flex items-center gap-1">
                    <Zap className="h-3 w-3" />
                    {productivityMetrics.deepWorkSessions} deep work sessions
                  </Badge>
                </>
              )}
            </div>

            {notifications.length > 0 && (
              <Button variant="ghost" size="sm" className="flex items-center gap-2">
                <Bell className="h-4 w-4" />
                {notifications.length} notifications
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Main Dashboard */}
      <Tabs value={currentTab} onValueChange={setCurrentTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="sessions" className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            Sessions
          </TabsTrigger>
          <TabsTrigger value="coaching" className="flex items-center gap-2">
            <Brain className="h-4 w-4" />
            Coaching
          </TabsTrigger>
          <TabsTrigger value="goals" className="flex items-center gap-2">
            <Target className="h-4 w-4" />
            Goals
          </TabsTrigger>
          <TabsTrigger value="patterns" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Patterns
          </TabsTrigger>
          <TabsTrigger value="settings" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Settings
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <ProductivityOverview 
            metrics={productivityMetrics}
            sessions={focusSessions}
            timeRange={timeRange}
          />
        </TabsContent>

        <TabsContent value="sessions" className="space-y-6">
          <div className="text-center py-12">
            <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Focus Sessions</h3>
            <p className="text-gray-600">Detailed session analysis coming soon...</p>
          </div>
        </TabsContent>

        <TabsContent value="coaching" className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold">Real-time Coaching</h2>
              <p className="text-gray-600">AI-powered insights and suggestions for better productivity</p>
            </div>
            <div className="flex items-center gap-2">
              <div className={`w-3 h-3 rounded-full ${isMonitoring ? 'bg-green-500' : 'bg-gray-400'}`}></div>
              <span className="text-sm">{isMonitoring ? 'Coaching Active' : 'Coaching Inactive'}</span>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Recent Notifications */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Notifications</CardTitle>
                <CardDescription>Latest coaching insights and suggestions</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {notifications.length > 0 ? (
                    notifications.slice(0, 5).map((notification) => (
                      <div key={notification.id} className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
                        <div className={`w-2 h-2 rounded-full mt-2 ${
                          notification.priority === 'high' ? 'bg-red-500' :
                          notification.priority === 'medium' ? 'bg-yellow-500' : 'bg-blue-500'
                        }`}></div>
                        <div className="flex-1">
                          <h4 className="font-medium text-sm">{notification.title}</h4>
                          <p className="text-xs text-gray-600 mt-1">{notification.message}</p>
                          <div className="flex items-center justify-between mt-2">
                            <span className="text-xs text-gray-500">
                              {format(notification.timestamp, 'HH:mm')}
                            </span>
                            {notification.actionable && notification.action && (
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={notification.action.callback}
                                className="text-xs h-6"
                              >
                                {notification.action.label}
                              </Button>
                            )}
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8">
                      <Bell className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                      <p className="text-sm text-gray-500">No notifications yet</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Coaching Insights */}
            <Card>
              <CardHeader>
                <CardTitle>AI Insights</CardTitle>
                <CardDescription>Personalized productivity recommendations</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <div className="flex items-center gap-2 mb-2">
                      <Brain className="h-4 w-4 text-blue-600" />
                      <span className="font-medium text-blue-900">Focus Pattern</span>
                    </div>
                    <p className="text-sm text-blue-800">
                      You&apos;re most productive between 9-11 AM. Schedule important tasks during this window.
                    </p>
                  </div>

                  <div className="p-4 bg-green-50 rounded-lg border border-green-200">
                    <div className="flex items-center gap-2 mb-2">
                      <TrendingUp className="h-4 w-4 text-green-600" />
                      <span className="font-medium text-green-900">Improvement</span>
                    </div>
                    <p className="text-sm text-green-800">
                      Your focus score improved by 15% this week. Keep up the great work!
                    </p>
                  </div>

                  <div className="p-4 bg-orange-50 rounded-lg border border-orange-200">
                    <div className="flex items-center gap-2 mb-2">
                      <Coffee className="h-4 w-4 text-orange-600" />
                      <span className="font-medium text-orange-900">Break Suggestion</span>
                    </div>
                    <p className="text-sm text-orange-800">
                      Consider taking a 5-minute break every 45 minutes to maintain peak performance.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="goals" className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold">Productivity Goals</h2>
              <p className="text-gray-600">Track your progress towards better focus and productivity</p>
            </div>
            <Button onClick={() => toast.info("Goal creation coming soon!")}>
              <Target className="h-4 w-4 mr-2" />
              New Goal
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {goals.map((goal) => (
              <Card key={goal.id}>
                <CardHeader>
                  <CardTitle className="text-lg">{goal.name}</CardTitle>
                  <CardDescription>{goal.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <div className="flex justify-between text-sm mb-2">
                        <span>Progress</span>
                        <span>{goal.current} / {goal.target} {goal.unit}</span>
                      </div>
                      <Progress value={(goal.current / goal.target) * 100} className="h-2" />
                      <div className="text-xs text-gray-500 mt-1">
                        {Math.round((goal.current / goal.target) * 100)}% complete
                      </div>
                    </div>

                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-500">Status</span>
                      <Badge variant={goal.isActive ? "default" : "secondary"}>
                        {goal.isActive ? "Active" : "Inactive"}
                      </Badge>
                    </div>

                    {goal.deadline && (
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-500">Deadline</span>
                        <span>{format(goal.deadline, 'MMM dd, HH:mm')}</span>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {goals.length === 0 && (
            <Card>
              <CardContent className="text-center py-12">
                <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Goals Set</h3>
                <p className="text-gray-600 mb-4">Create your first productivity goal to start tracking progress</p>
                <Button onClick={() => toast.info("Goal creation coming soon!")}>
                  Create Goal
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="patterns" className="space-y-6">
          <div className="text-center py-12">
            <TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Pattern Insights</h3>
            <p className="text-gray-600">AI pattern recognition coming soon...</p>
          </div>
        </TabsContent>

        <TabsContent value="settings" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Analytics Configuration</CardTitle>
              <CardDescription>
                Customize how the Focus Analytics system tracks and analyzes your productivity
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium">Focus Threshold (minutes)</label>
                    <p className="text-xs text-gray-500 mb-2">Minimum duration to consider a focus session</p>
                    <input
                      type="number"
                      value={config.focusThreshold}
                      onChange={(e) => updateSettings({ focusThreshold: parseInt(e.target.value) })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      min="5"
                      max="60"
                    />
                  </div>

                  <div>
                    <label className="text-sm font-medium">Break Reminder Interval (minutes)</label>
                    <p className="text-xs text-gray-500 mb-2">How often to suggest breaks</p>
                    <input
                      type="number"
                      value={config.breakReminderInterval}
                      onChange={(e) => updateSettings({ breakReminderInterval: parseInt(e.target.value) })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      min="15"
                      max="180"
                    />
                  </div>

                  <div>
                    <label className="text-sm font-medium">Deep Work Minimum (minutes)</label>
                    <p className="text-xs text-gray-500 mb-2">Minimum duration for deep work classification</p>
                    <input
                      type="number"
                      value={config.deepWorkMinimum}
                      onChange={(e) => updateSettings({ deepWorkMinimum: parseInt(e.target.value) })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      min="30"
                      max="120"
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium">Real-time Coaching</label>
                      <p className="text-xs text-gray-500">Enable live productivity notifications</p>
                    </div>
                    <input
                      type="checkbox"
                      checked={config.enableRealTimeCoaching}
                      onChange={(e) => updateSettings({ enableRealTimeCoaching: e.target.checked })}
                      className="rounded"
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium">Break Reminders</label>
                      <p className="text-xs text-gray-500">Suggest breaks based on work intensity</p>
                    </div>
                    <input
                      type="checkbox"
                      checked={config.enableBreakReminders}
                      onChange={(e) => updateSettings({ enableBreakReminders: e.target.checked })}
                      className="rounded"
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium">Goal Tracking</label>
                      <p className="text-xs text-gray-500">Track progress towards productivity goals</p>
                    </div>
                    <input
                      type="checkbox"
                      checked={config.enableGoalTracking}
                      onChange={(e) => updateSettings({ enableGoalTracking: e.target.checked })}
                      className="rounded"
                    />
                  </div>
                </div>
              </div>

              <div className="pt-4 border-t">
                <h4 className="font-medium mb-3">Current Status</h4>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <div className="font-medium">Monitoring</div>
                    <div className={`text-xs ${isMonitoring ? 'text-green-600' : 'text-gray-500'}`}>
                      {isMonitoring ? 'Active' : 'Inactive'}
                    </div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <div className="font-medium">Sessions Today</div>
                    <div className="text-xs text-gray-600">{focusSessions.length}</div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <div className="font-medium">Active Goals</div>
                    <div className="text-xs text-gray-600">{goals.filter(g => g.isActive).length}</div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <div className="font-medium">Notifications</div>
                    <div className="text-xs text-gray-600">{notifications.length}</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
