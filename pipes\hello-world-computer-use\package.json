{"name": "desktop-to-table", "version": "0.0.4", "private": false, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ai-sdk/openai": "^1.3.2", "@anthropic-ai/sdk": "^0.39.0", "@assistant-ui/react": "^0.8.6", "@assistant-ui/react-ai-sdk": "^0.8.0", "@assistant-ui/react-markdown": "^0.8.0", "@modelcontextprotocol/sdk": "^1.7.0", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-tooltip": "^1.1.8", "@screenpipe/browser": "^0.1.40", "@screenpipe/js": "^1.0.21", "@types/express": "^5.0.1", "@types/lodash": "^4.17.16", "@types/nodemailer": "^6.4.17", "ai": "^4.2.5", "cheerio": "^1.0.0", "class-variance-authority": "^0.7.1", "cmdk": "^1.1.1", "dotenv": "^16.4.7", "express": "^4.21.2", "framer-motion": "^12.4.7", "js-levenshtein": "^1.1.6", "lodash": "^4.17.21", "lucide-react": "^0.484.0", "next": "^15.1.7", "next-themes": "^0.4.6", "nodemailer": "^6.9.16", "ollama-ai-provider": "^1.2.0", "openai": "^4.76.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^9.0.1", "react-syntax-highlighter": "^15.5.0", "readline": "^1.3.0", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "sonner": "^2.0.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@tailwindcss/typography": "^0.5.13", "@types/js-levenshtein": "^1.1.3", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-syntax-highlighter": "^15.5.13", "bun-types": "latest", "eslint": "^8", "eslint-config-next": "14.2.4", "postcss": "^8", "raw-loader": "^4.0.2", "tailwindcss": "^3.4.1", "typescript": "^5"}}