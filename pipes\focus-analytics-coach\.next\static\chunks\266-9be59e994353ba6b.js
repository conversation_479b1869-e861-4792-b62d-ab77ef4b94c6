"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[266],{5845:(e,r,o)=>{o.d(r,{i:()=>a});var t=o(12115),n=o(39033);function a({prop:e,defaultProp:r,onChange:o=()=>{}}){let[a,l]=function({defaultProp:e,onChange:r}){let o=t.useState(e),[a]=o,l=t.useRef(a),s=(0,n.c)(r);return t.useEffect(()=>{l.current!==a&&(s(a),l.current=a)},[a,l,s]),o}({defaultProp:r,onChange:o}),s=void 0!==e,i=s?e:a,d=(0,n.c)(o);return[i,t.useCallback(r=>{if(s){let o="function"==typeof r?r(e):r;o!==e&&d(o)}else l(r)},[s,e,l,d])]}},6101:(e,r,o)=>{o.d(r,{s:()=>l,t:()=>a});var t=o(12115);function n(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}function a(...e){return r=>{let o=!1,t=e.map(e=>{let t=n(e,r);return o||"function"!=typeof t||(o=!0),t});if(o)return()=>{for(let r=0;r<t.length;r++){let o=t[r];"function"==typeof o?o():n(e[r],null)}}}}function l(...e){return t.useCallback(a(...e),e)}},19946:(e,r,o)=>{o.d(r,{A:()=>i});var t=o(12115);let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=function(){for(var e=arguments.length,r=Array(e),o=0;o<e;o++)r[o]=arguments[o];return r.filter((e,r,o)=>!!e&&""!==e.trim()&&o.indexOf(e)===r).join(" ").trim()};var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let s=(0,t.forwardRef)((e,r)=>{let{color:o="currentColor",size:n=24,strokeWidth:s=2,absoluteStrokeWidth:i,className:d="",children:c,iconNode:u,...m}=e;return(0,t.createElement)("svg",{ref:r,...l,width:n,height:n,stroke:o,strokeWidth:i?24*Number(s)/Number(n):s,className:a("lucide",d),...m},[...u.map(e=>{let[r,o]=e;return(0,t.createElement)(r,o)}),...Array.isArray(c)?c:[c]])}),i=(e,r)=>{let o=(0,t.forwardRef)((o,l)=>{let{className:i,...d}=o;return(0,t.createElement)(s,{ref:l,iconNode:r,className:a("lucide-".concat(n(e)),i),...d})});return o.displayName="".concat(e),o}},20029:e=>{e.exports=function(){function e(e,r,o,t,n){return e<r||o<r?e>o?o+1:e+1:t===n?r:r+1}return function(r,o){if(r===o)return 0;if(r.length>o.length){var t,n,a,l,s,i,d,c,u,m,p,f,b=r;r=o,o=b}for(var g=r.length,h=o.length;g>0&&r.charCodeAt(g-1)===o.charCodeAt(h-1);)g--,h--;for(var v=0;v<g&&r.charCodeAt(v)===o.charCodeAt(v);)v++;if(g-=v,h-=v,0===g||h<3)return h;var k=0,w=[];for(t=0;t<g;t++)w.push(t+1),w.push(r.charCodeAt(v+t));for(var x=w.length-1;k<h-3;)for(t=0,u=o.charCodeAt(v+(n=k)),m=o.charCodeAt(v+(a=k+1)),p=o.charCodeAt(v+(l=k+2)),f=o.charCodeAt(v+(s=k+3)),i=k+=4;t<x;t+=2)n=e(d=w[t],n,a,u,c=w[t+1]),a=e(n,a,l,m,c),l=e(a,l,s,p,c),i=e(l,s,i,f,c),w[t]=i,s=l,l=a,a=n,n=d;for(;k<h;)for(t=0,u=o.charCodeAt(v+(n=k)),i=++k;t<x;t+=2)d=w[t],w[t]=i=e(d,n,i,u,w[t+1]),n=d;return i}}()},28905:(e,r,o)=>{o.d(r,{C:()=>l});var t=o(12115),n=o(6101),a=o(52712),l=e=>{let{present:r,children:o}=e,l=function(e){var r,o;let[n,l]=t.useState(),i=t.useRef({}),d=t.useRef(e),c=t.useRef("none"),[u,m]=(r=e?"mounted":"unmounted",o={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},t.useReducer((e,r)=>{let t=o[e][r];return null!=t?t:e},r));return t.useEffect(()=>{let e=s(i.current);c.current="mounted"===u?e:"none"},[u]),(0,a.N)(()=>{let r=i.current,o=d.current;if(o!==e){let t=c.current,n=s(r);e?m("MOUNT"):"none"===n||(null==r?void 0:r.display)==="none"?m("UNMOUNT"):o&&t!==n?m("ANIMATION_OUT"):m("UNMOUNT"),d.current=e}},[e,m]),(0,a.N)(()=>{if(n){var e;let r;let o=null!==(e=n.ownerDocument.defaultView)&&void 0!==e?e:window,t=e=>{let t=s(i.current).includes(e.animationName);if(e.target===n&&t&&(m("ANIMATION_END"),!d.current)){let e=n.style.animationFillMode;n.style.animationFillMode="forwards",r=o.setTimeout(()=>{"forwards"===n.style.animationFillMode&&(n.style.animationFillMode=e)})}},a=e=>{e.target===n&&(c.current=s(i.current))};return n.addEventListener("animationstart",a),n.addEventListener("animationcancel",t),n.addEventListener("animationend",t),()=>{o.clearTimeout(r),n.removeEventListener("animationstart",a),n.removeEventListener("animationcancel",t),n.removeEventListener("animationend",t)}}m("ANIMATION_END")},[n,m]),{isPresent:["mounted","unmountSuspended"].includes(u),ref:t.useCallback(e=>{e&&(i.current=getComputedStyle(e)),l(e)},[])}}(r),i="function"==typeof o?o({present:l.isPresent}):t.Children.only(o),d=(0,n.s)(l.ref,function(e){var r,o;let t=null===(r=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===r?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(n=(t=null===(o=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===o?void 0:o.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(i));return"function"==typeof o||l.isPresent?t.cloneElement(i,{ref:d}):null};function s(e){return(null==e?void 0:e.animationName)||"none"}l.displayName="Presence"},39033:(e,r,o)=>{o.d(r,{c:()=>n});var t=o(12115);function n(e){let r=t.useRef(e);return t.useEffect(()=>{r.current=e}),t.useMemo(()=>(...e)=>r.current?.(...e),[])}},39688:(e,r,o)=>{o.d(r,{QP:()=>ed});let t=e=>{let r=s(e),{conflictingClassGroups:o,conflictingClassGroupModifiers:t}=e;return{getClassGroupId:e=>{let o=e.split("-");return""===o[0]&&1!==o.length&&o.shift(),n(o,r)||l(e)},getConflictingClassGroupIds:(e,r)=>{let n=o[e]||[];return r&&t[e]?[...n,...t[e]]:n}}},n=(e,r)=>{if(0===e.length)return r.classGroupId;let o=e[0],t=r.nextPart.get(o),a=t?n(e.slice(1),t):void 0;if(a)return a;if(0===r.validators.length)return;let l=e.join("-");return r.validators.find(({validator:e})=>e(l))?.classGroupId},a=/^\[(.+)\]$/,l=e=>{if(a.test(e)){let r=a.exec(e)[1],o=r?.substring(0,r.indexOf(":"));if(o)return"arbitrary.."+o}},s=e=>{let{theme:r,classGroups:o}=e,t={nextPart:new Map,validators:[]};for(let e in o)i(o[e],t,e,r);return t},i=(e,r,o,t)=>{e.forEach(e=>{if("string"==typeof e){(""===e?r:d(r,e)).classGroupId=o;return}if("function"==typeof e){if(c(e)){i(e(t),r,o,t);return}r.validators.push({validator:e,classGroupId:o});return}Object.entries(e).forEach(([e,n])=>{i(n,d(r,e),o,t)})})},d=(e,r)=>{let o=e;return r.split("-").forEach(e=>{o.nextPart.has(e)||o.nextPart.set(e,{nextPart:new Map,validators:[]}),o=o.nextPart.get(e)}),o},c=e=>e.isThemeGetter,u=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let r=0,o=new Map,t=new Map,n=(n,a)=>{o.set(n,a),++r>e&&(r=0,t=o,o=new Map)};return{get(e){let r=o.get(e);return void 0!==r?r:void 0!==(r=t.get(e))?(n(e,r),r):void 0},set(e,r){o.has(e)?o.set(e,r):n(e,r)}}},m=e=>{let{prefix:r,experimentalParseClassName:o}=e,t=e=>{let r;let o=[],t=0,n=0,a=0;for(let l=0;l<e.length;l++){let s=e[l];if(0===t&&0===n){if(":"===s){o.push(e.slice(a,l)),a=l+1;continue}if("/"===s){r=l;continue}}"["===s?t++:"]"===s?t--:"("===s?n++:")"===s&&n--}let l=0===o.length?e:e.substring(a),s=p(l);return{modifiers:o,hasImportantModifier:s!==l,baseClassName:s,maybePostfixModifierPosition:r&&r>a?r-a:void 0}};if(r){let e=r+":",o=t;t=r=>r.startsWith(e)?o(r.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:r,maybePostfixModifierPosition:void 0}}if(o){let e=t;t=r=>o({className:r,parseClassName:e})}return t},p=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,f=e=>{let r=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let o=[],t=[];return e.forEach(e=>{"["===e[0]||r[e]?(o.push(...t.sort(),e),t=[]):t.push(e)}),o.push(...t.sort()),o}},b=e=>({cache:u(e.cacheSize),parseClassName:m(e),sortModifiers:f(e),...t(e)}),g=/\s+/,h=(e,r)=>{let{parseClassName:o,getClassGroupId:t,getConflictingClassGroupIds:n,sortModifiers:a}=r,l=[],s=e.trim().split(g),i="";for(let e=s.length-1;e>=0;e-=1){let r=s[e],{isExternal:d,modifiers:c,hasImportantModifier:u,baseClassName:m,maybePostfixModifierPosition:p}=o(r);if(d){i=r+(i.length>0?" "+i:i);continue}let f=!!p,b=t(f?m.substring(0,p):m);if(!b){if(!f||!(b=t(m))){i=r+(i.length>0?" "+i:i);continue}f=!1}let g=a(c).join(":"),h=u?g+"!":g,v=h+b;if(l.includes(v))continue;l.push(v);let k=n(b,f);for(let e=0;e<k.length;++e){let r=k[e];l.push(h+r)}i=r+(i.length>0?" "+i:i)}return i};function v(){let e,r,o=0,t="";for(;o<arguments.length;)(e=arguments[o++])&&(r=k(e))&&(t&&(t+=" "),t+=r);return t}let k=e=>{let r;if("string"==typeof e)return e;let o="";for(let t=0;t<e.length;t++)e[t]&&(r=k(e[t]))&&(o&&(o+=" "),o+=r);return o},w=e=>{let r=r=>r[e]||[];return r.isThemeGetter=!0,r},x=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,y=/^\((?:(\w[\w-]*):)?(.+)\)$/i,z=/^\d+\/\d+$/,N=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,C=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,M=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,A=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,j=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,E=e=>z.test(e),O=e=>!!e&&!Number.isNaN(Number(e)),P=e=>!!e&&Number.isInteger(Number(e)),T=e=>e.endsWith("%")&&O(e.slice(0,-1)),I=e=>N.test(e),$=()=>!0,_=e=>C.test(e)&&!M.test(e),S=()=>!1,R=e=>A.test(e),U=e=>j.test(e),G=e=>!W(e)&&!Q(e),L=e=>ee(e,en,S),W=e=>x.test(e),D=e=>ee(e,ea,_),F=e=>ee(e,el,O),q=e=>ee(e,eo,S),V=e=>ee(e,et,U),B=e=>ee(e,ei,R),Q=e=>y.test(e),Z=e=>er(e,ea),H=e=>er(e,es),J=e=>er(e,eo),K=e=>er(e,en),X=e=>er(e,et),Y=e=>er(e,ei,!0),ee=(e,r,o)=>{let t=x.exec(e);return!!t&&(t[1]?r(t[1]):o(t[2]))},er=(e,r,o=!1)=>{let t=y.exec(e);return!!t&&(t[1]?r(t[1]):o)},eo=e=>"position"===e||"percentage"===e,et=e=>"image"===e||"url"===e,en=e=>"length"===e||"size"===e||"bg-size"===e,ea=e=>"length"===e,el=e=>"number"===e,es=e=>"family-name"===e,ei=e=>"shadow"===e;Symbol.toStringTag;let ed=function(e,...r){let o,t,n;let a=function(s){return t=(o=b(r.reduce((e,r)=>r(e),e()))).cache.get,n=o.cache.set,a=l,l(s)};function l(e){let r=t(e);if(r)return r;let a=h(e,o);return n(e,a),a}return function(){return a(v.apply(null,arguments))}}(()=>{let e=w("color"),r=w("font"),o=w("text"),t=w("font-weight"),n=w("tracking"),a=w("leading"),l=w("breakpoint"),s=w("container"),i=w("spacing"),d=w("radius"),c=w("shadow"),u=w("inset-shadow"),m=w("text-shadow"),p=w("drop-shadow"),f=w("blur"),b=w("perspective"),g=w("aspect"),h=w("ease"),v=w("animate"),k=()=>["auto","avoid","all","avoid-page","page","left","right","column"],x=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],y=()=>[...x(),Q,W],z=()=>["auto","hidden","clip","visible","scroll"],N=()=>["auto","contain","none"],C=()=>[Q,W,i],M=()=>[E,"full","auto",...C()],A=()=>[P,"none","subgrid",Q,W],j=()=>["auto",{span:["full",P,Q,W]},P,Q,W],_=()=>[P,"auto",Q,W],S=()=>["auto","min","max","fr",Q,W],R=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],U=()=>["start","end","center","stretch","center-safe","end-safe"],ee=()=>["auto",...C()],er=()=>[E,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...C()],eo=()=>[e,Q,W],et=()=>[...x(),J,q,{position:[Q,W]}],en=()=>["no-repeat",{repeat:["","x","y","space","round"]}],ea=()=>["auto","cover","contain",K,L,{size:[Q,W]}],el=()=>[T,Z,D],es=()=>["","none","full",d,Q,W],ei=()=>["",O,Z,D],ed=()=>["solid","dashed","dotted","double"],ec=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],eu=()=>[O,T,J,q],em=()=>["","none",f,Q,W],ep=()=>["none",O,Q,W],ef=()=>["none",O,Q,W],eb=()=>[O,Q,W],eg=()=>[E,"full",...C()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[I],breakpoint:[I],color:[$],container:[I],"drop-shadow":[I],ease:["in","out","in-out"],font:[G],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[I],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[I],shadow:[I],spacing:["px",O],text:[I],"text-shadow":[I],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",E,W,Q,g]}],container:["container"],columns:[{columns:[O,W,Q,s]}],"break-after":[{"break-after":k()}],"break-before":[{"break-before":k()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:y()}],overflow:[{overflow:z()}],"overflow-x":[{"overflow-x":z()}],"overflow-y":[{"overflow-y":z()}],overscroll:[{overscroll:N()}],"overscroll-x":[{"overscroll-x":N()}],"overscroll-y":[{"overscroll-y":N()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:M()}],"inset-x":[{"inset-x":M()}],"inset-y":[{"inset-y":M()}],start:[{start:M()}],end:[{end:M()}],top:[{top:M()}],right:[{right:M()}],bottom:[{bottom:M()}],left:[{left:M()}],visibility:["visible","invisible","collapse"],z:[{z:[P,"auto",Q,W]}],basis:[{basis:[E,"full","auto",s,...C()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[O,E,"auto","initial","none",W]}],grow:[{grow:["",O,Q,W]}],shrink:[{shrink:["",O,Q,W]}],order:[{order:[P,"first","last","none",Q,W]}],"grid-cols":[{"grid-cols":A()}],"col-start-end":[{col:j()}],"col-start":[{"col-start":_()}],"col-end":[{"col-end":_()}],"grid-rows":[{"grid-rows":A()}],"row-start-end":[{row:j()}],"row-start":[{"row-start":_()}],"row-end":[{"row-end":_()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":S()}],"auto-rows":[{"auto-rows":S()}],gap:[{gap:C()}],"gap-x":[{"gap-x":C()}],"gap-y":[{"gap-y":C()}],"justify-content":[{justify:[...R(),"normal"]}],"justify-items":[{"justify-items":[...U(),"normal"]}],"justify-self":[{"justify-self":["auto",...U()]}],"align-content":[{content:["normal",...R()]}],"align-items":[{items:[...U(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...U(),{baseline:["","last"]}]}],"place-content":[{"place-content":R()}],"place-items":[{"place-items":[...U(),"baseline"]}],"place-self":[{"place-self":["auto",...U()]}],p:[{p:C()}],px:[{px:C()}],py:[{py:C()}],ps:[{ps:C()}],pe:[{pe:C()}],pt:[{pt:C()}],pr:[{pr:C()}],pb:[{pb:C()}],pl:[{pl:C()}],m:[{m:ee()}],mx:[{mx:ee()}],my:[{my:ee()}],ms:[{ms:ee()}],me:[{me:ee()}],mt:[{mt:ee()}],mr:[{mr:ee()}],mb:[{mb:ee()}],ml:[{ml:ee()}],"space-x":[{"space-x":C()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":C()}],"space-y-reverse":["space-y-reverse"],size:[{size:er()}],w:[{w:[s,"screen",...er()]}],"min-w":[{"min-w":[s,"screen","none",...er()]}],"max-w":[{"max-w":[s,"screen","none","prose",{screen:[l]},...er()]}],h:[{h:["screen","lh",...er()]}],"min-h":[{"min-h":["screen","lh","none",...er()]}],"max-h":[{"max-h":["screen","lh",...er()]}],"font-size":[{text:["base",o,Z,D]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[t,Q,F]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",T,W]}],"font-family":[{font:[H,W,r]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[n,Q,W]}],"line-clamp":[{"line-clamp":[O,"none",Q,F]}],leading:[{leading:[a,...C()]}],"list-image":[{"list-image":["none",Q,W]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",Q,W]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:eo()}],"text-color":[{text:eo()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ed(),"wavy"]}],"text-decoration-thickness":[{decoration:[O,"from-font","auto",Q,D]}],"text-decoration-color":[{decoration:eo()}],"underline-offset":[{"underline-offset":[O,"auto",Q,W]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:C()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",Q,W]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",Q,W]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:et()}],"bg-repeat":[{bg:en()}],"bg-size":[{bg:ea()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},P,Q,W],radial:["",Q,W],conic:[P,Q,W]},X,V]}],"bg-color":[{bg:eo()}],"gradient-from-pos":[{from:el()}],"gradient-via-pos":[{via:el()}],"gradient-to-pos":[{to:el()}],"gradient-from":[{from:eo()}],"gradient-via":[{via:eo()}],"gradient-to":[{to:eo()}],rounded:[{rounded:es()}],"rounded-s":[{"rounded-s":es()}],"rounded-e":[{"rounded-e":es()}],"rounded-t":[{"rounded-t":es()}],"rounded-r":[{"rounded-r":es()}],"rounded-b":[{"rounded-b":es()}],"rounded-l":[{"rounded-l":es()}],"rounded-ss":[{"rounded-ss":es()}],"rounded-se":[{"rounded-se":es()}],"rounded-ee":[{"rounded-ee":es()}],"rounded-es":[{"rounded-es":es()}],"rounded-tl":[{"rounded-tl":es()}],"rounded-tr":[{"rounded-tr":es()}],"rounded-br":[{"rounded-br":es()}],"rounded-bl":[{"rounded-bl":es()}],"border-w":[{border:ei()}],"border-w-x":[{"border-x":ei()}],"border-w-y":[{"border-y":ei()}],"border-w-s":[{"border-s":ei()}],"border-w-e":[{"border-e":ei()}],"border-w-t":[{"border-t":ei()}],"border-w-r":[{"border-r":ei()}],"border-w-b":[{"border-b":ei()}],"border-w-l":[{"border-l":ei()}],"divide-x":[{"divide-x":ei()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":ei()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ed(),"hidden","none"]}],"divide-style":[{divide:[...ed(),"hidden","none"]}],"border-color":[{border:eo()}],"border-color-x":[{"border-x":eo()}],"border-color-y":[{"border-y":eo()}],"border-color-s":[{"border-s":eo()}],"border-color-e":[{"border-e":eo()}],"border-color-t":[{"border-t":eo()}],"border-color-r":[{"border-r":eo()}],"border-color-b":[{"border-b":eo()}],"border-color-l":[{"border-l":eo()}],"divide-color":[{divide:eo()}],"outline-style":[{outline:[...ed(),"none","hidden"]}],"outline-offset":[{"outline-offset":[O,Q,W]}],"outline-w":[{outline:["",O,Z,D]}],"outline-color":[{outline:eo()}],shadow:[{shadow:["","none",c,Y,B]}],"shadow-color":[{shadow:eo()}],"inset-shadow":[{"inset-shadow":["none",u,Y,B]}],"inset-shadow-color":[{"inset-shadow":eo()}],"ring-w":[{ring:ei()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:eo()}],"ring-offset-w":[{"ring-offset":[O,D]}],"ring-offset-color":[{"ring-offset":eo()}],"inset-ring-w":[{"inset-ring":ei()}],"inset-ring-color":[{"inset-ring":eo()}],"text-shadow":[{"text-shadow":["none",m,Y,B]}],"text-shadow-color":[{"text-shadow":eo()}],opacity:[{opacity:[O,Q,W]}],"mix-blend":[{"mix-blend":[...ec(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ec()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[O]}],"mask-image-linear-from-pos":[{"mask-linear-from":eu()}],"mask-image-linear-to-pos":[{"mask-linear-to":eu()}],"mask-image-linear-from-color":[{"mask-linear-from":eo()}],"mask-image-linear-to-color":[{"mask-linear-to":eo()}],"mask-image-t-from-pos":[{"mask-t-from":eu()}],"mask-image-t-to-pos":[{"mask-t-to":eu()}],"mask-image-t-from-color":[{"mask-t-from":eo()}],"mask-image-t-to-color":[{"mask-t-to":eo()}],"mask-image-r-from-pos":[{"mask-r-from":eu()}],"mask-image-r-to-pos":[{"mask-r-to":eu()}],"mask-image-r-from-color":[{"mask-r-from":eo()}],"mask-image-r-to-color":[{"mask-r-to":eo()}],"mask-image-b-from-pos":[{"mask-b-from":eu()}],"mask-image-b-to-pos":[{"mask-b-to":eu()}],"mask-image-b-from-color":[{"mask-b-from":eo()}],"mask-image-b-to-color":[{"mask-b-to":eo()}],"mask-image-l-from-pos":[{"mask-l-from":eu()}],"mask-image-l-to-pos":[{"mask-l-to":eu()}],"mask-image-l-from-color":[{"mask-l-from":eo()}],"mask-image-l-to-color":[{"mask-l-to":eo()}],"mask-image-x-from-pos":[{"mask-x-from":eu()}],"mask-image-x-to-pos":[{"mask-x-to":eu()}],"mask-image-x-from-color":[{"mask-x-from":eo()}],"mask-image-x-to-color":[{"mask-x-to":eo()}],"mask-image-y-from-pos":[{"mask-y-from":eu()}],"mask-image-y-to-pos":[{"mask-y-to":eu()}],"mask-image-y-from-color":[{"mask-y-from":eo()}],"mask-image-y-to-color":[{"mask-y-to":eo()}],"mask-image-radial":[{"mask-radial":[Q,W]}],"mask-image-radial-from-pos":[{"mask-radial-from":eu()}],"mask-image-radial-to-pos":[{"mask-radial-to":eu()}],"mask-image-radial-from-color":[{"mask-radial-from":eo()}],"mask-image-radial-to-color":[{"mask-radial-to":eo()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":x()}],"mask-image-conic-pos":[{"mask-conic":[O]}],"mask-image-conic-from-pos":[{"mask-conic-from":eu()}],"mask-image-conic-to-pos":[{"mask-conic-to":eu()}],"mask-image-conic-from-color":[{"mask-conic-from":eo()}],"mask-image-conic-to-color":[{"mask-conic-to":eo()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:et()}],"mask-repeat":[{mask:en()}],"mask-size":[{mask:ea()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",Q,W]}],filter:[{filter:["","none",Q,W]}],blur:[{blur:em()}],brightness:[{brightness:[O,Q,W]}],contrast:[{contrast:[O,Q,W]}],"drop-shadow":[{"drop-shadow":["","none",p,Y,B]}],"drop-shadow-color":[{"drop-shadow":eo()}],grayscale:[{grayscale:["",O,Q,W]}],"hue-rotate":[{"hue-rotate":[O,Q,W]}],invert:[{invert:["",O,Q,W]}],saturate:[{saturate:[O,Q,W]}],sepia:[{sepia:["",O,Q,W]}],"backdrop-filter":[{"backdrop-filter":["","none",Q,W]}],"backdrop-blur":[{"backdrop-blur":em()}],"backdrop-brightness":[{"backdrop-brightness":[O,Q,W]}],"backdrop-contrast":[{"backdrop-contrast":[O,Q,W]}],"backdrop-grayscale":[{"backdrop-grayscale":["",O,Q,W]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[O,Q,W]}],"backdrop-invert":[{"backdrop-invert":["",O,Q,W]}],"backdrop-opacity":[{"backdrop-opacity":[O,Q,W]}],"backdrop-saturate":[{"backdrop-saturate":[O,Q,W]}],"backdrop-sepia":[{"backdrop-sepia":["",O,Q,W]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":C()}],"border-spacing-x":[{"border-spacing-x":C()}],"border-spacing-y":[{"border-spacing-y":C()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",Q,W]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[O,"initial",Q,W]}],ease:[{ease:["linear","initial",h,Q,W]}],delay:[{delay:[O,Q,W]}],animate:[{animate:["none",v,Q,W]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[b,Q,W]}],"perspective-origin":[{"perspective-origin":y()}],rotate:[{rotate:ep()}],"rotate-x":[{"rotate-x":ep()}],"rotate-y":[{"rotate-y":ep()}],"rotate-z":[{"rotate-z":ep()}],scale:[{scale:ef()}],"scale-x":[{"scale-x":ef()}],"scale-y":[{"scale-y":ef()}],"scale-z":[{"scale-z":ef()}],"scale-3d":["scale-3d"],skew:[{skew:eb()}],"skew-x":[{"skew-x":eb()}],"skew-y":[{"skew-y":eb()}],transform:[{transform:[Q,W,"","none","gpu","cpu"]}],"transform-origin":[{origin:y()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:eg()}],"translate-x":[{"translate-x":eg()}],"translate-y":[{"translate-y":eg()}],"translate-z":[{"translate-z":eg()}],"translate-none":["translate-none"],accent:[{accent:eo()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:eo()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",Q,W]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":C()}],"scroll-mx":[{"scroll-mx":C()}],"scroll-my":[{"scroll-my":C()}],"scroll-ms":[{"scroll-ms":C()}],"scroll-me":[{"scroll-me":C()}],"scroll-mt":[{"scroll-mt":C()}],"scroll-mr":[{"scroll-mr":C()}],"scroll-mb":[{"scroll-mb":C()}],"scroll-ml":[{"scroll-ml":C()}],"scroll-p":[{"scroll-p":C()}],"scroll-px":[{"scroll-px":C()}],"scroll-py":[{"scroll-py":C()}],"scroll-ps":[{"scroll-ps":C()}],"scroll-pe":[{"scroll-pe":C()}],"scroll-pt":[{"scroll-pt":C()}],"scroll-pr":[{"scroll-pr":C()}],"scroll-pb":[{"scroll-pb":C()}],"scroll-pl":[{"scroll-pl":C()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",Q,W]}],fill:[{fill:["none",...eo()]}],"stroke-w":[{stroke:[O,Z,D,F]}],stroke:[{stroke:["none",...eo()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}})},46081:(e,r,o)=>{o.d(r,{A:()=>a});var t=o(12115),n=o(95155);function a(e,r=[]){let o=[],l=()=>{let r=o.map(e=>t.createContext(e));return function(o){let n=o?.[e]||r;return t.useMemo(()=>({[`__scope${e}`]:{...o,[e]:n}}),[o,n])}};return l.scopeName=e,[function(r,a){let l=t.createContext(a),s=o.length;o=[...o,a];let i=r=>{let{scope:o,children:a,...i}=r,d=o?.[e]?.[s]||l,c=t.useMemo(()=>i,Object.values(i));return(0,n.jsx)(d.Provider,{value:c,children:a})};return i.displayName=r+"Provider",[i,function(o,n){let i=n?.[e]?.[s]||l,d=t.useContext(i);if(d)return d;if(void 0!==a)return a;throw Error(`\`${o}\` must be used within \`${r}\``)}]},function(...e){let r=e[0];if(1===e.length)return r;let o=()=>{let o=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let n=o.reduce((r,{useScope:o,scopeName:t})=>{let n=o(e)[`__scope${t}`];return{...r,...n}},{});return t.useMemo(()=>({[`__scope${r.scopeName}`]:n}),[n])}};return o.scopeName=r.scopeName,o}(l,...r)]}},52596:(e,r,o)=>{function t(){for(var e,r,o=0,t="",n=arguments.length;o<n;o++)(e=arguments[o])&&(r=function e(r){var o,t,n="";if("string"==typeof r||"number"==typeof r)n+=r;else if("object"==typeof r){if(Array.isArray(r)){var a=r.length;for(o=0;o<a;o++)r[o]&&(t=e(r[o]))&&(n&&(n+=" "),n+=t)}else for(t in r)r[t]&&(n&&(n+=" "),n+=t)}return n}(e))&&(t&&(t+=" "),t+=r);return t}o.d(r,{$:()=>t,A:()=>n});let n=t},52712:(e,r,o)=>{o.d(r,{N:()=>n});var t=o(12115),n=globalThis?.document?t.useLayoutEffect:()=>{}},74466:(e,r,o)=>{o.d(r,{F:()=>l});var t=o(52596);let n=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,a=t.$,l=(e,r)=>o=>{var t;if((null==r?void 0:r.variants)==null)return a(e,null==o?void 0:o.class,null==o?void 0:o.className);let{variants:l,defaultVariants:s}=r,i=Object.keys(l).map(e=>{let r=null==o?void 0:o[e],t=null==s?void 0:s[e];if(null===r)return null;let a=n(r)||n(t);return l[e][a]}),d=o&&Object.entries(o).reduce((e,r)=>{let[o,t]=r;return void 0===t||(e[o]=t),e},{});return a(e,i,null==r?void 0:null===(t=r.compoundVariants)||void 0===t?void 0:t.reduce((e,r)=>{let{class:o,className:t,...n}=r;return Object.entries(n).every(e=>{let[r,o]=e;return Array.isArray(o)?o.includes({...s,...d}[r]):({...s,...d})[r]===o})?[...e,o,t]:e},[]),null==o?void 0:o.class,null==o?void 0:o.className)}},85185:(e,r,o)=>{o.d(r,{m:()=>t});function t(e,r,{checkForDefaultPrevented:o=!0}={}){return function(t){if(e?.(t),!1===o||!t.defaultPrevented)return r?.(t)}}}}]);