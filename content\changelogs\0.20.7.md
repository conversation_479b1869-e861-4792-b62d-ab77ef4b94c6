Based on the provided commits, here is the changelog for the new Screenpipe update:

### **New Features:**
- **Multi-select enhancements:** Added functionality to improve the multi-selection process within the application.
- **Used Ollama API to fetch models:** Integrated Ollama API to enhance model retrieval capabilities.

### **Improvements:**
- **Make application binary smaller:** Optimized the application binary to reduce size, improving download and installation speed.

### **Fixes:**
- **Fixed Deepgram integration issue:** Resolved a problem with the Deepgram integration in Screenpipe Cloud.
- **Fixed Windows NSIS error:** Addressed an error with the Windows NSIS installer.
- **Fixed Obsidian pipe cron functionality:** Corrected issues with the cron job functionality for the Obsidian pipe.

#### **Full Changelog:** [b914d..36866](https://github.com/mediar-ai/screenpipe/compare/b914d..36866)

