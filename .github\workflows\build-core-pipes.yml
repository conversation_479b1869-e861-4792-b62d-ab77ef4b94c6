name: build and release pipes

on:
  push:
    paths:
      - 'pipes/**' # or this workflow change
      - '.github/workflows/build-core-pipes.yml'
    branches:
      - main

jobs:
  build-pipes:
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v4
      
      - name: setup node
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          
      - name: setup bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: 1.1.43
          
      - name: get core pipes list
        id: core-pipes
        run: |
          PIPES=$(ls -d pipes/* | grep -v "archive" | xargs -n 1 basename | jq -R -s -c 'split("\n")[:-1]')
          echo "pipes=$PIPES" >> $GITHUB_OUTPUT

      - name: build pipes
        working-directory: pipes
        run: |
          for pipe in pipes/*; do
            if [ -d "$pipe" ]; then
              echo "building $(basename $pipe)..."
              cd "$pipe"
              
              bun install
              bun run build
              
            fi
          done
          

   