Based on the provided commits, here is the changelog for the new Screenpipe update:

### **New Features:**
- **Added H1 and Pulse Badge:** Introduced new visual indicators to enhance user experience.

### **Fixes:**
- **Fixed ffmpeg shipping issue:** Ensured that the application ships with the necessary ffmpeg components.
- **Fixed .screenpipe folder upload issue:** Addressed the problem of the .screenpipe folder not being uploaded correctly.
- **Fixed tray icon behavior:** Resolved issue with the tray icon not changing on failed health checks.
- **Fixed headphones/AirPods configuration issue:** Corrected configuration settings for headphones and AirPods to ensure proper functionality.
- **Updated CONTRIBUTING.md for Windows:** Fixed inaccuracies in the Windows section of the contributing guidelines.

This summary highlights the valuable updates and fixes for end-users.

#### **Full Changelog:** [ea5fa..ef7c7](https://github.com/mediar-ai/screenpipe/compare/ea5fa..ef7c7)

