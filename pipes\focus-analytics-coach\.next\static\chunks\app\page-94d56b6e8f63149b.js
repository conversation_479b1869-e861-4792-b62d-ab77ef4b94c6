(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{4530:()=>{},8108:()=>{},18590:()=>{},30046:(e,t,i)=>{Promise.resolve().then(i.bind(i,86543))},41234:()=>{},53999:(e,t,i)=>{"use strict";i.d(t,{cn:()=>n});var s=i(52596),a=i(39688);function n(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];return(0,a.QP)((0,s.$)(t))}i(20029)},80551:()=>{},85817:()=>{},86543:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>eT});var s=i(95155),a=i(12115),n=i(34477);let r=(0,n.createServerReference)("00689101726a740ae1b809750642788b6cced5a9ee",n.callServer,void 0,n.findSourceMapURL,"getScreenpipeAppSettings"),o=(0,n.createServerReference)("40d1cc6778dd0e335ea346a2af0964e84396a12af9",n.callServer,void 0,n.findSourceMapURL,"updateScreenpipeAppSettings"),c={exampleSetting:"default value"};class l{getStore(){return this.store}subscribe(e){return this.listeners.add(e),()=>this.listeners.delete(e)}notify(){this.listeners.forEach(e=>e())}async setGlobalSettings(e){this.store.globalSettings=e,this.notify()}async setPipeSettings(e,t){this.store.pipeSettings[e]=t,this.notify()}async loadGlobalSettings(){try{let e=await r();return this.setGlobalSettings(e),e}catch(e){return console.error("failed to load global settings:",e),null}}async updateGlobalSettings(e){try{let t={...await r(),...e};return await o(t),this.setGlobalSettings(t),this.notify(),!0}catch(e){return console.error("failed to update global settings:",e),!1}}async loadPipeSettings(e){try{var t;let i=await r(),s={...c,...null===(t=i.customSettings)||void 0===t?void 0:t[e]};return this.setPipeSettings(e,s),s}catch(e){return console.error("failed to load pipe settings:",e),null}}async updatePipeSettings(e,t){try{var i,s;let a=await r(),n={...a,customSettings:{...a.customSettings||{},[e]:{...(null===(i=a.customSettings)||void 0===i?void 0:i[e])||{},...t}}};return await o(n),this.setGlobalSettings(n),this.setPipeSettings(e,{...(null===(s=a.customSettings)||void 0===s?void 0:s[e])||{},...t}),!0}catch(e){return console.error("failed to update pipe settings:",e),!1}}getPreset(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"aiPresetId";try{var i,s,a,n;let r;let o=null===(i=this.store.pipeSettings[e])||void 0===i?void 0:i[t],c=this.store.globalSettings;if(o&&(r=null==c?void 0:null===(a=c.aiPresets)||void 0===a?void 0:a.find(e=>e.id===o)),r||(r=null==c?void 0:null===(n=c.aiPresets)||void 0===n?void 0:n.find(e=>e.defaultPreset)),!r)return;let l="provider"in r&&"screenpipe-cloud"===r.provider?(null==c?void 0:null===(s=c.user)||void 0===s?void 0:s.token)||"":"provider"in r&&"apiKey"in r&&r.apiKey||"";return{id:r.id,maxContextChars:r.maxContextChars,url:r.url,model:r.model,defaultPreset:r.defaultPreset,prompt:r.prompt,provider:r.provider,apiKey:l}}catch(e){console.error("failed to get preset:",e);return}}constructor(){this.store={globalSettings:null,pipeSettings:{}},this.listeners=new Set}}let d=new l,u=(0,a.createContext)(void 0);function m(e){let{children:t}=e,i=function(){let[e,t]=(0,a.useState)(d.getStore().globalSettings),[i,s]=(0,a.useState)(!0);return(0,a.useEffect)(()=>{(async()=>{s(!0),await d.loadGlobalSettings(),s(!1)})();let e=d.subscribe(()=>{t(d.getStore().globalSettings)});return()=>{e()}},[]),{settings:e,updateSettings:async e=>d.updateGlobalSettings(e),loading:i}}();return console.log("settings provider initialized with data:",i.loading?"loading...":"loaded"),(0,s.jsx)(u.Provider,{value:i,children:t})}function h(e){let{children:t}=e,[i,n]=(0,a.useState)(!1);return((0,a.useEffect)(()=>{n(!0)},[]),i)?(0,s.jsx)(s.Fragment,{children:t}):null}var g=i(62418),p=i.n(g),f=i(53999);let y=a.forwardRef((e,t)=>{let{className:i,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,f.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",i),...a})});y.displayName="Card";let x=a.forwardRef((e,t)=>{let{className:i,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,f.cn)("flex flex-col space-y-1.5 p-6",i),...a})});x.displayName="CardHeader";let v=a.forwardRef((e,t)=>{let{className:i,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,f.cn)("text-2xl font-semibold leading-none tracking-tight",i),...a})});v.displayName="CardTitle";let w=a.forwardRef((e,t)=>{let{className:i,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,f.cn)("text-sm text-muted-foreground",i),...a})});w.displayName="CardDescription";let k=a.forwardRef((e,t)=>{let{className:i,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,f.cn)("p-6 pt-0",i),...a})});k.displayName="CardContent",a.forwardRef((e,t)=>{let{className:i,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,f.cn)("flex items-center p-6 pt-0",i),...a})}).displayName="CardFooter";var b=i(52832);let j=b.bL,S=a.forwardRef((e,t)=>{let{className:i,...a}=e;return(0,s.jsx)(b.B8,{ref:t,className:(0,f.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",i),...a})});S.displayName=b.B8.displayName;let N=a.forwardRef((e,t)=>{let{className:i,...a}=e;return(0,s.jsx)(b.l9,{ref:t,className:(0,f.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",i),...a})});N.displayName=b.l9.displayName;let T=a.forwardRef((e,t)=>{let{className:i,...a}=e;return(0,s.jsx)(b.UC,{ref:t,className:(0,f.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",i),...a})});T.displayName=b.UC.displayName;var P=i(99708),A=i(74466);let C=(0,A.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),M=a.forwardRef((e,t)=>{let{className:i,variant:a,size:n,asChild:r=!1,...o}=e,c=r?P.DX:"button";return(0,s.jsx)(c,{className:(0,f.cn)(C({variant:a,size:n,className:i})),ref:t,...o})});M.displayName="Button";let D=(0,A.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function R(e){let{className:t,variant:i,...a}=e;return(0,s.jsx)("div",{className:(0,f.cn)(D({variant:i}),t),...a})}var F=i(82178),z=i(85690),E=i(14186),I=i(33109),O=i(71539),B=i(23861),L=i(72713),W=i(49376),G=i(16785),H=i(381),_=i(47178),q=i(25727),Y=i(49067),U=i(90621);class K{async analyzeActivity(e){try{let t=await _.F.queryScreenpipe({startTime:e.start.toISOString(),endTime:e.end.toISOString(),contentType:"all",limit:1e3,includeFrames:!1});if(!(null==t?void 0:t.data))throw Error("No data received from Screenpipe");let i=this.processActivityData(t.data),s=this.calculateProductivityMetrics(i.focusSessions,i.contextSwitches,e.start);return{focusSessions:i.focusSessions,contextSwitches:i.contextSwitches,metrics:s}}catch(e){throw console.error("Error analyzing activity:",e),e}}processActivityData(e){let t=[],i=[],s=null,a=null;for(let n of e.sort((e,t)=>new Date(e.content.timestamp).getTime()-new Date(t.content.timestamp).getTime())){let e=new Date(n.content.timestamp),r="",o="";if("OCR"===n.type?(r=n.content.app_name||"",o=n.content.window_name||""):"UI"===n.type&&(r=n.content.app_name||"",o=n.content.window_name||""),r){if(a&&(a.app!==r||a.window!==o)){let n=(0,q.O)(e,a.timestamp);n>=this.config.contextSwitchWindow&&(i.push({id:(0,U.A)(),timestamp:e,fromApp:a.app,toApp:r,fromWindow:a.window,toWindow:o,duration:n,switchType:a.app!==r?"app":"window"}),s&&this.isDistractingSwitch(a.app,r)&&(this.finalizeSession(s,a.timestamp,t),s=null))}(!s||this.shouldStartNewSession(s,r,o,e))&&(s&&this.finalizeSession(s,e,t),s={id:(0,U.A)(),startTime:e,appName:r,windowName:o,taskCategory:this.categorizeTask(r,o),distractionCount:0,contextSwitches:0}),s&&(s.endTime=e,i.length>0&&i[i.length-1].timestamp>=s.startTime&&(s.contextSwitches=(s.contextSwitches||0)+1)),a={app:r,window:o,timestamp:e}}}return s&&a&&this.finalizeSession(s,a.timestamp,t),{focusSessions:t,contextSwitches:i}}finalizeSession(e,t,i){if(!e.startTime||!e.appName)return;let s=(0,Y.o)(t,e.startTime);if(s>=this.config.focusThreshold){let a=this.calculateFocusScore(s,e.contextSwitches||0,e.distractionCount||0);i.push({id:e.id,startTime:e.startTime,endTime:t,duration:s,appName:e.appName,windowName:e.windowName||"",taskCategory:e.taskCategory||"unknown",focusScore:a,distractionCount:e.distractionCount||0,contextSwitches:e.contextSwitches||0,productivity:this.getProductivityLevel(a)})}}calculateFocusScore(e,t,i){let s;return s=100-Math.min(5*t,30)-Math.min(10*i,40),e>=this.config.deepWorkMinimum&&(s+=10),Math.max(0,Math.min(100,s))}categorizeTask(e,t){let i="".concat(e," ").concat(t).toLowerCase();for(let[e,t]of Object.entries({"deep-work":["vscode","intellij","xcode","sublime","vim","emacs","figma","sketch"],communication:["slack","teams","discord","zoom","meet","skype","mail","outlook"],research:["chrome","firefox","safari","edge","browser","wikipedia","stackoverflow"],creative:["photoshop","illustrator","premiere","after effects","blender","canva"],administrative:["excel","sheets","word","docs","powerpoint","slides","notion"],learning:["coursera","udemy","youtube","khan academy","duolingo","anki"],entertainment:["netflix","youtube","spotify","twitch","gaming","steam"],social:["facebook","twitter","instagram","linkedin","reddit","tiktok"],distraction:["news","shopping","amazon","ebay","social media"]}))if(t.some(e=>i.includes(e)))return e;return"unknown"}isDistractingSwitch(e,t){return["vscode","intellij","figma","notion","excel"].some(t=>e.toLowerCase().includes(t))&&["chrome","firefox","safari","social","entertainment"].some(e=>t.toLowerCase().includes(e))}shouldStartNewSession(e,t,i,s){return!e.startTime||e.appName!==t&&(0,Y.o)(s,e.startTime)>=this.config.focusThreshold}getProductivityLevel(e){return e>=90?"very-high":e>=75?"high":e>=50?"medium":e>=25?"low":"very-low"}calculateProductivityMetrics(e,t,i){let s=e.reduce((e,t)=>e+t.duration,0),a=e.length>0?e.reduce((e,t)=>e+t.focusScore,0)/e.length:0,n=e.filter(e=>e.duration>=this.config.deepWorkMinimum).length,r=e.length>0?s/e.length:0,o=this.calculateHourlyProductivity(e),c=this.getMostProductiveHour(o),l=this.getLeastProductiveHour(o),d=this.calculateAppUsage(e),u=d.filter(e=>e.productivityScore>=70).sort((e,t)=>t.focusTime-e.focusTime).slice(0,5),m=d.filter(e=>e.productivityScore<50).sort((e,t)=>t.totalTime-e.totalTime).slice(0,5);return{date:i,totalFocusTime:s,totalDistractionTime:0,focusScore:a,contextSwitches:t.length,deepWorkSessions:n,averageSessionLength:r,mostProductiveHour:c,leastProductiveHour:l,topProductiveApps:u,topDistractingApps:m}}calculateHourlyProductivity(e){let t={};for(let e=0;e<24;e++)t[e]={totalTime:0,totalScore:0,count:0};e.forEach(e=>{let i=e.startTime.getHours();t[i].totalTime+=e.duration,t[i].totalScore+=e.focusScore,t[i].count+=1});let i={};for(let e=0;e<24;e++){let s=t[e];i[e]=s.count>0?s.totalScore/s.count:0}return i}getMostProductiveHour(e){return Object.entries(e).reduce((t,i)=>{let[s,a]=i;return a>e[t]?parseInt(s):t},0)}getLeastProductiveHour(e){return Object.entries(e).reduce((t,i)=>{let[s,a]=i;return a<e[t]?parseInt(s):t},0)}calculateAppUsage(e){let t={};return e.forEach(e=>{t[e.appName]||(t[e.appName]={totalTime:0,focusTime:0,sessionCount:0,totalScore:0});let i=t[e.appName];i.totalTime+=e.duration,i.sessionCount+=1,i.totalScore+=e.focusScore,e.focusScore>=70&&(i.focusTime+=e.duration)}),Object.entries(t).map(e=>{let[t,i]=e;return{appName:t,totalTime:i.totalTime,focusTime:i.focusTime,distractionTime:i.totalTime-i.focusTime,sessionCount:i.sessionCount,averageSessionLength:i.totalTime/i.sessionCount,productivityScore:i.totalScore/i.sessionCount}})}constructor(e){this.currentSession=null,this.contextSwitches=[],this.focusSessions=[],this.lastActivity=null,this.config=e}}class V{async startMonitoring(){!this.isMonitoring&&(this.isMonitoring=!0,console.log("Starting real-time productivity coaching..."),this.config.enableRealTimeCoaching&&this.startActivityMonitoring(),this.config.enableBreakReminders&&this.startBreakReminders(),this.config.enableGoalTracking&&this.startGoalTracking())}stopMonitoring(){this.isMonitoring=!1,console.log("Stopped productivity coaching")}onNotification(e){this.notificationCallbacks.push(e)}sendNotification(e){this.notifications.push(e),this.lastNotification=new Date,this.notificationCallbacks.forEach(t=>{try{t(e)}catch(e){console.error("Error in notification callback:",e)}}),"Notification"in window&&this.sendDesktopNotification(e)}async sendDesktopNotification(e){try{if("granted"===Notification.permission)new Notification(e.title,{body:e.message,icon:"/128x128.png",tag:e.type});else if("denied"!==Notification.permission){let t=await Notification.requestPermission();"granted"===t&&new Notification(e.title,{body:e.message,icon:"/128x128.png",tag:e.type})}}catch(e){console.error("Error sending desktop notification:",e)}}async startActivityMonitoring(){try{for await(let e of _.F.streamVision(!1)){if(!this.isMonitoring)break;await this.processVisionData(e)}}catch(e){console.error("Error in activity monitoring:",e),this.startPollingMonitoring()}}startPollingMonitoring(){let e=setInterval(async()=>{if(!this.isMonitoring){clearInterval(e);return}try{await this.checkCurrentActivity()}catch(e){console.error("Error in polling monitoring:",e)}},3e4)}async processVisionData(e){try{let t=e.appName||"",i=e.windowName||"",s=new Date;await this.detectFocusModeChanges(t,i,s),await this.checkForDistractions(t,i,s),this.updateCurrentSession(t,i,s)}catch(e){console.error("Error processing vision data:",e)}}async checkCurrentActivity(){try{let e=new Date,t=new Date(e.getTime()-3e5),i=await _.F.queryScreenpipe({startTime:t.toISOString(),endTime:e.toISOString(),contentType:"ocr",limit:10});if((null==i?void 0:i.data)&&i.data.length>0){let e=i.data[i.data.length-1];if("OCR"===e.type){let t=e.content.app_name||"",i=e.content.window_name||"",s=new Date(e.content.timestamp);await this.detectFocusModeChanges(t,i,s),await this.checkForDistractions(t,i,s),this.updateCurrentSession(t,i,s)}}}catch(e){console.error("Error checking current activity:",e)}}async detectFocusModeChanges(e,t,i){let s=this.isProductiveApp(e),a=this.isDistractingApp(e);if(!s||this.currentSession&&this.isProductiveApp(this.currentSession.appName)||this.sendNotification({id:(0,U.A)(),type:"focus-reminder",title:"Focus Mode Detected",message:"Great! You're starting focused work in ".concat(e,". Stay concentrated!"),timestamp:i,priority:"low",actionable:!1}),this.currentSession&&this.isProductiveApp(this.currentSession.appName)&&a){let t=(0,Y.o)(i,this.currentSession.startTime);t>=this.config.focusThreshold?this.sendNotification({id:(0,U.A)(),type:"session-complete",title:"Focus Session Complete",message:"Great job! You focused for ".concat(t," minutes. Consider taking a break."),timestamp:i,priority:"medium",actionable:!0,action:{label:"Take Break",callback:()=>this.suggestBreak()}}):this.sendNotification({id:(0,U.A)(),type:"distraction-alert",title:"Distraction Detected",message:"You switched to ".concat(e," after only ").concat(t," minutes. Try to maintain focus!"),timestamp:i,priority:"medium",actionable:!0,action:{label:"Return to Work",callback:()=>this.suggestReturnToWork()}})}}async checkForDistractions(e,t,i){if(!this.isDistractingApp(e))return;let s=(await this.getRecentActivity(15)).filter(e=>this.isDistractingApp(e.appName)).reduce((e,t)=>e+t.duration,0);s>=15&&this.sendNotification({id:(0,U.A)(),type:"distraction-alert",title:"Extended Distraction Detected",message:"You've been on distracting apps for ".concat(Math.round(s)," minutes. Time to refocus!"),timestamp:i,priority:"high",actionable:!0,action:{label:"Start Focus Session",callback:()=>this.suggestFocusSession()}})}updateCurrentSession(e,t,i){this.currentSession&&this.currentSession.appName===e?(this.currentSession.endTime=i,this.currentSession.duration=(0,Y.o)(i,this.currentSession.startTime)):this.currentSession={id:(0,U.A)(),startTime:i,endTime:i,duration:0,appName:e,windowName:t,taskCategory:this.categorizeApp(e),focusScore:0,distractionCount:0,contextSwitches:0,productivity:"medium"}}startBreakReminders(){let e=setInterval(()=>{if(!this.isMonitoring){clearInterval(e);return}this.checkBreakNeeds()},6e4*this.config.breakReminderInterval)}checkBreakNeeds(){if(!this.currentSession)return;let e=(0,Y.o)(new Date,this.currentSession.startTime);e>=120&&e%30==0?this.sendNotification({id:(0,U.A)(),type:"break-suggestion",title:"Long Session Break",message:"You've been working for ".concat(e," minutes. Take a 15-minute break!"),timestamp:new Date,priority:"medium",actionable:!0,action:{label:"Take Break",callback:()=>this.suggestBreak()}}):e>=60&&e%20==0&&this.sendNotification({id:(0,U.A)(),type:"break-suggestion",title:"Focus Break",message:"Great focus! Take a 5-minute break to recharge.",timestamp:new Date,priority:"low",actionable:!0,action:{label:"Take Break",callback:()=>this.suggestBreak()}})}startGoalTracking(){let e=setInterval(()=>{if(!this.isMonitoring){clearInterval(e);return}this.updateGoalProgress()},6e5)}async updateGoalProgress(){for(let e of this.goals.filter(e=>e.isActive)){let t=await this.calculateGoalProgress(e);if(t!==e.current){e.current=t;let i=t/e.target*100;i>=100?this.sendNotification({id:(0,U.A)(),type:"goal-progress",title:"Goal Achieved!",message:"Congratulations! You've achieved your goal: ".concat(e.name),timestamp:new Date,priority:"high",actionable:!1}):i>=75&&i<100&&this.sendNotification({id:(0,U.A)(),type:"goal-progress",title:"Almost There!",message:"You're ".concat(Math.round(i),"% towards your goal: ").concat(e.name),timestamp:new Date,priority:"medium",actionable:!1})}}}async calculateGoalProgress(e){let t=new Date,i=new Date(t.getFullYear(),t.getMonth(),t.getDate());try{let s=await _.F.queryScreenpipe({startTime:i.toISOString(),endTime:t.toISOString(),contentType:"all",limit:1e3});if(!(null==s?void 0:s.data))return e.current;switch(e.type){case"daily-focus-time":return this.calculateFocusTime(s.data);case"reduce-distractions":return this.calculateDistractionReduction(s.data);case"increase-deep-work":return this.calculateDeepWorkTime(s.data);default:return e.current}}catch(t){return console.error("Error calculating goal progress:",t),e.current}}isProductiveApp(e){return["vscode","intellij","xcode","figma","notion","excel","word"].some(t=>e.toLowerCase().includes(t))}isDistractingApp(e){return["facebook","twitter","instagram","youtube","netflix","reddit"].some(t=>e.toLowerCase().includes(t))}categorizeApp(e){return this.isProductiveApp(e)?"deep-work":this.isDistractingApp(e)?"distraction":"unknown"}async getRecentActivity(e){return[]}calculateFocusTime(e){return 0}calculateDistractionReduction(e){return 0}calculateDeepWorkTime(e){return 0}suggestBreak(){console.log("Suggesting break activities...")}suggestReturnToWork(){console.log("Suggesting return to productive work...")}suggestFocusSession(){console.log("Suggesting start of focus session...")}addGoal(e){this.goals.push({...e,id:(0,U.A)(),createdAt:new Date})}getGoals(){return[...this.goals]}updateGoal(e,t){let i=this.goals.findIndex(t=>t.id===e);-1!==i&&(this.goals[i]={...this.goals[i],...t})}deleteGoal(e){this.goals=this.goals.filter(t=>t.id!==e)}getNotifications(){return[...this.notifications]}clearNotifications(){this.notifications=[]}constructor(e){this.notifications=[],this.goals=[],this.isMonitoring=!1,this.currentSession=null,this.lastNotification=null,this.notificationCallbacks=[],this.config=e}}var Z=i(70607);class Q{async analyzeTaskContent(e){try{let t=await _.F.queryScreenpipe({startTime:e.start.toISOString(),endTime:e.end.toISOString(),contentType:"ocr",limit:500,includeFrames:!1});if(!(null==t?void 0:t.data))return{detectedTasks:[],insights:[]};let i=[],s=[];for(let e of t.data)if("OCR"===e.type){let t=e.content.text||"",s=e.content.app_name||"",a=e.content.window_name||"",n=this.analyzeOCRForTasks(t,s,a);n.confidence>.6&&i.push({id:(0,U.A)(),timestamp:new Date(e.content.timestamp),taskType:n.taskType,description:n.description,confidence:n.confidence,appName:s,windowName:a,ocrText:t.substring(0,200)})}let a=this.generateTaskInsights(i);return s.push(...a),{detectedTasks:i,insights:s}}catch(e){return console.error("Error analyzing task content:",e),{detectedTasks:[],insights:[]}}}analyzeOCRForTasks(e,t,i){let s=e.toLowerCase(),a=t.toLowerCase();i.toLowerCase();let n={taskType:"unknown",confidence:0,description:"Unknown activity"};for(let[e,i]of Object.entries({"deep-work":{keywords:["function","class","import","export","const","let","var","def","public","private"],apps:["vscode","intellij","xcode","sublime","vim"],confidence:.9},communication:{keywords:["message","chat","email","meeting","call","video","send","reply"],apps:["slack","teams","discord","zoom","mail"],confidence:.8},research:{keywords:["search","google","stackoverflow","documentation","tutorial","how to"],apps:["chrome","firefox","safari","edge"],confidence:.7},creative:{keywords:["design","layer","brush","color","font","canvas","artboard"],apps:["photoshop","illustrator","figma","sketch"],confidence:.8},administrative:{keywords:["spreadsheet","document","presentation","table","chart","formula"],apps:["excel","word","powerpoint","sheets","docs"],confidence:.7},learning:{keywords:["course","lesson","tutorial","learn","study","education"],apps:["coursera","udemy","khan"],confidence:.8},entertainment:{keywords:["watch","video","movie","music","game","play"],apps:["netflix","youtube","spotify","steam"],confidence:.9},social:{keywords:["post","like","share","comment","follow","friend"],apps:["facebook","twitter","instagram","linkedin"],confidence:.8}})){let r=0,o=[];i.apps.some(e=>a.includes(e))&&(r+=.4);let c=i.keywords.filter(e=>s.includes(e));c.length>0&&(r+=c.length/i.keywords.length*.6,o=c),(r*=i.confidence)>n.confidence&&(n={taskType:e,confidence:r,description:this.generateTaskDescription(e,o,t)})}return n}generateTaskDescription(e,t,i){let s={"deep-work":"Coding/development work in ".concat(i),communication:"Communication activity in ".concat(i),research:"Research and information gathering in ".concat(i),creative:"Creative/design work in ".concat(i),administrative:"Administrative tasks in ".concat(i),learning:"Learning activity in ".concat(i),entertainment:"Entertainment/leisure in ".concat(i),social:"Social media activity in ".concat(i),distraction:"Potentially distracting activity in ".concat(i),unknown:"Activity in ".concat(i)};return s[e]||s.unknown}generateTaskInsights(e){let t=[],i=e.reduce((e,t)=>(e[t.taskType]=(e[t.taskType]||0)+1,e),{}),s=e.length;if(0===s)return t;let a=Object.entries(i).sort((e,t)=>{let[,i]=e,[,s]=t;return s-i})[0];if(a){let[e,i]=a,n=Math.round(i/s*100);t.push("".concat(n,"% of detected activities were ").concat(e.replace("-"," ")))}let n=Math.round(e.filter(e=>["deep-work","creative","learning","research"].includes(e.taskType)).length/s*100);n>=70?t.push("High productivity detected - great focus on meaningful work!"):n>=40?t.push("Moderate productivity - consider reducing distractions"):t.push("Low productivity detected - focus on high-value tasks");let r=this.analyzeAppSwitching(e);return r>10&&t.push("High app switching detected (".concat(r," switches) - consider batching similar tasks")),t}analyzeAppSwitching(e){let t=0,i="";for(let s of e.sort((e,t)=>e.timestamp.getTime()-t.timestamp.getTime()))i&&i!==s.appName&&t++,i=s.appName;return t}async detectProductivityPatterns(e){let t=[];for(let[i,s]of Object.entries(this.groupSessionsByTimePattern(e))){if(s.length<3)continue;let a=s.reduce((e,t)=>e+t.focusScore,0)/s.length,n=Math.min(100,s.length/e.length*100),[r,o]=i.split("|"),[c,l]=r.split("-").map(Number);t.push({id:(0,U.A)(),name:this.generatePatternName(c,l,o),description:this.generatePatternDescription(a,s.length),timePattern:{startHour:c,endHour:l,daysOfWeek:o.split(",").map(Number)},averageProductivity:a,confidence:n,recommendations:this.generatePatternRecommendations(a,c,l)})}return t.sort((e,t)=>t.confidence-e.confidence)}groupSessionsByTimePattern(e){let t={};return e.forEach(e=>{let i=e.startTime.getHours(),s=e.startTime.getDay(),a=2*Math.floor(i/2),n="".concat(a,"-").concat(a+2),r="".concat(n,"|").concat(0===s||6===s?"0,6":"1,2,3,4,5");t[r]||(t[r]=[]),t[r].push(e)}),t}generatePatternName(e,t,i){let s="".concat(e,":00-").concat(t,":00");return"".concat("0,6"===i?"Weekends":"Weekdays"," ").concat(s)}generatePatternDescription(e,t){return"".concat(e>=80?"high":e>=60?"moderate":"low"," productivity pattern with ").concat(t," sessions")}generatePatternRecommendations(e,t,i){let s=[];return e>=80?(s.push("Excellent productivity window - schedule important tasks here"),s.push("Consider extending this time block if possible")):e>=60?(s.push("Good productivity window - optimize environment for better focus"),s.push("Minimize distractions during this time")):(s.push("Low productivity window - consider rescheduling demanding tasks"),s.push("Use this time for lighter activities or breaks")),t>=6&&t<=10?s.push("Morning energy - great for creative and analytical work"):t>=14&&t<=16?s.push("Post-lunch dip - consider light exercise or break"):t>=20&&s.push("Evening hours - wind down with lighter tasks"),s}async generateBreakRecommendation(e,t){if(!e)return null;let i=(Date.now()-e.startTime.getTime())/6e4,s=0,a="micro",n=5,r="";return(i>=120?(s=90,a="long",n=15,r="Extended focus session - time for a longer break"):i>=60?(s=70,a="short",n=10,r="Good focus session - take a short break to recharge"):i>=25&&(s=50,a="micro",n=5,r="Pomodoro break - quick refresh recommended"),this.analyzeRecentBreaks(t).timeSinceLastBreak>180&&(s=Math.min(100,s+30),r="Long period without breaks - rest is important"),s<40)?null:{id:(0,U.A)(),timestamp:new Date,reason:r,type:a,suggestedDuration:n,activities:this.getBreakActivities(a),urgency:s}}analyzeRecentBreaks(e){let t=Date.now(),i=e[e.length-1];return{timeSinceLastBreak:i?(t-i.endTime.getTime())/6e4:0,averageBreakInterval:60}}getBreakActivities(e){return({micro:["Look away from screen (20-20-20 rule)","Deep breathing exercises","Stretch your neck and shoulders","Drink water"],short:["Walk around the room","Light stretching","Grab a healthy snack","Step outside for fresh air","Chat with a colleague"],long:["Take a walk outside","Have a proper meal","Do some exercise","Meditate or relax","Call a friend or family member"]})[e]}constructor(){this.fuse=new Z.A([],{keys:["text","appName","windowName"],threshold:.3})}}var X=i(36489),$=i(66848),J=i(12039);class ee{async initializeModel(){try{this.model=J.ilg({layers:[J.ZFI.dense({inputShape:[7],units:16,activation:"relu"}),J.ZFI.dropout({rate:.2}),J.ZFI.dense({units:8,activation:"relu"}),J.ZFI.dense({units:1,activation:"sigmoid"})]}),this.model.compile({optimizer:"adam",loss:"meanSquaredError",metrics:["mae"]}),console.log("Pattern recognition model initialized")}catch(e){console.error("Error initializing ML model:",e)}}async analyzeProductivityPatterns(e,t){if(e.length<10)return console.warn("Insufficient data for pattern analysis"),[];let i=this.groupSessionsByTimePatterns(e),s=[];for(let[e,t]of Object.entries(i)){if(t.length<3)continue;let i=await this.analyzeTimePattern(e,t);i&&s.push(i)}let a=this.analyzeWeeklyPatterns(e);s.push(...a);let n=this.analyzeTaskPatterns(e);return s.push(...n),s.sort((e,t)=>t.confidence-e.confidence)}groupSessionsByTimePatterns(e){let t={};return e.forEach(e=>{let i=(0,X.q)(e.startTime),s=(0,$.P)(e.startTime),a=2*Math.floor(i/2),n="".concat(a,"-").concat(a+2,"_").concat(0===s||6===s?"weekend":"weekday");t[n]||(t[n]=[]),t[n].push(e)}),t}async analyzeTimePattern(e,t){let[i,s]=e.split("_"),[a,n]=i.split("-").map(Number),r=t.reduce((e,t)=>e+t.focusScore,0)/t.length,o=t.reduce((e,t)=>e+t.duration,0)/t.length,c=t.length,l=(Math.max(0,100-this.calculateVariance(t.map(e=>e.focusScore)))+Math.min(100,c/10*100))/2;if(l<30)return null;let d=this.generatePatternInsights(t,r,o),u=this.generatePatternRecommendations(r,a,n,s);return{id:"pattern_".concat(e),name:this.generatePatternName(a,n,s),description:"".concat(s," productivity pattern (").concat(c," sessions)"),timePattern:{startHour:a,endHour:n,daysOfWeek:"weekend"===s?[0,6]:[1,2,3,4,5]},averageProductivity:r,confidence:l,recommendations:[...d,...u]}}analyzeWeeklyPatterns(e){let t={};e.forEach(e=>{let i=(0,$.P)(e.startTime);t[i]||(t[i]=[]),t[i].push(e)});let i=[],s=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"];return Object.entries(t).forEach(t=>{let[a,n]=t;if(n.length<3)return;let r=parseInt(a),o=n.reduce((e,t)=>e+t.focusScore,0)/n.length,c=Math.min(100,n.length/e.length*700);c>=20&&i.push({id:"weekly_".concat(a),name:"".concat(s[r]," Pattern"),description:"Productivity pattern for ".concat(s[r],"s"),timePattern:{startHour:0,endHour:24,daysOfWeek:[r]},averageProductivity:o,confidence:c,recommendations:this.generateDaySpecificRecommendations(r,o)})}),i}analyzeTaskPatterns(e){let t={};e.forEach(e=>{t[e.taskCategory]||(t[e.taskCategory]=[]),t[e.taskCategory].push(e)});let i=[];return Object.entries(t).forEach(t=>{let[s,a]=t;if(a.length<5)return;let n=a.reduce((e,t)=>e+t.focusScore,0)/a.length,r=Math.min(100,a.length/e.length*500),o=this.analyzeHourlyPerformance(a),c=this.findBestPerformanceHours(o);r>=30&&c.length>0&&i.push({id:"task_".concat(s),name:"".concat(s.replace("-"," ")," Optimization"),description:"Best times for ".concat(s.replace("-"," ")," tasks"),timePattern:{startHour:Math.min(...c),endHour:Math.max(...c)+1,daysOfWeek:[1,2,3,4,5]},averageProductivity:n,confidence:r,recommendations:this.generateTaskSpecificRecommendations(s,c)})}),i}async predictOptimalSchedule(e,t){let i=[];if(!this.model)return console.warn("ML model not available for predictions"),this.generateRuleBasedSchedule(e,t);try{let s=(0,$.P)(e),a=+(0===s||6===s);for(let n=8;n<18;n++)for(let r of t){let t=[n/24,s/7,a,this.encodeTaskType(r),this.getHistoricalPerformance(n,s,r),this.getEnergyLevel(n),this.getContextualFactors(e,n)],o=this.model.predict(J.KtR([t])),c=100*(await o.data())[0];o.dispose();let l=this.calculatePredictionConfidence(n,s,r);i.push({hour:n,taskType:r,predictedProductivity:c,confidence:l,reasoning:this.generatePredictionReasoning(n,r,c)})}return i.sort((e,t)=>t.predictedProductivity-e.predictedProductivity).slice(0,8)}catch(i){return console.error("Error in ML prediction:",i),this.generateRuleBasedSchedule(e,t)}}generateRuleBasedSchedule(e,t){let i=[],s=(0,$.P)(e),a=0===s||6===s,n={"deep-work":a?[10,11,14,15]:[9,10,14,15],creative:[10,11,16,17],communication:[11,12,14,16],administrative:[13,14,15,16],learning:[9,10,11,15],research:[9,10,14,15]};return t.forEach(e=>{(n[e]||[10,11,14,15]).forEach(t=>{i.push({hour:t,taskType:e,predictedProductivity:this.getRuleBasedProductivity(t,e,a),confidence:70,reasoning:"Based on general productivity patterns for ".concat(e.replace("-"," "))})})}),i.sort((e,t)=>t.predictedProductivity-e.predictedProductivity)}calculateVariance(e){let t=e.reduce((e,t)=>e+t,0)/e.length;return e.map(e=>Math.pow(e-t,2)).reduce((e,t)=>e+t,0)/e.length}generatePatternName(e,t,i){let s="".concat(e,":00-").concat(t,":00");return"".concat("weekend"===i?"Weekend":"Weekday"," ").concat(s)}generatePatternInsights(e,t,i){let s=[];return t>=80?s.push("High-performance time window - excellent for important tasks"):t>=60?s.push("Good productivity window - suitable for focused work"):s.push("Lower productivity period - consider lighter tasks"),i>=60?s.push("Long focus sessions typical - good for deep work"):i>=30?s.push("Moderate session length - good for regular tasks"):s.push("Short sessions typical - better for quick tasks"),s}generatePatternRecommendations(e,t,i,s){let a=[];return e>=80?(a.push("Schedule your most important work during this time"),a.push("Minimize interruptions and distractions")):e>=60?(a.push("Good time for regular focused work"),a.push("Consider optimizing environment for better focus")):(a.push("Use for lighter tasks or administrative work"),a.push("Consider taking breaks or doing physical activity")),t<=10?a.push("Morning energy - great for analytical tasks"):t>=14&&t<=16&&a.push("Post-lunch period - may need energy boost"),a}generateDaySpecificRecommendations(e,t){let i=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"][e],s=["Optimize ".concat(i," productivity")];return 1===e?s.push("Start week with planning and goal setting"):5===e?s.push("Wrap up weekly tasks and plan for next week"):(0===e||6===e)&&s.push("Focus on personal projects and learning"),s}generateTaskSpecificRecommendations(e,t){let i=[],s=t.map(e=>"".concat(e,":00")).join(", ");switch(i.push("Best performance for ".concat(e.replace("-"," ")," at ").concat(s)),e){case"deep-work":i.push("Minimize distractions and notifications"),i.push("Ensure comfortable environment and good lighting");break;case"creative":i.push("Allow for inspiration and experimentation"),i.push("Consider background music or change of scenery");break;case"communication":i.push("Batch similar communication tasks together"),i.push("Set specific times for checking messages")}return i}analyzeHourlyPerformance(e){let t={};e.forEach(e=>{let i=(0,X.q)(e.startTime);t[i]||(t[i]={total:0,count:0}),t[i].total+=e.focusScore,t[i].count+=1});let i={};return Object.entries(t).forEach(e=>{let[t,s]=e;i[parseInt(t)]=s.total/s.count}),i}findBestPerformanceHours(e){let t=Object.values(e).reduce((e,t)=>e+t,0)/Object.values(e).length;return Object.entries(e).filter(e=>{let[,i]=e;return i>1.1*t}).map(e=>{let[t]=e;return parseInt(t)})}encodeTaskType(e){return({"deep-work":.9,creative:.8,research:.7,learning:.6,administrative:.5,communication:.4,social:.3,entertainment:.2,distraction:.1,unknown:0})[e]||0}getHistoricalPerformance(e,t,i){return .7}getEnergyLevel(e){return e>=9&&e<=11?.9:e>=14&&e<=16?.8:e>=20||e<=6?.3:.6}getContextualFactors(e,t){return .5}calculatePredictionConfidence(e,t,i){return 75}generatePredictionReasoning(e,t,i){return i>=80?"Optimal time for ".concat(t.replace("-"," ")," based on historical performance"):i>=60?"Good time for ".concat(t.replace("-"," ")," with moderate productivity expected"):"Lower productivity expected for ".concat(t.replace("-"," ")," at this time")}getRuleBasedProductivity(e,t,i){let s=70;return e>=9&&e<=11&&(s+=15),e>=14&&e<=16&&(s+=10),(e<=8||e>=18)&&(s-=20),"deep-work"===t&&e>=9&&e<=11&&(s+=10),"creative"===t&&e>=10&&e<=12&&(s+=10),"communication"===t&&e>=11&&e<=16&&(s+=5),i&&(s-=10),Math.max(0,Math.min(100,s))}constructor(){this.patterns=[],this.energyLevels=[],this.model=null,this.initializeModel()}}var et=i(6711),ei=i(19828),es=i(72132),ea=i(56671),en=i(83540),er=i(99445),eo=i(94754),ec=i(96025),el=i(16238),ed=i(94517),eu=i(62341),em=i(8782),eh=i(34e3),eg=i(54811),ep=i(93504),ef=i(21374),ey=i(1243),ex=i(67312),ev=i(40646),ew=i(10415);function ek(e){let{metrics:t,sessions:i,timeRange:a}=e;if(!t)return(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[void 0,void 0,void 0,void 0].map((e,t)=>(0,s.jsx)(y,{children:(0,s.jsx)(k,{className:"p-6",children:(0,s.jsxs)("div",{className:"animate-pulse",children:[(0,s.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-2"}),(0,s.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/2"})]})})},t))});let n=function(e){let t={};for(let e=0;e<24;e++)t[e]={totalScore:0,count:0};return e.forEach(e=>{let i=(0,X.q)(e.startTime);t[i].totalScore+=e.focusScore,t[i].count+=1}),Object.entries(t).map(e=>{let[t,i]=e;return{hour:parseInt(t),focusScore:i.count>0?i.totalScore/i.count:0}})}(i),r=function(e){let t={},i={"deep-work":"#3b82f6",communication:"#10b981",research:"#f59e0b",creative:"#8b5cf6",administrative:"#6b7280",learning:"#06b6d4",entertainment:"#ef4444",social:"#ec4899",distraction:"#f97316",unknown:"#9ca3af"};return e.forEach(e=>{t[e.taskCategory]=(t[e.taskCategory]||0)+e.duration}),Object.entries(t).map(e=>{let[t,s]=e;return{name:t.replace("-"," "),value:s,color:i[t]||"#9ca3af"}})}(i),o=i.sort((e,t)=>e.startTime.getTime()-t.startTime.getTime()).map(e=>({time:(0,ew.GP)(e.startTime,"HH:mm"),focusScore:e.focusScore}));return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,s.jsx)(eb,{title:"Total Focus Time",value:"".concat(Math.round(t.totalFocusTime)," min"),icon:(0,s.jsx)(E.A,{className:"h-5 w-5"}),trend:eS("focusTime",t.totalFocusTime),color:"blue"}),(0,s.jsx)(eb,{title:"Focus Score",value:"".concat(Math.round(t.focusScore),"%"),icon:(0,s.jsx)(W.A,{className:"h-5 w-5"}),trend:eS("focusScore",t.focusScore),color:"green"}),(0,s.jsx)(eb,{title:"Deep Work Sessions",value:t.deepWorkSessions.toString(),icon:(0,s.jsx)(O.A,{className:"h-5 w-5"}),trend:eS("deepWork",t.deepWorkSessions),color:"purple"}),(0,s.jsx)(eb,{title:"Context Switches",value:t.contextSwitches.toString(),icon:(0,s.jsx)(ey.A,{className:"h-5 w-5"}),trend:eS("contextSwitches",t.contextSwitches),color:"orange",inverse:!0})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,s.jsxs)(y,{children:[(0,s.jsxs)(x,{children:[(0,s.jsx)(v,{children:"Productivity by Hour"}),(0,s.jsx)(w,{children:"Your focus patterns throughout the day"})]}),(0,s.jsx)(k,{children:(0,s.jsx)(en.u,{width:"100%",height:300,children:(0,s.jsxs)(er.Q,{data:n,children:[(0,s.jsx)(eo.d,{strokeDasharray:"3 3"}),(0,s.jsx)(ec.W,{dataKey:"hour"}),(0,s.jsx)(el.h,{}),(0,s.jsx)(ed.m,{formatter:e=>["".concat(Math.round(e),"%"),"Focus Score"],labelFormatter:e=>"".concat(e,":00")}),(0,s.jsx)(eu.G,{type:"monotone",dataKey:"focusScore",stroke:"#3b82f6",fill:"#3b82f6",fillOpacity:.3})]})})})]}),(0,s.jsxs)(y,{children:[(0,s.jsxs)(x,{children:[(0,s.jsx)(v,{children:"Task Distribution"}),(0,s.jsx)(w,{children:"Time spent on different types of activities"})]}),(0,s.jsx)(k,{children:(0,s.jsx)(en.u,{width:"100%",height:300,children:(0,s.jsxs)(em.r,{children:[(0,s.jsx)(eh.F,{data:r,cx:"50%",cy:"50%",labelLine:!1,label:e=>{let{name:t,percent:i}=e;return"".concat(t," ").concat((100*i).toFixed(0),"%")},outerRadius:80,fill:"#8884d8",dataKey:"value",children:r.map((e,t)=>(0,s.jsx)(eg.f,{fill:e.color},"cell-".concat(t)))}),(0,s.jsx)(ed.m,{formatter:e=>["".concat(Math.round(e)," min"),"Time"]})]})})})]})]}),(0,s.jsxs)(y,{children:[(0,s.jsxs)(x,{children:[(0,s.jsx)(v,{children:"Productivity Trend"}),(0,s.jsx)(w,{children:"Focus score progression over time"})]}),(0,s.jsx)(k,{children:(0,s.jsx)(en.u,{width:"100%",height:200,children:(0,s.jsxs)(ep.b,{data:o,children:[(0,s.jsx)(eo.d,{strokeDasharray:"3 3"}),(0,s.jsx)(ec.W,{dataKey:"time"}),(0,s.jsx)(el.h,{domain:[0,100]}),(0,s.jsx)(ed.m,{formatter:e=>["".concat(Math.round(e),"%"),"Focus Score"]}),(0,s.jsx)(ef.N,{type:"monotone",dataKey:"focusScore",stroke:"#10b981",strokeWidth:2,dot:{fill:"#10b981",strokeWidth:2,r:4}})]})})})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,s.jsxs)(y,{children:[(0,s.jsxs)(x,{children:[(0,s.jsx)(v,{children:"Most Productive Apps"}),(0,s.jsx)(w,{children:"Apps where you maintain highest focus"})]}),(0,s.jsx)(k,{children:(0,s.jsx)("div",{className:"space-y-4",children:t.topProductiveApps.slice(0,5).map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-green-600 font-semibold text-sm",children:t+1})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium",children:e.appName}),(0,s.jsxs)("p",{className:"text-sm text-gray-500",children:[Math.round(e.focusTime)," min focused"]})]})]}),(0,s.jsxs)(R,{variant:"outline",className:"bg-green-50 text-green-700",children:[Math.round(e.productivityScore),"%"]})]},e.appName))})})]}),(0,s.jsxs)(y,{children:[(0,s.jsxs)(x,{children:[(0,s.jsx)(v,{children:"Distraction Sources"}),(0,s.jsx)(w,{children:"Apps that tend to break your focus"})]}),(0,s.jsx)(k,{children:(0,s.jsx)("div",{className:"space-y-4",children:t.topDistractingApps.slice(0,5).map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-orange-600 font-semibold text-sm",children:t+1})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium",children:e.appName}),(0,s.jsxs)("p",{className:"text-sm text-gray-500",children:[Math.round(e.distractionTime)," min distracted"]})]})]}),(0,s.jsxs)(R,{variant:"outline",className:"bg-orange-50 text-orange-700",children:[e.sessionCount," interruptions"]})]},e.appName))})})]})]}),(0,s.jsxs)(y,{children:[(0,s.jsxs)(x,{children:[(0,s.jsx)(v,{children:"Key Insights"}),(0,s.jsx)(w,{children:"AI-generated insights about your productivity patterns"})]}),(0,s.jsx)(k,{children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsx)(ej,{icon:(0,s.jsx)(I.A,{className:"h-5 w-5 text-green-600"}),title:"Peak Performance",description:"Your most productive hour is ".concat(t.mostProductiveHour,":00"),type:"positive"}),(0,s.jsx)(ej,{icon:(0,s.jsx)(ex.A,{className:"h-5 w-5 text-blue-600"}),title:"Energy Dip",description:"Consider breaks around ".concat(t.leastProductiveHour,":00"),type:"neutral"}),(0,s.jsx)(ej,{icon:(0,s.jsx)(ev.A,{className:"h-5 w-5 text-green-600"}),title:"Session Quality",description:"Average session length: ".concat(Math.round(t.averageSessionLength)," minutes"),type:"positive"}),(0,s.jsx)(ej,{icon:(0,s.jsx)(ey.A,{className:"h-5 w-5 text-orange-600"}),title:"Focus Improvement",description:"".concat(t.contextSwitches," context switches detected"),type:"warning"})]})})]})]})}function eb(e){let{title:t,value:i,icon:a,trend:n,color:r,inverse:o=!1}=e;return(0,s.jsx)(y,{children:(0,s.jsxs)(k,{className:"p-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("div",{className:"p-2 rounded-lg ".concat({blue:"bg-blue-50 text-blue-600",green:"bg-green-50 text-green-600",purple:"bg-purple-50 text-purple-600",orange:"bg-orange-50 text-orange-600"}[r]),children:a}),n&&(0,s.jsxs)(R,{variant:"outline",className:"".concat(n.isPositive&&!o||!n.isPositive&&o?"bg-green-50 text-green-700":"bg-red-50 text-red-700"),children:[n.isPositive?"+":"",n.value,"%"]})]}),(0,s.jsxs)("div",{className:"mt-4",children:[(0,s.jsx)("h3",{className:"text-2xl font-bold",children:i}),(0,s.jsx)("p",{className:"text-gray-600 text-sm",children:t})]})]})})}function ej(e){let{icon:t,title:i,description:a,type:n}=e;return(0,s.jsx)("div",{className:"p-4 rounded-lg border ".concat({positive:"border-green-200 bg-green-50",neutral:"border-blue-200 bg-blue-50",warning:"border-orange-200 bg-orange-50"}[n]),children:(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[t,(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium",children:i}),(0,s.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:a})]})]})})}function eS(e,t){return({focusTime:{value:12,isPositive:!0},focusScore:{value:8,isPositive:!0},deepWork:{value:15,isPositive:!0},contextSwitches:{value:-5,isPositive:!1}})[e]}function eN(){let[e,t]=(0,a.useState)(!1),[i,n]=(0,a.useState)("overview"),[r,o]=(0,a.useState)("today"),[c,l]=(0,a.useState)(!0),[d,u]=(0,a.useState)(null),[m,h]=(0,a.useState)([]),[g,p]=(0,a.useState)([]),[f,x]=(0,a.useState)([]),[v,w]=(0,a.useState)(null),[b,P]=(0,a.useState)(null),[A,C]=(0,a.useState)(null),[D,_]=(0,a.useState)(null),[q,Y]=(0,a.useState)({focusThreshold:15,distractionThreshold:30,contextSwitchWindow:5,breakReminderInterval:60,deepWorkMinimum:45,productivityCategories:[],enableRealTimeCoaching:!0,enableBreakReminders:!0,enableGoalTracking:!0});(0,a.useEffect)(()=>{(async()=>{try{let e=new K(q),t=new V(q),i=new Q,s=new ee;w(e),P(t),C(i),_(s),t.onNotification(e=>{var t,i;p(t=>[e,...t.slice(0,9)]),(0,ea.oR)(e.title,{description:e.message,action:e.actionable?{label:(null===(t=e.action)||void 0===t?void 0:t.label)||"Action",onClick:(null===(i=e.action)||void 0===i?void 0:i.callback)||(()=>{})}:void 0})}),l(!1)}catch(e){console.error("Error initializing analytics engines:",e),ea.oR.error("Failed to initialize analytics engines"),l(!1)}})()},[q]),(0,a.useEffect)(()=>{v&&!c&&(async()=>{try{let e={today:{start:(0,et.o)(new Date),end:(0,ei.D)(new Date)},yesterday:{start:(0,et.o)((0,es.e)(new Date,1)),end:(0,ei.D)((0,es.e)(new Date,1))},week:{start:(0,et.o)((0,es.e)(new Date,7)),end:(0,ei.D)(new Date)},month:{start:(0,et.o)((0,es.e)(new Date,30)),end:(0,ei.D)(new Date)}}[r],t=await v.analyzeActivity(e);h(t.focusSessions),u(t.metrics)}catch(e){console.error("Error loading analytics data:",e),ea.oR.error("Failed to load analytics data")}})()},[v,r,c]);let U=async()=>{if(b)try{e?(b.stopMonitoring(),t(!1),ea.oR.success("Stopped productivity monitoring")):(await b.startMonitoring(),t(!0),ea.oR.success("Started productivity monitoring"))}catch(e){console.error("Error toggling monitoring:",e),ea.oR.error("Failed to toggle monitoring")}};return c?(0,s.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Initializing Focus Analytics..."})]})}):(0,s.jsxs)("div",{className:"container mx-auto p-6 space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Focus Analytics & Productivity Coach"}),(0,s.jsx)("p",{className:"text-gray-600 mt-1",children:"AI-powered insights to optimize your productivity and focus"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsxs)("select",{value:r,onChange:e=>o(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"today",children:"Today"}),(0,s.jsx)("option",{value:"yesterday",children:"Yesterday"}),(0,s.jsx)("option",{value:"week",children:"Last 7 Days"}),(0,s.jsx)("option",{value:"month",children:"Last 30 Days"})]}),(0,s.jsx)(M,{onClick:U,variant:e?"destructive":"default",className:"flex items-center gap-2",children:e?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(F.A,{className:"h-4 w-4"}),"Stop Monitoring"]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(z.A,{className:"h-4 w-4"}),"Start Monitoring"]})})]})]}),(0,s.jsx)(y,{children:(0,s.jsx)(k,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-3 h-3 rounded-full ".concat(e?"bg-green-500":"bg-gray-400")}),(0,s.jsx)("span",{className:"text-sm font-medium",children:e?"Monitoring Active":"Monitoring Inactive"})]}),d&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(R,{variant:"outline",className:"flex items-center gap-1",children:[(0,s.jsx)(E.A,{className:"h-3 w-3"}),Math.round(d.totalFocusTime)," min focused"]}),(0,s.jsxs)(R,{variant:"outline",className:"flex items-center gap-1",children:[(0,s.jsx)(I.A,{className:"h-3 w-3"}),Math.round(d.focusScore),"% focus score"]}),(0,s.jsxs)(R,{variant:"outline",className:"flex items-center gap-1",children:[(0,s.jsx)(O.A,{className:"h-3 w-3"}),d.deepWorkSessions," deep work sessions"]})]})]}),g.length>0&&(0,s.jsxs)(M,{variant:"ghost",size:"sm",className:"flex items-center gap-2",children:[(0,s.jsx)(B.A,{className:"h-4 w-4"}),g.length," notifications"]})]})})}),(0,s.jsxs)(j,{value:i,onValueChange:n,className:"space-y-6",children:[(0,s.jsxs)(S,{className:"grid w-full grid-cols-6",children:[(0,s.jsxs)(N,{value:"overview",className:"flex items-center gap-2",children:[(0,s.jsx)(L.A,{className:"h-4 w-4"}),"Overview"]}),(0,s.jsxs)(N,{value:"sessions",className:"flex items-center gap-2",children:[(0,s.jsx)(E.A,{className:"h-4 w-4"}),"Sessions"]}),(0,s.jsxs)(N,{value:"coaching",className:"flex items-center gap-2",children:[(0,s.jsx)(W.A,{className:"h-4 w-4"}),"Coaching"]}),(0,s.jsxs)(N,{value:"goals",className:"flex items-center gap-2",children:[(0,s.jsx)(G.A,{className:"h-4 w-4"}),"Goals"]}),(0,s.jsxs)(N,{value:"patterns",className:"flex items-center gap-2",children:[(0,s.jsx)(I.A,{className:"h-4 w-4"}),"Patterns"]}),(0,s.jsxs)(N,{value:"settings",className:"flex items-center gap-2",children:[(0,s.jsx)(H.A,{className:"h-4 w-4"}),"Settings"]})]}),(0,s.jsx)(T,{value:"overview",className:"space-y-6",children:(0,s.jsx)(ek,{metrics:d,sessions:m,timeRange:r})}),(0,s.jsx)(T,{value:"sessions",className:"space-y-6",children:(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)(E.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Focus Sessions"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Detailed session analysis coming soon..."})]})}),(0,s.jsx)(T,{value:"coaching",className:"space-y-6",children:(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)(W.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Real-time Coaching"}),(0,s.jsx)("p",{className:"text-gray-600",children:"AI coaching features coming soon..."})]})}),(0,s.jsx)(T,{value:"goals",className:"space-y-6",children:(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)(G.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Goal Tracking"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Productivity goals coming soon..."})]})}),(0,s.jsx)(T,{value:"patterns",className:"space-y-6",children:(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)(I.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Pattern Insights"}),(0,s.jsx)("p",{className:"text-gray-600",children:"AI pattern recognition coming soon..."})]})}),(0,s.jsx)(T,{value:"settings",className:"space-y-6",children:(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)(H.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Settings"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Configuration options coming soon..."})]})})]})]})}function eT(){return(0,s.jsx)(m,{children:(0,s.jsx)(h,{children:(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 ".concat(p().className),children:(0,s.jsx)(eN,{})})})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[183,229,690,272,660,56,87,251,674,266,120,441,684,358],()=>t(30046)),_N_E=e.O()}]);