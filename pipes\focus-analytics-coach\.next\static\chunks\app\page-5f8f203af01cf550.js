(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{4530:()=>{},8108:()=>{},18590:()=>{},30046:(e,t,s)=>{Promise.resolve().then(s.bind(s,77333))},41234:()=>{},53999:(e,t,s)=>{"use strict";s.d(t,{cn:()=>n});var i=s(52596),r=s(39688);function n(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.QP)((0,i.$)(t))}s(20029)},77333:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>eK});var i=s(95155),r=s(12115),n=s(34477);let a=(0,n.createServerReference)("00dd940e07828289ffa35be4849402c3efc5e5804e",n.callServer,void 0,n.findSourceMapURL,"getScreenpipeAppSettings"),o=(0,n.createServerReference)("40369bb696b313d1bf35d518802188672f34858d64",n.callServer,void 0,n.findSourceMapURL,"updateScreenpipeAppSettings"),c={exampleSetting:"default value"};class l{getStore(){return this.store}subscribe(e){return this.listeners.add(e),()=>this.listeners.delete(e)}notify(){this.listeners.forEach(e=>e())}async setGlobalSettings(e){this.store.globalSettings=e,this.notify()}async setPipeSettings(e,t){this.store.pipeSettings[e]=t,this.notify()}async loadGlobalSettings(){try{let e=await a();return this.setGlobalSettings(e),e}catch(e){return console.error("failed to load global settings:",e),null}}async updateGlobalSettings(e){try{let t={...await a(),...e};return await o(t),this.setGlobalSettings(t),this.notify(),!0}catch(e){return console.error("failed to update global settings:",e),!1}}async loadPipeSettings(e){try{var t;let s=await a(),i={...c,...null===(t=s.customSettings)||void 0===t?void 0:t[e]};return this.setPipeSettings(e,i),i}catch(e){return console.error("failed to load pipe settings:",e),null}}async updatePipeSettings(e,t){try{var s,i;let r=await a(),n={...r,customSettings:{...r.customSettings||{},[e]:{...(null===(s=r.customSettings)||void 0===s?void 0:s[e])||{},...t}}};return await o(n),this.setGlobalSettings(n),this.setPipeSettings(e,{...(null===(i=r.customSettings)||void 0===i?void 0:i[e])||{},...t}),!0}catch(e){return console.error("failed to update pipe settings:",e),!1}}getPreset(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"aiPresetId";try{var s,i,r,n;let a;let o=null===(s=this.store.pipeSettings[e])||void 0===s?void 0:s[t],c=this.store.globalSettings;if(o&&(a=null==c?void 0:null===(r=c.aiPresets)||void 0===r?void 0:r.find(e=>e.id===o)),a||(a=null==c?void 0:null===(n=c.aiPresets)||void 0===n?void 0:n.find(e=>e.defaultPreset)),!a)return;let l="provider"in a&&"screenpipe-cloud"===a.provider?(null==c?void 0:null===(i=c.user)||void 0===i?void 0:i.token)||"":"provider"in a&&"apiKey"in a&&a.apiKey||"";return{id:a.id,maxContextChars:a.maxContextChars,url:a.url,model:a.model,defaultPreset:a.defaultPreset,prompt:a.prompt,provider:a.provider,apiKey:l}}catch(e){console.error("failed to get preset:",e);return}}constructor(){this.store={globalSettings:null,pipeSettings:{}},this.listeners=new Set}}let d=new l,u=(0,r.createContext)(void 0);async function m(){try{let e=await fetch("http://localhost:3030/health",{method:"GET",signal:AbortSignal.timeout(3e3)});if(e.ok)return{connected:!0};return{connected:!1,error:"Screenpipe responded with status ".concat(e.status)}}catch(e){if(e instanceof Error){if("AbortError"===e.name)return{connected:!1,error:"Connection timeout - Screenpipe may not be running"};if(e.message.includes("fetch"))return{connected:!1,error:"Cannot connect to Screenpipe - check if it's running on port 3030"};return{connected:!1,error:e.message}}return{connected:!1,error:"Unknown connection error"}}}function h(e){let{children:t}=e,s=function(){let[e,t]=(0,r.useState)(d.getStore().globalSettings),[s,i]=(0,r.useState)(!0);return(0,r.useEffect)(()=>{(async()=>{i(!0),await d.loadGlobalSettings(),i(!1)})();let e=d.subscribe(()=>{t(d.getStore().globalSettings)});return()=>{e()}},[]),{settings:e,updateSettings:async e=>d.updateGlobalSettings(e),loading:s}}(),[n,a]=(0,r.useState)("checking"),[o,c]=(0,r.useState)();(0,r.useEffect)(()=>{let e,t=!0,s=async()=>{if(!t)return;let e=await m();t&&(e.connected?(a("connected"),c(void 0),console.log("✅ Screenpipe connection established")):(a("disconnected"),c(e.error),console.warn("⚠️ Screenpipe not available:",e.error),console.info("ℹ️ Plugin will run in demo mode with sample data")))};return s(),e=setInterval(s,3e4),()=>{t=!1,e&&clearInterval(e)}},[]),(0,r.useEffect)(()=>{let e=console.error;return console.error=function(t){for(var s=arguments.length,i=Array(s>1?s-1:0),r=1;r<s;r++)i[r-1]=arguments[r];if("disconnected"===n&&"string"==typeof t&&["settings stream timeout","failed to fetch settings","ERR_CONNECTION_REFUSED","Settings timeout","NetworkError when attempting to fetch resource","websocket connection error","error streaming vision"].some(e=>t.includes(e))){window._screenpipeErrorsSuppressed||(console.warn("\uD83D\uDD07 Suppressing Screenpipe connection errors (plugin running in demo mode)"),window._screenpipeErrorsSuppressed=!0);return}e.apply(console,[t,...i])},()=>{console.error=e}},[n]),console.log("settings provider initialized with data:",s.loading?"loading...":"loaded","| screenpipe: ".concat(n));let l={...s,screenpipeStatus:n,screenpipeError:o};return(0,i.jsx)(u.Provider,{value:l,children:t})}function p(){let e=(0,r.useContext)(u);if(void 0===e)throw Error("useSettings must be used within a SettingsProvider");return e}function g(e){let{children:t}=e,[s,n]=(0,r.useState)(!1);return((0,r.useEffect)(()=>{n(!0)},[]),s)?(0,i.jsx)(i.Fragment,{children:t}):null}var f=s(62418),x=s.n(f),y=s(53999);let v=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,i.jsx)("div",{ref:t,className:(0,y.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",s),...r})});v.displayName="Card";let w=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,i.jsx)("div",{ref:t,className:(0,y.cn)("flex flex-col space-y-1.5 p-6",s),...r})});w.displayName="CardHeader";let j=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,i.jsx)("div",{ref:t,className:(0,y.cn)("text-2xl font-semibold leading-none tracking-tight",s),...r})});j.displayName="CardTitle";let b=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,i.jsx)("div",{ref:t,className:(0,y.cn)("text-sm text-muted-foreground",s),...r})});b.displayName="CardDescription";let k=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,i.jsx)("div",{ref:t,className:(0,y.cn)("p-6 pt-0",s),...r})});k.displayName="CardContent",r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,i.jsx)("div",{ref:t,className:(0,y.cn)("flex items-center p-6 pt-0",s),...r})}).displayName="CardFooter";var N=s(62841);let S=N.bL,T=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,i.jsx)(N.B8,{ref:t,className:(0,y.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",s),...r})});T.displayName=N.B8.displayName;let A=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,i.jsx)(N.l9,{ref:t,className:(0,y.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",s),...r})});A.displayName=N.l9.displayName;let C=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,i.jsx)(N.UC,{ref:t,className:(0,y.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",s),...r})});C.displayName=N.UC.displayName;var P=s(99708),D=s(74466);let M=(0,D.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),R=r.forwardRef((e,t)=>{let{className:s,variant:r,size:n,asChild:a=!1,...o}=e,c=a?P.DX:"button";return(0,i.jsx)(c,{className:(0,y.cn)(M({variant:r,size:n,className:s})),ref:t,...o})});R.displayName="Button";let E=(0,D.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function F(e){let{className:t,variant:s,...r}=e;return(0,i.jsx)("div",{className:(0,y.cn)(E({variant:s}),t),...r})}var I=s(55863);let z=r.forwardRef((e,t)=>{let{className:s,value:r,...n}=e;return(0,i.jsx)(I.bL,{ref:t,className:(0,y.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",s),...n,children:(0,i.jsx)(I.C1,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:"translateX(-".concat(100-(r||0),"%)")}})})});z.displayName=I.bL.displayName;var O=s(82178),B=s(85690),G=s(14186),L=s(33109),W=s(71539),H=s(23861),Y=s(72713),_=s(49376),U=s(16785),K=s(381),q=s(67312),V=s(47178),Z=s(25727),Q=s(49067),X=s(90621);class J{async analyzeActivity(e){try{let t=await V.F.queryScreenpipe({startTime:e.start.toISOString(),endTime:e.end.toISOString(),contentType:"all",limit:1e3,includeFrames:!1});if(!(null==t?void 0:t.data))throw Error("No data received from Screenpipe");let s=this.processActivityData(t.data),i=this.calculateProductivityMetrics(s.focusSessions,s.contextSwitches,e.start);return{focusSessions:s.focusSessions,contextSwitches:s.contextSwitches,metrics:i}}catch(e){throw console.error("Error analyzing activity:",e),e}}processActivityData(e){let t=[],s=[],i=null,r=null;for(let n of e.sort((e,t)=>new Date(e.content.timestamp).getTime()-new Date(t.content.timestamp).getTime())){let e=new Date(n.content.timestamp),a="",o="";if("OCR"===n.type?(a=n.content.appName||"",o=n.content.windowName||""):"UI"===n.type&&(a=n.content.appName||"",o=n.content.windowName||""),a){if(r&&(r.app!==a||r.window!==o)){let n=(0,Z.O)(e,r.timestamp);n>=this.config.contextSwitchWindow&&(s.push({id:(0,X.A)(),timestamp:e,fromApp:r.app,toApp:a,fromWindow:r.window,toWindow:o,duration:n,switchType:r.app!==a?"app":"window"}),i&&this.isDistractingSwitch(r.app,a)&&(this.finalizeSession(i,r.timestamp,t),i=null))}(!i||this.shouldStartNewSession(i,a,o,e))&&(i&&this.finalizeSession(i,e,t),i={id:(0,X.A)(),startTime:e,appName:a,windowName:o,taskCategory:this.categorizeTask(a,o),distractionCount:0,contextSwitches:0}),i&&(i.endTime=e,s.length>0&&i.startTime&&s[s.length-1].timestamp>=i.startTime&&(i.contextSwitches=(i.contextSwitches||0)+1)),r={app:a,window:o,timestamp:e}}}return i&&r&&this.finalizeSession(i,r.timestamp,t),{focusSessions:t,contextSwitches:s}}finalizeSession(e,t,s){if(!e.startTime||!e.appName)return;let i=(0,Q.o)(t,e.startTime);if(i>=this.config.focusThreshold){let r=this.calculateFocusScore(i,e.contextSwitches||0,e.distractionCount||0);s.push({id:e.id,startTime:e.startTime,endTime:t,duration:i,appName:e.appName,windowName:e.windowName||"",taskCategory:e.taskCategory||"unknown",focusScore:r,distractionCount:e.distractionCount||0,contextSwitches:e.contextSwitches||0,productivity:this.getProductivityLevel(r)})}}calculateFocusScore(e,t,s){let i;return i=100-Math.min(5*t,30)-Math.min(10*s,40),e>=this.config.deepWorkMinimum&&(i+=10),Math.max(0,Math.min(100,i))}categorizeTask(e,t){let s="".concat(e," ").concat(t).toLowerCase();for(let[e,t]of Object.entries({"deep-work":["vscode","intellij","xcode","sublime","vim","emacs","figma","sketch"],communication:["slack","teams","discord","zoom","meet","skype","mail","outlook"],research:["chrome","firefox","safari","edge","browser","wikipedia","stackoverflow"],creative:["photoshop","illustrator","premiere","after effects","blender","canva"],administrative:["excel","sheets","word","docs","powerpoint","slides","notion"],learning:["coursera","udemy","youtube","khan academy","duolingo","anki"],entertainment:["netflix","youtube","spotify","twitch","gaming","steam"],social:["facebook","twitter","instagram","linkedin","reddit","tiktok"],distraction:["news","shopping","amazon","ebay","social media"],unknown:[]}))if(t.some(e=>s.includes(e)))return e;return"unknown"}isDistractingSwitch(e,t){return["vscode","intellij","figma","notion","excel"].some(t=>e.toLowerCase().includes(t))&&["chrome","firefox","safari","social","entertainment"].some(e=>t.toLowerCase().includes(e))}shouldStartNewSession(e,t,s,i){return!e.startTime||e.appName!==t&&(0,Q.o)(i,e.startTime)>=this.config.focusThreshold}getProductivityLevel(e){return e>=90?"very-high":e>=75?"high":e>=50?"medium":e>=25?"low":"very-low"}calculateProductivityMetrics(e,t,s){let i=e.reduce((e,t)=>e+t.duration,0),r=e.length>0?e.reduce((e,t)=>e+t.focusScore,0)/e.length:0,n=e.filter(e=>e.duration>=this.config.deepWorkMinimum).length,a=e.length>0?i/e.length:0,o=this.calculateHourlyProductivity(e),c=this.getMostProductiveHour(o),l=this.getLeastProductiveHour(o),d=this.calculateAppUsage(e),u=d.filter(e=>e.productivityScore>=70).sort((e,t)=>t.focusTime-e.focusTime).slice(0,5),m=d.filter(e=>e.productivityScore<50).sort((e,t)=>t.totalTime-e.totalTime).slice(0,5);return{date:s,totalFocusTime:i,totalDistractionTime:0,focusScore:r,contextSwitches:t.length,deepWorkSessions:n,averageSessionLength:a,mostProductiveHour:c,leastProductiveHour:l,topProductiveApps:u,topDistractingApps:m}}calculateHourlyProductivity(e){let t={};for(let e=0;e<24;e++)t[e]={totalTime:0,totalScore:0,count:0};e.forEach(e=>{let s=e.startTime.getHours();t[s].totalTime+=e.duration,t[s].totalScore+=e.focusScore,t[s].count+=1});let s={};for(let e=0;e<24;e++){let i=t[e];s[e]=i.count>0?i.totalScore/i.count:0}return s}getMostProductiveHour(e){return Object.entries(e).reduce((t,s)=>{let[i,r]=s;return r>e[t]?parseInt(i):t},0)}getLeastProductiveHour(e){return Object.entries(e).reduce((t,s)=>{let[i,r]=s;return r<e[t]?parseInt(i):t},0)}calculateAppUsage(e){let t={};return e.forEach(e=>{t[e.appName]||(t[e.appName]={totalTime:0,focusTime:0,sessionCount:0,totalScore:0});let s=t[e.appName];s.totalTime+=e.duration,s.sessionCount+=1,s.totalScore+=e.focusScore,e.focusScore>=70&&(s.focusTime+=e.duration)}),Object.entries(t).map(e=>{let[t,s]=e;return{appName:t,totalTime:s.totalTime,focusTime:s.focusTime,distractionTime:s.totalTime-s.focusTime,sessionCount:s.sessionCount,averageSessionLength:s.totalTime/s.sessionCount,productivityScore:s.totalScore/s.sessionCount}})}constructor(e){this.config=e}}class ${async startMonitoring(){!this.isMonitoring&&(this.isMonitoring=!0,console.log("Starting real-time productivity coaching..."),this.config.enableRealTimeCoaching&&this.startActivityMonitoring(),this.config.enableBreakReminders&&this.startBreakReminders(),this.config.enableGoalTracking&&this.startGoalTracking())}stopMonitoring(){this.isMonitoring=!1,console.log("Stopped productivity coaching")}onNotification(e){this.notificationCallbacks.push(e)}sendNotification(e){this.notifications.push(e),this.lastNotification=new Date,this.notificationCallbacks.forEach(t=>{try{t(e)}catch(e){console.error("Error in notification callback:",e)}}),"Notification"in window&&this.sendDesktopNotification(e)}async sendDesktopNotification(e){try{if("granted"===Notification.permission)new Notification(e.title,{body:e.message,icon:"/128x128.png",tag:e.type});else if("denied"!==Notification.permission){let t=await Notification.requestPermission();"granted"===t&&new Notification(e.title,{body:e.message,icon:"/128x128.png",tag:e.type})}}catch(e){console.error("Error sending desktop notification:",e)}}async startActivityMonitoring(){try{for await(let e of V.F.streamVision(!1)){if(!this.isMonitoring)break;await this.processVisionData(e)}}catch(e){console.error("Error in activity monitoring:",e),this.startPollingMonitoring()}}startPollingMonitoring(){let e=setInterval(async()=>{if(!this.isMonitoring){clearInterval(e);return}try{await this.checkCurrentActivity()}catch(e){console.error("Error in polling monitoring:",e)}},3e4)}async processVisionData(e){try{let t=e.appName||"",s=e.windowName||"",i=new Date;await this.detectFocusModeChanges(t,s,i),await this.checkForDistractions(t,s,i),this.updateCurrentSession(t,s,i)}catch(e){console.error("Error processing vision data:",e)}}async checkCurrentActivity(){try{let e=new Date,t=new Date(e.getTime()-3e5),s=await V.F.queryScreenpipe({startTime:t.toISOString(),endTime:e.toISOString(),contentType:"ocr",limit:10});if((null==s?void 0:s.data)&&s.data.length>0){let e=s.data[s.data.length-1];if("OCR"===e.type){let t=e.content.appName||"",s=e.content.windowName||"",i=new Date(e.content.timestamp);await this.detectFocusModeChanges(t,s,i),await this.checkForDistractions(t,s,i),this.updateCurrentSession(t,s,i)}}}catch(e){console.error("Error checking current activity:",e)}}async detectFocusModeChanges(e,t,s){let i=this.isProductiveApp(e),r=this.isDistractingApp(e);if(!i||this.currentSession&&this.isProductiveApp(this.currentSession.appName)||this.sendNotification({id:(0,X.A)(),type:"focus-reminder",title:"Focus Mode Detected",message:"Great! You're starting focused work in ".concat(e,". Stay concentrated!"),timestamp:s,priority:"low",actionable:!1}),this.currentSession&&this.isProductiveApp(this.currentSession.appName)&&r){let t=(0,Q.o)(s,this.currentSession.startTime);t>=this.config.focusThreshold?this.sendNotification({id:(0,X.A)(),type:"session-complete",title:"Focus Session Complete",message:"Great job! You focused for ".concat(t," minutes. Consider taking a break."),timestamp:s,priority:"medium",actionable:!0,action:{label:"Take Break",callback:()=>this.suggestBreak()}}):this.sendNotification({id:(0,X.A)(),type:"distraction-alert",title:"Distraction Detected",message:"You switched to ".concat(e," after only ").concat(t," minutes. Try to maintain focus!"),timestamp:s,priority:"medium",actionable:!0,action:{label:"Return to Work",callback:()=>this.suggestReturnToWork()}})}}async checkForDistractions(e,t,s){if(!this.isDistractingApp(e))return;let i=(await this.getRecentActivity(15)).filter(e=>this.isDistractingApp(e.appName)).reduce((e,t)=>e+t.duration,0);i>=15&&this.sendNotification({id:(0,X.A)(),type:"distraction-alert",title:"Extended Distraction Detected",message:"You've been on distracting apps for ".concat(Math.round(i)," minutes. Time to refocus!"),timestamp:s,priority:"high",actionable:!0,action:{label:"Start Focus Session",callback:()=>this.suggestFocusSession()}})}updateCurrentSession(e,t,s){this.currentSession&&this.currentSession.appName===e?(this.currentSession.endTime=s,this.currentSession.duration=(0,Q.o)(s,this.currentSession.startTime)):this.currentSession={id:(0,X.A)(),startTime:s,endTime:s,duration:0,appName:e,windowName:t,taskCategory:this.categorizeApp(e),focusScore:0,distractionCount:0,contextSwitches:0,productivity:"medium"}}startBreakReminders(){let e=setInterval(()=>{if(!this.isMonitoring){clearInterval(e);return}this.checkBreakNeeds()},6e4*this.config.breakReminderInterval)}checkBreakNeeds(){if(!this.currentSession)return;let e=(0,Q.o)(new Date,this.currentSession.startTime);e>=120&&e%30==0?this.sendNotification({id:(0,X.A)(),type:"break-suggestion",title:"Long Session Break",message:"You've been working for ".concat(e," minutes. Take a 15-minute break!"),timestamp:new Date,priority:"medium",actionable:!0,action:{label:"Take Break",callback:()=>this.suggestBreak()}}):e>=60&&e%20==0&&this.sendNotification({id:(0,X.A)(),type:"break-suggestion",title:"Focus Break",message:"Great focus! Take a 5-minute break to recharge.",timestamp:new Date,priority:"low",actionable:!0,action:{label:"Take Break",callback:()=>this.suggestBreak()}})}startGoalTracking(){let e=setInterval(()=>{if(!this.isMonitoring){clearInterval(e);return}this.updateGoalProgress()},6e5)}async updateGoalProgress(){for(let e of this.goals.filter(e=>e.isActive)){let t=await this.calculateGoalProgress(e);if(t!==e.current){e.current=t;let s=t/e.target*100;s>=100?this.sendNotification({id:(0,X.A)(),type:"goal-progress",title:"Goal Achieved!",message:"Congratulations! You've achieved your goal: ".concat(e.name),timestamp:new Date,priority:"high",actionable:!1}):s>=75&&s<100&&this.sendNotification({id:(0,X.A)(),type:"goal-progress",title:"Almost There!",message:"You're ".concat(Math.round(s),"% towards your goal: ").concat(e.name),timestamp:new Date,priority:"medium",actionable:!1})}}}async calculateGoalProgress(e){let t=new Date,s=new Date(t.getFullYear(),t.getMonth(),t.getDate());try{let i=await V.F.queryScreenpipe({startTime:s.toISOString(),endTime:t.toISOString(),contentType:"all",limit:1e3});if(!(null==i?void 0:i.data))return e.current;switch(e.type){case"daily-focus-time":return this.calculateFocusTime(i.data);case"reduce-distractions":return this.calculateDistractionReduction(i.data);case"increase-deep-work":return this.calculateDeepWorkTime(i.data);default:return e.current}}catch(t){return console.error("Error calculating goal progress:",t),e.current}}isProductiveApp(e){return["vscode","intellij","xcode","figma","notion","excel","word"].some(t=>e.toLowerCase().includes(t))}isDistractingApp(e){return["facebook","twitter","instagram","youtube","netflix","reddit"].some(t=>e.toLowerCase().includes(t))}categorizeApp(e){return this.isProductiveApp(e)?"deep-work":this.isDistractingApp(e)?"distraction":"unknown"}async getRecentActivity(e){return[]}calculateFocusTime(e){return 0}calculateDistractionReduction(e){return 0}calculateDeepWorkTime(e){return 0}suggestBreak(){console.log("Suggesting break activities...")}suggestReturnToWork(){console.log("Suggesting return to productive work...")}suggestFocusSession(){console.log("Suggesting start of focus session...")}addGoal(e){this.goals.push({...e,id:(0,X.A)(),createdAt:new Date})}getGoals(){return[...this.goals]}updateGoal(e,t){let s=this.goals.findIndex(t=>t.id===e);-1!==s&&(this.goals[s]={...this.goals[s],...t})}deleteGoal(e){this.goals=this.goals.filter(t=>t.id!==e)}getNotifications(){return[...this.notifications]}clearNotifications(){this.notifications=[]}constructor(e){this.notifications=[],this.goals=[],this.isMonitoring=!1,this.currentSession=null,this.lastNotification=null,this.notificationCallbacks=[],this.config=e}}var ee=s(70607);class et{async analyzeTaskContent(e){try{let t=await V.F.queryScreenpipe({startTime:e.start.toISOString(),endTime:e.end.toISOString(),contentType:"ocr",limit:500,includeFrames:!1});if(!(null==t?void 0:t.data))return{detectedTasks:[],insights:[]};let s=[],i=[];for(let e of t.data)if("OCR"===e.type){let t=e.content.text||"",i=e.content.appName||"",r=e.content.windowName||"",n=this.analyzeOCRForTasks(t,i,r);n.confidence>.6&&s.push({id:(0,X.A)(),timestamp:new Date(e.content.timestamp),taskType:n.taskType,description:n.description,confidence:n.confidence,appName:i,windowName:r,ocrText:t.substring(0,200)})}let r=this.generateTaskInsights(s);return i.push(...r),{detectedTasks:s,insights:i}}catch(e){return console.error("Error analyzing task content:",e),{detectedTasks:[],insights:[]}}}analyzeOCRForTasks(e,t,s){let i=e.toLowerCase(),r=t.toLowerCase();s.toLowerCase();let n={taskType:"unknown",confidence:0,description:"Unknown activity"};for(let[e,s]of Object.entries({"deep-work":{keywords:["function","class","import","export","const","let","var","def","public","private"],apps:["vscode","intellij","xcode","sublime","vim"],confidence:.9},communication:{keywords:["message","chat","email","meeting","call","video","send","reply"],apps:["slack","teams","discord","zoom","mail"],confidence:.8},research:{keywords:["search","google","stackoverflow","documentation","tutorial","how to"],apps:["chrome","firefox","safari","edge"],confidence:.7},creative:{keywords:["design","layer","brush","color","font","canvas","artboard"],apps:["photoshop","illustrator","figma","sketch"],confidence:.8},administrative:{keywords:["spreadsheet","document","presentation","table","chart","formula"],apps:["excel","word","powerpoint","sheets","docs"],confidence:.7},learning:{keywords:["course","lesson","tutorial","learn","study","education"],apps:["coursera","udemy","khan"],confidence:.8},entertainment:{keywords:["watch","video","movie","music","game","play"],apps:["netflix","youtube","spotify","steam"],confidence:.9},social:{keywords:["post","like","share","comment","follow","friend"],apps:["facebook","twitter","instagram","linkedin"],confidence:.8}})){let a=0,o=[];s.apps.some(e=>r.includes(e))&&(a+=.4);let c=s.keywords.filter(e=>i.includes(e));c.length>0&&(a+=c.length/s.keywords.length*.6,o=c),(a*=s.confidence)>n.confidence&&(n={taskType:e,confidence:a,description:this.generateTaskDescription(e,o,t)})}return n}generateTaskDescription(e,t,s){let i={"deep-work":"Coding/development work in ".concat(s),communication:"Communication activity in ".concat(s),research:"Research and information gathering in ".concat(s),creative:"Creative/design work in ".concat(s),administrative:"Administrative tasks in ".concat(s),learning:"Learning activity in ".concat(s),entertainment:"Entertainment/leisure in ".concat(s),social:"Social media activity in ".concat(s),distraction:"Potentially distracting activity in ".concat(s),unknown:"Activity in ".concat(s)};return i[e]||i.unknown}generateTaskInsights(e){let t=[],s=e.reduce((e,t)=>(e[t.taskType]=(e[t.taskType]||0)+1,e),{}),i=e.length;if(0===i)return t;let r=Object.entries(s).sort((e,t)=>{let[,s]=e,[,i]=t;return i-s})[0];if(r){let[e,s]=r,n=Math.round(s/i*100);t.push("".concat(n,"% of detected activities were ").concat(e.replace("-"," ")))}let n=Math.round(e.filter(e=>["deep-work","creative","learning","research"].includes(e.taskType)).length/i*100);n>=70?t.push("High productivity detected - great focus on meaningful work!"):n>=40?t.push("Moderate productivity - consider reducing distractions"):t.push("Low productivity detected - focus on high-value tasks");let a=this.analyzeAppSwitching(e);return a>10&&t.push("High app switching detected (".concat(a," switches) - consider batching similar tasks")),t}analyzeAppSwitching(e){let t=0,s="";for(let i of e.sort((e,t)=>e.timestamp.getTime()-t.timestamp.getTime()))s&&s!==i.appName&&t++,s=i.appName;return t}async detectProductivityPatterns(e){let t=[];for(let[s,i]of Object.entries(this.groupSessionsByTimePattern(e))){if(i.length<3)continue;let r=i.reduce((e,t)=>e+t.focusScore,0)/i.length,n=Math.min(100,i.length/e.length*100),[a,o]=s.split("|"),[c,l]=a.split("-").map(Number);t.push({id:(0,X.A)(),name:this.generatePatternName(c,l,o),description:this.generatePatternDescription(r,i.length),timePattern:{startHour:c,endHour:l,daysOfWeek:o.split(",").map(Number)},averageProductivity:r,confidence:n,recommendations:this.generatePatternRecommendations(r,c,l)})}return t.sort((e,t)=>t.confidence-e.confidence)}groupSessionsByTimePattern(e){let t={};return e.forEach(e=>{let s=e.startTime.getHours(),i=e.startTime.getDay(),r=2*Math.floor(s/2),n="".concat(r,"-").concat(r+2),a="".concat(n,"|").concat(0===i||6===i?"0,6":"1,2,3,4,5");t[a]||(t[a]=[]),t[a].push(e)}),t}generatePatternName(e,t,s){let i="".concat(e,":00-").concat(t,":00");return"".concat("0,6"===s?"Weekends":"Weekdays"," ").concat(i)}generatePatternDescription(e,t){return"".concat(e>=80?"high":e>=60?"moderate":"low"," productivity pattern with ").concat(t," sessions")}generatePatternRecommendations(e,t,s){let i=[];return e>=80?(i.push("Excellent productivity window - schedule important tasks here"),i.push("Consider extending this time block if possible")):e>=60?(i.push("Good productivity window - optimize environment for better focus"),i.push("Minimize distractions during this time")):(i.push("Low productivity window - consider rescheduling demanding tasks"),i.push("Use this time for lighter activities or breaks")),t>=6&&t<=10?i.push("Morning energy - great for creative and analytical work"):t>=14&&t<=16?i.push("Post-lunch dip - consider light exercise or break"):t>=20&&i.push("Evening hours - wind down with lighter tasks"),i}async generateBreakRecommendation(e,t){if(!e)return null;let s=(Date.now()-e.startTime.getTime())/6e4,i=0,r="micro",n=5,a="";return(s>=120?(i=90,r="long",n=15,a="Extended focus session - time for a longer break"):s>=60?(i=70,r="short",n=10,a="Good focus session - take a short break to recharge"):s>=25&&(i=50,r="micro",n=5,a="Pomodoro break - quick refresh recommended"),this.analyzeRecentBreaks(t).timeSinceLastBreak>180&&(i=Math.min(100,i+30),a="Long period without breaks - rest is important"),i<40)?null:{id:(0,X.A)(),timestamp:new Date,reason:a,type:r,suggestedDuration:n,activities:this.getBreakActivities(r),urgency:i}}analyzeRecentBreaks(e){let t=Date.now(),s=e[e.length-1];return{timeSinceLastBreak:s?(t-s.endTime.getTime())/6e4:0,averageBreakInterval:60}}getBreakActivities(e){return({micro:["Look away from screen (20-20-20 rule)","Deep breathing exercises","Stretch your neck and shoulders","Drink water"],short:["Walk around the room","Light stretching","Grab a healthy snack","Step outside for fresh air","Chat with a colleague"],long:["Take a walk outside","Have a proper meal","Do some exercise","Meditate or relax","Call a friend or family member"]})[e]}constructor(){this.fuse=new ee.A([],{keys:["text","appName","windowName"],threshold:.3})}}var es=s(36489),ei=s(66848),er=s(12039);class en{async initializeModel(){try{this.model=er.ilg({layers:[er.ZFI.dense({inputShape:[7],units:16,activation:"relu"}),er.ZFI.dropout({rate:.2}),er.ZFI.dense({units:8,activation:"relu"}),er.ZFI.dense({units:1,activation:"sigmoid"})]}),this.model.compile({optimizer:"adam",loss:"meanSquaredError",metrics:["mae"]}),console.log("Pattern recognition model initialized")}catch(e){console.error("Error initializing ML model:",e)}}async analyzeProductivityPatterns(e,t){if(e.length<10)return console.warn("Insufficient data for pattern analysis"),[];let s=this.groupSessionsByTimePatterns(e),i=[];for(let[e,t]of Object.entries(s)){if(t.length<3)continue;let s=await this.analyzeTimePattern(e,t);s&&i.push(s)}let r=this.analyzeWeeklyPatterns(e);i.push(...r);let n=this.analyzeTaskPatterns(e);return i.push(...n),i.sort((e,t)=>t.confidence-e.confidence)}groupSessionsByTimePatterns(e){let t={};return e.forEach(e=>{let s=(0,es.q)(e.startTime),i=(0,ei.P)(e.startTime),r=2*Math.floor(s/2),n="".concat(r,"-").concat(r+2,"_").concat(0===i||6===i?"weekend":"weekday");t[n]||(t[n]=[]),t[n].push(e)}),t}async analyzeTimePattern(e,t){let[s,i]=e.split("_"),[r,n]=s.split("-").map(Number),a=t.reduce((e,t)=>e+t.focusScore,0)/t.length,o=t.reduce((e,t)=>e+t.duration,0)/t.length,c=t.length,l=(Math.max(0,100-this.calculateVariance(t.map(e=>e.focusScore)))+Math.min(100,c/10*100))/2;if(l<30)return null;let d=this.generatePatternInsights(t,a,o),u=this.generatePatternRecommendations(a,r,n,i);return{id:"pattern_".concat(e),name:this.generatePatternName(r,n,i),description:"".concat(i," productivity pattern (").concat(c," sessions)"),timePattern:{startHour:r,endHour:n,daysOfWeek:"weekend"===i?[0,6]:[1,2,3,4,5]},averageProductivity:a,confidence:l,recommendations:[...d,...u]}}analyzeWeeklyPatterns(e){let t={};e.forEach(e=>{let s=(0,ei.P)(e.startTime);t[s]||(t[s]=[]),t[s].push(e)});let s=[],i=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"];return Object.entries(t).forEach(t=>{let[r,n]=t;if(n.length<3)return;let a=parseInt(r),o=n.reduce((e,t)=>e+t.focusScore,0)/n.length,c=Math.min(100,n.length/e.length*700);c>=20&&s.push({id:"weekly_".concat(r),name:"".concat(i[a]," Pattern"),description:"Productivity pattern for ".concat(i[a],"s"),timePattern:{startHour:0,endHour:24,daysOfWeek:[a]},averageProductivity:o,confidence:c,recommendations:this.generateDaySpecificRecommendations(a,o)})}),s}analyzeTaskPatterns(e){let t={};e.forEach(e=>{t[e.taskCategory]||(t[e.taskCategory]=[]),t[e.taskCategory].push(e)});let s=[];return Object.entries(t).forEach(t=>{let[i,r]=t;if(r.length<5)return;let n=r.reduce((e,t)=>e+t.focusScore,0)/r.length,a=Math.min(100,r.length/e.length*500),o=this.analyzeHourlyPerformance(r),c=this.findBestPerformanceHours(o);a>=30&&c.length>0&&s.push({id:"task_".concat(i),name:"".concat(i.replace("-"," ")," Optimization"),description:"Best times for ".concat(i.replace("-"," ")," tasks"),timePattern:{startHour:Math.min(...c),endHour:Math.max(...c)+1,daysOfWeek:[1,2,3,4,5]},averageProductivity:n,confidence:a,recommendations:this.generateTaskSpecificRecommendations(i,c)})}),s}async predictOptimalSchedule(e,t){let s=[];if(!this.model)return console.warn("ML model not available for predictions"),this.generateRuleBasedSchedule(e,t);try{let i=(0,ei.P)(e),r=+(0===i||6===i);for(let n=8;n<18;n++)for(let a of t){let t=[n/24,i/7,r,this.encodeTaskType(a),this.getHistoricalPerformance(n,i,a),this.getEnergyLevel(n),this.getContextualFactors(e,n)],o=this.model.predict(er.KtR([t])),c=100*(await o.data())[0];o.dispose();let l=this.calculatePredictionConfidence(n,i,a);s.push({hour:n,taskType:a,predictedProductivity:c,confidence:l,reasoning:this.generatePredictionReasoning(n,a,c)})}return s.sort((e,t)=>t.predictedProductivity-e.predictedProductivity).slice(0,8)}catch(s){return console.error("Error in ML prediction:",s),this.generateRuleBasedSchedule(e,t)}}generateRuleBasedSchedule(e,t){let s=[],i=(0,ei.P)(e),r=0===i||6===i,n={"deep-work":r?[10,11,14,15]:[9,10,14,15],creative:[10,11,16,17],communication:[11,12,14,16],administrative:[13,14,15,16],learning:[9,10,11,15],research:[9,10,14,15],entertainment:[18,19,20,21],social:[12,13,17,18],distraction:[16,17,18,19],unknown:[10,11,14,15]};return t.forEach(e=>{(n[e]||[10,11,14,15]).forEach(t=>{s.push({hour:t,taskType:e,predictedProductivity:this.getRuleBasedProductivity(t,e,r),confidence:70,reasoning:"Based on general productivity patterns for ".concat(e.replace("-"," "))})})}),s.sort((e,t)=>t.predictedProductivity-e.predictedProductivity)}calculateVariance(e){let t=e.reduce((e,t)=>e+t,0)/e.length;return e.map(e=>Math.pow(e-t,2)).reduce((e,t)=>e+t,0)/e.length}generatePatternName(e,t,s){let i="".concat(e,":00-").concat(t,":00");return"".concat("weekend"===s?"Weekend":"Weekday"," ").concat(i)}generatePatternInsights(e,t,s){let i=[];return t>=80?i.push("High-performance time window - excellent for important tasks"):t>=60?i.push("Good productivity window - suitable for focused work"):i.push("Lower productivity period - consider lighter tasks"),s>=60?i.push("Long focus sessions typical - good for deep work"):s>=30?i.push("Moderate session length - good for regular tasks"):i.push("Short sessions typical - better for quick tasks"),i}generatePatternRecommendations(e,t,s,i){let r=[];return e>=80?(r.push("Schedule your most important work during this time"),r.push("Minimize interruptions and distractions")):e>=60?(r.push("Good time for regular focused work"),r.push("Consider optimizing environment for better focus")):(r.push("Use for lighter tasks or administrative work"),r.push("Consider taking breaks or doing physical activity")),t<=10?r.push("Morning energy - great for analytical tasks"):t>=14&&t<=16&&r.push("Post-lunch period - may need energy boost"),r}generateDaySpecificRecommendations(e,t){let s=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"][e],i=["Optimize ".concat(s," productivity")];return 1===e?i.push("Start week with planning and goal setting"):5===e?i.push("Wrap up weekly tasks and plan for next week"):(0===e||6===e)&&i.push("Focus on personal projects and learning"),i}generateTaskSpecificRecommendations(e,t){let s=[],i=t.map(e=>"".concat(e,":00")).join(", ");switch(s.push("Best performance for ".concat(e.replace("-"," ")," at ").concat(i)),e){case"deep-work":s.push("Minimize distractions and notifications"),s.push("Ensure comfortable environment and good lighting");break;case"creative":s.push("Allow for inspiration and experimentation"),s.push("Consider background music or change of scenery");break;case"communication":s.push("Batch similar communication tasks together"),s.push("Set specific times for checking messages")}return s}analyzeHourlyPerformance(e){let t={};e.forEach(e=>{let s=(0,es.q)(e.startTime);t[s]||(t[s]={total:0,count:0}),t[s].total+=e.focusScore,t[s].count+=1});let s={};return Object.entries(t).forEach(e=>{let[t,i]=e;s[parseInt(t)]=i.total/i.count}),s}findBestPerformanceHours(e){let t=Object.values(e).reduce((e,t)=>e+t,0)/Object.values(e).length;return Object.entries(e).filter(e=>{let[,s]=e;return s>1.1*t}).map(e=>{let[t]=e;return parseInt(t)})}encodeTaskType(e){return({"deep-work":.9,creative:.8,research:.7,learning:.6,administrative:.5,communication:.4,social:.3,entertainment:.2,distraction:.1,unknown:0})[e]||0}getHistoricalPerformance(e,t,s){return .7}getEnergyLevel(e){return e>=9&&e<=11?.9:e>=14&&e<=16?.8:e>=20||e<=6?.3:.6}getContextualFactors(e,t){return .5}calculatePredictionConfidence(e,t,s){return 75}generatePredictionReasoning(e,t,s){return s>=80?"Optimal time for ".concat(t.replace("-"," ")," based on historical performance"):s>=60?"Good time for ".concat(t.replace("-"," ")," with moderate productivity expected"):"Lower productivity expected for ".concat(t.replace("-"," ")," at this time")}getRuleBasedProductivity(e,t,s){let i=70;return e>=9&&e<=11&&(i+=15),e>=14&&e<=16&&(i+=10),(e<=8||e>=18)&&(i-=20),"deep-work"===t&&e>=9&&e<=11&&(i+=10),"creative"===t&&e>=10&&e<=12&&(i+=10),"communication"===t&&e>=11&&e<=16&&(i+=5),s&&(i-=10),Math.max(0,Math.min(100,i))}constructor(){this.patterns=[],this.energyLevels=[],this.model=null,this.initializeModel()}}var ea=s(6711),eo=s(19828),ec=s(72132),el=s(10415),ed=s(56671),eu=s(84868),em=s(58086),eh=s(14173);function ep(){let e=new Date,t=[],s=(0,eu.O)(e,8);[{app:"Visual Studio Code",category:"deep-work",duration:45,focusScore:85,distractions:2},{app:"Chrome",category:"research",duration:25,focusScore:65,distractions:5},{app:"Slack",category:"communication",duration:15,focusScore:40,distractions:8},{app:"Figma",category:"creative",duration:60,focusScore:90,distractions:1},{app:"Notion",category:"administrative",duration:30,focusScore:70,distractions:3},{app:"YouTube",category:"entertainment",duration:20,focusScore:20,distractions:12},{app:"IntelliJ IDEA",category:"deep-work",duration:75,focusScore:88,distractions:1},{app:"Discord",category:"social",duration:10,focusScore:30,distractions:6}].forEach((e,i)=>{var r;let n=(0,em.z)(s,20*i),a=(0,em.z)(n,e.duration);t.push({id:(0,X.A)(),startTime:n,endTime:a,duration:e.duration,appName:e.app,windowName:"".concat(e.app," - Main Window"),taskCategory:e.category,focusScore:e.focusScore,distractionCount:e.distractions,contextSwitches:Math.floor(e.distractions/2),productivity:(r=e.focusScore)>=90?"very-high":r>=75?"high":r>=50?"medium":r>=25?"low":"very-low"})});let i=t.reduce((e,t)=>e+t.duration,0),r=t.reduce((e,t)=>e+t.focusScore,0)/t.length,n=t.filter(e=>e.duration>=45&&e.focusScore>=70).length,a=t.reduce((e,t)=>e+t.contextSwitches,0),o=i/t.length,c=new Map;t.forEach(e=>{let t=c.get(e.appName);t?(t.totalTime+=e.duration,t.sessionCount+=1,t.focusTime+=e.focusScore>=70?e.duration:0,t.distractionTime+=e.focusScore<50?e.duration:0):c.set(e.appName,{appName:e.appName,totalTime:e.duration,focusTime:e.focusScore>=70?e.duration:0,distractionTime:e.focusScore<50?e.duration:0,sessionCount:1,averageSessionLength:e.duration,productivityScore:e.focusScore})}),c.forEach(e=>{e.averageSessionLength=e.totalTime/e.sessionCount});let l=Array.from(c.values()),d=l.filter(e=>e.productivityScore>=70).sort((e,t)=>t.focusTime-e.focusTime).slice(0,5),u=l.filter(e=>e.productivityScore<50).sort((e,t)=>t.distractionTime-e.distractionTime).slice(0,5),m=new Map;t.forEach(e=>{let t=e.startTime.getHours(),s=m.get(t);s?(s.total+=e.focusScore,s.count+=1):m.set(t,{total:e.focusScore,count:1})});let h=9,p=15,g=0,f=100;m.forEach((e,t)=>{let s=e.total/e.count;s>g&&(g=s,h=t),s<f&&(f=s,p=t)});let x={date:e,totalFocusTime:i,totalDistractionTime:t.filter(e=>e.focusScore<50).reduce((e,t)=>e+t.duration,0),focusScore:r,contextSwitches:a,deepWorkSessions:n,averageSessionLength:o,mostProductiveHour:h,leastProductiveHour:p,topProductiveApps:d,topDistractingApps:u};return{focusSessions:t,productivityMetrics:x}}function eg(){return[{id:(0,X.A)(),type:"focus-reminder",title:"Great Focus Session!",message:"You've been focused for 45 minutes. Keep up the excellent work!",timestamp:(0,eh.Y)(new Date,5),priority:"low",actionable:!1},{id:(0,X.A)(),type:"break-suggestion",title:"Time for a Break",message:"You've been working for 2 hours. Consider taking a 10-minute break.",timestamp:(0,eh.Y)(new Date,15),priority:"medium",actionable:!0,action:{label:"Take Break",callback:()=>console.log("Taking break...")}},{id:(0,X.A)(),type:"distraction-alert",title:"Distraction Detected",message:"You switched to social media. Try to stay focused on your current task.",timestamp:(0,eh.Y)(new Date,30),priority:"high",actionable:!0,action:{label:"Return to Work",callback:()=>console.log("Returning to work...")}}]}function ef(){return[{id:(0,X.A)(),name:"Daily Focus Time",description:"Achieve 6 hours of focused work per day",type:"daily-focus-time",target:360,current:265,unit:"minutes",deadline:new Date(new Date().setHours(23,59,59)),isActive:!0,createdAt:(0,eu.O)(new Date,24)},{id:(0,X.A)(),name:"Deep Work Sessions",description:"Complete 3 deep work sessions (45+ minutes) daily",type:"increase-deep-work",target:3,current:2,unit:"sessions",deadline:new Date(new Date().setHours(23,59,59)),isActive:!0,createdAt:(0,eu.O)(new Date,24)},{id:(0,X.A)(),name:"Reduce Distractions",description:"Keep context switches under 15 per day",type:"reduce-distractions",target:15,current:12,unit:"switches",deadline:new Date(new Date().setHours(23,59,59)),isActive:!0,createdAt:(0,eu.O)(new Date,24)}]}var ex=s(76011);let ey=ex.Kq,ev=ex.bL,ew=ex.l9,ej=r.forwardRef((e,t)=>{let{className:s,sideOffset:r=4,...n}=e;return(0,i.jsx)(ex.UC,{ref:t,sideOffset:r,className:(0,y.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",s),...n})});ej.displayName=ex.UC.displayName;var eb=s(51154),ek=s(40646),eN=s(54861),eS=s(85339);function eT(){let{screenpipeStatus:e,screenpipeError:t}=p(),s=(()=>{switch(e){case"checking":return{icon:(0,i.jsx)(eb.A,{className:"h-3 w-3 animate-spin"}),label:"Checking...",variant:"secondary",description:"Checking Screenpipe connection..."};case"connected":return{icon:(0,i.jsx)(ek.A,{className:"h-3 w-3"}),label:"Connected",variant:"default",description:"Connected to Screenpipe - real data available"};case"disconnected":return{icon:(0,i.jsx)(eN.A,{className:"h-3 w-3"}),label:"Demo Mode",variant:"destructive",description:t||"Screenpipe not available - showing demo data"};default:return{icon:(0,i.jsx)(eS.A,{className:"h-3 w-3"}),label:"Unknown",variant:"secondary",description:"Unknown connection status"}}})();return(0,i.jsx)(ey,{children:(0,i.jsxs)(ev,{children:[(0,i.jsx)(ew,{asChild:!0,children:(0,i.jsxs)(F,{variant:s.variant,className:"flex items-center gap-1 cursor-help",children:[s.icon,s.label]})}),(0,i.jsx)(ej,{children:(0,i.jsx)("p",{className:"max-w-xs",children:s.description})})]})})}function eA(){let{screenpipeStatus:e,screenpipeError:t}=p();return"connected"===e?null:"checking"===e?(0,i.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6",children:(0,i.jsxs)("div",{className:"flex items-center gap-3",children:[(0,i.jsx)(eb.A,{className:"h-5 w-5 text-blue-600 animate-spin"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"font-medium text-blue-900",children:"Checking Screenpipe Connection"}),(0,i.jsx)("p",{className:"text-sm text-blue-700",children:"Verifying if Screenpipe is running..."})]})]})}):(0,i.jsx)("div",{className:"bg-orange-50 border border-orange-200 rounded-lg p-4 mb-6",children:(0,i.jsxs)("div",{className:"flex items-start gap-3",children:[(0,i.jsx)(eS.A,{className:"h-5 w-5 text-orange-600 mt-0.5"}),(0,i.jsxs)("div",{className:"flex-1",children:[(0,i.jsx)("h3",{className:"font-medium text-orange-900",children:"Demo Mode Active"}),(0,i.jsx)("p",{className:"text-sm text-orange-700 mt-1",children:"Screenpipe is not running. The plugin is displaying sample data for demonstration."}),t&&(0,i.jsx)("p",{className:"text-xs text-orange-600 mt-2 font-mono bg-orange-100 p-2 rounded",children:t}),(0,i.jsxs)("div",{className:"mt-3 text-sm text-orange-700",children:[(0,i.jsx)("p",{className:"font-medium",children:"To connect to real data:"}),(0,i.jsxs)("ol",{className:"list-decimal list-inside mt-1 space-y-1",children:[(0,i.jsx)("li",{children:"Install and start Screenpipe"}),(0,i.jsx)("li",{children:"Ensure it's running on port 3030"}),(0,i.jsx)("li",{children:"Refresh this page"})]})]})]})]})})}var eC=s(83540),eP=s(99445),eD=s(94754),eM=s(96025),eR=s(16238),eE=s(94517),eF=s(62341),eI=s(8782),ez=s(34e3),eO=s(54811),eB=s(93504),eG=s(21374),eL=s(1243);function eW(e){let{metrics:t,sessions:s,timeRange:r}=e;if(!t)return(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[void 0,void 0,void 0,void 0].map((e,t)=>(0,i.jsx)(v,{children:(0,i.jsx)(k,{className:"p-6",children:(0,i.jsxs)("div",{className:"animate-pulse",children:[(0,i.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-2"}),(0,i.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/2"})]})})},t))});let n=function(e){let t={};for(let e=0;e<24;e++)t[e]={totalScore:0,count:0};return e.forEach(e=>{let s=(0,es.q)(e.startTime);t[s].totalScore+=e.focusScore,t[s].count+=1}),Object.entries(t).map(e=>{let[t,s]=e;return{hour:parseInt(t),focusScore:s.count>0?s.totalScore/s.count:0}})}(s),a=function(e){let t={},s={"deep-work":"#3b82f6",communication:"#10b981",research:"#f59e0b",creative:"#8b5cf6",administrative:"#6b7280",learning:"#06b6d4",entertainment:"#ef4444",social:"#ec4899",distraction:"#f97316",unknown:"#9ca3af"};return e.forEach(e=>{t[e.taskCategory]=(t[e.taskCategory]||0)+e.duration}),Object.entries(t).map(e=>{let[t,i]=e;return{name:t.replace("-"," "),value:i,color:s[t]||"#9ca3af"}})}(s),o=s.sort((e,t)=>e.startTime.getTime()-t.startTime.getTime()).map(e=>({time:(0,el.GP)(e.startTime,"HH:mm"),focusScore:e.focusScore}));return(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,i.jsx)(eH,{title:"Total Focus Time",value:"".concat(Math.round(t.totalFocusTime)," min"),icon:(0,i.jsx)(G.A,{className:"h-5 w-5"}),trend:e_("focusTime",t.totalFocusTime),color:"blue"}),(0,i.jsx)(eH,{title:"Focus Score",value:"".concat(Math.round(t.focusScore),"%"),icon:(0,i.jsx)(_.A,{className:"h-5 w-5"}),trend:e_("focusScore",t.focusScore),color:"green"}),(0,i.jsx)(eH,{title:"Deep Work Sessions",value:t.deepWorkSessions.toString(),icon:(0,i.jsx)(W.A,{className:"h-5 w-5"}),trend:e_("deepWork",t.deepWorkSessions),color:"purple"}),(0,i.jsx)(eH,{title:"Context Switches",value:t.contextSwitches.toString(),icon:(0,i.jsx)(eL.A,{className:"h-5 w-5"}),trend:e_("contextSwitches",t.contextSwitches),color:"orange",inverse:!0})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,i.jsxs)(v,{children:[(0,i.jsxs)(w,{children:[(0,i.jsx)(j,{children:"Productivity by Hour"}),(0,i.jsx)(b,{children:"Your focus patterns throughout the day"})]}),(0,i.jsx)(k,{children:(0,i.jsx)(eC.u,{width:"100%",height:300,children:(0,i.jsxs)(eP.Q,{data:n,children:[(0,i.jsx)(eD.d,{strokeDasharray:"3 3"}),(0,i.jsx)(eM.W,{dataKey:"hour"}),(0,i.jsx)(eR.h,{}),(0,i.jsx)(eE.m,{formatter:e=>["".concat(Math.round(e),"%"),"Focus Score"],labelFormatter:e=>"".concat(e,":00")}),(0,i.jsx)(eF.G,{type:"monotone",dataKey:"focusScore",stroke:"#3b82f6",fill:"#3b82f6",fillOpacity:.3})]})})})]}),(0,i.jsxs)(v,{children:[(0,i.jsxs)(w,{children:[(0,i.jsx)(j,{children:"Task Distribution"}),(0,i.jsx)(b,{children:"Time spent on different types of activities"})]}),(0,i.jsx)(k,{children:(0,i.jsx)(eC.u,{width:"100%",height:300,children:(0,i.jsxs)(eI.r,{children:[(0,i.jsx)(ez.F,{data:a,cx:"50%",cy:"50%",labelLine:!1,label:e=>{let{name:t,percent:s}=e;return"".concat(t," ").concat((100*s).toFixed(0),"%")},outerRadius:80,fill:"#8884d8",dataKey:"value",children:a.map((e,t)=>(0,i.jsx)(eO.f,{fill:e.color},"cell-".concat(t)))}),(0,i.jsx)(eE.m,{formatter:e=>["".concat(Math.round(e)," min"),"Time"]})]})})})]})]}),(0,i.jsxs)(v,{children:[(0,i.jsxs)(w,{children:[(0,i.jsx)(j,{children:"Productivity Trend"}),(0,i.jsx)(b,{children:"Focus score progression over time"})]}),(0,i.jsx)(k,{children:(0,i.jsx)(eC.u,{width:"100%",height:200,children:(0,i.jsxs)(eB.b,{data:o,children:[(0,i.jsx)(eD.d,{strokeDasharray:"3 3"}),(0,i.jsx)(eM.W,{dataKey:"time"}),(0,i.jsx)(eR.h,{domain:[0,100]}),(0,i.jsx)(eE.m,{formatter:e=>["".concat(Math.round(e),"%"),"Focus Score"]}),(0,i.jsx)(eG.N,{type:"monotone",dataKey:"focusScore",stroke:"#10b981",strokeWidth:2,dot:{fill:"#10b981",strokeWidth:2,r:4}})]})})})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,i.jsxs)(v,{children:[(0,i.jsxs)(w,{children:[(0,i.jsx)(j,{children:"Most Productive Apps"}),(0,i.jsx)(b,{children:"Apps where you maintain highest focus"})]}),(0,i.jsx)(k,{children:(0,i.jsx)("div",{className:"space-y-4",children:t.topProductiveApps.slice(0,5).map((e,t)=>(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"flex items-center gap-3",children:[(0,i.jsx)("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:(0,i.jsx)("span",{className:"text-green-600 font-semibold text-sm",children:t+1})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"font-medium",children:e.appName}),(0,i.jsxs)("p",{className:"text-sm text-gray-500",children:[Math.round(e.focusTime)," min focused"]})]})]}),(0,i.jsxs)(F,{variant:"outline",className:"bg-green-50 text-green-700",children:[Math.round(e.productivityScore),"%"]})]},e.appName))})})]}),(0,i.jsxs)(v,{children:[(0,i.jsxs)(w,{children:[(0,i.jsx)(j,{children:"Distraction Sources"}),(0,i.jsx)(b,{children:"Apps that tend to break your focus"})]}),(0,i.jsx)(k,{children:(0,i.jsx)("div",{className:"space-y-4",children:t.topDistractingApps.slice(0,5).map((e,t)=>(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"flex items-center gap-3",children:[(0,i.jsx)("div",{className:"w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center",children:(0,i.jsx)("span",{className:"text-orange-600 font-semibold text-sm",children:t+1})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"font-medium",children:e.appName}),(0,i.jsxs)("p",{className:"text-sm text-gray-500",children:[Math.round(e.distractionTime)," min distracted"]})]})]}),(0,i.jsxs)(F,{variant:"outline",className:"bg-orange-50 text-orange-700",children:[e.sessionCount," interruptions"]})]},e.appName))})})]})]}),(0,i.jsxs)(v,{children:[(0,i.jsxs)(w,{children:[(0,i.jsx)(j,{children:"Key Insights"}),(0,i.jsx)(b,{children:"AI-generated insights about your productivity patterns"})]}),(0,i.jsx)(k,{children:(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsx)(eY,{icon:(0,i.jsx)(L.A,{className:"h-5 w-5 text-green-600"}),title:"Peak Performance",description:"Your most productive hour is ".concat(t.mostProductiveHour,":00"),type:"positive"}),(0,i.jsx)(eY,{icon:(0,i.jsx)(q.A,{className:"h-5 w-5 text-blue-600"}),title:"Energy Dip",description:"Consider breaks around ".concat(t.leastProductiveHour,":00"),type:"neutral"}),(0,i.jsx)(eY,{icon:(0,i.jsx)(ek.A,{className:"h-5 w-5 text-green-600"}),title:"Session Quality",description:"Average session length: ".concat(Math.round(t.averageSessionLength)," minutes"),type:"positive"}),(0,i.jsx)(eY,{icon:(0,i.jsx)(eL.A,{className:"h-5 w-5 text-orange-600"}),title:"Focus Improvement",description:"".concat(t.contextSwitches," context switches detected"),type:"warning"})]})})]})]})}function eH(e){let{title:t,value:s,icon:r,trend:n,color:a,inverse:o=!1}=e;return(0,i.jsx)(v,{children:(0,i.jsxs)(k,{className:"p-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsx)("div",{className:"p-2 rounded-lg ".concat({blue:"bg-blue-50 text-blue-600",green:"bg-green-50 text-green-600",purple:"bg-purple-50 text-purple-600",orange:"bg-orange-50 text-orange-600"}[a]),children:r}),n&&(0,i.jsxs)(F,{variant:"outline",className:"".concat(n.isPositive&&!o||!n.isPositive&&o?"bg-green-50 text-green-700":"bg-red-50 text-red-700"),children:[n.isPositive?"+":"",n.value,"%"]})]}),(0,i.jsxs)("div",{className:"mt-4",children:[(0,i.jsx)("h3",{className:"text-2xl font-bold",children:s}),(0,i.jsx)("p",{className:"text-gray-600 text-sm",children:t})]})]})})}function eY(e){let{icon:t,title:s,description:r,type:n}=e;return(0,i.jsx)("div",{className:"p-4 rounded-lg border ".concat({positive:"border-green-200 bg-green-50",neutral:"border-blue-200 bg-blue-50",warning:"border-orange-200 bg-orange-50"}[n]),children:(0,i.jsxs)("div",{className:"flex items-start gap-3",children:[t,(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-medium",children:s}),(0,i.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:r})]})]})})}function e_(e,t){return({focusTime:{value:12,isPositive:!0},focusScore:{value:8,isPositive:!0},deepWork:{value:15,isPositive:!0},contextSwitches:{value:-5,isPositive:!1}})[e]}function eU(){let[e,t]=(0,r.useState)(!1),[s,n]=(0,r.useState)("overview"),[a,o]=(0,r.useState)("today"),[c,l]=(0,r.useState)(!0),[d,u]=(0,r.useState)(null),[m,h]=(0,r.useState)([]),[p,g]=(0,r.useState)([]),[f,x]=(0,r.useState)([]),[y,N]=(0,r.useState)(null),[P,D]=(0,r.useState)(null),[M,E]=(0,r.useState)(null),[I,V]=(0,r.useState)(null),[Z,Q]=(0,r.useState)({focusThreshold:15,distractionThreshold:30,contextSwitchWindow:5,breakReminderInterval:60,deepWorkMinimum:45,productivityCategories:[],enableRealTimeCoaching:!0,enableBreakReminders:!0,enableGoalTracking:!0});(0,r.useEffect)(()=>{(async()=>{try{let e=new J(Z),t=new $(Z),s=new et,i=new en;N(e),D(t),E(s),V(i),t.onNotification(e=>{var t,s;g(t=>[e,...t.slice(0,9)]),(0,ed.oR)(e.title,{description:e.message,action:e.actionable?{label:(null===(t=e.action)||void 0===t?void 0:t.label)||"Action",onClick:(null===(s=e.action)||void 0===s?void 0:s.callback)||(()=>{})}:void 0})}),l(!1)}catch(e){console.error("Error initializing analytics engines:",e),ed.oR.error("Failed to initialize analytics engines"),l(!1)}})()},[Z]),(0,r.useEffect)(()=>{!c&&(async()=>{try{if(y){let e={today:{start:(0,ea.o)(new Date),end:(0,eo.D)(new Date)},yesterday:{start:(0,ea.o)((0,ec.e)(new Date,1)),end:(0,eo.D)((0,ec.e)(new Date,1))},week:{start:(0,ea.o)((0,ec.e)(new Date,7)),end:(0,eo.D)(new Date)},month:{start:(0,ea.o)((0,ec.e)(new Date,30)),end:(0,eo.D)(new Date)}}[a],t=await y.analyzeActivity(e);if(t.focusSessions.length>0)h(t.focusSessions),u(t.metrics);else{let e=ep();h(e.focusSessions),u(e.productivityMetrics),ed.oR.info("Using demo data - start Screenpipe to see real analytics")}}else{let e=ep();h(e.focusSessions),u(e.productivityMetrics),g(eg()),x(ef()),ed.oR.info("Demo mode - install and run Screenpipe for real analytics")}}catch(t){console.error("Error loading analytics data:",t);let e=ep();h(e.focusSessions),u(e.productivityMetrics),g(eg()),x(ef()),ed.oR.error("Failed to load real data, showing demo data")}})()},[y,a,c]);let X=async()=>{if(P)try{e?(P.stopMonitoring(),t(!1),ed.oR.success("Stopped productivity monitoring")):(await P.startMonitoring(),t(!0),ed.oR.success("Started productivity monitoring"))}catch(e){console.error("Error toggling monitoring:",e),ed.oR.error("Failed to toggle monitoring")}},ee=e=>{Q(t=>({...t,...e})),ed.oR.success("Settings updated")};return c?(0,i.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,i.jsx)("p",{className:"text-gray-600",children:"Initializing Focus Analytics..."})]})}):(0,i.jsxs)("div",{className:"container mx-auto p-6 space-y-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Focus Analytics & Productivity Coach"}),(0,i.jsx)("p",{className:"text-gray-600 mt-1",children:"AI-powered insights to optimize your productivity and focus"})]}),(0,i.jsxs)("div",{className:"flex items-center gap-4",children:[(0,i.jsx)(eT,{}),(0,i.jsxs)("select",{value:a,onChange:e=>o(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,i.jsx)("option",{value:"today",children:"Today"}),(0,i.jsx)("option",{value:"yesterday",children:"Yesterday"}),(0,i.jsx)("option",{value:"week",children:"Last 7 Days"}),(0,i.jsx)("option",{value:"month",children:"Last 30 Days"})]}),(0,i.jsx)(R,{onClick:X,variant:e?"destructive":"default",className:"flex items-center gap-2",children:e?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(O.A,{className:"h-4 w-4"}),"Stop Monitoring"]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(B.A,{className:"h-4 w-4"}),"Start Monitoring"]})})]})]}),(0,i.jsx)(eA,{}),(0,i.jsx)(v,{children:(0,i.jsx)(k,{className:"p-4",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"flex items-center gap-4",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)("div",{className:"w-3 h-3 rounded-full ".concat(e?"bg-green-500":"bg-gray-400")}),(0,i.jsx)("span",{className:"text-sm font-medium",children:e?"Monitoring Active":"Monitoring Inactive"})]}),d&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)(F,{variant:"outline",className:"flex items-center gap-1",children:[(0,i.jsx)(G.A,{className:"h-3 w-3"}),Math.round(d.totalFocusTime)," min focused"]}),(0,i.jsxs)(F,{variant:"outline",className:"flex items-center gap-1",children:[(0,i.jsx)(L.A,{className:"h-3 w-3"}),Math.round(d.focusScore),"% focus score"]}),(0,i.jsxs)(F,{variant:"outline",className:"flex items-center gap-1",children:[(0,i.jsx)(W.A,{className:"h-3 w-3"}),d.deepWorkSessions," deep work sessions"]})]})]}),p.length>0&&(0,i.jsxs)(R,{variant:"ghost",size:"sm",className:"flex items-center gap-2",children:[(0,i.jsx)(H.A,{className:"h-4 w-4"}),p.length," notifications"]})]})})}),(0,i.jsxs)(S,{value:s,onValueChange:n,className:"space-y-6",children:[(0,i.jsxs)(T,{className:"grid w-full grid-cols-6",children:[(0,i.jsxs)(A,{value:"overview",className:"flex items-center gap-2",children:[(0,i.jsx)(Y.A,{className:"h-4 w-4"}),"Overview"]}),(0,i.jsxs)(A,{value:"sessions",className:"flex items-center gap-2",children:[(0,i.jsx)(G.A,{className:"h-4 w-4"}),"Sessions"]}),(0,i.jsxs)(A,{value:"coaching",className:"flex items-center gap-2",children:[(0,i.jsx)(_.A,{className:"h-4 w-4"}),"Coaching"]}),(0,i.jsxs)(A,{value:"goals",className:"flex items-center gap-2",children:[(0,i.jsx)(U.A,{className:"h-4 w-4"}),"Goals"]}),(0,i.jsxs)(A,{value:"patterns",className:"flex items-center gap-2",children:[(0,i.jsx)(L.A,{className:"h-4 w-4"}),"Patterns"]}),(0,i.jsxs)(A,{value:"settings",className:"flex items-center gap-2",children:[(0,i.jsx)(K.A,{className:"h-4 w-4"}),"Settings"]})]}),(0,i.jsx)(C,{value:"overview",className:"space-y-6",children:(0,i.jsx)(eW,{metrics:d,sessions:m,timeRange:a})}),(0,i.jsx)(C,{value:"sessions",className:"space-y-6",children:(0,i.jsxs)("div",{className:"text-center py-12",children:[(0,i.jsx)(G.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,i.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Focus Sessions"}),(0,i.jsx)("p",{className:"text-gray-600",children:"Detailed session analysis coming soon..."})]})}),(0,i.jsxs)(C,{value:"coaching",className:"space-y-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h2",{className:"text-2xl font-bold",children:"Real-time Coaching"}),(0,i.jsx)("p",{className:"text-gray-600",children:"AI-powered insights and suggestions for better productivity"})]}),(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)("div",{className:"w-3 h-3 rounded-full ".concat(e?"bg-green-500":"bg-gray-400")}),(0,i.jsx)("span",{className:"text-sm",children:e?"Coaching Active":"Coaching Inactive"})]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,i.jsxs)(v,{children:[(0,i.jsxs)(w,{children:[(0,i.jsx)(j,{children:"Recent Notifications"}),(0,i.jsx)(b,{children:"Latest coaching insights and suggestions"})]}),(0,i.jsx)(k,{children:(0,i.jsx)("div",{className:"space-y-4",children:p.length>0?p.slice(0,5).map(e=>(0,i.jsxs)("div",{className:"flex items-start gap-3 p-3 bg-gray-50 rounded-lg",children:[(0,i.jsx)("div",{className:"w-2 h-2 rounded-full mt-2 ".concat("high"===e.priority?"bg-red-500":"medium"===e.priority?"bg-yellow-500":"bg-blue-500")}),(0,i.jsxs)("div",{className:"flex-1",children:[(0,i.jsx)("h4",{className:"font-medium text-sm",children:e.title}),(0,i.jsx)("p",{className:"text-xs text-gray-600 mt-1",children:e.message}),(0,i.jsxs)("div",{className:"flex items-center justify-between mt-2",children:[(0,i.jsx)("span",{className:"text-xs text-gray-500",children:(0,el.GP)(e.timestamp,"HH:mm")}),e.actionable&&e.action&&(0,i.jsx)(R,{size:"sm",variant:"outline",onClick:e.action.callback,className:"text-xs h-6",children:e.action.label})]})]})]},e.id)):(0,i.jsxs)("div",{className:"text-center py-8",children:[(0,i.jsx)(H.A,{className:"h-8 w-8 text-gray-400 mx-auto mb-2"}),(0,i.jsx)("p",{className:"text-sm text-gray-500",children:"No notifications yet"})]})})})]}),(0,i.jsxs)(v,{children:[(0,i.jsxs)(w,{children:[(0,i.jsx)(j,{children:"AI Insights"}),(0,i.jsx)(b,{children:"Personalized productivity recommendations"})]}),(0,i.jsx)(k,{children:(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg border border-blue-200",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,i.jsx)(_.A,{className:"h-4 w-4 text-blue-600"}),(0,i.jsx)("span",{className:"font-medium text-blue-900",children:"Focus Pattern"})]}),(0,i.jsx)("p",{className:"text-sm text-blue-800",children:"You're most productive between 9-11 AM. Schedule important tasks during this window."})]}),(0,i.jsxs)("div",{className:"p-4 bg-green-50 rounded-lg border border-green-200",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,i.jsx)(L.A,{className:"h-4 w-4 text-green-600"}),(0,i.jsx)("span",{className:"font-medium text-green-900",children:"Improvement"})]}),(0,i.jsx)("p",{className:"text-sm text-green-800",children:"Your focus score improved by 15% this week. Keep up the great work!"})]}),(0,i.jsxs)("div",{className:"p-4 bg-orange-50 rounded-lg border border-orange-200",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,i.jsx)(q.A,{className:"h-4 w-4 text-orange-600"}),(0,i.jsx)("span",{className:"font-medium text-orange-900",children:"Break Suggestion"})]}),(0,i.jsx)("p",{className:"text-sm text-orange-800",children:"Consider taking a 5-minute break every 45 minutes to maintain peak performance."})]})]})})]})]})]}),(0,i.jsxs)(C,{value:"goals",className:"space-y-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h2",{className:"text-2xl font-bold",children:"Productivity Goals"}),(0,i.jsx)("p",{className:"text-gray-600",children:"Track your progress towards better focus and productivity"})]}),(0,i.jsxs)(R,{onClick:()=>ed.oR.info("Goal creation coming soon!"),children:[(0,i.jsx)(U.A,{className:"h-4 w-4 mr-2"}),"New Goal"]})]}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:f.map(e=>(0,i.jsxs)(v,{children:[(0,i.jsxs)(w,{children:[(0,i.jsx)(j,{className:"text-lg",children:e.name}),(0,i.jsx)(b,{children:e.description})]}),(0,i.jsx)(k,{children:(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsxs)("div",{className:"flex justify-between text-sm mb-2",children:[(0,i.jsx)("span",{children:"Progress"}),(0,i.jsxs)("span",{children:[e.current," / ",e.target," ",e.unit]})]}),(0,i.jsx)(z,{value:e.current/e.target*100,className:"h-2"}),(0,i.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:[Math.round(e.current/e.target*100),"% complete"]})]}),(0,i.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,i.jsx)("span",{className:"text-gray-500",children:"Status"}),(0,i.jsx)(F,{variant:e.isActive?"default":"secondary",children:e.isActive?"Active":"Inactive"})]}),e.deadline&&(0,i.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,i.jsx)("span",{className:"text-gray-500",children:"Deadline"}),(0,i.jsx)("span",{children:(0,el.GP)(e.deadline,"MMM dd, HH:mm")})]})]})})]},e.id))}),0===f.length&&(0,i.jsx)(v,{children:(0,i.jsxs)(k,{className:"text-center py-12",children:[(0,i.jsx)(U.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,i.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Goals Set"}),(0,i.jsx)("p",{className:"text-gray-600 mb-4",children:"Create your first productivity goal to start tracking progress"}),(0,i.jsx)(R,{onClick:()=>ed.oR.info("Goal creation coming soon!"),children:"Create Goal"})]})})]}),(0,i.jsx)(C,{value:"patterns",className:"space-y-6",children:(0,i.jsxs)("div",{className:"text-center py-12",children:[(0,i.jsx)(L.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,i.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Pattern Insights"}),(0,i.jsx)("p",{className:"text-gray-600",children:"AI pattern recognition coming soon..."})]})}),(0,i.jsx)(C,{value:"settings",className:"space-y-6",children:(0,i.jsxs)(v,{children:[(0,i.jsxs)(w,{children:[(0,i.jsx)(j,{children:"Analytics Configuration"}),(0,i.jsx)(b,{children:"Customize how the Focus Analytics system tracks and analyzes your productivity"})]}),(0,i.jsxs)(k,{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"text-sm font-medium",children:"Focus Threshold (minutes)"}),(0,i.jsx)("p",{className:"text-xs text-gray-500 mb-2",children:"Minimum duration to consider a focus session"}),(0,i.jsx)("input",{type:"number",value:Z.focusThreshold,onChange:e=>ee({focusThreshold:parseInt(e.target.value)}),className:"w-full px-3 py-2 border border-gray-300 rounded-md",min:"5",max:"60"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"text-sm font-medium",children:"Break Reminder Interval (minutes)"}),(0,i.jsx)("p",{className:"text-xs text-gray-500 mb-2",children:"How often to suggest breaks"}),(0,i.jsx)("input",{type:"number",value:Z.breakReminderInterval,onChange:e=>ee({breakReminderInterval:parseInt(e.target.value)}),className:"w-full px-3 py-2 border border-gray-300 rounded-md",min:"15",max:"180"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"text-sm font-medium",children:"Deep Work Minimum (minutes)"}),(0,i.jsx)("p",{className:"text-xs text-gray-500 mb-2",children:"Minimum duration for deep work classification"}),(0,i.jsx)("input",{type:"number",value:Z.deepWorkMinimum,onChange:e=>ee({deepWorkMinimum:parseInt(e.target.value)}),className:"w-full px-3 py-2 border border-gray-300 rounded-md",min:"30",max:"120"})]})]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"text-sm font-medium",children:"Real-time Coaching"}),(0,i.jsx)("p",{className:"text-xs text-gray-500",children:"Enable live productivity notifications"})]}),(0,i.jsx)("input",{type:"checkbox",checked:Z.enableRealTimeCoaching,onChange:e=>ee({enableRealTimeCoaching:e.target.checked}),className:"rounded"})]}),(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"text-sm font-medium",children:"Break Reminders"}),(0,i.jsx)("p",{className:"text-xs text-gray-500",children:"Suggest breaks based on work intensity"})]}),(0,i.jsx)("input",{type:"checkbox",checked:Z.enableBreakReminders,onChange:e=>ee({enableBreakReminders:e.target.checked}),className:"rounded"})]}),(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"text-sm font-medium",children:"Goal Tracking"}),(0,i.jsx)("p",{className:"text-xs text-gray-500",children:"Track progress towards productivity goals"})]}),(0,i.jsx)("input",{type:"checkbox",checked:Z.enableGoalTracking,onChange:e=>ee({enableGoalTracking:e.target.checked}),className:"rounded"})]})]})]}),(0,i.jsxs)("div",{className:"pt-4 border-t",children:[(0,i.jsx)("h4",{className:"font-medium mb-3",children:"Current Status"}),(0,i.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm",children:[(0,i.jsxs)("div",{className:"text-center p-3 bg-gray-50 rounded-lg",children:[(0,i.jsx)("div",{className:"font-medium",children:"Monitoring"}),(0,i.jsx)("div",{className:"text-xs ".concat(e?"text-green-600":"text-gray-500"),children:e?"Active":"Inactive"})]}),(0,i.jsxs)("div",{className:"text-center p-3 bg-gray-50 rounded-lg",children:[(0,i.jsx)("div",{className:"font-medium",children:"Sessions Today"}),(0,i.jsx)("div",{className:"text-xs text-gray-600",children:m.length})]}),(0,i.jsxs)("div",{className:"text-center p-3 bg-gray-50 rounded-lg",children:[(0,i.jsx)("div",{className:"font-medium",children:"Active Goals"}),(0,i.jsx)("div",{className:"text-xs text-gray-600",children:f.filter(e=>e.isActive).length})]}),(0,i.jsxs)("div",{className:"text-center p-3 bg-gray-50 rounded-lg",children:[(0,i.jsx)("div",{className:"font-medium",children:"Notifications"}),(0,i.jsx)("div",{className:"text-xs text-gray-600",children:p.length})]})]})]})]})]})})]})]})}function eK(){return(0,i.jsx)(h,{children:(0,i.jsx)(g,{children:(0,i.jsx)("div",{className:"min-h-screen bg-gray-50 ".concat(x().className),children:(0,i.jsx)(eU,{})})})})}},80551:()=>{},85817:()=>{}},e=>{var t=t=>e(e.s=t);e.O(0,[183,229,690,272,660,56,87,251,674,266,803,441,684,358],()=>t(30046)),_N_E=e.O()}]);