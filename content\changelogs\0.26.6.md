Based on your commits, here is the changelog for the new Screenpipe update:

### **New Features:**
- **Added API endpoint in Tauri app:** Implemented an API endpoint allowing developers to change the window size of the applications.

### **Improvements:**
- **Enhanced Obsidian prompt:** Improved the prompt in Obsidian to link notes using the Obsidian format.
- **Updated documentation for JS SDK:** Added documentation for the JavaScript SDK to assist developers.

### **Fixes:**
- **Refactored timeline to rewind:** Renamed and streamlined the timeline feature to improve clarity and usability. 

Please let me know if you need any further assistance!

#### **Full Changelog:** [884a9..135df](https://github.com/mediar-ai/screenpipe/compare/884a9..135df)

