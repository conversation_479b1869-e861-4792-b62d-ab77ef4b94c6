# Focus Analytics & Productivity Coach - Installation Guide

## Prerequisites

Before installing the Focus Analytics & Productivity Coach plugin, ensure you have:

1. **Screenpipe** installed and running on your system
   - Download from: https://github.com/mediar-ai/screenpipe
   - Follow the installation instructions for your operating system

2. **Node.js** (version 18 or higher)
   - Download from: https://nodejs.org/

3. **Bun** (recommended) or npm
   - Install Bun: `curl -fsSL https://bun.sh/install | bash`

## Installation Steps

### 1. Navigate to Screenpipe Directory
```bash
cd path/to/screenpipe/screenpipe
```

### 2. Create the Plugin
```bash
# Using the Screenpipe CLI
bunx --bun @screenpipe/dev@latest pipe create

# When prompted:
# - Name: focus-analytics-coach
# - Path: pipes/focus-analytics-coach
```

### 3. Install Dependencies
```bash
cd pipes/focus-analytics-coach
bun install

# Install additional dependencies for analytics
bun add recharts date-fns fuse.js ml-matrix @tensorflow/tfjs framer-motion react-hook-form sonner lodash @types/lodash
```

### 4. Replace Generated Files
Replace the generated template files with the Focus Analytics implementation:

- Copy all files from this repository to `pipes/focus-analytics-coach/`
- Ensure all TypeScript files, components, and configuration are in place

### 5. Build the Plugin
```bash
bun run build
```

### 6. Start Development Server
```bash
bun run dev
```

The plugin will be available at `http://localhost:3000`

## Configuration

### Analytics Settings

The plugin comes with sensible defaults, but you can customize the analytics configuration:

```typescript
{
  focusThreshold: 15,        // Minimum minutes for focus session
  distractionThreshold: 30,  // Seconds before considering distraction
  contextSwitchWindow: 5,    // Seconds to group rapid switches
  breakReminderInterval: 60, // Minutes between break reminders
  deepWorkMinimum: 45,       // Minimum minutes for deep work
  enableRealTimeCoaching: true,
  enableBreakReminders: true,
  enableGoalTracking: true
}
```

### Cron Jobs

The plugin includes automated processing:

- **Analytics Processing**: Every 15 minutes (`0 */15 * * * *`)
- **Daily Summary**: Every day at 9 AM (`0 0 9 * * *`)

## Verification

### 1. Check Plugin Status
Visit `http://localhost:3000` and verify:
- ✅ Dashboard loads without errors
- ✅ Demo data is displayed (if no Screenpipe data available)
- ✅ All tabs are accessible (Overview, Sessions, Coaching, Goals, Patterns, Settings)

### 2. Test API Endpoints
```bash
# Test analytics processing endpoint
curl http://localhost:3000/api/analytics/process

# Test daily summary endpoint
curl http://localhost:3000/api/analytics/daily-summary
```

Both should return JSON responses with `"success": true`.

### 3. Monitor Console
Check browser console for any errors. You should see:
- TensorFlow.js initialization messages
- Settings provider initialization
- No critical errors

## Troubleshooting

### Common Issues

**1. Build Errors**
```bash
# Clear cache and reinstall
rm -rf node_modules .next
bun install
bun run build
```

**2. TypeScript Errors**
- Ensure all dependencies are installed
- Check that TypeScript version is compatible
- Verify all import paths are correct

**3. Missing Data**
- The plugin shows demo data when Screenpipe is not running
- Start Screenpipe to see real analytics data
- Check Screenpipe logs for any issues

**4. Port Conflicts**
```bash
# Use different port
bun run dev -- --port 3001
```

### Performance Optimization

**1. Reduce TensorFlow.js Bundle Size**
If the bundle is too large, you can disable ML features:
```typescript
// In analytics config
const config = {
  // ... other settings
  enableMLPredictions: false
}
```

**2. Optimize Data Processing**
For large datasets, consider:
- Reducing query limits
- Implementing data pagination
- Using Web Workers for heavy processing

## Integration with Screenpipe

### Data Sources
The plugin analyzes:
- **OCR Data**: Screen text content for task detection
- **Audio Data**: Meeting and communication analysis
- **UI Data**: Application and window tracking

### Privacy
- All processing happens locally
- No data is sent to external servers
- Analytics data is stored locally only

### Performance Impact
- Minimal CPU usage during normal operation
- Periodic processing every 15 minutes
- Real-time monitoring has low overhead

## Next Steps

1. **Start Screenpipe** to begin collecting real data
2. **Configure Goals** in the Goals tab
3. **Enable Monitoring** to start real-time coaching
4. **Review Analytics** daily for insights
5. **Customize Settings** based on your workflow

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Review Screenpipe documentation
3. Check browser console for error messages
4. Ensure all dependencies are properly installed

## Advanced Configuration

### Custom Task Categories
Modify `lib/analytics-engine.ts` to add custom task categorization rules:

```typescript
const categories: Record<TaskCategoryType, string[]> = {
  'custom-category': ['your-app', 'another-app'],
  // ... existing categories
};
```

### Custom Notifications
Extend the coaching system in `lib/coaching-system.ts` to add custom notification types and triggers.

### Data Export
The plugin supports exporting analytics data through the API endpoints for integration with other tools.
