### **New Features:**
- **Added real-time availability of AI models:** Users can now view a list of available AI models in real-time on the screenpipe-cloud.
- **Implemented model list endpoint:** Introduced a new endpoint in the screenpipe-cloud for accessing the list of AI models.
- **Enhanced data-table with AI search:** Significantly improved the data-table functionality by incorporating AI-powered search capabilities.

### **Improvements:**
- **Improved data-table clarity:** Made the data-table 10 times cleaner for better usability.
- **Enhanced onboarding process:** Fixed various issues to streamline the onboarding experience.
- **Improved Obsidian settings loading:** Optimized the loading of settings in Obsidian for better performance.

### **Fixes:**
- **Made identify-speakers feature free:** Removed charges for the identify-speakers feature, making it accessible to all users.

#### **Full Changelog:** [c40b4..8de59](https://github.com/mediar-ai/screenpipe/compare/c40b4..8de59)

