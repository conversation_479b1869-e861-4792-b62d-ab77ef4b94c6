"use server";

import { pipe } from "@screenpipe/js";
import type { Settings as ScreenpipeAppSettings } from "@screenpipe/js";

// Default settings for when Screenpipe is not available
const DEFAULT_SCREENPIPE_SETTINGS: Partial<ScreenpipeAppSettings> = {
  aiModel: "gpt-4",
  aiUrl: "https://api.openai.com/v1",
  customPrompt: "",
  port: 3030,
  dataDir: "",
  disableAudio: false,
  ignoredWindows: [],
  includedWindows: [],
  aiProviderType: "openai",
  enableFrameCache: true,
  enableUiMonitoring: true,
  aiMaxContextChars: 128000,
  analyticsEnabled: false,
  monitorIds: [],
  audioDevices: [],
  audioTranscriptionEngine: "whisper",
  enableRealtimeAudioTranscription: false,
  realtimeAudioTranscriptionEngine: "whisper",
  disableVision: false,
  aiPresets: [],
  customSettings: {},
};

export async function getScreenpipeAppSettings(): Promise<Partial<ScreenpipeAppSettings>> {
  try {
    // Add timeout to prevent hanging
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error("Settings timeout")), 3000);
    });

    const settingsPromise = pipe.settings.getAll();

    return await Promise.race([settingsPromise, timeoutPromise]);
  } catch (error) {
    console.warn("Failed to connect to Screenpipe settings, using defaults:", error);
    return DEFAULT_SCREENPIPE_SETTINGS;
  }
}

export async function updateScreenpipeAppSettings(
  newSettings: Partial<ScreenpipeAppSettings>
): Promise<Partial<ScreenpipeAppSettings>> {
  try {
    // Add timeout to prevent hanging
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error("Settings update timeout")), 3000);
    });

    const updatePromise = pipe.settings.update(newSettings);

    return await Promise.race([updatePromise, timeoutPromise]);
  } catch (error) {
    console.warn("Failed to update Screenpipe settings, returning current:", error);
    return newSettings;
  }
}
