Based on the provided commits, here’s the changelog for the new Screenpipe update:

### **Fixes:**
- **Fixed nextjs error:** Resolved an issue related to Next.js to enhance application stability.

### **Improvements:**
- **Updated small AI default settings:** Adjusted default settings for the AI feature to improve performance. 

No new features were evident from the provided commits, and thus none have been added to the changelog.

#### **Full Changelog:** [87f73..db544](https://github.com/mediar-ai/screenpipe/compare/87f73..db544)

