(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{4530:()=>{},8108:()=>{},11800:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>ez});var i=s(95155),a=s(12115),r=s(34477);let n=(0,r.createServerReference)("00689101726a740ae1b809750642788b6cced5a9ee",r.callServer,void 0,r.findSourceMapURL,"getScreenpipeAppSettings"),o=(0,r.createServerReference)("40d1cc6778dd0e335ea346a2af0964e84396a12af9",r.callServer,void 0,r.findSourceMapURL,"updateScreenpipeAppSettings"),c={exampleSetting:"default value"};class l{getStore(){return this.store}subscribe(e){return this.listeners.add(e),()=>this.listeners.delete(e)}notify(){this.listeners.forEach(e=>e())}async setGlobalSettings(e){this.store.globalSettings=e,this.notify()}async setPipeSettings(e,t){this.store.pipeSettings[e]=t,this.notify()}async loadGlobalSettings(){try{let e=await n();return this.setGlobalSettings(e),e}catch(e){return console.error("failed to load global settings:",e),null}}async updateGlobalSettings(e){try{let t={...await n(),...e};return await o(t),this.setGlobalSettings(t),this.notify(),!0}catch(e){return console.error("failed to update global settings:",e),!1}}async loadPipeSettings(e){try{var t;let s=await n(),i={...c,...null===(t=s.customSettings)||void 0===t?void 0:t[e]};return this.setPipeSettings(e,i),i}catch(e){return console.error("failed to load pipe settings:",e),null}}async updatePipeSettings(e,t){try{var s,i;let a=await n(),r={...a,customSettings:{...a.customSettings||{},[e]:{...(null===(s=a.customSettings)||void 0===s?void 0:s[e])||{},...t}}};return await o(r),this.setGlobalSettings(r),this.setPipeSettings(e,{...(null===(i=a.customSettings)||void 0===i?void 0:i[e])||{},...t}),!0}catch(e){return console.error("failed to update pipe settings:",e),!1}}getPreset(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"aiPresetId";try{var s,i,a,r;let n;let o=null===(s=this.store.pipeSettings[e])||void 0===s?void 0:s[t],c=this.store.globalSettings;if(o&&(n=null==c?void 0:null===(a=c.aiPresets)||void 0===a?void 0:a.find(e=>e.id===o)),n||(n=null==c?void 0:null===(r=c.aiPresets)||void 0===r?void 0:r.find(e=>e.defaultPreset)),!n)return;let l="provider"in n&&"screenpipe-cloud"===n.provider?(null==c?void 0:null===(i=c.user)||void 0===i?void 0:i.token)||"":"provider"in n&&"apiKey"in n&&n.apiKey||"";return{id:n.id,maxContextChars:n.maxContextChars,url:n.url,model:n.model,defaultPreset:n.defaultPreset,prompt:n.prompt,provider:n.provider,apiKey:l}}catch(e){console.error("failed to get preset:",e);return}}constructor(){this.store={globalSettings:null,pipeSettings:{}},this.listeners=new Set}}let d=new l,u=(0,a.createContext)(void 0);function m(e){let{children:t}=e,s=function(){let[e,t]=(0,a.useState)(d.getStore().globalSettings),[s,i]=(0,a.useState)(!0);return(0,a.useEffect)(()=>{(async()=>{i(!0),await d.loadGlobalSettings(),i(!1)})();let e=d.subscribe(()=>{t(d.getStore().globalSettings)});return()=>{e()}},[]),{settings:e,updateSettings:async e=>d.updateGlobalSettings(e),loading:s}}();return console.log("settings provider initialized with data:",s.loading?"loading...":"loaded"),(0,i.jsx)(u.Provider,{value:s,children:t})}function h(e){let{children:t}=e,[s,r]=(0,a.useState)(!1);return((0,a.useEffect)(()=>{r(!0)},[]),s)?(0,i.jsx)(i.Fragment,{children:t}):null}var g=s(62418),p=s.n(g),f=s(53999);let x=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,i.jsx)("div",{ref:t,className:(0,f.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",s),...a})});x.displayName="Card";let y=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,i.jsx)("div",{ref:t,className:(0,f.cn)("flex flex-col space-y-1.5 p-6",s),...a})});y.displayName="CardHeader";let v=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,i.jsx)("div",{ref:t,className:(0,f.cn)("text-2xl font-semibold leading-none tracking-tight",s),...a})});v.displayName="CardTitle";let w=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,i.jsx)("div",{ref:t,className:(0,f.cn)("text-sm text-muted-foreground",s),...a})});w.displayName="CardDescription";let j=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,i.jsx)("div",{ref:t,className:(0,f.cn)("p-6 pt-0",s),...a})});j.displayName="CardContent",a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,i.jsx)("div",{ref:t,className:(0,f.cn)("flex items-center p-6 pt-0",s),...a})}).displayName="CardFooter";var b=s(62841);let k=b.bL,N=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,i.jsx)(b.B8,{ref:t,className:(0,f.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",s),...a})});N.displayName=b.B8.displayName;let S=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,i.jsx)(b.l9,{ref:t,className:(0,f.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",s),...a})});S.displayName=b.l9.displayName;let T=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,i.jsx)(b.UC,{ref:t,className:(0,f.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",s),...a})});T.displayName=b.UC.displayName;var A=s(99708),C=s(74466);let P=(0,C.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),D=a.forwardRef((e,t)=>{let{className:s,variant:a,size:r,asChild:n=!1,...o}=e,c=n?A.DX:"button";return(0,i.jsx)(c,{className:(0,f.cn)(P({variant:a,size:r,className:s})),ref:t,...o})});D.displayName="Button";let M=(0,C.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function R(e){let{className:t,variant:s,...a}=e;return(0,i.jsx)("div",{className:(0,f.cn)(M({variant:s}),t),...a})}var F=s(55863);let z=a.forwardRef((e,t)=>{let{className:s,value:a,...r}=e;return(0,i.jsx)(F.bL,{ref:t,className:(0,f.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",s),...r,children:(0,i.jsx)(F.C1,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:"translateX(-".concat(100-(a||0),"%)")}})})});z.displayName=F.bL.displayName;var I=s(82178),E=s(85690),B=s(14186),G=s(33109),O=s(71539),L=s(23861),W=s(72713),H=s(49376),Y=s(16785),_=s(381),K=s(67312),q=s(47178),U=s(25727),V=s(49067),Z=s(90621);class Q{async analyzeActivity(e){try{let t=await q.F.queryScreenpipe({startTime:e.start.toISOString(),endTime:e.end.toISOString(),contentType:"all",limit:1e3,includeFrames:!1});if(!(null==t?void 0:t.data))throw Error("No data received from Screenpipe");let s=this.processActivityData(t.data),i=this.calculateProductivityMetrics(s.focusSessions,s.contextSwitches,e.start);return{focusSessions:s.focusSessions,contextSwitches:s.contextSwitches,metrics:i}}catch(e){throw console.error("Error analyzing activity:",e),e}}processActivityData(e){let t=[],s=[],i=null,a=null;for(let r of e.sort((e,t)=>new Date(e.content.timestamp).getTime()-new Date(t.content.timestamp).getTime())){let e=new Date(r.content.timestamp),n="",o="";if("OCR"===r.type?(n=r.content.appName||"",o=r.content.windowName||""):"UI"===r.type&&(n=r.content.appName||"",o=r.content.windowName||""),n){if(a&&(a.app!==n||a.window!==o)){let r=(0,U.O)(e,a.timestamp);r>=this.config.contextSwitchWindow&&(s.push({id:(0,Z.A)(),timestamp:e,fromApp:a.app,toApp:n,fromWindow:a.window,toWindow:o,duration:r,switchType:a.app!==n?"app":"window"}),i&&this.isDistractingSwitch(a.app,n)&&(this.finalizeSession(i,a.timestamp,t),i=null))}(!i||this.shouldStartNewSession(i,n,o,e))&&(i&&this.finalizeSession(i,e,t),i={id:(0,Z.A)(),startTime:e,appName:n,windowName:o,taskCategory:this.categorizeTask(n,o),distractionCount:0,contextSwitches:0}),i&&(i.endTime=e,s.length>0&&i.startTime&&s[s.length-1].timestamp>=i.startTime&&(i.contextSwitches=(i.contextSwitches||0)+1)),a={app:n,window:o,timestamp:e}}}return i&&a&&this.finalizeSession(i,a.timestamp,t),{focusSessions:t,contextSwitches:s}}finalizeSession(e,t,s){if(!e.startTime||!e.appName)return;let i=(0,V.o)(t,e.startTime);if(i>=this.config.focusThreshold){let a=this.calculateFocusScore(i,e.contextSwitches||0,e.distractionCount||0);s.push({id:e.id,startTime:e.startTime,endTime:t,duration:i,appName:e.appName,windowName:e.windowName||"",taskCategory:e.taskCategory||"unknown",focusScore:a,distractionCount:e.distractionCount||0,contextSwitches:e.contextSwitches||0,productivity:this.getProductivityLevel(a)})}}calculateFocusScore(e,t,s){let i;return i=100-Math.min(5*t,30)-Math.min(10*s,40),e>=this.config.deepWorkMinimum&&(i+=10),Math.max(0,Math.min(100,i))}categorizeTask(e,t){let s="".concat(e," ").concat(t).toLowerCase();for(let[e,t]of Object.entries({"deep-work":["vscode","intellij","xcode","sublime","vim","emacs","figma","sketch"],communication:["slack","teams","discord","zoom","meet","skype","mail","outlook"],research:["chrome","firefox","safari","edge","browser","wikipedia","stackoverflow"],creative:["photoshop","illustrator","premiere","after effects","blender","canva"],administrative:["excel","sheets","word","docs","powerpoint","slides","notion"],learning:["coursera","udemy","youtube","khan academy","duolingo","anki"],entertainment:["netflix","youtube","spotify","twitch","gaming","steam"],social:["facebook","twitter","instagram","linkedin","reddit","tiktok"],distraction:["news","shopping","amazon","ebay","social media"],unknown:[]}))if(t.some(e=>s.includes(e)))return e;return"unknown"}isDistractingSwitch(e,t){return["vscode","intellij","figma","notion","excel"].some(t=>e.toLowerCase().includes(t))&&["chrome","firefox","safari","social","entertainment"].some(e=>t.toLowerCase().includes(e))}shouldStartNewSession(e,t,s,i){return!e.startTime||e.appName!==t&&(0,V.o)(i,e.startTime)>=this.config.focusThreshold}getProductivityLevel(e){return e>=90?"very-high":e>=75?"high":e>=50?"medium":e>=25?"low":"very-low"}calculateProductivityMetrics(e,t,s){let i=e.reduce((e,t)=>e+t.duration,0),a=e.length>0?e.reduce((e,t)=>e+t.focusScore,0)/e.length:0,r=e.filter(e=>e.duration>=this.config.deepWorkMinimum).length,n=e.length>0?i/e.length:0,o=this.calculateHourlyProductivity(e),c=this.getMostProductiveHour(o),l=this.getLeastProductiveHour(o),d=this.calculateAppUsage(e),u=d.filter(e=>e.productivityScore>=70).sort((e,t)=>t.focusTime-e.focusTime).slice(0,5),m=d.filter(e=>e.productivityScore<50).sort((e,t)=>t.totalTime-e.totalTime).slice(0,5);return{date:s,totalFocusTime:i,totalDistractionTime:0,focusScore:a,contextSwitches:t.length,deepWorkSessions:r,averageSessionLength:n,mostProductiveHour:c,leastProductiveHour:l,topProductiveApps:u,topDistractingApps:m}}calculateHourlyProductivity(e){let t={};for(let e=0;e<24;e++)t[e]={totalTime:0,totalScore:0,count:0};e.forEach(e=>{let s=e.startTime.getHours();t[s].totalTime+=e.duration,t[s].totalScore+=e.focusScore,t[s].count+=1});let s={};for(let e=0;e<24;e++){let i=t[e];s[e]=i.count>0?i.totalScore/i.count:0}return s}getMostProductiveHour(e){return Object.entries(e).reduce((t,s)=>{let[i,a]=s;return a>e[t]?parseInt(i):t},0)}getLeastProductiveHour(e){return Object.entries(e).reduce((t,s)=>{let[i,a]=s;return a<e[t]?parseInt(i):t},0)}calculateAppUsage(e){let t={};return e.forEach(e=>{t[e.appName]||(t[e.appName]={totalTime:0,focusTime:0,sessionCount:0,totalScore:0});let s=t[e.appName];s.totalTime+=e.duration,s.sessionCount+=1,s.totalScore+=e.focusScore,e.focusScore>=70&&(s.focusTime+=e.duration)}),Object.entries(t).map(e=>{let[t,s]=e;return{appName:t,totalTime:s.totalTime,focusTime:s.focusTime,distractionTime:s.totalTime-s.focusTime,sessionCount:s.sessionCount,averageSessionLength:s.totalTime/s.sessionCount,productivityScore:s.totalScore/s.sessionCount}})}constructor(e){this.config=e}}class X{async startMonitoring(){!this.isMonitoring&&(this.isMonitoring=!0,console.log("Starting real-time productivity coaching..."),this.config.enableRealTimeCoaching&&this.startActivityMonitoring(),this.config.enableBreakReminders&&this.startBreakReminders(),this.config.enableGoalTracking&&this.startGoalTracking())}stopMonitoring(){this.isMonitoring=!1,console.log("Stopped productivity coaching")}onNotification(e){this.notificationCallbacks.push(e)}sendNotification(e){this.notifications.push(e),this.lastNotification=new Date,this.notificationCallbacks.forEach(t=>{try{t(e)}catch(e){console.error("Error in notification callback:",e)}}),"Notification"in window&&this.sendDesktopNotification(e)}async sendDesktopNotification(e){try{if("granted"===Notification.permission)new Notification(e.title,{body:e.message,icon:"/128x128.png",tag:e.type});else if("denied"!==Notification.permission){let t=await Notification.requestPermission();"granted"===t&&new Notification(e.title,{body:e.message,icon:"/128x128.png",tag:e.type})}}catch(e){console.error("Error sending desktop notification:",e)}}async startActivityMonitoring(){try{for await(let e of q.F.streamVision(!1)){if(!this.isMonitoring)break;await this.processVisionData(e)}}catch(e){console.error("Error in activity monitoring:",e),this.startPollingMonitoring()}}startPollingMonitoring(){let e=setInterval(async()=>{if(!this.isMonitoring){clearInterval(e);return}try{await this.checkCurrentActivity()}catch(e){console.error("Error in polling monitoring:",e)}},3e4)}async processVisionData(e){try{let t=e.appName||"",s=e.windowName||"",i=new Date;await this.detectFocusModeChanges(t,s,i),await this.checkForDistractions(t,s,i),this.updateCurrentSession(t,s,i)}catch(e){console.error("Error processing vision data:",e)}}async checkCurrentActivity(){try{let e=new Date,t=new Date(e.getTime()-3e5),s=await q.F.queryScreenpipe({startTime:t.toISOString(),endTime:e.toISOString(),contentType:"ocr",limit:10});if((null==s?void 0:s.data)&&s.data.length>0){let e=s.data[s.data.length-1];if("OCR"===e.type){let t=e.content.appName||"",s=e.content.windowName||"",i=new Date(e.content.timestamp);await this.detectFocusModeChanges(t,s,i),await this.checkForDistractions(t,s,i),this.updateCurrentSession(t,s,i)}}}catch(e){console.error("Error checking current activity:",e)}}async detectFocusModeChanges(e,t,s){let i=this.isProductiveApp(e),a=this.isDistractingApp(e);if(!i||this.currentSession&&this.isProductiveApp(this.currentSession.appName)||this.sendNotification({id:(0,Z.A)(),type:"focus-reminder",title:"Focus Mode Detected",message:"Great! You're starting focused work in ".concat(e,". Stay concentrated!"),timestamp:s,priority:"low",actionable:!1}),this.currentSession&&this.isProductiveApp(this.currentSession.appName)&&a){let t=(0,V.o)(s,this.currentSession.startTime);t>=this.config.focusThreshold?this.sendNotification({id:(0,Z.A)(),type:"session-complete",title:"Focus Session Complete",message:"Great job! You focused for ".concat(t," minutes. Consider taking a break."),timestamp:s,priority:"medium",actionable:!0,action:{label:"Take Break",callback:()=>this.suggestBreak()}}):this.sendNotification({id:(0,Z.A)(),type:"distraction-alert",title:"Distraction Detected",message:"You switched to ".concat(e," after only ").concat(t," minutes. Try to maintain focus!"),timestamp:s,priority:"medium",actionable:!0,action:{label:"Return to Work",callback:()=>this.suggestReturnToWork()}})}}async checkForDistractions(e,t,s){if(!this.isDistractingApp(e))return;let i=(await this.getRecentActivity(15)).filter(e=>this.isDistractingApp(e.appName)).reduce((e,t)=>e+t.duration,0);i>=15&&this.sendNotification({id:(0,Z.A)(),type:"distraction-alert",title:"Extended Distraction Detected",message:"You've been on distracting apps for ".concat(Math.round(i)," minutes. Time to refocus!"),timestamp:s,priority:"high",actionable:!0,action:{label:"Start Focus Session",callback:()=>this.suggestFocusSession()}})}updateCurrentSession(e,t,s){this.currentSession&&this.currentSession.appName===e?(this.currentSession.endTime=s,this.currentSession.duration=(0,V.o)(s,this.currentSession.startTime)):this.currentSession={id:(0,Z.A)(),startTime:s,endTime:s,duration:0,appName:e,windowName:t,taskCategory:this.categorizeApp(e),focusScore:0,distractionCount:0,contextSwitches:0,productivity:"medium"}}startBreakReminders(){let e=setInterval(()=>{if(!this.isMonitoring){clearInterval(e);return}this.checkBreakNeeds()},6e4*this.config.breakReminderInterval)}checkBreakNeeds(){if(!this.currentSession)return;let e=(0,V.o)(new Date,this.currentSession.startTime);e>=120&&e%30==0?this.sendNotification({id:(0,Z.A)(),type:"break-suggestion",title:"Long Session Break",message:"You've been working for ".concat(e," minutes. Take a 15-minute break!"),timestamp:new Date,priority:"medium",actionable:!0,action:{label:"Take Break",callback:()=>this.suggestBreak()}}):e>=60&&e%20==0&&this.sendNotification({id:(0,Z.A)(),type:"break-suggestion",title:"Focus Break",message:"Great focus! Take a 5-minute break to recharge.",timestamp:new Date,priority:"low",actionable:!0,action:{label:"Take Break",callback:()=>this.suggestBreak()}})}startGoalTracking(){let e=setInterval(()=>{if(!this.isMonitoring){clearInterval(e);return}this.updateGoalProgress()},6e5)}async updateGoalProgress(){for(let e of this.goals.filter(e=>e.isActive)){let t=await this.calculateGoalProgress(e);if(t!==e.current){e.current=t;let s=t/e.target*100;s>=100?this.sendNotification({id:(0,Z.A)(),type:"goal-progress",title:"Goal Achieved!",message:"Congratulations! You've achieved your goal: ".concat(e.name),timestamp:new Date,priority:"high",actionable:!1}):s>=75&&s<100&&this.sendNotification({id:(0,Z.A)(),type:"goal-progress",title:"Almost There!",message:"You're ".concat(Math.round(s),"% towards your goal: ").concat(e.name),timestamp:new Date,priority:"medium",actionable:!1})}}}async calculateGoalProgress(e){let t=new Date,s=new Date(t.getFullYear(),t.getMonth(),t.getDate());try{let i=await q.F.queryScreenpipe({startTime:s.toISOString(),endTime:t.toISOString(),contentType:"all",limit:1e3});if(!(null==i?void 0:i.data))return e.current;switch(e.type){case"daily-focus-time":return this.calculateFocusTime(i.data);case"reduce-distractions":return this.calculateDistractionReduction(i.data);case"increase-deep-work":return this.calculateDeepWorkTime(i.data);default:return e.current}}catch(t){return console.error("Error calculating goal progress:",t),e.current}}isProductiveApp(e){return["vscode","intellij","xcode","figma","notion","excel","word"].some(t=>e.toLowerCase().includes(t))}isDistractingApp(e){return["facebook","twitter","instagram","youtube","netflix","reddit"].some(t=>e.toLowerCase().includes(t))}categorizeApp(e){return this.isProductiveApp(e)?"deep-work":this.isDistractingApp(e)?"distraction":"unknown"}async getRecentActivity(e){return[]}calculateFocusTime(e){return 0}calculateDistractionReduction(e){return 0}calculateDeepWorkTime(e){return 0}suggestBreak(){console.log("Suggesting break activities...")}suggestReturnToWork(){console.log("Suggesting return to productive work...")}suggestFocusSession(){console.log("Suggesting start of focus session...")}addGoal(e){this.goals.push({...e,id:(0,Z.A)(),createdAt:new Date})}getGoals(){return[...this.goals]}updateGoal(e,t){let s=this.goals.findIndex(t=>t.id===e);-1!==s&&(this.goals[s]={...this.goals[s],...t})}deleteGoal(e){this.goals=this.goals.filter(t=>t.id!==e)}getNotifications(){return[...this.notifications]}clearNotifications(){this.notifications=[]}constructor(e){this.notifications=[],this.goals=[],this.isMonitoring=!1,this.currentSession=null,this.lastNotification=null,this.notificationCallbacks=[],this.config=e}}var J=s(70607);class ${async analyzeTaskContent(e){try{let t=await q.F.queryScreenpipe({startTime:e.start.toISOString(),endTime:e.end.toISOString(),contentType:"ocr",limit:500,includeFrames:!1});if(!(null==t?void 0:t.data))return{detectedTasks:[],insights:[]};let s=[],i=[];for(let e of t.data)if("OCR"===e.type){let t=e.content.text||"",i=e.content.appName||"",a=e.content.windowName||"",r=this.analyzeOCRForTasks(t,i,a);r.confidence>.6&&s.push({id:(0,Z.A)(),timestamp:new Date(e.content.timestamp),taskType:r.taskType,description:r.description,confidence:r.confidence,appName:i,windowName:a,ocrText:t.substring(0,200)})}let a=this.generateTaskInsights(s);return i.push(...a),{detectedTasks:s,insights:i}}catch(e){return console.error("Error analyzing task content:",e),{detectedTasks:[],insights:[]}}}analyzeOCRForTasks(e,t,s){let i=e.toLowerCase(),a=t.toLowerCase();s.toLowerCase();let r={taskType:"unknown",confidence:0,description:"Unknown activity"};for(let[e,s]of Object.entries({"deep-work":{keywords:["function","class","import","export","const","let","var","def","public","private"],apps:["vscode","intellij","xcode","sublime","vim"],confidence:.9},communication:{keywords:["message","chat","email","meeting","call","video","send","reply"],apps:["slack","teams","discord","zoom","mail"],confidence:.8},research:{keywords:["search","google","stackoverflow","documentation","tutorial","how to"],apps:["chrome","firefox","safari","edge"],confidence:.7},creative:{keywords:["design","layer","brush","color","font","canvas","artboard"],apps:["photoshop","illustrator","figma","sketch"],confidence:.8},administrative:{keywords:["spreadsheet","document","presentation","table","chart","formula"],apps:["excel","word","powerpoint","sheets","docs"],confidence:.7},learning:{keywords:["course","lesson","tutorial","learn","study","education"],apps:["coursera","udemy","khan"],confidence:.8},entertainment:{keywords:["watch","video","movie","music","game","play"],apps:["netflix","youtube","spotify","steam"],confidence:.9},social:{keywords:["post","like","share","comment","follow","friend"],apps:["facebook","twitter","instagram","linkedin"],confidence:.8}})){let n=0,o=[];s.apps.some(e=>a.includes(e))&&(n+=.4);let c=s.keywords.filter(e=>i.includes(e));c.length>0&&(n+=c.length/s.keywords.length*.6,o=c),(n*=s.confidence)>r.confidence&&(r={taskType:e,confidence:n,description:this.generateTaskDescription(e,o,t)})}return r}generateTaskDescription(e,t,s){let i={"deep-work":"Coding/development work in ".concat(s),communication:"Communication activity in ".concat(s),research:"Research and information gathering in ".concat(s),creative:"Creative/design work in ".concat(s),administrative:"Administrative tasks in ".concat(s),learning:"Learning activity in ".concat(s),entertainment:"Entertainment/leisure in ".concat(s),social:"Social media activity in ".concat(s),distraction:"Potentially distracting activity in ".concat(s),unknown:"Activity in ".concat(s)};return i[e]||i.unknown}generateTaskInsights(e){let t=[],s=e.reduce((e,t)=>(e[t.taskType]=(e[t.taskType]||0)+1,e),{}),i=e.length;if(0===i)return t;let a=Object.entries(s).sort((e,t)=>{let[,s]=e,[,i]=t;return i-s})[0];if(a){let[e,s]=a,r=Math.round(s/i*100);t.push("".concat(r,"% of detected activities were ").concat(e.replace("-"," ")))}let r=Math.round(e.filter(e=>["deep-work","creative","learning","research"].includes(e.taskType)).length/i*100);r>=70?t.push("High productivity detected - great focus on meaningful work!"):r>=40?t.push("Moderate productivity - consider reducing distractions"):t.push("Low productivity detected - focus on high-value tasks");let n=this.analyzeAppSwitching(e);return n>10&&t.push("High app switching detected (".concat(n," switches) - consider batching similar tasks")),t}analyzeAppSwitching(e){let t=0,s="";for(let i of e.sort((e,t)=>e.timestamp.getTime()-t.timestamp.getTime()))s&&s!==i.appName&&t++,s=i.appName;return t}async detectProductivityPatterns(e){let t=[];for(let[s,i]of Object.entries(this.groupSessionsByTimePattern(e))){if(i.length<3)continue;let a=i.reduce((e,t)=>e+t.focusScore,0)/i.length,r=Math.min(100,i.length/e.length*100),[n,o]=s.split("|"),[c,l]=n.split("-").map(Number);t.push({id:(0,Z.A)(),name:this.generatePatternName(c,l,o),description:this.generatePatternDescription(a,i.length),timePattern:{startHour:c,endHour:l,daysOfWeek:o.split(",").map(Number)},averageProductivity:a,confidence:r,recommendations:this.generatePatternRecommendations(a,c,l)})}return t.sort((e,t)=>t.confidence-e.confidence)}groupSessionsByTimePattern(e){let t={};return e.forEach(e=>{let s=e.startTime.getHours(),i=e.startTime.getDay(),a=2*Math.floor(s/2),r="".concat(a,"-").concat(a+2),n="".concat(r,"|").concat(0===i||6===i?"0,6":"1,2,3,4,5");t[n]||(t[n]=[]),t[n].push(e)}),t}generatePatternName(e,t,s){let i="".concat(e,":00-").concat(t,":00");return"".concat("0,6"===s?"Weekends":"Weekdays"," ").concat(i)}generatePatternDescription(e,t){return"".concat(e>=80?"high":e>=60?"moderate":"low"," productivity pattern with ").concat(t," sessions")}generatePatternRecommendations(e,t,s){let i=[];return e>=80?(i.push("Excellent productivity window - schedule important tasks here"),i.push("Consider extending this time block if possible")):e>=60?(i.push("Good productivity window - optimize environment for better focus"),i.push("Minimize distractions during this time")):(i.push("Low productivity window - consider rescheduling demanding tasks"),i.push("Use this time for lighter activities or breaks")),t>=6&&t<=10?i.push("Morning energy - great for creative and analytical work"):t>=14&&t<=16?i.push("Post-lunch dip - consider light exercise or break"):t>=20&&i.push("Evening hours - wind down with lighter tasks"),i}async generateBreakRecommendation(e,t){if(!e)return null;let s=(Date.now()-e.startTime.getTime())/6e4,i=0,a="micro",r=5,n="";return(s>=120?(i=90,a="long",r=15,n="Extended focus session - time for a longer break"):s>=60?(i=70,a="short",r=10,n="Good focus session - take a short break to recharge"):s>=25&&(i=50,a="micro",r=5,n="Pomodoro break - quick refresh recommended"),this.analyzeRecentBreaks(t).timeSinceLastBreak>180&&(i=Math.min(100,i+30),n="Long period without breaks - rest is important"),i<40)?null:{id:(0,Z.A)(),timestamp:new Date,reason:n,type:a,suggestedDuration:r,activities:this.getBreakActivities(a),urgency:i}}analyzeRecentBreaks(e){let t=Date.now(),s=e[e.length-1];return{timeSinceLastBreak:s?(t-s.endTime.getTime())/6e4:0,averageBreakInterval:60}}getBreakActivities(e){return({micro:["Look away from screen (20-20-20 rule)","Deep breathing exercises","Stretch your neck and shoulders","Drink water"],short:["Walk around the room","Light stretching","Grab a healthy snack","Step outside for fresh air","Chat with a colleague"],long:["Take a walk outside","Have a proper meal","Do some exercise","Meditate or relax","Call a friend or family member"]})[e]}constructor(){this.fuse=new J.A([],{keys:["text","appName","windowName"],threshold:.3})}}var ee=s(36489),et=s(66848),es=s(12039);class ei{async initializeModel(){try{this.model=es.ilg({layers:[es.ZFI.dense({inputShape:[7],units:16,activation:"relu"}),es.ZFI.dropout({rate:.2}),es.ZFI.dense({units:8,activation:"relu"}),es.ZFI.dense({units:1,activation:"sigmoid"})]}),this.model.compile({optimizer:"adam",loss:"meanSquaredError",metrics:["mae"]}),console.log("Pattern recognition model initialized")}catch(e){console.error("Error initializing ML model:",e)}}async analyzeProductivityPatterns(e,t){if(e.length<10)return console.warn("Insufficient data for pattern analysis"),[];let s=this.groupSessionsByTimePatterns(e),i=[];for(let[e,t]of Object.entries(s)){if(t.length<3)continue;let s=await this.analyzeTimePattern(e,t);s&&i.push(s)}let a=this.analyzeWeeklyPatterns(e);i.push(...a);let r=this.analyzeTaskPatterns(e);return i.push(...r),i.sort((e,t)=>t.confidence-e.confidence)}groupSessionsByTimePatterns(e){let t={};return e.forEach(e=>{let s=(0,ee.q)(e.startTime),i=(0,et.P)(e.startTime),a=2*Math.floor(s/2),r="".concat(a,"-").concat(a+2,"_").concat(0===i||6===i?"weekend":"weekday");t[r]||(t[r]=[]),t[r].push(e)}),t}async analyzeTimePattern(e,t){let[s,i]=e.split("_"),[a,r]=s.split("-").map(Number),n=t.reduce((e,t)=>e+t.focusScore,0)/t.length,o=t.reduce((e,t)=>e+t.duration,0)/t.length,c=t.length,l=(Math.max(0,100-this.calculateVariance(t.map(e=>e.focusScore)))+Math.min(100,c/10*100))/2;if(l<30)return null;let d=this.generatePatternInsights(t,n,o),u=this.generatePatternRecommendations(n,a,r,i);return{id:"pattern_".concat(e),name:this.generatePatternName(a,r,i),description:"".concat(i," productivity pattern (").concat(c," sessions)"),timePattern:{startHour:a,endHour:r,daysOfWeek:"weekend"===i?[0,6]:[1,2,3,4,5]},averageProductivity:n,confidence:l,recommendations:[...d,...u]}}analyzeWeeklyPatterns(e){let t={};e.forEach(e=>{let s=(0,et.P)(e.startTime);t[s]||(t[s]=[]),t[s].push(e)});let s=[],i=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"];return Object.entries(t).forEach(t=>{let[a,r]=t;if(r.length<3)return;let n=parseInt(a),o=r.reduce((e,t)=>e+t.focusScore,0)/r.length,c=Math.min(100,r.length/e.length*700);c>=20&&s.push({id:"weekly_".concat(a),name:"".concat(i[n]," Pattern"),description:"Productivity pattern for ".concat(i[n],"s"),timePattern:{startHour:0,endHour:24,daysOfWeek:[n]},averageProductivity:o,confidence:c,recommendations:this.generateDaySpecificRecommendations(n,o)})}),s}analyzeTaskPatterns(e){let t={};e.forEach(e=>{t[e.taskCategory]||(t[e.taskCategory]=[]),t[e.taskCategory].push(e)});let s=[];return Object.entries(t).forEach(t=>{let[i,a]=t;if(a.length<5)return;let r=a.reduce((e,t)=>e+t.focusScore,0)/a.length,n=Math.min(100,a.length/e.length*500),o=this.analyzeHourlyPerformance(a),c=this.findBestPerformanceHours(o);n>=30&&c.length>0&&s.push({id:"task_".concat(i),name:"".concat(i.replace("-"," ")," Optimization"),description:"Best times for ".concat(i.replace("-"," ")," tasks"),timePattern:{startHour:Math.min(...c),endHour:Math.max(...c)+1,daysOfWeek:[1,2,3,4,5]},averageProductivity:r,confidence:n,recommendations:this.generateTaskSpecificRecommendations(i,c)})}),s}async predictOptimalSchedule(e,t){let s=[];if(!this.model)return console.warn("ML model not available for predictions"),this.generateRuleBasedSchedule(e,t);try{let i=(0,et.P)(e),a=+(0===i||6===i);for(let r=8;r<18;r++)for(let n of t){let t=[r/24,i/7,a,this.encodeTaskType(n),this.getHistoricalPerformance(r,i,n),this.getEnergyLevel(r),this.getContextualFactors(e,r)],o=this.model.predict(es.KtR([t])),c=100*(await o.data())[0];o.dispose();let l=this.calculatePredictionConfidence(r,i,n);s.push({hour:r,taskType:n,predictedProductivity:c,confidence:l,reasoning:this.generatePredictionReasoning(r,n,c)})}return s.sort((e,t)=>t.predictedProductivity-e.predictedProductivity).slice(0,8)}catch(s){return console.error("Error in ML prediction:",s),this.generateRuleBasedSchedule(e,t)}}generateRuleBasedSchedule(e,t){let s=[],i=(0,et.P)(e),a=0===i||6===i,r={"deep-work":a?[10,11,14,15]:[9,10,14,15],creative:[10,11,16,17],communication:[11,12,14,16],administrative:[13,14,15,16],learning:[9,10,11,15],research:[9,10,14,15],entertainment:[18,19,20,21],social:[12,13,17,18],distraction:[16,17,18,19],unknown:[10,11,14,15]};return t.forEach(e=>{(r[e]||[10,11,14,15]).forEach(t=>{s.push({hour:t,taskType:e,predictedProductivity:this.getRuleBasedProductivity(t,e,a),confidence:70,reasoning:"Based on general productivity patterns for ".concat(e.replace("-"," "))})})}),s.sort((e,t)=>t.predictedProductivity-e.predictedProductivity)}calculateVariance(e){let t=e.reduce((e,t)=>e+t,0)/e.length;return e.map(e=>Math.pow(e-t,2)).reduce((e,t)=>e+t,0)/e.length}generatePatternName(e,t,s){let i="".concat(e,":00-").concat(t,":00");return"".concat("weekend"===s?"Weekend":"Weekday"," ").concat(i)}generatePatternInsights(e,t,s){let i=[];return t>=80?i.push("High-performance time window - excellent for important tasks"):t>=60?i.push("Good productivity window - suitable for focused work"):i.push("Lower productivity period - consider lighter tasks"),s>=60?i.push("Long focus sessions typical - good for deep work"):s>=30?i.push("Moderate session length - good for regular tasks"):i.push("Short sessions typical - better for quick tasks"),i}generatePatternRecommendations(e,t,s,i){let a=[];return e>=80?(a.push("Schedule your most important work during this time"),a.push("Minimize interruptions and distractions")):e>=60?(a.push("Good time for regular focused work"),a.push("Consider optimizing environment for better focus")):(a.push("Use for lighter tasks or administrative work"),a.push("Consider taking breaks or doing physical activity")),t<=10?a.push("Morning energy - great for analytical tasks"):t>=14&&t<=16&&a.push("Post-lunch period - may need energy boost"),a}generateDaySpecificRecommendations(e,t){let s=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"][e],i=["Optimize ".concat(s," productivity")];return 1===e?i.push("Start week with planning and goal setting"):5===e?i.push("Wrap up weekly tasks and plan for next week"):(0===e||6===e)&&i.push("Focus on personal projects and learning"),i}generateTaskSpecificRecommendations(e,t){let s=[],i=t.map(e=>"".concat(e,":00")).join(", ");switch(s.push("Best performance for ".concat(e.replace("-"," ")," at ").concat(i)),e){case"deep-work":s.push("Minimize distractions and notifications"),s.push("Ensure comfortable environment and good lighting");break;case"creative":s.push("Allow for inspiration and experimentation"),s.push("Consider background music or change of scenery");break;case"communication":s.push("Batch similar communication tasks together"),s.push("Set specific times for checking messages")}return s}analyzeHourlyPerformance(e){let t={};e.forEach(e=>{let s=(0,ee.q)(e.startTime);t[s]||(t[s]={total:0,count:0}),t[s].total+=e.focusScore,t[s].count+=1});let s={};return Object.entries(t).forEach(e=>{let[t,i]=e;s[parseInt(t)]=i.total/i.count}),s}findBestPerformanceHours(e){let t=Object.values(e).reduce((e,t)=>e+t,0)/Object.values(e).length;return Object.entries(e).filter(e=>{let[,s]=e;return s>1.1*t}).map(e=>{let[t]=e;return parseInt(t)})}encodeTaskType(e){return({"deep-work":.9,creative:.8,research:.7,learning:.6,administrative:.5,communication:.4,social:.3,entertainment:.2,distraction:.1,unknown:0})[e]||0}getHistoricalPerformance(e,t,s){return .7}getEnergyLevel(e){return e>=9&&e<=11?.9:e>=14&&e<=16?.8:e>=20||e<=6?.3:.6}getContextualFactors(e,t){return .5}calculatePredictionConfidence(e,t,s){return 75}generatePredictionReasoning(e,t,s){return s>=80?"Optimal time for ".concat(t.replace("-"," ")," based on historical performance"):s>=60?"Good time for ".concat(t.replace("-"," ")," with moderate productivity expected"):"Lower productivity expected for ".concat(t.replace("-"," ")," at this time")}getRuleBasedProductivity(e,t,s){let i=70;return e>=9&&e<=11&&(i+=15),e>=14&&e<=16&&(i+=10),(e<=8||e>=18)&&(i-=20),"deep-work"===t&&e>=9&&e<=11&&(i+=10),"creative"===t&&e>=10&&e<=12&&(i+=10),"communication"===t&&e>=11&&e<=16&&(i+=5),s&&(i-=10),Math.max(0,Math.min(100,i))}constructor(){this.patterns=[],this.energyLevels=[],this.model=null,this.initializeModel()}}var ea=s(6711),er=s(19828),en=s(72132),eo=s(10415),ec=s(56671),el=s(84868),ed=s(58086),eu=s(14173);function em(){let e=new Date,t=[],s=(0,el.O)(e,8);[{app:"Visual Studio Code",category:"deep-work",duration:45,focusScore:85,distractions:2},{app:"Chrome",category:"research",duration:25,focusScore:65,distractions:5},{app:"Slack",category:"communication",duration:15,focusScore:40,distractions:8},{app:"Figma",category:"creative",duration:60,focusScore:90,distractions:1},{app:"Notion",category:"administrative",duration:30,focusScore:70,distractions:3},{app:"YouTube",category:"entertainment",duration:20,focusScore:20,distractions:12},{app:"IntelliJ IDEA",category:"deep-work",duration:75,focusScore:88,distractions:1},{app:"Discord",category:"social",duration:10,focusScore:30,distractions:6}].forEach((e,i)=>{var a;let r=(0,ed.z)(s,20*i),n=(0,ed.z)(r,e.duration);t.push({id:(0,Z.A)(),startTime:r,endTime:n,duration:e.duration,appName:e.app,windowName:"".concat(e.app," - Main Window"),taskCategory:e.category,focusScore:e.focusScore,distractionCount:e.distractions,contextSwitches:Math.floor(e.distractions/2),productivity:(a=e.focusScore)>=90?"very-high":a>=75?"high":a>=50?"medium":a>=25?"low":"very-low"})});let i=t.reduce((e,t)=>e+t.duration,0),a=t.reduce((e,t)=>e+t.focusScore,0)/t.length,r=t.filter(e=>e.duration>=45&&e.focusScore>=70).length,n=t.reduce((e,t)=>e+t.contextSwitches,0),o=i/t.length,c=new Map;t.forEach(e=>{let t=c.get(e.appName);t?(t.totalTime+=e.duration,t.sessionCount+=1,t.focusTime+=e.focusScore>=70?e.duration:0,t.distractionTime+=e.focusScore<50?e.duration:0):c.set(e.appName,{appName:e.appName,totalTime:e.duration,focusTime:e.focusScore>=70?e.duration:0,distractionTime:e.focusScore<50?e.duration:0,sessionCount:1,averageSessionLength:e.duration,productivityScore:e.focusScore})}),c.forEach(e=>{e.averageSessionLength=e.totalTime/e.sessionCount});let l=Array.from(c.values()),d=l.filter(e=>e.productivityScore>=70).sort((e,t)=>t.focusTime-e.focusTime).slice(0,5),u=l.filter(e=>e.productivityScore<50).sort((e,t)=>t.distractionTime-e.distractionTime).slice(0,5),m=new Map;t.forEach(e=>{let t=e.startTime.getHours(),s=m.get(t);s?(s.total+=e.focusScore,s.count+=1):m.set(t,{total:e.focusScore,count:1})});let h=9,g=15,p=0,f=100;m.forEach((e,t)=>{let s=e.total/e.count;s>p&&(p=s,h=t),s<f&&(f=s,g=t)});let x={date:e,totalFocusTime:i,totalDistractionTime:t.filter(e=>e.focusScore<50).reduce((e,t)=>e+t.duration,0),focusScore:a,contextSwitches:n,deepWorkSessions:r,averageSessionLength:o,mostProductiveHour:h,leastProductiveHour:g,topProductiveApps:d,topDistractingApps:u};return{focusSessions:t,productivityMetrics:x}}function eh(){return[{id:(0,Z.A)(),type:"focus-reminder",title:"Great Focus Session!",message:"You've been focused for 45 minutes. Keep up the excellent work!",timestamp:(0,eu.Y)(new Date,5),priority:"low",actionable:!1},{id:(0,Z.A)(),type:"break-suggestion",title:"Time for a Break",message:"You've been working for 2 hours. Consider taking a 10-minute break.",timestamp:(0,eu.Y)(new Date,15),priority:"medium",actionable:!0,action:{label:"Take Break",callback:()=>console.log("Taking break...")}},{id:(0,Z.A)(),type:"distraction-alert",title:"Distraction Detected",message:"You switched to social media. Try to stay focused on your current task.",timestamp:(0,eu.Y)(new Date,30),priority:"high",actionable:!0,action:{label:"Return to Work",callback:()=>console.log("Returning to work...")}}]}function eg(){return[{id:(0,Z.A)(),name:"Daily Focus Time",description:"Achieve 6 hours of focused work per day",type:"daily-focus-time",target:360,current:265,unit:"minutes",deadline:new Date(new Date().setHours(23,59,59)),isActive:!0,createdAt:(0,el.O)(new Date,24)},{id:(0,Z.A)(),name:"Deep Work Sessions",description:"Complete 3 deep work sessions (45+ minutes) daily",type:"increase-deep-work",target:3,current:2,unit:"sessions",deadline:new Date(new Date().setHours(23,59,59)),isActive:!0,createdAt:(0,el.O)(new Date,24)},{id:(0,Z.A)(),name:"Reduce Distractions",description:"Keep context switches under 15 per day",type:"reduce-distractions",target:15,current:12,unit:"switches",deadline:new Date(new Date().setHours(23,59,59)),isActive:!0,createdAt:(0,el.O)(new Date,24)}]}var ep=s(83540),ef=s(99445),ex=s(94754),ey=s(96025),ev=s(16238),ew=s(94517),ej=s(62341),eb=s(8782),ek=s(34e3),eN=s(54811),eS=s(93504),eT=s(21374),eA=s(1243),eC=s(40646);function eP(e){let{metrics:t,sessions:s,timeRange:a}=e;if(!t)return(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[void 0,void 0,void 0,void 0].map((e,t)=>(0,i.jsx)(x,{children:(0,i.jsx)(j,{className:"p-6",children:(0,i.jsxs)("div",{className:"animate-pulse",children:[(0,i.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-2"}),(0,i.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/2"})]})})},t))});let r=function(e){let t={};for(let e=0;e<24;e++)t[e]={totalScore:0,count:0};return e.forEach(e=>{let s=(0,ee.q)(e.startTime);t[s].totalScore+=e.focusScore,t[s].count+=1}),Object.entries(t).map(e=>{let[t,s]=e;return{hour:parseInt(t),focusScore:s.count>0?s.totalScore/s.count:0}})}(s),n=function(e){let t={},s={"deep-work":"#3b82f6",communication:"#10b981",research:"#f59e0b",creative:"#8b5cf6",administrative:"#6b7280",learning:"#06b6d4",entertainment:"#ef4444",social:"#ec4899",distraction:"#f97316",unknown:"#9ca3af"};return e.forEach(e=>{t[e.taskCategory]=(t[e.taskCategory]||0)+e.duration}),Object.entries(t).map(e=>{let[t,i]=e;return{name:t.replace("-"," "),value:i,color:s[t]||"#9ca3af"}})}(s),o=s.sort((e,t)=>e.startTime.getTime()-t.startTime.getTime()).map(e=>({time:(0,eo.GP)(e.startTime,"HH:mm"),focusScore:e.focusScore}));return(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,i.jsx)(eD,{title:"Total Focus Time",value:"".concat(Math.round(t.totalFocusTime)," min"),icon:(0,i.jsx)(B.A,{className:"h-5 w-5"}),trend:eR("focusTime",t.totalFocusTime),color:"blue"}),(0,i.jsx)(eD,{title:"Focus Score",value:"".concat(Math.round(t.focusScore),"%"),icon:(0,i.jsx)(H.A,{className:"h-5 w-5"}),trend:eR("focusScore",t.focusScore),color:"green"}),(0,i.jsx)(eD,{title:"Deep Work Sessions",value:t.deepWorkSessions.toString(),icon:(0,i.jsx)(O.A,{className:"h-5 w-5"}),trend:eR("deepWork",t.deepWorkSessions),color:"purple"}),(0,i.jsx)(eD,{title:"Context Switches",value:t.contextSwitches.toString(),icon:(0,i.jsx)(eA.A,{className:"h-5 w-5"}),trend:eR("contextSwitches",t.contextSwitches),color:"orange",inverse:!0})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,i.jsxs)(x,{children:[(0,i.jsxs)(y,{children:[(0,i.jsx)(v,{children:"Productivity by Hour"}),(0,i.jsx)(w,{children:"Your focus patterns throughout the day"})]}),(0,i.jsx)(j,{children:(0,i.jsx)(ep.u,{width:"100%",height:300,children:(0,i.jsxs)(ef.Q,{data:r,children:[(0,i.jsx)(ex.d,{strokeDasharray:"3 3"}),(0,i.jsx)(ey.W,{dataKey:"hour"}),(0,i.jsx)(ev.h,{}),(0,i.jsx)(ew.m,{formatter:e=>["".concat(Math.round(e),"%"),"Focus Score"],labelFormatter:e=>"".concat(e,":00")}),(0,i.jsx)(ej.G,{type:"monotone",dataKey:"focusScore",stroke:"#3b82f6",fill:"#3b82f6",fillOpacity:.3})]})})})]}),(0,i.jsxs)(x,{children:[(0,i.jsxs)(y,{children:[(0,i.jsx)(v,{children:"Task Distribution"}),(0,i.jsx)(w,{children:"Time spent on different types of activities"})]}),(0,i.jsx)(j,{children:(0,i.jsx)(ep.u,{width:"100%",height:300,children:(0,i.jsxs)(eb.r,{children:[(0,i.jsx)(ek.F,{data:n,cx:"50%",cy:"50%",labelLine:!1,label:e=>{let{name:t,percent:s}=e;return"".concat(t," ").concat((100*s).toFixed(0),"%")},outerRadius:80,fill:"#8884d8",dataKey:"value",children:n.map((e,t)=>(0,i.jsx)(eN.f,{fill:e.color},"cell-".concat(t)))}),(0,i.jsx)(ew.m,{formatter:e=>["".concat(Math.round(e)," min"),"Time"]})]})})})]})]}),(0,i.jsxs)(x,{children:[(0,i.jsxs)(y,{children:[(0,i.jsx)(v,{children:"Productivity Trend"}),(0,i.jsx)(w,{children:"Focus score progression over time"})]}),(0,i.jsx)(j,{children:(0,i.jsx)(ep.u,{width:"100%",height:200,children:(0,i.jsxs)(eS.b,{data:o,children:[(0,i.jsx)(ex.d,{strokeDasharray:"3 3"}),(0,i.jsx)(ey.W,{dataKey:"time"}),(0,i.jsx)(ev.h,{domain:[0,100]}),(0,i.jsx)(ew.m,{formatter:e=>["".concat(Math.round(e),"%"),"Focus Score"]}),(0,i.jsx)(eT.N,{type:"monotone",dataKey:"focusScore",stroke:"#10b981",strokeWidth:2,dot:{fill:"#10b981",strokeWidth:2,r:4}})]})})})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,i.jsxs)(x,{children:[(0,i.jsxs)(y,{children:[(0,i.jsx)(v,{children:"Most Productive Apps"}),(0,i.jsx)(w,{children:"Apps where you maintain highest focus"})]}),(0,i.jsx)(j,{children:(0,i.jsx)("div",{className:"space-y-4",children:t.topProductiveApps.slice(0,5).map((e,t)=>(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"flex items-center gap-3",children:[(0,i.jsx)("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:(0,i.jsx)("span",{className:"text-green-600 font-semibold text-sm",children:t+1})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"font-medium",children:e.appName}),(0,i.jsxs)("p",{className:"text-sm text-gray-500",children:[Math.round(e.focusTime)," min focused"]})]})]}),(0,i.jsxs)(R,{variant:"outline",className:"bg-green-50 text-green-700",children:[Math.round(e.productivityScore),"%"]})]},e.appName))})})]}),(0,i.jsxs)(x,{children:[(0,i.jsxs)(y,{children:[(0,i.jsx)(v,{children:"Distraction Sources"}),(0,i.jsx)(w,{children:"Apps that tend to break your focus"})]}),(0,i.jsx)(j,{children:(0,i.jsx)("div",{className:"space-y-4",children:t.topDistractingApps.slice(0,5).map((e,t)=>(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"flex items-center gap-3",children:[(0,i.jsx)("div",{className:"w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center",children:(0,i.jsx)("span",{className:"text-orange-600 font-semibold text-sm",children:t+1})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"font-medium",children:e.appName}),(0,i.jsxs)("p",{className:"text-sm text-gray-500",children:[Math.round(e.distractionTime)," min distracted"]})]})]}),(0,i.jsxs)(R,{variant:"outline",className:"bg-orange-50 text-orange-700",children:[e.sessionCount," interruptions"]})]},e.appName))})})]})]}),(0,i.jsxs)(x,{children:[(0,i.jsxs)(y,{children:[(0,i.jsx)(v,{children:"Key Insights"}),(0,i.jsx)(w,{children:"AI-generated insights about your productivity patterns"})]}),(0,i.jsx)(j,{children:(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsx)(eM,{icon:(0,i.jsx)(G.A,{className:"h-5 w-5 text-green-600"}),title:"Peak Performance",description:"Your most productive hour is ".concat(t.mostProductiveHour,":00"),type:"positive"}),(0,i.jsx)(eM,{icon:(0,i.jsx)(K.A,{className:"h-5 w-5 text-blue-600"}),title:"Energy Dip",description:"Consider breaks around ".concat(t.leastProductiveHour,":00"),type:"neutral"}),(0,i.jsx)(eM,{icon:(0,i.jsx)(eC.A,{className:"h-5 w-5 text-green-600"}),title:"Session Quality",description:"Average session length: ".concat(Math.round(t.averageSessionLength)," minutes"),type:"positive"}),(0,i.jsx)(eM,{icon:(0,i.jsx)(eA.A,{className:"h-5 w-5 text-orange-600"}),title:"Focus Improvement",description:"".concat(t.contextSwitches," context switches detected"),type:"warning"})]})})]})]})}function eD(e){let{title:t,value:s,icon:a,trend:r,color:n,inverse:o=!1}=e;return(0,i.jsx)(x,{children:(0,i.jsxs)(j,{className:"p-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsx)("div",{className:"p-2 rounded-lg ".concat({blue:"bg-blue-50 text-blue-600",green:"bg-green-50 text-green-600",purple:"bg-purple-50 text-purple-600",orange:"bg-orange-50 text-orange-600"}[n]),children:a}),r&&(0,i.jsxs)(R,{variant:"outline",className:"".concat(r.isPositive&&!o||!r.isPositive&&o?"bg-green-50 text-green-700":"bg-red-50 text-red-700"),children:[r.isPositive?"+":"",r.value,"%"]})]}),(0,i.jsxs)("div",{className:"mt-4",children:[(0,i.jsx)("h3",{className:"text-2xl font-bold",children:s}),(0,i.jsx)("p",{className:"text-gray-600 text-sm",children:t})]})]})})}function eM(e){let{icon:t,title:s,description:a,type:r}=e;return(0,i.jsx)("div",{className:"p-4 rounded-lg border ".concat({positive:"border-green-200 bg-green-50",neutral:"border-blue-200 bg-blue-50",warning:"border-orange-200 bg-orange-50"}[r]),children:(0,i.jsxs)("div",{className:"flex items-start gap-3",children:[t,(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-medium",children:s}),(0,i.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:a})]})]})})}function eR(e,t){return({focusTime:{value:12,isPositive:!0},focusScore:{value:8,isPositive:!0},deepWork:{value:15,isPositive:!0},contextSwitches:{value:-5,isPositive:!1}})[e]}function eF(){let[e,t]=(0,a.useState)(!1),[s,r]=(0,a.useState)("overview"),[n,o]=(0,a.useState)("today"),[c,l]=(0,a.useState)(!0),[d,u]=(0,a.useState)(null),[m,h]=(0,a.useState)([]),[g,p]=(0,a.useState)([]),[f,b]=(0,a.useState)([]),[A,C]=(0,a.useState)(null),[P,M]=(0,a.useState)(null),[F,q]=(0,a.useState)(null),[U,V]=(0,a.useState)(null),[Z,J]=(0,a.useState)({focusThreshold:15,distractionThreshold:30,contextSwitchWindow:5,breakReminderInterval:60,deepWorkMinimum:45,productivityCategories:[],enableRealTimeCoaching:!0,enableBreakReminders:!0,enableGoalTracking:!0});(0,a.useEffect)(()=>{(async()=>{try{let e=new Q(Z),t=new X(Z),s=new $,i=new ei;C(e),M(t),q(s),V(i),t.onNotification(e=>{var t,s;p(t=>[e,...t.slice(0,9)]),(0,ec.oR)(e.title,{description:e.message,action:e.actionable?{label:(null===(t=e.action)||void 0===t?void 0:t.label)||"Action",onClick:(null===(s=e.action)||void 0===s?void 0:s.callback)||(()=>{})}:void 0})}),l(!1)}catch(e){console.error("Error initializing analytics engines:",e),ec.oR.error("Failed to initialize analytics engines"),l(!1)}})()},[Z]),(0,a.useEffect)(()=>{!c&&(async()=>{try{if(A){let e={today:{start:(0,ea.o)(new Date),end:(0,er.D)(new Date)},yesterday:{start:(0,ea.o)((0,en.e)(new Date,1)),end:(0,er.D)((0,en.e)(new Date,1))},week:{start:(0,ea.o)((0,en.e)(new Date,7)),end:(0,er.D)(new Date)},month:{start:(0,ea.o)((0,en.e)(new Date,30)),end:(0,er.D)(new Date)}}[n],t=await A.analyzeActivity(e);if(t.focusSessions.length>0)h(t.focusSessions),u(t.metrics);else{let e=em();h(e.focusSessions),u(e.productivityMetrics),ec.oR.info("Using demo data - start Screenpipe to see real analytics")}}else{let e=em();h(e.focusSessions),u(e.productivityMetrics),p(eh()),b(eg()),ec.oR.info("Demo mode - install and run Screenpipe for real analytics")}}catch(t){console.error("Error loading analytics data:",t);let e=em();h(e.focusSessions),u(e.productivityMetrics),p(eh()),b(eg()),ec.oR.error("Failed to load real data, showing demo data")}})()},[A,n,c]);let ee=async()=>{if(P)try{e?(P.stopMonitoring(),t(!1),ec.oR.success("Stopped productivity monitoring")):(await P.startMonitoring(),t(!0),ec.oR.success("Started productivity monitoring"))}catch(e){console.error("Error toggling monitoring:",e),ec.oR.error("Failed to toggle monitoring")}},et=e=>{J(t=>({...t,...e})),ec.oR.success("Settings updated")};return c?(0,i.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,i.jsx)("p",{className:"text-gray-600",children:"Initializing Focus Analytics..."})]})}):(0,i.jsxs)("div",{className:"container mx-auto p-6 space-y-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Focus Analytics & Productivity Coach"}),(0,i.jsx)("p",{className:"text-gray-600 mt-1",children:"AI-powered insights to optimize your productivity and focus"})]}),(0,i.jsxs)("div",{className:"flex items-center gap-4",children:[(0,i.jsxs)("select",{value:n,onChange:e=>o(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,i.jsx)("option",{value:"today",children:"Today"}),(0,i.jsx)("option",{value:"yesterday",children:"Yesterday"}),(0,i.jsx)("option",{value:"week",children:"Last 7 Days"}),(0,i.jsx)("option",{value:"month",children:"Last 30 Days"})]}),(0,i.jsx)(D,{onClick:ee,variant:e?"destructive":"default",className:"flex items-center gap-2",children:e?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(I.A,{className:"h-4 w-4"}),"Stop Monitoring"]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(E.A,{className:"h-4 w-4"}),"Start Monitoring"]})})]})]}),(0,i.jsx)(x,{children:(0,i.jsx)(j,{className:"p-4",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"flex items-center gap-4",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)("div",{className:"w-3 h-3 rounded-full ".concat(e?"bg-green-500":"bg-gray-400")}),(0,i.jsx)("span",{className:"text-sm font-medium",children:e?"Monitoring Active":"Monitoring Inactive"})]}),d&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)(R,{variant:"outline",className:"flex items-center gap-1",children:[(0,i.jsx)(B.A,{className:"h-3 w-3"}),Math.round(d.totalFocusTime)," min focused"]}),(0,i.jsxs)(R,{variant:"outline",className:"flex items-center gap-1",children:[(0,i.jsx)(G.A,{className:"h-3 w-3"}),Math.round(d.focusScore),"% focus score"]}),(0,i.jsxs)(R,{variant:"outline",className:"flex items-center gap-1",children:[(0,i.jsx)(O.A,{className:"h-3 w-3"}),d.deepWorkSessions," deep work sessions"]})]})]}),g.length>0&&(0,i.jsxs)(D,{variant:"ghost",size:"sm",className:"flex items-center gap-2",children:[(0,i.jsx)(L.A,{className:"h-4 w-4"}),g.length," notifications"]})]})})}),(0,i.jsxs)(k,{value:s,onValueChange:r,className:"space-y-6",children:[(0,i.jsxs)(N,{className:"grid w-full grid-cols-6",children:[(0,i.jsxs)(S,{value:"overview",className:"flex items-center gap-2",children:[(0,i.jsx)(W.A,{className:"h-4 w-4"}),"Overview"]}),(0,i.jsxs)(S,{value:"sessions",className:"flex items-center gap-2",children:[(0,i.jsx)(B.A,{className:"h-4 w-4"}),"Sessions"]}),(0,i.jsxs)(S,{value:"coaching",className:"flex items-center gap-2",children:[(0,i.jsx)(H.A,{className:"h-4 w-4"}),"Coaching"]}),(0,i.jsxs)(S,{value:"goals",className:"flex items-center gap-2",children:[(0,i.jsx)(Y.A,{className:"h-4 w-4"}),"Goals"]}),(0,i.jsxs)(S,{value:"patterns",className:"flex items-center gap-2",children:[(0,i.jsx)(G.A,{className:"h-4 w-4"}),"Patterns"]}),(0,i.jsxs)(S,{value:"settings",className:"flex items-center gap-2",children:[(0,i.jsx)(_.A,{className:"h-4 w-4"}),"Settings"]})]}),(0,i.jsx)(T,{value:"overview",className:"space-y-6",children:(0,i.jsx)(eP,{metrics:d,sessions:m,timeRange:n})}),(0,i.jsx)(T,{value:"sessions",className:"space-y-6",children:(0,i.jsxs)("div",{className:"text-center py-12",children:[(0,i.jsx)(B.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,i.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Focus Sessions"}),(0,i.jsx)("p",{className:"text-gray-600",children:"Detailed session analysis coming soon..."})]})}),(0,i.jsxs)(T,{value:"coaching",className:"space-y-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h2",{className:"text-2xl font-bold",children:"Real-time Coaching"}),(0,i.jsx)("p",{className:"text-gray-600",children:"AI-powered insights and suggestions for better productivity"})]}),(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)("div",{className:"w-3 h-3 rounded-full ".concat(e?"bg-green-500":"bg-gray-400")}),(0,i.jsx)("span",{className:"text-sm",children:e?"Coaching Active":"Coaching Inactive"})]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,i.jsxs)(x,{children:[(0,i.jsxs)(y,{children:[(0,i.jsx)(v,{children:"Recent Notifications"}),(0,i.jsx)(w,{children:"Latest coaching insights and suggestions"})]}),(0,i.jsx)(j,{children:(0,i.jsx)("div",{className:"space-y-4",children:g.length>0?g.slice(0,5).map(e=>(0,i.jsxs)("div",{className:"flex items-start gap-3 p-3 bg-gray-50 rounded-lg",children:[(0,i.jsx)("div",{className:"w-2 h-2 rounded-full mt-2 ".concat("high"===e.priority?"bg-red-500":"medium"===e.priority?"bg-yellow-500":"bg-blue-500")}),(0,i.jsxs)("div",{className:"flex-1",children:[(0,i.jsx)("h4",{className:"font-medium text-sm",children:e.title}),(0,i.jsx)("p",{className:"text-xs text-gray-600 mt-1",children:e.message}),(0,i.jsxs)("div",{className:"flex items-center justify-between mt-2",children:[(0,i.jsx)("span",{className:"text-xs text-gray-500",children:(0,eo.GP)(e.timestamp,"HH:mm")}),e.actionable&&e.action&&(0,i.jsx)(D,{size:"sm",variant:"outline",onClick:e.action.callback,className:"text-xs h-6",children:e.action.label})]})]})]},e.id)):(0,i.jsxs)("div",{className:"text-center py-8",children:[(0,i.jsx)(L.A,{className:"h-8 w-8 text-gray-400 mx-auto mb-2"}),(0,i.jsx)("p",{className:"text-sm text-gray-500",children:"No notifications yet"})]})})})]}),(0,i.jsxs)(x,{children:[(0,i.jsxs)(y,{children:[(0,i.jsx)(v,{children:"AI Insights"}),(0,i.jsx)(w,{children:"Personalized productivity recommendations"})]}),(0,i.jsx)(j,{children:(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg border border-blue-200",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,i.jsx)(H.A,{className:"h-4 w-4 text-blue-600"}),(0,i.jsx)("span",{className:"font-medium text-blue-900",children:"Focus Pattern"})]}),(0,i.jsx)("p",{className:"text-sm text-blue-800",children:"You're most productive between 9-11 AM. Schedule important tasks during this window."})]}),(0,i.jsxs)("div",{className:"p-4 bg-green-50 rounded-lg border border-green-200",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,i.jsx)(G.A,{className:"h-4 w-4 text-green-600"}),(0,i.jsx)("span",{className:"font-medium text-green-900",children:"Improvement"})]}),(0,i.jsx)("p",{className:"text-sm text-green-800",children:"Your focus score improved by 15% this week. Keep up the great work!"})]}),(0,i.jsxs)("div",{className:"p-4 bg-orange-50 rounded-lg border border-orange-200",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,i.jsx)(K.A,{className:"h-4 w-4 text-orange-600"}),(0,i.jsx)("span",{className:"font-medium text-orange-900",children:"Break Suggestion"})]}),(0,i.jsx)("p",{className:"text-sm text-orange-800",children:"Consider taking a 5-minute break every 45 minutes to maintain peak performance."})]})]})})]})]})]}),(0,i.jsxs)(T,{value:"goals",className:"space-y-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h2",{className:"text-2xl font-bold",children:"Productivity Goals"}),(0,i.jsx)("p",{className:"text-gray-600",children:"Track your progress towards better focus and productivity"})]}),(0,i.jsxs)(D,{onClick:()=>ec.oR.info("Goal creation coming soon!"),children:[(0,i.jsx)(Y.A,{className:"h-4 w-4 mr-2"}),"New Goal"]})]}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:f.map(e=>(0,i.jsxs)(x,{children:[(0,i.jsxs)(y,{children:[(0,i.jsx)(v,{className:"text-lg",children:e.name}),(0,i.jsx)(w,{children:e.description})]}),(0,i.jsx)(j,{children:(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsxs)("div",{className:"flex justify-between text-sm mb-2",children:[(0,i.jsx)("span",{children:"Progress"}),(0,i.jsxs)("span",{children:[e.current," / ",e.target," ",e.unit]})]}),(0,i.jsx)(z,{value:e.current/e.target*100,className:"h-2"}),(0,i.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:[Math.round(e.current/e.target*100),"% complete"]})]}),(0,i.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,i.jsx)("span",{className:"text-gray-500",children:"Status"}),(0,i.jsx)(R,{variant:e.isActive?"default":"secondary",children:e.isActive?"Active":"Inactive"})]}),e.deadline&&(0,i.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,i.jsx)("span",{className:"text-gray-500",children:"Deadline"}),(0,i.jsx)("span",{children:(0,eo.GP)(e.deadline,"MMM dd, HH:mm")})]})]})})]},e.id))}),0===f.length&&(0,i.jsx)(x,{children:(0,i.jsxs)(j,{className:"text-center py-12",children:[(0,i.jsx)(Y.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,i.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Goals Set"}),(0,i.jsx)("p",{className:"text-gray-600 mb-4",children:"Create your first productivity goal to start tracking progress"}),(0,i.jsx)(D,{onClick:()=>ec.oR.info("Goal creation coming soon!"),children:"Create Goal"})]})})]}),(0,i.jsx)(T,{value:"patterns",className:"space-y-6",children:(0,i.jsxs)("div",{className:"text-center py-12",children:[(0,i.jsx)(G.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,i.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Pattern Insights"}),(0,i.jsx)("p",{className:"text-gray-600",children:"AI pattern recognition coming soon..."})]})}),(0,i.jsx)(T,{value:"settings",className:"space-y-6",children:(0,i.jsxs)(x,{children:[(0,i.jsxs)(y,{children:[(0,i.jsx)(v,{children:"Analytics Configuration"}),(0,i.jsx)(w,{children:"Customize how the Focus Analytics system tracks and analyzes your productivity"})]}),(0,i.jsxs)(j,{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"text-sm font-medium",children:"Focus Threshold (minutes)"}),(0,i.jsx)("p",{className:"text-xs text-gray-500 mb-2",children:"Minimum duration to consider a focus session"}),(0,i.jsx)("input",{type:"number",value:Z.focusThreshold,onChange:e=>et({focusThreshold:parseInt(e.target.value)}),className:"w-full px-3 py-2 border border-gray-300 rounded-md",min:"5",max:"60"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"text-sm font-medium",children:"Break Reminder Interval (minutes)"}),(0,i.jsx)("p",{className:"text-xs text-gray-500 mb-2",children:"How often to suggest breaks"}),(0,i.jsx)("input",{type:"number",value:Z.breakReminderInterval,onChange:e=>et({breakReminderInterval:parseInt(e.target.value)}),className:"w-full px-3 py-2 border border-gray-300 rounded-md",min:"15",max:"180"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"text-sm font-medium",children:"Deep Work Minimum (minutes)"}),(0,i.jsx)("p",{className:"text-xs text-gray-500 mb-2",children:"Minimum duration for deep work classification"}),(0,i.jsx)("input",{type:"number",value:Z.deepWorkMinimum,onChange:e=>et({deepWorkMinimum:parseInt(e.target.value)}),className:"w-full px-3 py-2 border border-gray-300 rounded-md",min:"30",max:"120"})]})]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"text-sm font-medium",children:"Real-time Coaching"}),(0,i.jsx)("p",{className:"text-xs text-gray-500",children:"Enable live productivity notifications"})]}),(0,i.jsx)("input",{type:"checkbox",checked:Z.enableRealTimeCoaching,onChange:e=>et({enableRealTimeCoaching:e.target.checked}),className:"rounded"})]}),(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"text-sm font-medium",children:"Break Reminders"}),(0,i.jsx)("p",{className:"text-xs text-gray-500",children:"Suggest breaks based on work intensity"})]}),(0,i.jsx)("input",{type:"checkbox",checked:Z.enableBreakReminders,onChange:e=>et({enableBreakReminders:e.target.checked}),className:"rounded"})]}),(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"text-sm font-medium",children:"Goal Tracking"}),(0,i.jsx)("p",{className:"text-xs text-gray-500",children:"Track progress towards productivity goals"})]}),(0,i.jsx)("input",{type:"checkbox",checked:Z.enableGoalTracking,onChange:e=>et({enableGoalTracking:e.target.checked}),className:"rounded"})]})]})]}),(0,i.jsxs)("div",{className:"pt-4 border-t",children:[(0,i.jsx)("h4",{className:"font-medium mb-3",children:"Current Status"}),(0,i.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm",children:[(0,i.jsxs)("div",{className:"text-center p-3 bg-gray-50 rounded-lg",children:[(0,i.jsx)("div",{className:"font-medium",children:"Monitoring"}),(0,i.jsx)("div",{className:"text-xs ".concat(e?"text-green-600":"text-gray-500"),children:e?"Active":"Inactive"})]}),(0,i.jsxs)("div",{className:"text-center p-3 bg-gray-50 rounded-lg",children:[(0,i.jsx)("div",{className:"font-medium",children:"Sessions Today"}),(0,i.jsx)("div",{className:"text-xs text-gray-600",children:m.length})]}),(0,i.jsxs)("div",{className:"text-center p-3 bg-gray-50 rounded-lg",children:[(0,i.jsx)("div",{className:"font-medium",children:"Active Goals"}),(0,i.jsx)("div",{className:"text-xs text-gray-600",children:f.filter(e=>e.isActive).length})]}),(0,i.jsxs)("div",{className:"text-center p-3 bg-gray-50 rounded-lg",children:[(0,i.jsx)("div",{className:"font-medium",children:"Notifications"}),(0,i.jsx)("div",{className:"text-xs text-gray-600",children:g.length})]})]})]})]})]})})]})]})}function ez(){return(0,i.jsx)(m,{children:(0,i.jsx)(h,{children:(0,i.jsx)("div",{className:"min-h-screen bg-gray-50 ".concat(p().className),children:(0,i.jsx)(eF,{})})})})}},18590:()=>{},30046:(e,t,s)=>{Promise.resolve().then(s.bind(s,11800))},41234:()=>{},53999:(e,t,s)=>{"use strict";s.d(t,{cn:()=>r});var i=s(52596),a=s(39688);function r(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,a.QP)((0,i.$)(t))}s(20029)},80551:()=>{},85817:()=>{}},e=>{var t=t=>e(e.s=t);e.O(0,[183,229,690,272,660,56,87,251,674,266,370,441,684,358],()=>t(30046)),_N_E=e.O()}]);