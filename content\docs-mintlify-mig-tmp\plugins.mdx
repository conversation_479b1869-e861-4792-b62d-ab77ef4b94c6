---
title: "Pipes"
icon: "puzzle-piece"
---

screenpipe is built for extensibility through plugins that interact with captured screen and audio data. whether you need to tag activities, generate summaries, or send data to third-party services, plugins let you build powerful workflows.

plugins run within screenpipe's sandboxed environment. written in typescript/javascript and nextjs.

[feel free to use our docs as context in cursor agent through MCP](https://docs.screenpi.pe/mcp-server#mintlify-mcp-use-our-docs-in-cursor%2C-claude%2C-etc)

### why build pipes? 🚀

regardless of progress in AI architecture, it's as good as the given context. screenpipe is the bridge between dull hallunicating AI and super intelligent agents.

#### for developers

- **zero infrastructure**: 100% local by default, no servers or complex setups, access to your auth tokens (unlike cloud agents)
- **typescript \+ rust \+ bun**: blazing fast environment, highly optimized pipeline, 4 AI models, running on $200 laptops, best local STT in the market
- **full context**: rich OCR, desktop scrapping, keyboard/mouse, and audio transcription APIs
- **open source**: no BS, no tricks, MIT license
- **monetization ready**: Stripe integration to monetize your pipes
- **no lock-in**: use our store for distribution, then drive traffic into your exported standalone app
  - [screenpipe-tauri-template](https://github.com/LorenzoBloedow/screenpipe-tauri-template-dev)
  - [screenpipe-electron-template](https://github.com/neo773/screenpipe-electron)

#### killer features

- **ai flexibility**: OpenAI, local LLMs (ollama), Anthropic, Gemini, etc.
- **rich APIs**:
  - `pipe.queryScreenpipe` for context
  - `pipe.settings` for app settings
  - experimental `pipe.streamTranscriptions` for audio transcription streaming (make sure to enable it in settings)
  - experimental `pipe.streamVision` for OCR/Accessibility/Frames/browser URL streaming
  - experimental `pipe.input` for keyboard/mouse control
- **sandboxed & cross-platform**: safe execution on all OS
- **real-time**: process screen & audio as it happens
- **cron jobs**: schedule your pipes to run at specific times, same API as Vercel
- **nextjs**: build desktop native apps with NextJS - no native hell

### quick start

we recommend using `bun`, [install](https://bun.sh/docs/installation) or make sure it's up to date.

The fastest way to create a new pipe is using our CLI:

follow installation instructions & test your pipe locally

```bash
bunx --bun @screenpipe/dev@latest pipe create # or use npx @screenpipe/dev pipe create
bun dev
```

### developer CLI

for developers wanting to publish pipes to the store, we provide a dedicated CLI tool:

![developer account](https://raw.githubusercontent.com/mediar-ai/screenpipe/main/content/developer-account.png)

prerequisite: connect your Stripe account in settings/account to obtain your developer API key.

available commands:

```bash
# authenticate with your API key
bunx --bun @screenpipe/dev@latest login 

# create a new pipe
bunx --bun @screenpipe/dev@latest pipe register --name my-pipe [--paid --price 9.99] # MAKE SURE TO SET THE PRICE IF YOU WANT TO SELL IT - IRREVERSIBLE
# you'll receive payouts to your stripe account on a daily basis

# publish your pipe to the store
bunx --bun @screenpipe/dev@latest pipe publish --name my-pipe
# our team will review your pipe and publish it to the store
# if approved, your pipe will be available in the store to everyone

# list all versions of your pipe
bunx --bun @screenpipe/dev@latest pipe list-versions --name my-pipe

# add predefined components to your pipe (shadcn style)
bunx --bun @screenpipe/dev@latest components add
# select components from the interactive menu:
# - use-health: health monitoring hooks
# - use-settings: settings management
# - route-settings: settings page routing
# - use-sql-autocomplete: SQL query assistance
# - sql-autocomplete-input: SQL input component
# - use-search-history: search history management
# - use-ai-provider: AI integration hooks

# end current session
bunx --bun @screenpipe/dev@latest logout
```

you can deploy your pipe to your screenpipe app through the UI or using `screenpipe pipe install <path>` and `screenpipe pipe enable <id/folder of your pipe>`.

when you're ready to deploy, send a PR to the [screenpipe repo](https://github.com/mediar-ai/screenpipe) to add your pipe to the store.

PS: monetization requires a stripe account and US bank account, we recommend [wise](https://wise.com) for international users.

(louis) i am french and i don't have a personal US bank account, so i use wise.

### available pipes

| |                                                                        | |
| --- | ---------------------------------------------------------------------- | --- |
| | automate your second brain by logging activities to obsidian           | |
| | google-photo like gallery of your screen recordings with AI insights   | |
| | explore your data in a powerful table view with filtering and sorting  | |
| | search through your screen recordings and audio transcripts with AI    | |
| | visualize your day with AI-powered timeline of activities              | |
| | automatically identify and label different speakers using AI           | |
| | organize and summarize meetings with AI - get transcripts and insights | |
| | automate business development on linkedin                              | |

to install a pipe from the store, just add the url of the folder in the UI and click install.

### LLM links

paste these links into your Cursor chat for context:

- https://github.com/mediar-ai/screenpipe/blob/main/screenpipe-js/browser-sdk/src/index.ts
- https://github.com/mediar-ai/screenpipe/blob/main/pipes/obsidian/src/app/api/log/route.ts
- https://github.com/mediar-ai/screenpipe/blob/main/pipes/search/src/components/search-chat.tsx
- https://github.com/mediar-ai/screenpipe/blob/main/pipes/rewind/src/lib/hooks/use-app-name-suggestion.tsx
- https://github.com/mediar-ai/screenpipe/blob/main/screenpipe-server/src/server.rs
- https://github.com/mediar-ai/screenpipe/blob/main/screenpipe-core/src/pipes.rs
