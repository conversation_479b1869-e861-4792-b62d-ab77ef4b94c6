### **New Features:**
- **Implemented AI query generation:** Enhanced the search pipe with AI features to speed up querying.
- **Added regular logs and Tokio console support:** Introduced support for regular logs and added a Tokio console for better monitoring and performance insights.
- **Added shebang to CLI:** Improved the command-line interface by adding a shebang for easier execution.

### **Improvements:**
- **Updated health check:** Tuned the health check for better performance and reliability.
- **Improved logging configuration and tracing setup:** Refactored and enhanced the logging configuration and tracing for better diagnostics.

### **Fixes:**
- **Fixed Linux build issue:** Resolved issues preventing successful builds on Linux.
- **Handled exit errors gracefully in CLI:** Ensured the CLI exits without errors when the user terminates the prompt.
- **Fixed shutdown process:** Corrected shutdown issues to ensure the application closes cleanly.

#### **Full Changelog:** [b3b85..b7d50](https://github.com/mediar-ai/screenpipe/compare/b3b85..b7d50)

