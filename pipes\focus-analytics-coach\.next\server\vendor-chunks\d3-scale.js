"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/d3-scale";
exports.ids = ["vendor-chunks/d3-scale"];
exports.modules = {

/***/ "(ssr)/./node_modules/d3-scale/src/band.js":
/*!*******************************************!*\
  !*** ./node_modules/d3-scale/src/band.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ band),\n/* harmony export */   point: () => (/* binding */ point)\n/* harmony export */ });\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/range.js\");\n/* harmony import */ var _init_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./init.js */ \"(ssr)/./node_modules/d3-scale/src/init.js\");\n/* harmony import */ var _ordinal_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ordinal.js */ \"(ssr)/./node_modules/d3-scale/src/ordinal.js\");\n\n\n\n\nfunction band() {\n  var scale = (0,_ordinal_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])().unknown(undefined),\n      domain = scale.domain,\n      ordinalRange = scale.range,\n      r0 = 0,\n      r1 = 1,\n      step,\n      bandwidth,\n      round = false,\n      paddingInner = 0,\n      paddingOuter = 0,\n      align = 0.5;\n\n  delete scale.unknown;\n\n  function rescale() {\n    var n = domain().length,\n        reverse = r1 < r0,\n        start = reverse ? r1 : r0,\n        stop = reverse ? r0 : r1;\n    step = (stop - start) / Math.max(1, n - paddingInner + paddingOuter * 2);\n    if (round) step = Math.floor(step);\n    start += (stop - start - step * (n - paddingInner)) * align;\n    bandwidth = step * (1 - paddingInner);\n    if (round) start = Math.round(start), bandwidth = Math.round(bandwidth);\n    var values = (0,d3_array__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(n).map(function(i) { return start + step * i; });\n    return ordinalRange(reverse ? values.reverse() : values);\n  }\n\n  scale.domain = function(_) {\n    return arguments.length ? (domain(_), rescale()) : domain();\n  };\n\n  scale.range = function(_) {\n    return arguments.length ? ([r0, r1] = _, r0 = +r0, r1 = +r1, rescale()) : [r0, r1];\n  };\n\n  scale.rangeRound = function(_) {\n    return [r0, r1] = _, r0 = +r0, r1 = +r1, round = true, rescale();\n  };\n\n  scale.bandwidth = function() {\n    return bandwidth;\n  };\n\n  scale.step = function() {\n    return step;\n  };\n\n  scale.round = function(_) {\n    return arguments.length ? (round = !!_, rescale()) : round;\n  };\n\n  scale.padding = function(_) {\n    return arguments.length ? (paddingInner = Math.min(1, paddingOuter = +_), rescale()) : paddingInner;\n  };\n\n  scale.paddingInner = function(_) {\n    return arguments.length ? (paddingInner = Math.min(1, _), rescale()) : paddingInner;\n  };\n\n  scale.paddingOuter = function(_) {\n    return arguments.length ? (paddingOuter = +_, rescale()) : paddingOuter;\n  };\n\n  scale.align = function(_) {\n    return arguments.length ? (align = Math.max(0, Math.min(1, _)), rescale()) : align;\n  };\n\n  scale.copy = function() {\n    return band(domain(), [r0, r1])\n        .round(round)\n        .paddingInner(paddingInner)\n        .paddingOuter(paddingOuter)\n        .align(align);\n  };\n\n  return _init_js__WEBPACK_IMPORTED_MODULE_2__.initRange.apply(rescale(), arguments);\n}\n\nfunction pointish(scale) {\n  var copy = scale.copy;\n\n  scale.padding = scale.paddingOuter;\n  delete scale.paddingInner;\n  delete scale.paddingOuter;\n\n  scale.copy = function() {\n    return pointish(copy());\n  };\n\n  return scale;\n}\n\nfunction point() {\n  return pointish(band.apply(null, arguments).paddingInner(1));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-scale/src/band.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-scale/src/constant.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-scale/src/constant.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ constants)\n/* harmony export */ });\nfunction constants(x) {\n  return function() {\n    return x;\n  };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtc2NhbGUvc3JjL2NvbnN0YW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJHOlxccHJvamVjdHNcXHNjcmVlbnBpcGVcXHNjcmVlbnBpcGVcXHBpcGVzXFxmb2N1cy1hbmFseXRpY3MtY29hY2hcXG5vZGVfbW9kdWxlc1xcZDMtc2NhbGVcXHNyY1xcY29uc3RhbnQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gY29uc3RhbnRzKHgpIHtcbiAgcmV0dXJuIGZ1bmN0aW9uKCkge1xuICAgIHJldHVybiB4O1xuICB9O1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-scale/src/constant.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-scale/src/continuous.js":
/*!*************************************************!*\
  !*** ./node_modules/d3-scale/src/continuous.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   copy: () => (/* binding */ copy),\n/* harmony export */   \"default\": () => (/* binding */ continuous),\n/* harmony export */   identity: () => (/* binding */ identity),\n/* harmony export */   transformer: () => (/* binding */ transformer)\n/* harmony export */ });\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/bisect.js\");\n/* harmony import */ var d3_interpolate__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! d3-interpolate */ \"(ssr)/./node_modules/d3-interpolate/src/value.js\");\n/* harmony import */ var d3_interpolate__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! d3-interpolate */ \"(ssr)/./node_modules/d3-interpolate/src/number.js\");\n/* harmony import */ var d3_interpolate__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! d3-interpolate */ \"(ssr)/./node_modules/d3-interpolate/src/round.js\");\n/* harmony import */ var _constant_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constant.js */ \"(ssr)/./node_modules/d3-scale/src/constant.js\");\n/* harmony import */ var _number_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./number.js */ \"(ssr)/./node_modules/d3-scale/src/number.js\");\n\n\n\n\n\nvar unit = [0, 1];\n\nfunction identity(x) {\n  return x;\n}\n\nfunction normalize(a, b) {\n  return (b -= (a = +a))\n      ? function(x) { return (x - a) / b; }\n      : (0,_constant_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(isNaN(b) ? NaN : 0.5);\n}\n\nfunction clamper(a, b) {\n  var t;\n  if (a > b) t = a, a = b, b = t;\n  return function(x) { return Math.max(a, Math.min(b, x)); };\n}\n\n// normalize(a, b)(x) takes a domain value x in [a,b] and returns the corresponding parameter t in [0,1].\n// interpolate(a, b)(t) takes a parameter t in [0,1] and returns the corresponding range value x in [a,b].\nfunction bimap(domain, range, interpolate) {\n  var d0 = domain[0], d1 = domain[1], r0 = range[0], r1 = range[1];\n  if (d1 < d0) d0 = normalize(d1, d0), r0 = interpolate(r1, r0);\n  else d0 = normalize(d0, d1), r0 = interpolate(r0, r1);\n  return function(x) { return r0(d0(x)); };\n}\n\nfunction polymap(domain, range, interpolate) {\n  var j = Math.min(domain.length, range.length) - 1,\n      d = new Array(j),\n      r = new Array(j),\n      i = -1;\n\n  // Reverse descending domains.\n  if (domain[j] < domain[0]) {\n    domain = domain.slice().reverse();\n    range = range.slice().reverse();\n  }\n\n  while (++i < j) {\n    d[i] = normalize(domain[i], domain[i + 1]);\n    r[i] = interpolate(range[i], range[i + 1]);\n  }\n\n  return function(x) {\n    var i = (0,d3_array__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(domain, x, 1, j) - 1;\n    return r[i](d[i](x));\n  };\n}\n\nfunction copy(source, target) {\n  return target\n      .domain(source.domain())\n      .range(source.range())\n      .interpolate(source.interpolate())\n      .clamp(source.clamp())\n      .unknown(source.unknown());\n}\n\nfunction transformer() {\n  var domain = unit,\n      range = unit,\n      interpolate = d3_interpolate__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n      transform,\n      untransform,\n      unknown,\n      clamp = identity,\n      piecewise,\n      output,\n      input;\n\n  function rescale() {\n    var n = Math.min(domain.length, range.length);\n    if (clamp !== identity) clamp = clamper(domain[0], domain[n - 1]);\n    piecewise = n > 2 ? polymap : bimap;\n    output = input = null;\n    return scale;\n  }\n\n  function scale(x) {\n    return x == null || isNaN(x = +x) ? unknown : (output || (output = piecewise(domain.map(transform), range, interpolate)))(transform(clamp(x)));\n  }\n\n  scale.invert = function(y) {\n    return clamp(untransform((input || (input = piecewise(range, domain.map(transform), d3_interpolate__WEBPACK_IMPORTED_MODULE_3__[\"default\"])))(y)));\n  };\n\n  scale.domain = function(_) {\n    return arguments.length ? (domain = Array.from(_, _number_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]), rescale()) : domain.slice();\n  };\n\n  scale.range = function(_) {\n    return arguments.length ? (range = Array.from(_), rescale()) : range.slice();\n  };\n\n  scale.rangeRound = function(_) {\n    return range = Array.from(_), interpolate = d3_interpolate__WEBPACK_IMPORTED_MODULE_5__[\"default\"], rescale();\n  };\n\n  scale.clamp = function(_) {\n    return arguments.length ? (clamp = _ ? true : identity, rescale()) : clamp !== identity;\n  };\n\n  scale.interpolate = function(_) {\n    return arguments.length ? (interpolate = _, rescale()) : interpolate;\n  };\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  return function(t, u) {\n    transform = t, untransform = u;\n    return rescale();\n  };\n}\n\nfunction continuous() {\n  return transformer()(identity, identity);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-scale/src/continuous.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-scale/src/diverging.js":
/*!************************************************!*\
  !*** ./node_modules/d3-scale/src/diverging.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ diverging),\n/* harmony export */   divergingLog: () => (/* binding */ divergingLog),\n/* harmony export */   divergingPow: () => (/* binding */ divergingPow),\n/* harmony export */   divergingSqrt: () => (/* binding */ divergingSqrt),\n/* harmony export */   divergingSymlog: () => (/* binding */ divergingSymlog)\n/* harmony export */ });\n/* harmony import */ var d3_interpolate__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! d3-interpolate */ \"(ssr)/./node_modules/d3-interpolate/src/piecewise.js\");\n/* harmony import */ var d3_interpolate__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! d3-interpolate */ \"(ssr)/./node_modules/d3-interpolate/src/value.js\");\n/* harmony import */ var d3_interpolate__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! d3-interpolate */ \"(ssr)/./node_modules/d3-interpolate/src/round.js\");\n/* harmony import */ var _continuous_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./continuous.js */ \"(ssr)/./node_modules/d3-scale/src/continuous.js\");\n/* harmony import */ var _init_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./init.js */ \"(ssr)/./node_modules/d3-scale/src/init.js\");\n/* harmony import */ var _linear_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./linear.js */ \"(ssr)/./node_modules/d3-scale/src/linear.js\");\n/* harmony import */ var _log_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./log.js */ \"(ssr)/./node_modules/d3-scale/src/log.js\");\n/* harmony import */ var _sequential_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./sequential.js */ \"(ssr)/./node_modules/d3-scale/src/sequential.js\");\n/* harmony import */ var _symlog_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./symlog.js */ \"(ssr)/./node_modules/d3-scale/src/symlog.js\");\n/* harmony import */ var _pow_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./pow.js */ \"(ssr)/./node_modules/d3-scale/src/pow.js\");\n\n\n\n\n\n\n\n\n\nfunction transformer() {\n  var x0 = 0,\n      x1 = 0.5,\n      x2 = 1,\n      s = 1,\n      t0,\n      t1,\n      t2,\n      k10,\n      k21,\n      interpolator = _continuous_js__WEBPACK_IMPORTED_MODULE_0__.identity,\n      transform,\n      clamp = false,\n      unknown;\n\n  function scale(x) {\n    return isNaN(x = +x) ? unknown : (x = 0.5 + ((x = +transform(x)) - t1) * (s * x < s * t1 ? k10 : k21), interpolator(clamp ? Math.max(0, Math.min(1, x)) : x));\n  }\n\n  scale.domain = function(_) {\n    return arguments.length ? ([x0, x1, x2] = _, t0 = transform(x0 = +x0), t1 = transform(x1 = +x1), t2 = transform(x2 = +x2), k10 = t0 === t1 ? 0 : 0.5 / (t1 - t0), k21 = t1 === t2 ? 0 : 0.5 / (t2 - t1), s = t1 < t0 ? -1 : 1, scale) : [x0, x1, x2];\n  };\n\n  scale.clamp = function(_) {\n    return arguments.length ? (clamp = !!_, scale) : clamp;\n  };\n\n  scale.interpolator = function(_) {\n    return arguments.length ? (interpolator = _, scale) : interpolator;\n  };\n\n  function range(interpolate) {\n    return function(_) {\n      var r0, r1, r2;\n      return arguments.length ? ([r0, r1, r2] = _, interpolator = (0,d3_interpolate__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(interpolate, [r0, r1, r2]), scale) : [interpolator(0), interpolator(0.5), interpolator(1)];\n    };\n  }\n\n  scale.range = range(d3_interpolate__WEBPACK_IMPORTED_MODULE_2__[\"default\"]);\n\n  scale.rangeRound = range(d3_interpolate__WEBPACK_IMPORTED_MODULE_3__[\"default\"]);\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  return function(t) {\n    transform = t, t0 = t(x0), t1 = t(x1), t2 = t(x2), k10 = t0 === t1 ? 0 : 0.5 / (t1 - t0), k21 = t1 === t2 ? 0 : 0.5 / (t2 - t1), s = t1 < t0 ? -1 : 1;\n    return scale;\n  };\n}\n\nfunction diverging() {\n  var scale = (0,_linear_js__WEBPACK_IMPORTED_MODULE_4__.linearish)(transformer()(_continuous_js__WEBPACK_IMPORTED_MODULE_0__.identity));\n\n  scale.copy = function() {\n    return (0,_sequential_js__WEBPACK_IMPORTED_MODULE_5__.copy)(scale, diverging());\n  };\n\n  return _init_js__WEBPACK_IMPORTED_MODULE_6__.initInterpolator.apply(scale, arguments);\n}\n\nfunction divergingLog() {\n  var scale = (0,_log_js__WEBPACK_IMPORTED_MODULE_7__.loggish)(transformer()).domain([0.1, 1, 10]);\n\n  scale.copy = function() {\n    return (0,_sequential_js__WEBPACK_IMPORTED_MODULE_5__.copy)(scale, divergingLog()).base(scale.base());\n  };\n\n  return _init_js__WEBPACK_IMPORTED_MODULE_6__.initInterpolator.apply(scale, arguments);\n}\n\nfunction divergingSymlog() {\n  var scale = (0,_symlog_js__WEBPACK_IMPORTED_MODULE_8__.symlogish)(transformer());\n\n  scale.copy = function() {\n    return (0,_sequential_js__WEBPACK_IMPORTED_MODULE_5__.copy)(scale, divergingSymlog()).constant(scale.constant());\n  };\n\n  return _init_js__WEBPACK_IMPORTED_MODULE_6__.initInterpolator.apply(scale, arguments);\n}\n\nfunction divergingPow() {\n  var scale = (0,_pow_js__WEBPACK_IMPORTED_MODULE_9__.powish)(transformer());\n\n  scale.copy = function() {\n    return (0,_sequential_js__WEBPACK_IMPORTED_MODULE_5__.copy)(scale, divergingPow()).exponent(scale.exponent());\n  };\n\n  return _init_js__WEBPACK_IMPORTED_MODULE_6__.initInterpolator.apply(scale, arguments);\n}\n\nfunction divergingSqrt() {\n  return divergingPow.apply(null, arguments).exponent(0.5);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-scale/src/diverging.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-scale/src/identity.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-scale/src/identity.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ identity)\n/* harmony export */ });\n/* harmony import */ var _linear_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./linear.js */ \"(ssr)/./node_modules/d3-scale/src/linear.js\");\n/* harmony import */ var _number_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./number.js */ \"(ssr)/./node_modules/d3-scale/src/number.js\");\n\n\n\nfunction identity(domain) {\n  var unknown;\n\n  function scale(x) {\n    return x == null || isNaN(x = +x) ? unknown : x;\n  }\n\n  scale.invert = scale;\n\n  scale.domain = scale.range = function(_) {\n    return arguments.length ? (domain = Array.from(_, _number_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]), scale) : domain.slice();\n  };\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  scale.copy = function() {\n    return identity(domain).unknown(unknown);\n  };\n\n  domain = arguments.length ? Array.from(domain, _number_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]) : [0, 1];\n\n  return (0,_linear_js__WEBPACK_IMPORTED_MODULE_1__.linearish)(scale);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtc2NhbGUvc3JjL2lkZW50aXR5LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFzQztBQUNMOztBQUVsQjtBQUNmOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBLHNEQUFzRCxrREFBTTtBQUM1RDs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBLGlEQUFpRCxrREFBTTs7QUFFdkQsU0FBUyxxREFBUztBQUNsQiIsInNvdXJjZXMiOlsiRzpcXHByb2plY3RzXFxzY3JlZW5waXBlXFxzY3JlZW5waXBlXFxwaXBlc1xcZm9jdXMtYW5hbHl0aWNzLWNvYWNoXFxub2RlX21vZHVsZXNcXGQzLXNjYWxlXFxzcmNcXGlkZW50aXR5LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7bGluZWFyaXNofSBmcm9tIFwiLi9saW5lYXIuanNcIjtcbmltcG9ydCBudW1iZXIgZnJvbSBcIi4vbnVtYmVyLmpzXCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGlkZW50aXR5KGRvbWFpbikge1xuICB2YXIgdW5rbm93bjtcblxuICBmdW5jdGlvbiBzY2FsZSh4KSB7XG4gICAgcmV0dXJuIHggPT0gbnVsbCB8fCBpc05hTih4ID0gK3gpID8gdW5rbm93biA6IHg7XG4gIH1cblxuICBzY2FsZS5pbnZlcnQgPSBzY2FsZTtcblxuICBzY2FsZS5kb21haW4gPSBzY2FsZS5yYW5nZSA9IGZ1bmN0aW9uKF8pIHtcbiAgICByZXR1cm4gYXJndW1lbnRzLmxlbmd0aCA/IChkb21haW4gPSBBcnJheS5mcm9tKF8sIG51bWJlciksIHNjYWxlKSA6IGRvbWFpbi5zbGljZSgpO1xuICB9O1xuXG4gIHNjYWxlLnVua25vd24gPSBmdW5jdGlvbihfKSB7XG4gICAgcmV0dXJuIGFyZ3VtZW50cy5sZW5ndGggPyAodW5rbm93biA9IF8sIHNjYWxlKSA6IHVua25vd247XG4gIH07XG5cbiAgc2NhbGUuY29weSA9IGZ1bmN0aW9uKCkge1xuICAgIHJldHVybiBpZGVudGl0eShkb21haW4pLnVua25vd24odW5rbm93bik7XG4gIH07XG5cbiAgZG9tYWluID0gYXJndW1lbnRzLmxlbmd0aCA/IEFycmF5LmZyb20oZG9tYWluLCBudW1iZXIpIDogWzAsIDFdO1xuXG4gIHJldHVybiBsaW5lYXJpc2goc2NhbGUpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-scale/src/identity.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-scale/src/index.js":
/*!********************************************!*\
  !*** ./node_modules/d3-scale/src/index.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   scaleBand: () => (/* reexport safe */ _band_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   scaleDiverging: () => (/* reexport safe */ _diverging_js__WEBPACK_IMPORTED_MODULE_15__[\"default\"]),\n/* harmony export */   scaleDivergingLog: () => (/* reexport safe */ _diverging_js__WEBPACK_IMPORTED_MODULE_15__.divergingLog),\n/* harmony export */   scaleDivergingPow: () => (/* reexport safe */ _diverging_js__WEBPACK_IMPORTED_MODULE_15__.divergingPow),\n/* harmony export */   scaleDivergingSqrt: () => (/* reexport safe */ _diverging_js__WEBPACK_IMPORTED_MODULE_15__.divergingSqrt),\n/* harmony export */   scaleDivergingSymlog: () => (/* reexport safe */ _diverging_js__WEBPACK_IMPORTED_MODULE_15__.divergingSymlog),\n/* harmony export */   scaleIdentity: () => (/* reexport safe */ _identity_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   scaleImplicit: () => (/* reexport safe */ _ordinal_js__WEBPACK_IMPORTED_MODULE_5__.implicit),\n/* harmony export */   scaleLinear: () => (/* reexport safe */ _linear_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   scaleLog: () => (/* reexport safe */ _log_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   scaleOrdinal: () => (/* reexport safe */ _ordinal_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   scalePoint: () => (/* reexport safe */ _band_js__WEBPACK_IMPORTED_MODULE_0__.point),\n/* harmony export */   scalePow: () => (/* reexport safe */ _pow_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   scaleQuantile: () => (/* reexport safe */ _quantile_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"]),\n/* harmony export */   scaleQuantize: () => (/* reexport safe */ _quantize_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"]),\n/* harmony export */   scaleRadial: () => (/* reexport safe */ _radial_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"]),\n/* harmony export */   scaleSequential: () => (/* reexport safe */ _sequential_js__WEBPACK_IMPORTED_MODULE_13__[\"default\"]),\n/* harmony export */   scaleSequentialLog: () => (/* reexport safe */ _sequential_js__WEBPACK_IMPORTED_MODULE_13__.sequentialLog),\n/* harmony export */   scaleSequentialPow: () => (/* reexport safe */ _sequential_js__WEBPACK_IMPORTED_MODULE_13__.sequentialPow),\n/* harmony export */   scaleSequentialQuantile: () => (/* reexport safe */ _sequentialQuantile_js__WEBPACK_IMPORTED_MODULE_14__[\"default\"]),\n/* harmony export */   scaleSequentialSqrt: () => (/* reexport safe */ _sequential_js__WEBPACK_IMPORTED_MODULE_13__.sequentialSqrt),\n/* harmony export */   scaleSequentialSymlog: () => (/* reexport safe */ _sequential_js__WEBPACK_IMPORTED_MODULE_13__.sequentialSymlog),\n/* harmony export */   scaleSqrt: () => (/* reexport safe */ _pow_js__WEBPACK_IMPORTED_MODULE_6__.sqrt),\n/* harmony export */   scaleSymlog: () => (/* reexport safe */ _symlog_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   scaleThreshold: () => (/* reexport safe */ _threshold_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"]),\n/* harmony export */   scaleTime: () => (/* reexport safe */ _time_js__WEBPACK_IMPORTED_MODULE_11__[\"default\"]),\n/* harmony export */   scaleUtc: () => (/* reexport safe */ _utcTime_js__WEBPACK_IMPORTED_MODULE_12__[\"default\"]),\n/* harmony export */   tickFormat: () => (/* reexport safe */ _tickFormat_js__WEBPACK_IMPORTED_MODULE_16__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _band_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./band.js */ \"(ssr)/./node_modules/d3-scale/src/band.js\");\n/* harmony import */ var _identity_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./identity.js */ \"(ssr)/./node_modules/d3-scale/src/identity.js\");\n/* harmony import */ var _linear_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./linear.js */ \"(ssr)/./node_modules/d3-scale/src/linear.js\");\n/* harmony import */ var _log_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./log.js */ \"(ssr)/./node_modules/d3-scale/src/log.js\");\n/* harmony import */ var _symlog_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./symlog.js */ \"(ssr)/./node_modules/d3-scale/src/symlog.js\");\n/* harmony import */ var _ordinal_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ordinal.js */ \"(ssr)/./node_modules/d3-scale/src/ordinal.js\");\n/* harmony import */ var _pow_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./pow.js */ \"(ssr)/./node_modules/d3-scale/src/pow.js\");\n/* harmony import */ var _radial_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./radial.js */ \"(ssr)/./node_modules/d3-scale/src/radial.js\");\n/* harmony import */ var _quantile_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./quantile.js */ \"(ssr)/./node_modules/d3-scale/src/quantile.js\");\n/* harmony import */ var _quantize_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./quantize.js */ \"(ssr)/./node_modules/d3-scale/src/quantize.js\");\n/* harmony import */ var _threshold_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./threshold.js */ \"(ssr)/./node_modules/d3-scale/src/threshold.js\");\n/* harmony import */ var _time_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./time.js */ \"(ssr)/./node_modules/d3-scale/src/time.js\");\n/* harmony import */ var _utcTime_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./utcTime.js */ \"(ssr)/./node_modules/d3-scale/src/utcTime.js\");\n/* harmony import */ var _sequential_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./sequential.js */ \"(ssr)/./node_modules/d3-scale/src/sequential.js\");\n/* harmony import */ var _sequentialQuantile_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./sequentialQuantile.js */ \"(ssr)/./node_modules/d3-scale/src/sequentialQuantile.js\");\n/* harmony import */ var _diverging_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./diverging.js */ \"(ssr)/./node_modules/d3-scale/src/diverging.js\");\n/* harmony import */ var _tickFormat_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./tickFormat.js */ \"(ssr)/./node_modules/d3-scale/src/tickFormat.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-scale/src/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-scale/src/init.js":
/*!*******************************************!*\
  !*** ./node_modules/d3-scale/src/init.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   initInterpolator: () => (/* binding */ initInterpolator),\n/* harmony export */   initRange: () => (/* binding */ initRange)\n/* harmony export */ });\nfunction initRange(domain, range) {\n  switch (arguments.length) {\n    case 0: break;\n    case 1: this.range(domain); break;\n    default: this.range(range).domain(domain); break;\n  }\n  return this;\n}\n\nfunction initInterpolator(domain, interpolator) {\n  switch (arguments.length) {\n    case 0: break;\n    case 1: {\n      if (typeof domain === \"function\") this.interpolator(domain);\n      else this.range(domain);\n      break;\n    }\n    default: {\n      this.domain(domain);\n      if (typeof interpolator === \"function\") this.interpolator(interpolator);\n      else this.range(interpolator);\n      break;\n    }\n  }\n  return this;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtc2NhbGUvc3JjL2luaXQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQSxnQ0FBZ0M7QUFDaEMsK0NBQStDO0FBQy9DO0FBQ0E7QUFDQTs7QUFFTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJHOlxccHJvamVjdHNcXHNjcmVlbnBpcGVcXHNjcmVlbnBpcGVcXHBpcGVzXFxmb2N1cy1hbmFseXRpY3MtY29hY2hcXG5vZGVfbW9kdWxlc1xcZDMtc2NhbGVcXHNyY1xcaW5pdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZnVuY3Rpb24gaW5pdFJhbmdlKGRvbWFpbiwgcmFuZ2UpIHtcbiAgc3dpdGNoIChhcmd1bWVudHMubGVuZ3RoKSB7XG4gICAgY2FzZSAwOiBicmVhaztcbiAgICBjYXNlIDE6IHRoaXMucmFuZ2UoZG9tYWluKTsgYnJlYWs7XG4gICAgZGVmYXVsdDogdGhpcy5yYW5nZShyYW5nZSkuZG9tYWluKGRvbWFpbik7IGJyZWFrO1xuICB9XG4gIHJldHVybiB0aGlzO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gaW5pdEludGVycG9sYXRvcihkb21haW4sIGludGVycG9sYXRvcikge1xuICBzd2l0Y2ggKGFyZ3VtZW50cy5sZW5ndGgpIHtcbiAgICBjYXNlIDA6IGJyZWFrO1xuICAgIGNhc2UgMToge1xuICAgICAgaWYgKHR5cGVvZiBkb21haW4gPT09IFwiZnVuY3Rpb25cIikgdGhpcy5pbnRlcnBvbGF0b3IoZG9tYWluKTtcbiAgICAgIGVsc2UgdGhpcy5yYW5nZShkb21haW4pO1xuICAgICAgYnJlYWs7XG4gICAgfVxuICAgIGRlZmF1bHQ6IHtcbiAgICAgIHRoaXMuZG9tYWluKGRvbWFpbik7XG4gICAgICBpZiAodHlwZW9mIGludGVycG9sYXRvciA9PT0gXCJmdW5jdGlvblwiKSB0aGlzLmludGVycG9sYXRvcihpbnRlcnBvbGF0b3IpO1xuICAgICAgZWxzZSB0aGlzLnJhbmdlKGludGVycG9sYXRvcik7XG4gICAgICBicmVhaztcbiAgICB9XG4gIH1cbiAgcmV0dXJuIHRoaXM7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-scale/src/init.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-scale/src/linear.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-scale/src/linear.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ linear),\n/* harmony export */   linearish: () => (/* binding */ linearish)\n/* harmony export */ });\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/ticks.js\");\n/* harmony import */ var _continuous_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./continuous.js */ \"(ssr)/./node_modules/d3-scale/src/continuous.js\");\n/* harmony import */ var _init_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./init.js */ \"(ssr)/./node_modules/d3-scale/src/init.js\");\n/* harmony import */ var _tickFormat_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./tickFormat.js */ \"(ssr)/./node_modules/d3-scale/src/tickFormat.js\");\n\n\n\n\n\nfunction linearish(scale) {\n  var domain = scale.domain;\n\n  scale.ticks = function(count) {\n    var d = domain();\n    return (0,d3_array__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(d[0], d[d.length - 1], count == null ? 10 : count);\n  };\n\n  scale.tickFormat = function(count, specifier) {\n    var d = domain();\n    return (0,_tickFormat_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(d[0], d[d.length - 1], count == null ? 10 : count, specifier);\n  };\n\n  scale.nice = function(count) {\n    if (count == null) count = 10;\n\n    var d = domain();\n    var i0 = 0;\n    var i1 = d.length - 1;\n    var start = d[i0];\n    var stop = d[i1];\n    var prestep;\n    var step;\n    var maxIter = 10;\n\n    if (stop < start) {\n      step = start, start = stop, stop = step;\n      step = i0, i0 = i1, i1 = step;\n    }\n    \n    while (maxIter-- > 0) {\n      step = (0,d3_array__WEBPACK_IMPORTED_MODULE_0__.tickIncrement)(start, stop, count);\n      if (step === prestep) {\n        d[i0] = start\n        d[i1] = stop\n        return domain(d);\n      } else if (step > 0) {\n        start = Math.floor(start / step) * step;\n        stop = Math.ceil(stop / step) * step;\n      } else if (step < 0) {\n        start = Math.ceil(start * step) / step;\n        stop = Math.floor(stop * step) / step;\n      } else {\n        break;\n      }\n      prestep = step;\n    }\n\n    return scale;\n  };\n\n  return scale;\n}\n\nfunction linear() {\n  var scale = (0,_continuous_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n\n  scale.copy = function() {\n    return (0,_continuous_js__WEBPACK_IMPORTED_MODULE_2__.copy)(scale, linear());\n  };\n\n  _init_js__WEBPACK_IMPORTED_MODULE_3__.initRange.apply(scale, arguments);\n\n  return linearish(scale);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-scale/src/linear.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-scale/src/log.js":
/*!******************************************!*\
  !*** ./node_modules/d3-scale/src/log.js ***!
  \******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ log),\n/* harmony export */   loggish: () => (/* binding */ loggish)\n/* harmony export */ });\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/ticks.js\");\n/* harmony import */ var d3_format__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! d3-format */ \"(ssr)/./node_modules/d3-format/src/formatSpecifier.js\");\n/* harmony import */ var d3_format__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! d3-format */ \"(ssr)/./node_modules/d3-format/src/defaultLocale.js\");\n/* harmony import */ var _nice_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./nice.js */ \"(ssr)/./node_modules/d3-scale/src/nice.js\");\n/* harmony import */ var _continuous_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./continuous.js */ \"(ssr)/./node_modules/d3-scale/src/continuous.js\");\n/* harmony import */ var _init_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./init.js */ \"(ssr)/./node_modules/d3-scale/src/init.js\");\n\n\n\n\n\n\nfunction transformLog(x) {\n  return Math.log(x);\n}\n\nfunction transformExp(x) {\n  return Math.exp(x);\n}\n\nfunction transformLogn(x) {\n  return -Math.log(-x);\n}\n\nfunction transformExpn(x) {\n  return -Math.exp(-x);\n}\n\nfunction pow10(x) {\n  return isFinite(x) ? +(\"1e\" + x) : x < 0 ? 0 : x;\n}\n\nfunction powp(base) {\n  return base === 10 ? pow10\n      : base === Math.E ? Math.exp\n      : x => Math.pow(base, x);\n}\n\nfunction logp(base) {\n  return base === Math.E ? Math.log\n      : base === 10 && Math.log10\n      || base === 2 && Math.log2\n      || (base = Math.log(base), x => Math.log(x) / base);\n}\n\nfunction reflect(f) {\n  return (x, k) => -f(-x, k);\n}\n\nfunction loggish(transform) {\n  const scale = transform(transformLog, transformExp);\n  const domain = scale.domain;\n  let base = 10;\n  let logs;\n  let pows;\n\n  function rescale() {\n    logs = logp(base), pows = powp(base);\n    if (domain()[0] < 0) {\n      logs = reflect(logs), pows = reflect(pows);\n      transform(transformLogn, transformExpn);\n    } else {\n      transform(transformLog, transformExp);\n    }\n    return scale;\n  }\n\n  scale.base = function(_) {\n    return arguments.length ? (base = +_, rescale()) : base;\n  };\n\n  scale.domain = function(_) {\n    return arguments.length ? (domain(_), rescale()) : domain();\n  };\n\n  scale.ticks = count => {\n    const d = domain();\n    let u = d[0];\n    let v = d[d.length - 1];\n    const r = v < u;\n\n    if (r) ([u, v] = [v, u]);\n\n    let i = logs(u);\n    let j = logs(v);\n    let k;\n    let t;\n    const n = count == null ? 10 : +count;\n    let z = [];\n\n    if (!(base % 1) && j - i < n) {\n      i = Math.floor(i), j = Math.ceil(j);\n      if (u > 0) for (; i <= j; ++i) {\n        for (k = 1; k < base; ++k) {\n          t = i < 0 ? k / pows(-i) : k * pows(i);\n          if (t < u) continue;\n          if (t > v) break;\n          z.push(t);\n        }\n      } else for (; i <= j; ++i) {\n        for (k = base - 1; k >= 1; --k) {\n          t = i > 0 ? k / pows(-i) : k * pows(i);\n          if (t < u) continue;\n          if (t > v) break;\n          z.push(t);\n        }\n      }\n      if (z.length * 2 < n) z = (0,d3_array__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(u, v, n);\n    } else {\n      z = (0,d3_array__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(i, j, Math.min(j - i, n)).map(pows);\n    }\n    return r ? z.reverse() : z;\n  };\n\n  scale.tickFormat = (count, specifier) => {\n    if (count == null) count = 10;\n    if (specifier == null) specifier = base === 10 ? \"s\" : \",\";\n    if (typeof specifier !== \"function\") {\n      if (!(base % 1) && (specifier = (0,d3_format__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(specifier)).precision == null) specifier.trim = true;\n      specifier = (0,d3_format__WEBPACK_IMPORTED_MODULE_2__.format)(specifier);\n    }\n    if (count === Infinity) return specifier;\n    const k = Math.max(1, base * count / scale.ticks().length); // TODO fast estimate?\n    return d => {\n      let i = d / pows(Math.round(logs(d)));\n      if (i * base < base - 0.5) i *= base;\n      return i <= k ? specifier(d) : \"\";\n    };\n  };\n\n  scale.nice = () => {\n    return domain((0,_nice_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(domain(), {\n      floor: x => pows(Math.floor(logs(x))),\n      ceil: x => pows(Math.ceil(logs(x)))\n    }));\n  };\n\n  return scale;\n}\n\nfunction log() {\n  const scale = loggish((0,_continuous_js__WEBPACK_IMPORTED_MODULE_4__.transformer)()).domain([1, 10]);\n  scale.copy = () => (0,_continuous_js__WEBPACK_IMPORTED_MODULE_4__.copy)(scale, log()).base(scale.base());\n  _init_js__WEBPACK_IMPORTED_MODULE_5__.initRange.apply(scale, arguments);\n  return scale;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-scale/src/log.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-scale/src/nice.js":
/*!*******************************************!*\
  !*** ./node_modules/d3-scale/src/nice.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ nice)\n/* harmony export */ });\nfunction nice(domain, interval) {\n  domain = domain.slice();\n\n  var i0 = 0,\n      i1 = domain.length - 1,\n      x0 = domain[i0],\n      x1 = domain[i1],\n      t;\n\n  if (x1 < x0) {\n    t = i0, i0 = i1, i1 = t;\n    t = x0, x0 = x1, x1 = t;\n  }\n\n  domain[i0] = interval.floor(x0);\n  domain[i1] = interval.ceil(x1);\n  return domain;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtc2NhbGUvc3JjL25pY2UuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFlO0FBQ2Y7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRzpcXHByb2plY3RzXFxzY3JlZW5waXBlXFxzY3JlZW5waXBlXFxwaXBlc1xcZm9jdXMtYW5hbHl0aWNzLWNvYWNoXFxub2RlX21vZHVsZXNcXGQzLXNjYWxlXFxzcmNcXG5pY2UuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gbmljZShkb21haW4sIGludGVydmFsKSB7XG4gIGRvbWFpbiA9IGRvbWFpbi5zbGljZSgpO1xuXG4gIHZhciBpMCA9IDAsXG4gICAgICBpMSA9IGRvbWFpbi5sZW5ndGggLSAxLFxuICAgICAgeDAgPSBkb21haW5baTBdLFxuICAgICAgeDEgPSBkb21haW5baTFdLFxuICAgICAgdDtcblxuICBpZiAoeDEgPCB4MCkge1xuICAgIHQgPSBpMCwgaTAgPSBpMSwgaTEgPSB0O1xuICAgIHQgPSB4MCwgeDAgPSB4MSwgeDEgPSB0O1xuICB9XG5cbiAgZG9tYWluW2kwXSA9IGludGVydmFsLmZsb29yKHgwKTtcbiAgZG9tYWluW2kxXSA9IGludGVydmFsLmNlaWwoeDEpO1xuICByZXR1cm4gZG9tYWluO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-scale/src/nice.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-scale/src/number.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-scale/src/number.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ number)\n/* harmony export */ });\nfunction number(x) {\n  return +x;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtc2NhbGUvc3JjL251bWJlci5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQWU7QUFDZjtBQUNBIiwic291cmNlcyI6WyJHOlxccHJvamVjdHNcXHNjcmVlbnBpcGVcXHNjcmVlbnBpcGVcXHBpcGVzXFxmb2N1cy1hbmFseXRpY3MtY29hY2hcXG5vZGVfbW9kdWxlc1xcZDMtc2NhbGVcXHNyY1xcbnVtYmVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIG51bWJlcih4KSB7XG4gIHJldHVybiAreDtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-scale/src/number.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-scale/src/ordinal.js":
/*!**********************************************!*\
  !*** ./node_modules/d3-scale/src/ordinal.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ordinal),\n/* harmony export */   implicit: () => (/* binding */ implicit)\n/* harmony export */ });\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/internmap/src/index.js\");\n/* harmony import */ var _init_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./init.js */ \"(ssr)/./node_modules/d3-scale/src/init.js\");\n\n\n\nconst implicit = Symbol(\"implicit\");\n\nfunction ordinal() {\n  var index = new d3_array__WEBPACK_IMPORTED_MODULE_0__.InternMap(),\n      domain = [],\n      range = [],\n      unknown = implicit;\n\n  function scale(d) {\n    let i = index.get(d);\n    if (i === undefined) {\n      if (unknown !== implicit) return unknown;\n      index.set(d, i = domain.push(d) - 1);\n    }\n    return range[i % range.length];\n  }\n\n  scale.domain = function(_) {\n    if (!arguments.length) return domain.slice();\n    domain = [], index = new d3_array__WEBPACK_IMPORTED_MODULE_0__.InternMap();\n    for (const value of _) {\n      if (index.has(value)) continue;\n      index.set(value, domain.push(value) - 1);\n    }\n    return scale;\n  };\n\n  scale.range = function(_) {\n    return arguments.length ? (range = Array.from(_), scale) : range.slice();\n  };\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  scale.copy = function() {\n    return ordinal(domain, range).unknown(unknown);\n  };\n\n  _init_js__WEBPACK_IMPORTED_MODULE_1__.initRange.apply(scale, arguments);\n\n  return scale;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-scale/src/ordinal.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-scale/src/pow.js":
/*!******************************************!*\
  !*** ./node_modules/d3-scale/src/pow.js ***!
  \******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ pow),\n/* harmony export */   powish: () => (/* binding */ powish),\n/* harmony export */   sqrt: () => (/* binding */ sqrt)\n/* harmony export */ });\n/* harmony import */ var _linear_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./linear.js */ \"(ssr)/./node_modules/d3-scale/src/linear.js\");\n/* harmony import */ var _continuous_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./continuous.js */ \"(ssr)/./node_modules/d3-scale/src/continuous.js\");\n/* harmony import */ var _init_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./init.js */ \"(ssr)/./node_modules/d3-scale/src/init.js\");\n\n\n\n\nfunction transformPow(exponent) {\n  return function(x) {\n    return x < 0 ? -Math.pow(-x, exponent) : Math.pow(x, exponent);\n  };\n}\n\nfunction transformSqrt(x) {\n  return x < 0 ? -Math.sqrt(-x) : Math.sqrt(x);\n}\n\nfunction transformSquare(x) {\n  return x < 0 ? -x * x : x * x;\n}\n\nfunction powish(transform) {\n  var scale = transform(_continuous_js__WEBPACK_IMPORTED_MODULE_0__.identity, _continuous_js__WEBPACK_IMPORTED_MODULE_0__.identity),\n      exponent = 1;\n\n  function rescale() {\n    return exponent === 1 ? transform(_continuous_js__WEBPACK_IMPORTED_MODULE_0__.identity, _continuous_js__WEBPACK_IMPORTED_MODULE_0__.identity)\n        : exponent === 0.5 ? transform(transformSqrt, transformSquare)\n        : transform(transformPow(exponent), transformPow(1 / exponent));\n  }\n\n  scale.exponent = function(_) {\n    return arguments.length ? (exponent = +_, rescale()) : exponent;\n  };\n\n  return (0,_linear_js__WEBPACK_IMPORTED_MODULE_1__.linearish)(scale);\n}\n\nfunction pow() {\n  var scale = powish((0,_continuous_js__WEBPACK_IMPORTED_MODULE_0__.transformer)());\n\n  scale.copy = function() {\n    return (0,_continuous_js__WEBPACK_IMPORTED_MODULE_0__.copy)(scale, pow()).exponent(scale.exponent());\n  };\n\n  _init_js__WEBPACK_IMPORTED_MODULE_2__.initRange.apply(scale, arguments);\n\n  return scale;\n}\n\nfunction sqrt() {\n  return pow.apply(null, arguments).exponent(0.5);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-scale/src/pow.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-scale/src/quantile.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-scale/src/quantile.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ quantile)\n/* harmony export */ });\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/quantile.js\");\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/bisect.js\");\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/ascending.js\");\n/* harmony import */ var _init_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./init.js */ \"(ssr)/./node_modules/d3-scale/src/init.js\");\n\n\n\nfunction quantile() {\n  var domain = [],\n      range = [],\n      thresholds = [],\n      unknown;\n\n  function rescale() {\n    var i = 0, n = Math.max(1, range.length);\n    thresholds = new Array(n - 1);\n    while (++i < n) thresholds[i - 1] = (0,d3_array__WEBPACK_IMPORTED_MODULE_0__.quantileSorted)(domain, i / n);\n    return scale;\n  }\n\n  function scale(x) {\n    return x == null || isNaN(x = +x) ? unknown : range[(0,d3_array__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(thresholds, x)];\n  }\n\n  scale.invertExtent = function(y) {\n    var i = range.indexOf(y);\n    return i < 0 ? [NaN, NaN] : [\n      i > 0 ? thresholds[i - 1] : domain[0],\n      i < thresholds.length ? thresholds[i] : domain[domain.length - 1]\n    ];\n  };\n\n  scale.domain = function(_) {\n    if (!arguments.length) return domain.slice();\n    domain = [];\n    for (let d of _) if (d != null && !isNaN(d = +d)) domain.push(d);\n    domain.sort(d3_array__WEBPACK_IMPORTED_MODULE_2__[\"default\"]);\n    return rescale();\n  };\n\n  scale.range = function(_) {\n    return arguments.length ? (range = Array.from(_), rescale()) : range.slice();\n  };\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  scale.quantiles = function() {\n    return thresholds.slice();\n  };\n\n  scale.copy = function() {\n    return quantile()\n        .domain(domain)\n        .range(range)\n        .unknown(unknown);\n  };\n\n  return _init_js__WEBPACK_IMPORTED_MODULE_3__.initRange.apply(scale, arguments);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-scale/src/quantile.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-scale/src/quantize.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-scale/src/quantize.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ quantize)\n/* harmony export */ });\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/bisect.js\");\n/* harmony import */ var _linear_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./linear.js */ \"(ssr)/./node_modules/d3-scale/src/linear.js\");\n/* harmony import */ var _init_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./init.js */ \"(ssr)/./node_modules/d3-scale/src/init.js\");\n\n\n\n\nfunction quantize() {\n  var x0 = 0,\n      x1 = 1,\n      n = 1,\n      domain = [0.5],\n      range = [0, 1],\n      unknown;\n\n  function scale(x) {\n    return x != null && x <= x ? range[(0,d3_array__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(domain, x, 0, n)] : unknown;\n  }\n\n  function rescale() {\n    var i = -1;\n    domain = new Array(n);\n    while (++i < n) domain[i] = ((i + 1) * x1 - (i - n) * x0) / (n + 1);\n    return scale;\n  }\n\n  scale.domain = function(_) {\n    return arguments.length ? ([x0, x1] = _, x0 = +x0, x1 = +x1, rescale()) : [x0, x1];\n  };\n\n  scale.range = function(_) {\n    return arguments.length ? (n = (range = Array.from(_)).length - 1, rescale()) : range.slice();\n  };\n\n  scale.invertExtent = function(y) {\n    var i = range.indexOf(y);\n    return i < 0 ? [NaN, NaN]\n        : i < 1 ? [x0, domain[0]]\n        : i >= n ? [domain[n - 1], x1]\n        : [domain[i - 1], domain[i]];\n  };\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : scale;\n  };\n\n  scale.thresholds = function() {\n    return domain.slice();\n  };\n\n  scale.copy = function() {\n    return quantize()\n        .domain([x0, x1])\n        .range(range)\n        .unknown(unknown);\n  };\n\n  return _init_js__WEBPACK_IMPORTED_MODULE_1__.initRange.apply((0,_linear_js__WEBPACK_IMPORTED_MODULE_2__.linearish)(scale), arguments);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-scale/src/quantize.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-scale/src/radial.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-scale/src/radial.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ radial)\n/* harmony export */ });\n/* harmony import */ var _continuous_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./continuous.js */ \"(ssr)/./node_modules/d3-scale/src/continuous.js\");\n/* harmony import */ var _init_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./init.js */ \"(ssr)/./node_modules/d3-scale/src/init.js\");\n/* harmony import */ var _linear_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./linear.js */ \"(ssr)/./node_modules/d3-scale/src/linear.js\");\n/* harmony import */ var _number_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./number.js */ \"(ssr)/./node_modules/d3-scale/src/number.js\");\n\n\n\n\n\nfunction square(x) {\n  return Math.sign(x) * x * x;\n}\n\nfunction unsquare(x) {\n  return Math.sign(x) * Math.sqrt(Math.abs(x));\n}\n\nfunction radial() {\n  var squared = (0,_continuous_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(),\n      range = [0, 1],\n      round = false,\n      unknown;\n\n  function scale(x) {\n    var y = unsquare(squared(x));\n    return isNaN(y) ? unknown : round ? Math.round(y) : y;\n  }\n\n  scale.invert = function(y) {\n    return squared.invert(square(y));\n  };\n\n  scale.domain = function(_) {\n    return arguments.length ? (squared.domain(_), scale) : squared.domain();\n  };\n\n  scale.range = function(_) {\n    return arguments.length ? (squared.range((range = Array.from(_, _number_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])).map(square)), scale) : range.slice();\n  };\n\n  scale.rangeRound = function(_) {\n    return scale.range(_).round(true);\n  };\n\n  scale.round = function(_) {\n    return arguments.length ? (round = !!_, scale) : round;\n  };\n\n  scale.clamp = function(_) {\n    return arguments.length ? (squared.clamp(_), scale) : squared.clamp();\n  };\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  scale.copy = function() {\n    return radial(squared.domain(), range)\n        .round(round)\n        .clamp(squared.clamp())\n        .unknown(unknown);\n  };\n\n  _init_js__WEBPACK_IMPORTED_MODULE_2__.initRange.apply(scale, arguments);\n\n  return (0,_linear_js__WEBPACK_IMPORTED_MODULE_3__.linearish)(scale);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-scale/src/radial.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-scale/src/sequential.js":
/*!*************************************************!*\
  !*** ./node_modules/d3-scale/src/sequential.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   copy: () => (/* binding */ copy),\n/* harmony export */   \"default\": () => (/* binding */ sequential),\n/* harmony export */   sequentialLog: () => (/* binding */ sequentialLog),\n/* harmony export */   sequentialPow: () => (/* binding */ sequentialPow),\n/* harmony export */   sequentialSqrt: () => (/* binding */ sequentialSqrt),\n/* harmony export */   sequentialSymlog: () => (/* binding */ sequentialSymlog)\n/* harmony export */ });\n/* harmony import */ var d3_interpolate__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! d3-interpolate */ \"(ssr)/./node_modules/d3-interpolate/src/value.js\");\n/* harmony import */ var d3_interpolate__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! d3-interpolate */ \"(ssr)/./node_modules/d3-interpolate/src/round.js\");\n/* harmony import */ var _continuous_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./continuous.js */ \"(ssr)/./node_modules/d3-scale/src/continuous.js\");\n/* harmony import */ var _init_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./init.js */ \"(ssr)/./node_modules/d3-scale/src/init.js\");\n/* harmony import */ var _linear_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./linear.js */ \"(ssr)/./node_modules/d3-scale/src/linear.js\");\n/* harmony import */ var _log_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./log.js */ \"(ssr)/./node_modules/d3-scale/src/log.js\");\n/* harmony import */ var _symlog_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./symlog.js */ \"(ssr)/./node_modules/d3-scale/src/symlog.js\");\n/* harmony import */ var _pow_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./pow.js */ \"(ssr)/./node_modules/d3-scale/src/pow.js\");\n\n\n\n\n\n\n\n\nfunction transformer() {\n  var x0 = 0,\n      x1 = 1,\n      t0,\n      t1,\n      k10,\n      transform,\n      interpolator = _continuous_js__WEBPACK_IMPORTED_MODULE_0__.identity,\n      clamp = false,\n      unknown;\n\n  function scale(x) {\n    return x == null || isNaN(x = +x) ? unknown : interpolator(k10 === 0 ? 0.5 : (x = (transform(x) - t0) * k10, clamp ? Math.max(0, Math.min(1, x)) : x));\n  }\n\n  scale.domain = function(_) {\n    return arguments.length ? ([x0, x1] = _, t0 = transform(x0 = +x0), t1 = transform(x1 = +x1), k10 = t0 === t1 ? 0 : 1 / (t1 - t0), scale) : [x0, x1];\n  };\n\n  scale.clamp = function(_) {\n    return arguments.length ? (clamp = !!_, scale) : clamp;\n  };\n\n  scale.interpolator = function(_) {\n    return arguments.length ? (interpolator = _, scale) : interpolator;\n  };\n\n  function range(interpolate) {\n    return function(_) {\n      var r0, r1;\n      return arguments.length ? ([r0, r1] = _, interpolator = interpolate(r0, r1), scale) : [interpolator(0), interpolator(1)];\n    };\n  }\n\n  scale.range = range(d3_interpolate__WEBPACK_IMPORTED_MODULE_1__[\"default\"]);\n\n  scale.rangeRound = range(d3_interpolate__WEBPACK_IMPORTED_MODULE_2__[\"default\"]);\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  return function(t) {\n    transform = t, t0 = t(x0), t1 = t(x1), k10 = t0 === t1 ? 0 : 1 / (t1 - t0);\n    return scale;\n  };\n}\n\nfunction copy(source, target) {\n  return target\n      .domain(source.domain())\n      .interpolator(source.interpolator())\n      .clamp(source.clamp())\n      .unknown(source.unknown());\n}\n\nfunction sequential() {\n  var scale = (0,_linear_js__WEBPACK_IMPORTED_MODULE_3__.linearish)(transformer()(_continuous_js__WEBPACK_IMPORTED_MODULE_0__.identity));\n\n  scale.copy = function() {\n    return copy(scale, sequential());\n  };\n\n  return _init_js__WEBPACK_IMPORTED_MODULE_4__.initInterpolator.apply(scale, arguments);\n}\n\nfunction sequentialLog() {\n  var scale = (0,_log_js__WEBPACK_IMPORTED_MODULE_5__.loggish)(transformer()).domain([1, 10]);\n\n  scale.copy = function() {\n    return copy(scale, sequentialLog()).base(scale.base());\n  };\n\n  return _init_js__WEBPACK_IMPORTED_MODULE_4__.initInterpolator.apply(scale, arguments);\n}\n\nfunction sequentialSymlog() {\n  var scale = (0,_symlog_js__WEBPACK_IMPORTED_MODULE_6__.symlogish)(transformer());\n\n  scale.copy = function() {\n    return copy(scale, sequentialSymlog()).constant(scale.constant());\n  };\n\n  return _init_js__WEBPACK_IMPORTED_MODULE_4__.initInterpolator.apply(scale, arguments);\n}\n\nfunction sequentialPow() {\n  var scale = (0,_pow_js__WEBPACK_IMPORTED_MODULE_7__.powish)(transformer());\n\n  scale.copy = function() {\n    return copy(scale, sequentialPow()).exponent(scale.exponent());\n  };\n\n  return _init_js__WEBPACK_IMPORTED_MODULE_4__.initInterpolator.apply(scale, arguments);\n}\n\nfunction sequentialSqrt() {\n  return sequentialPow.apply(null, arguments).exponent(0.5);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtc2NhbGUvc3JjL3NlcXVlbnRpYWwuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBNkQ7QUFDcEI7QUFDRTtBQUNMO0FBQ0w7QUFDSztBQUNOOztBQUVoQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQixvREFBUTtBQUM3QjtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsc0JBQXNCLHNEQUFXOztBQUVqQywyQkFBMkIsc0RBQWdCOztBQUUzQztBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFZTtBQUNmLGNBQWMscURBQVMsZUFBZSxvREFBUTs7QUFFOUM7QUFDQTtBQUNBOztBQUVBLFNBQVMsc0RBQWdCO0FBQ3pCOztBQUVPO0FBQ1AsY0FBYyxnREFBTzs7QUFFckI7QUFDQTtBQUNBOztBQUVBLFNBQVMsc0RBQWdCO0FBQ3pCOztBQUVPO0FBQ1AsY0FBYyxxREFBUzs7QUFFdkI7QUFDQTtBQUNBOztBQUVBLFNBQVMsc0RBQWdCO0FBQ3pCOztBQUVPO0FBQ1AsY0FBYywrQ0FBTTs7QUFFcEI7QUFDQTtBQUNBOztBQUVBLFNBQVMsc0RBQWdCO0FBQ3pCOztBQUVPO0FBQ1A7QUFDQSIsInNvdXJjZXMiOlsiRzpcXHByb2plY3RzXFxzY3JlZW5waXBlXFxzY3JlZW5waXBlXFxwaXBlc1xcZm9jdXMtYW5hbHl0aWNzLWNvYWNoXFxub2RlX21vZHVsZXNcXGQzLXNjYWxlXFxzcmNcXHNlcXVlbnRpYWwuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtpbnRlcnBvbGF0ZSwgaW50ZXJwb2xhdGVSb3VuZH0gZnJvbSBcImQzLWludGVycG9sYXRlXCI7XG5pbXBvcnQge2lkZW50aXR5fSBmcm9tIFwiLi9jb250aW51b3VzLmpzXCI7XG5pbXBvcnQge2luaXRJbnRlcnBvbGF0b3J9IGZyb20gXCIuL2luaXQuanNcIjtcbmltcG9ydCB7bGluZWFyaXNofSBmcm9tIFwiLi9saW5lYXIuanNcIjtcbmltcG9ydCB7bG9nZ2lzaH0gZnJvbSBcIi4vbG9nLmpzXCI7XG5pbXBvcnQge3N5bWxvZ2lzaH0gZnJvbSBcIi4vc3ltbG9nLmpzXCI7XG5pbXBvcnQge3Bvd2lzaH0gZnJvbSBcIi4vcG93LmpzXCI7XG5cbmZ1bmN0aW9uIHRyYW5zZm9ybWVyKCkge1xuICB2YXIgeDAgPSAwLFxuICAgICAgeDEgPSAxLFxuICAgICAgdDAsXG4gICAgICB0MSxcbiAgICAgIGsxMCxcbiAgICAgIHRyYW5zZm9ybSxcbiAgICAgIGludGVycG9sYXRvciA9IGlkZW50aXR5LFxuICAgICAgY2xhbXAgPSBmYWxzZSxcbiAgICAgIHVua25vd247XG5cbiAgZnVuY3Rpb24gc2NhbGUoeCkge1xuICAgIHJldHVybiB4ID09IG51bGwgfHwgaXNOYU4oeCA9ICt4KSA/IHVua25vd24gOiBpbnRlcnBvbGF0b3IoazEwID09PSAwID8gMC41IDogKHggPSAodHJhbnNmb3JtKHgpIC0gdDApICogazEwLCBjbGFtcCA/IE1hdGgubWF4KDAsIE1hdGgubWluKDEsIHgpKSA6IHgpKTtcbiAgfVxuXG4gIHNjYWxlLmRvbWFpbiA9IGZ1bmN0aW9uKF8pIHtcbiAgICByZXR1cm4gYXJndW1lbnRzLmxlbmd0aCA/IChbeDAsIHgxXSA9IF8sIHQwID0gdHJhbnNmb3JtKHgwID0gK3gwKSwgdDEgPSB0cmFuc2Zvcm0oeDEgPSAreDEpLCBrMTAgPSB0MCA9PT0gdDEgPyAwIDogMSAvICh0MSAtIHQwKSwgc2NhbGUpIDogW3gwLCB4MV07XG4gIH07XG5cbiAgc2NhbGUuY2xhbXAgPSBmdW5jdGlvbihfKSB7XG4gICAgcmV0dXJuIGFyZ3VtZW50cy5sZW5ndGggPyAoY2xhbXAgPSAhIV8sIHNjYWxlKSA6IGNsYW1wO1xuICB9O1xuXG4gIHNjYWxlLmludGVycG9sYXRvciA9IGZ1bmN0aW9uKF8pIHtcbiAgICByZXR1cm4gYXJndW1lbnRzLmxlbmd0aCA/IChpbnRlcnBvbGF0b3IgPSBfLCBzY2FsZSkgOiBpbnRlcnBvbGF0b3I7XG4gIH07XG5cbiAgZnVuY3Rpb24gcmFuZ2UoaW50ZXJwb2xhdGUpIHtcbiAgICByZXR1cm4gZnVuY3Rpb24oXykge1xuICAgICAgdmFyIHIwLCByMTtcbiAgICAgIHJldHVybiBhcmd1bWVudHMubGVuZ3RoID8gKFtyMCwgcjFdID0gXywgaW50ZXJwb2xhdG9yID0gaW50ZXJwb2xhdGUocjAsIHIxKSwgc2NhbGUpIDogW2ludGVycG9sYXRvcigwKSwgaW50ZXJwb2xhdG9yKDEpXTtcbiAgICB9O1xuICB9XG5cbiAgc2NhbGUucmFuZ2UgPSByYW5nZShpbnRlcnBvbGF0ZSk7XG5cbiAgc2NhbGUucmFuZ2VSb3VuZCA9IHJhbmdlKGludGVycG9sYXRlUm91bmQpO1xuXG4gIHNjYWxlLnVua25vd24gPSBmdW5jdGlvbihfKSB7XG4gICAgcmV0dXJuIGFyZ3VtZW50cy5sZW5ndGggPyAodW5rbm93biA9IF8sIHNjYWxlKSA6IHVua25vd247XG4gIH07XG5cbiAgcmV0dXJuIGZ1bmN0aW9uKHQpIHtcbiAgICB0cmFuc2Zvcm0gPSB0LCB0MCA9IHQoeDApLCB0MSA9IHQoeDEpLCBrMTAgPSB0MCA9PT0gdDEgPyAwIDogMSAvICh0MSAtIHQwKTtcbiAgICByZXR1cm4gc2NhbGU7XG4gIH07XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBjb3B5KHNvdXJjZSwgdGFyZ2V0KSB7XG4gIHJldHVybiB0YXJnZXRcbiAgICAgIC5kb21haW4oc291cmNlLmRvbWFpbigpKVxuICAgICAgLmludGVycG9sYXRvcihzb3VyY2UuaW50ZXJwb2xhdG9yKCkpXG4gICAgICAuY2xhbXAoc291cmNlLmNsYW1wKCkpXG4gICAgICAudW5rbm93bihzb3VyY2UudW5rbm93bigpKTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gc2VxdWVudGlhbCgpIHtcbiAgdmFyIHNjYWxlID0gbGluZWFyaXNoKHRyYW5zZm9ybWVyKCkoaWRlbnRpdHkpKTtcblxuICBzY2FsZS5jb3B5ID0gZnVuY3Rpb24oKSB7XG4gICAgcmV0dXJuIGNvcHkoc2NhbGUsIHNlcXVlbnRpYWwoKSk7XG4gIH07XG5cbiAgcmV0dXJuIGluaXRJbnRlcnBvbGF0b3IuYXBwbHkoc2NhbGUsIGFyZ3VtZW50cyk7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBzZXF1ZW50aWFsTG9nKCkge1xuICB2YXIgc2NhbGUgPSBsb2dnaXNoKHRyYW5zZm9ybWVyKCkpLmRvbWFpbihbMSwgMTBdKTtcblxuICBzY2FsZS5jb3B5ID0gZnVuY3Rpb24oKSB7XG4gICAgcmV0dXJuIGNvcHkoc2NhbGUsIHNlcXVlbnRpYWxMb2coKSkuYmFzZShzY2FsZS5iYXNlKCkpO1xuICB9O1xuXG4gIHJldHVybiBpbml0SW50ZXJwb2xhdG9yLmFwcGx5KHNjYWxlLCBhcmd1bWVudHMpO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gc2VxdWVudGlhbFN5bWxvZygpIHtcbiAgdmFyIHNjYWxlID0gc3ltbG9naXNoKHRyYW5zZm9ybWVyKCkpO1xuXG4gIHNjYWxlLmNvcHkgPSBmdW5jdGlvbigpIHtcbiAgICByZXR1cm4gY29weShzY2FsZSwgc2VxdWVudGlhbFN5bWxvZygpKS5jb25zdGFudChzY2FsZS5jb25zdGFudCgpKTtcbiAgfTtcblxuICByZXR1cm4gaW5pdEludGVycG9sYXRvci5hcHBseShzY2FsZSwgYXJndW1lbnRzKTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIHNlcXVlbnRpYWxQb3coKSB7XG4gIHZhciBzY2FsZSA9IHBvd2lzaCh0cmFuc2Zvcm1lcigpKTtcblxuICBzY2FsZS5jb3B5ID0gZnVuY3Rpb24oKSB7XG4gICAgcmV0dXJuIGNvcHkoc2NhbGUsIHNlcXVlbnRpYWxQb3coKSkuZXhwb25lbnQoc2NhbGUuZXhwb25lbnQoKSk7XG4gIH07XG5cbiAgcmV0dXJuIGluaXRJbnRlcnBvbGF0b3IuYXBwbHkoc2NhbGUsIGFyZ3VtZW50cyk7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBzZXF1ZW50aWFsU3FydCgpIHtcbiAgcmV0dXJuIHNlcXVlbnRpYWxQb3cuYXBwbHkobnVsbCwgYXJndW1lbnRzKS5leHBvbmVudCgwLjUpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-scale/src/sequential.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-scale/src/sequentialQuantile.js":
/*!*********************************************************!*\
  !*** ./node_modules/d3-scale/src/sequentialQuantile.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ sequentialQuantile)\n/* harmony export */ });\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/bisect.js\");\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/ascending.js\");\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/quantile.js\");\n/* harmony import */ var _continuous_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./continuous.js */ \"(ssr)/./node_modules/d3-scale/src/continuous.js\");\n/* harmony import */ var _init_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./init.js */ \"(ssr)/./node_modules/d3-scale/src/init.js\");\n\n\n\n\nfunction sequentialQuantile() {\n  var domain = [],\n      interpolator = _continuous_js__WEBPACK_IMPORTED_MODULE_0__.identity;\n\n  function scale(x) {\n    if (x != null && !isNaN(x = +x)) return interpolator(((0,d3_array__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(domain, x, 1) - 1) / (domain.length - 1));\n  }\n\n  scale.domain = function(_) {\n    if (!arguments.length) return domain.slice();\n    domain = [];\n    for (let d of _) if (d != null && !isNaN(d = +d)) domain.push(d);\n    domain.sort(d3_array__WEBPACK_IMPORTED_MODULE_2__[\"default\"]);\n    return scale;\n  };\n\n  scale.interpolator = function(_) {\n    return arguments.length ? (interpolator = _, scale) : interpolator;\n  };\n\n  scale.range = function() {\n    return domain.map((d, i) => interpolator(i / (domain.length - 1)));\n  };\n\n  scale.quantiles = function(n) {\n    return Array.from({length: n + 1}, (_, i) => (0,d3_array__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(domain, i / n));\n  };\n\n  scale.copy = function() {\n    return sequentialQuantile(interpolator).domain(domain);\n  };\n\n  return _init_js__WEBPACK_IMPORTED_MODULE_4__.initInterpolator.apply(scale, arguments);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-scale/src/sequentialQuantile.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-scale/src/symlog.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-scale/src/symlog.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ symlog),\n/* harmony export */   symlogish: () => (/* binding */ symlogish)\n/* harmony export */ });\n/* harmony import */ var _linear_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./linear.js */ \"(ssr)/./node_modules/d3-scale/src/linear.js\");\n/* harmony import */ var _continuous_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./continuous.js */ \"(ssr)/./node_modules/d3-scale/src/continuous.js\");\n/* harmony import */ var _init_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./init.js */ \"(ssr)/./node_modules/d3-scale/src/init.js\");\n\n\n\n\nfunction transformSymlog(c) {\n  return function(x) {\n    return Math.sign(x) * Math.log1p(Math.abs(x / c));\n  };\n}\n\nfunction transformSymexp(c) {\n  return function(x) {\n    return Math.sign(x) * Math.expm1(Math.abs(x)) * c;\n  };\n}\n\nfunction symlogish(transform) {\n  var c = 1, scale = transform(transformSymlog(c), transformSymexp(c));\n\n  scale.constant = function(_) {\n    return arguments.length ? transform(transformSymlog(c = +_), transformSymexp(c)) : c;\n  };\n\n  return (0,_linear_js__WEBPACK_IMPORTED_MODULE_0__.linearish)(scale);\n}\n\nfunction symlog() {\n  var scale = symlogish((0,_continuous_js__WEBPACK_IMPORTED_MODULE_1__.transformer)());\n\n  scale.copy = function() {\n    return (0,_continuous_js__WEBPACK_IMPORTED_MODULE_1__.copy)(scale, symlog()).constant(scale.constant());\n  };\n\n  return _init_js__WEBPACK_IMPORTED_MODULE_2__.initRange.apply(scale, arguments);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtc2NhbGUvc3JjL3N5bWxvZy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFzQztBQUNZO0FBQ2Q7O0FBRXBDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFTztBQUNQOztBQUVBO0FBQ0E7QUFDQTs7QUFFQSxTQUFTLHFEQUFTO0FBQ2xCOztBQUVlO0FBQ2Ysd0JBQXdCLDJEQUFXOztBQUVuQztBQUNBLFdBQVcsb0RBQUk7QUFDZjs7QUFFQSxTQUFTLCtDQUFTO0FBQ2xCIiwic291cmNlcyI6WyJHOlxccHJvamVjdHNcXHNjcmVlbnBpcGVcXHNjcmVlbnBpcGVcXHBpcGVzXFxmb2N1cy1hbmFseXRpY3MtY29hY2hcXG5vZGVfbW9kdWxlc1xcZDMtc2NhbGVcXHNyY1xcc3ltbG9nLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7bGluZWFyaXNofSBmcm9tIFwiLi9saW5lYXIuanNcIjtcbmltcG9ydCB7Y29weSwgdHJhbnNmb3JtZXJ9IGZyb20gXCIuL2NvbnRpbnVvdXMuanNcIjtcbmltcG9ydCB7aW5pdFJhbmdlfSBmcm9tIFwiLi9pbml0LmpzXCI7XG5cbmZ1bmN0aW9uIHRyYW5zZm9ybVN5bWxvZyhjKSB7XG4gIHJldHVybiBmdW5jdGlvbih4KSB7XG4gICAgcmV0dXJuIE1hdGguc2lnbih4KSAqIE1hdGgubG9nMXAoTWF0aC5hYnMoeCAvIGMpKTtcbiAgfTtcbn1cblxuZnVuY3Rpb24gdHJhbnNmb3JtU3ltZXhwKGMpIHtcbiAgcmV0dXJuIGZ1bmN0aW9uKHgpIHtcbiAgICByZXR1cm4gTWF0aC5zaWduKHgpICogTWF0aC5leHBtMShNYXRoLmFicyh4KSkgKiBjO1xuICB9O1xufVxuXG5leHBvcnQgZnVuY3Rpb24gc3ltbG9naXNoKHRyYW5zZm9ybSkge1xuICB2YXIgYyA9IDEsIHNjYWxlID0gdHJhbnNmb3JtKHRyYW5zZm9ybVN5bWxvZyhjKSwgdHJhbnNmb3JtU3ltZXhwKGMpKTtcblxuICBzY2FsZS5jb25zdGFudCA9IGZ1bmN0aW9uKF8pIHtcbiAgICByZXR1cm4gYXJndW1lbnRzLmxlbmd0aCA/IHRyYW5zZm9ybSh0cmFuc2Zvcm1TeW1sb2coYyA9ICtfKSwgdHJhbnNmb3JtU3ltZXhwKGMpKSA6IGM7XG4gIH07XG5cbiAgcmV0dXJuIGxpbmVhcmlzaChzY2FsZSk7XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHN5bWxvZygpIHtcbiAgdmFyIHNjYWxlID0gc3ltbG9naXNoKHRyYW5zZm9ybWVyKCkpO1xuXG4gIHNjYWxlLmNvcHkgPSBmdW5jdGlvbigpIHtcbiAgICByZXR1cm4gY29weShzY2FsZSwgc3ltbG9nKCkpLmNvbnN0YW50KHNjYWxlLmNvbnN0YW50KCkpO1xuICB9O1xuXG4gIHJldHVybiBpbml0UmFuZ2UuYXBwbHkoc2NhbGUsIGFyZ3VtZW50cyk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-scale/src/symlog.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-scale/src/threshold.js":
/*!************************************************!*\
  !*** ./node_modules/d3-scale/src/threshold.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ threshold)\n/* harmony export */ });\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/bisect.js\");\n/* harmony import */ var _init_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./init.js */ \"(ssr)/./node_modules/d3-scale/src/init.js\");\n\n\n\nfunction threshold() {\n  var domain = [0.5],\n      range = [0, 1],\n      unknown,\n      n = 1;\n\n  function scale(x) {\n    return x != null && x <= x ? range[(0,d3_array__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(domain, x, 0, n)] : unknown;\n  }\n\n  scale.domain = function(_) {\n    return arguments.length ? (domain = Array.from(_), n = Math.min(domain.length, range.length - 1), scale) : domain.slice();\n  };\n\n  scale.range = function(_) {\n    return arguments.length ? (range = Array.from(_), n = Math.min(domain.length, range.length - 1), scale) : range.slice();\n  };\n\n  scale.invertExtent = function(y) {\n    var i = range.indexOf(y);\n    return [domain[i - 1], domain[i]];\n  };\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  scale.copy = function() {\n    return threshold()\n        .domain(domain)\n        .range(range)\n        .unknown(unknown);\n  };\n\n  return _init_js__WEBPACK_IMPORTED_MODULE_1__.initRange.apply(scale, arguments);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-scale/src/threshold.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-scale/src/tickFormat.js":
/*!*************************************************!*\
  !*** ./node_modules/d3-scale/src/tickFormat.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ tickFormat)\n/* harmony export */ });\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/ticks.js\");\n/* harmony import */ var d3_format__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! d3-format */ \"(ssr)/./node_modules/d3-format/src/formatSpecifier.js\");\n/* harmony import */ var d3_format__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! d3-format */ \"(ssr)/./node_modules/d3-format/src/precisionPrefix.js\");\n/* harmony import */ var d3_format__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! d3-format */ \"(ssr)/./node_modules/d3-format/src/defaultLocale.js\");\n/* harmony import */ var d3_format__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! d3-format */ \"(ssr)/./node_modules/d3-format/src/precisionRound.js\");\n/* harmony import */ var d3_format__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! d3-format */ \"(ssr)/./node_modules/d3-format/src/precisionFixed.js\");\n\n\n\nfunction tickFormat(start, stop, count, specifier) {\n  var step = (0,d3_array__WEBPACK_IMPORTED_MODULE_0__.tickStep)(start, stop, count),\n      precision;\n  specifier = (0,d3_format__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(specifier == null ? \",f\" : specifier);\n  switch (specifier.type) {\n    case \"s\": {\n      var value = Math.max(Math.abs(start), Math.abs(stop));\n      if (specifier.precision == null && !isNaN(precision = (0,d3_format__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(step, value))) specifier.precision = precision;\n      return (0,d3_format__WEBPACK_IMPORTED_MODULE_3__.formatPrefix)(specifier, value);\n    }\n    case \"\":\n    case \"e\":\n    case \"g\":\n    case \"p\":\n    case \"r\": {\n      if (specifier.precision == null && !isNaN(precision = (0,d3_format__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(step, Math.max(Math.abs(start), Math.abs(stop))))) specifier.precision = precision - (specifier.type === \"e\");\n      break;\n    }\n    case \"f\":\n    case \"%\": {\n      if (specifier.precision == null && !isNaN(precision = (0,d3_format__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(step))) specifier.precision = precision - (specifier.type === \"%\") * 2;\n      break;\n    }\n  }\n  return (0,d3_format__WEBPACK_IMPORTED_MODULE_3__.format)(specifier);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-scale/src/tickFormat.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-scale/src/time.js":
/*!*******************************************!*\
  !*** ./node_modules/d3-scale/src/time.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calendar: () => (/* binding */ calendar),\n/* harmony export */   \"default\": () => (/* binding */ time)\n/* harmony export */ });\n/* harmony import */ var d3_time__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! d3-time */ \"(ssr)/./node_modules/d3-time/src/ticks.js\");\n/* harmony import */ var d3_time__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! d3-time */ \"(ssr)/./node_modules/d3-time/src/year.js\");\n/* harmony import */ var d3_time__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! d3-time */ \"(ssr)/./node_modules/d3-time/src/month.js\");\n/* harmony import */ var d3_time__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! d3-time */ \"(ssr)/./node_modules/d3-time/src/week.js\");\n/* harmony import */ var d3_time__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! d3-time */ \"(ssr)/./node_modules/d3-time/src/day.js\");\n/* harmony import */ var d3_time__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! d3-time */ \"(ssr)/./node_modules/d3-time/src/hour.js\");\n/* harmony import */ var d3_time__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! d3-time */ \"(ssr)/./node_modules/d3-time/src/minute.js\");\n/* harmony import */ var d3_time__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! d3-time */ \"(ssr)/./node_modules/d3-time/src/second.js\");\n/* harmony import */ var d3_time_format__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! d3-time-format */ \"(ssr)/./node_modules/d3-time-format/src/defaultLocale.js\");\n/* harmony import */ var _continuous_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./continuous.js */ \"(ssr)/./node_modules/d3-scale/src/continuous.js\");\n/* harmony import */ var _init_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./init.js */ \"(ssr)/./node_modules/d3-scale/src/init.js\");\n/* harmony import */ var _nice_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./nice.js */ \"(ssr)/./node_modules/d3-scale/src/nice.js\");\n\n\n\n\n\n\nfunction date(t) {\n  return new Date(t);\n}\n\nfunction number(t) {\n  return t instanceof Date ? +t : +new Date(+t);\n}\n\nfunction calendar(ticks, tickInterval, year, month, week, day, hour, minute, second, format) {\n  var scale = (0,_continuous_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(),\n      invert = scale.invert,\n      domain = scale.domain;\n\n  var formatMillisecond = format(\".%L\"),\n      formatSecond = format(\":%S\"),\n      formatMinute = format(\"%I:%M\"),\n      formatHour = format(\"%I %p\"),\n      formatDay = format(\"%a %d\"),\n      formatWeek = format(\"%b %d\"),\n      formatMonth = format(\"%B\"),\n      formatYear = format(\"%Y\");\n\n  function tickFormat(date) {\n    return (second(date) < date ? formatMillisecond\n        : minute(date) < date ? formatSecond\n        : hour(date) < date ? formatMinute\n        : day(date) < date ? formatHour\n        : month(date) < date ? (week(date) < date ? formatDay : formatWeek)\n        : year(date) < date ? formatMonth\n        : formatYear)(date);\n  }\n\n  scale.invert = function(y) {\n    return new Date(invert(y));\n  };\n\n  scale.domain = function(_) {\n    return arguments.length ? domain(Array.from(_, number)) : domain().map(date);\n  };\n\n  scale.ticks = function(interval) {\n    var d = domain();\n    return ticks(d[0], d[d.length - 1], interval == null ? 10 : interval);\n  };\n\n  scale.tickFormat = function(count, specifier) {\n    return specifier == null ? tickFormat : format(specifier);\n  };\n\n  scale.nice = function(interval) {\n    var d = domain();\n    if (!interval || typeof interval.range !== \"function\") interval = tickInterval(d[0], d[d.length - 1], interval == null ? 10 : interval);\n    return interval ? domain((0,_nice_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(d, interval)) : scale;\n  };\n\n  scale.copy = function() {\n    return (0,_continuous_js__WEBPACK_IMPORTED_MODULE_0__.copy)(scale, calendar(ticks, tickInterval, year, month, week, day, hour, minute, second, format));\n  };\n\n  return scale;\n}\n\nfunction time() {\n  return _init_js__WEBPACK_IMPORTED_MODULE_2__.initRange.apply(calendar(d3_time__WEBPACK_IMPORTED_MODULE_3__.timeTicks, d3_time__WEBPACK_IMPORTED_MODULE_3__.timeTickInterval, d3_time__WEBPACK_IMPORTED_MODULE_4__.timeYear, d3_time__WEBPACK_IMPORTED_MODULE_5__.timeMonth, d3_time__WEBPACK_IMPORTED_MODULE_6__.timeSunday, d3_time__WEBPACK_IMPORTED_MODULE_7__.timeDay, d3_time__WEBPACK_IMPORTED_MODULE_8__.timeHour, d3_time__WEBPACK_IMPORTED_MODULE_9__.timeMinute, d3_time__WEBPACK_IMPORTED_MODULE_10__.second, d3_time_format__WEBPACK_IMPORTED_MODULE_11__.timeFormat).domain([new Date(2000, 0, 1), new Date(2000, 0, 2)]), arguments);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-scale/src/time.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-scale/src/utcTime.js":
/*!**********************************************!*\
  !*** ./node_modules/d3-scale/src/utcTime.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ utcTime)\n/* harmony export */ });\n/* harmony import */ var d3_time__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! d3-time */ \"(ssr)/./node_modules/d3-time/src/ticks.js\");\n/* harmony import */ var d3_time__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! d3-time */ \"(ssr)/./node_modules/d3-time/src/year.js\");\n/* harmony import */ var d3_time__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! d3-time */ \"(ssr)/./node_modules/d3-time/src/month.js\");\n/* harmony import */ var d3_time__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! d3-time */ \"(ssr)/./node_modules/d3-time/src/week.js\");\n/* harmony import */ var d3_time__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! d3-time */ \"(ssr)/./node_modules/d3-time/src/day.js\");\n/* harmony import */ var d3_time__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! d3-time */ \"(ssr)/./node_modules/d3-time/src/hour.js\");\n/* harmony import */ var d3_time__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! d3-time */ \"(ssr)/./node_modules/d3-time/src/minute.js\");\n/* harmony import */ var d3_time__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! d3-time */ \"(ssr)/./node_modules/d3-time/src/second.js\");\n/* harmony import */ var d3_time_format__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! d3-time-format */ \"(ssr)/./node_modules/d3-time-format/src/defaultLocale.js\");\n/* harmony import */ var _time_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./time.js */ \"(ssr)/./node_modules/d3-scale/src/time.js\");\n/* harmony import */ var _init_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./init.js */ \"(ssr)/./node_modules/d3-scale/src/init.js\");\n\n\n\n\n\nfunction utcTime() {\n  return _init_js__WEBPACK_IMPORTED_MODULE_0__.initRange.apply((0,_time_js__WEBPACK_IMPORTED_MODULE_1__.calendar)(d3_time__WEBPACK_IMPORTED_MODULE_2__.utcTicks, d3_time__WEBPACK_IMPORTED_MODULE_2__.utcTickInterval, d3_time__WEBPACK_IMPORTED_MODULE_3__.utcYear, d3_time__WEBPACK_IMPORTED_MODULE_4__.utcMonth, d3_time__WEBPACK_IMPORTED_MODULE_5__.utcSunday, d3_time__WEBPACK_IMPORTED_MODULE_6__.utcDay, d3_time__WEBPACK_IMPORTED_MODULE_7__.utcHour, d3_time__WEBPACK_IMPORTED_MODULE_8__.utcMinute, d3_time__WEBPACK_IMPORTED_MODULE_9__.second, d3_time_format__WEBPACK_IMPORTED_MODULE_10__.utcFormat).domain([Date.UTC(2000, 0, 1), Date.UTC(2000, 0, 2)]), arguments);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtc2NhbGUvc3JjL3V0Y1RpbWUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBQXFIO0FBQzVFO0FBQ047QUFDQzs7QUFFckI7QUFDZixTQUFTLCtDQUFTLE9BQU8sa0RBQVEsQ0FBQyw2Q0FBUSxFQUFFLG9EQUFlLEVBQUUsNENBQU8sRUFBRSw2Q0FBUSxFQUFFLDhDQUFPLEVBQUUsMkNBQU0sRUFBRSw0Q0FBTyxFQUFFLDhDQUFTLEVBQUUsMkNBQVMsRUFBRSxzREFBUztBQUN6SSIsInNvdXJjZXMiOlsiRzpcXHByb2plY3RzXFxzY3JlZW5waXBlXFxzY3JlZW5waXBlXFxwaXBlc1xcZm9jdXMtYW5hbHl0aWNzLWNvYWNoXFxub2RlX21vZHVsZXNcXGQzLXNjYWxlXFxzcmNcXHV0Y1RpbWUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHt1dGNZZWFyLCB1dGNNb250aCwgdXRjV2VlaywgdXRjRGF5LCB1dGNIb3VyLCB1dGNNaW51dGUsIHV0Y1NlY29uZCwgdXRjVGlja3MsIHV0Y1RpY2tJbnRlcnZhbH0gZnJvbSBcImQzLXRpbWVcIjtcbmltcG9ydCB7dXRjRm9ybWF0fSBmcm9tIFwiZDMtdGltZS1mb3JtYXRcIjtcbmltcG9ydCB7Y2FsZW5kYXJ9IGZyb20gXCIuL3RpbWUuanNcIjtcbmltcG9ydCB7aW5pdFJhbmdlfSBmcm9tIFwiLi9pbml0LmpzXCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHV0Y1RpbWUoKSB7XG4gIHJldHVybiBpbml0UmFuZ2UuYXBwbHkoY2FsZW5kYXIodXRjVGlja3MsIHV0Y1RpY2tJbnRlcnZhbCwgdXRjWWVhciwgdXRjTW9udGgsIHV0Y1dlZWssIHV0Y0RheSwgdXRjSG91ciwgdXRjTWludXRlLCB1dGNTZWNvbmQsIHV0Y0Zvcm1hdCkuZG9tYWluKFtEYXRlLlVUQygyMDAwLCAwLCAxKSwgRGF0ZS5VVEMoMjAwMCwgMCwgMildKSwgYXJndW1lbnRzKTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-scale/src/utcTime.js\n");

/***/ })

};
;