import { 
  FocusSession, 
  ProductivityMetrics, 
  TaskCategoryType, 
  ProductivityLevel,
  AppUsage 
} from "./types";
import { subHours, subMinutes, addMinutes } from "date-fns";
import { v4 as uuidv4 } from "uuid";

/**
 * Generate demo data for testing the Focus Analytics dashboard
 */
export function generateDemoData(): {
  focusSessions: FocusSession[];
  productivityMetrics: ProductivityMetrics;
} {
  const now = new Date();
  const sessions: FocusSession[] = [];

  // Generate demo focus sessions for the last 8 hours
  const sessionData = [
    { app: "Visual Studio Code", category: "deep-work" as TaskCategoryType, duration: 45, focusScore: 85, distractions: 2 },
    { app: "Chrome", category: "research" as TaskCategoryType, duration: 25, focusScore: 65, distractions: 5 },
    { app: "Slack", category: "communication" as TaskCategoryType, duration: 15, focusScore: 40, distractions: 8 },
    { app: "Figma", category: "creative" as TaskCategoryType, duration: 60, focusScore: 90, distractions: 1 },
    { app: "Notion", category: "administrative" as TaskCategoryType, duration: 30, focusScore: 70, distractions: 3 },
    { app: "YouTube", category: "entertainment" as TaskCategoryType, duration: 20, focusScore: 20, distractions: 12 },
    { app: "IntelliJ IDEA", category: "deep-work" as TaskCategoryType, duration: 75, focusScore: 88, distractions: 1 },
    { app: "Discord", category: "social" as TaskCategoryType, duration: 10, focusScore: 30, distractions: 6 }
  ];

  let currentTime = subHours(now, 8);

  sessionData.forEach((data, index) => {
    const startTime = addMinutes(currentTime, index * 20);
    const endTime = addMinutes(startTime, data.duration);
    
    sessions.push({
      id: uuidv4(),
      startTime,
      endTime,
      duration: data.duration,
      appName: data.app,
      windowName: `${data.app} - Main Window`,
      taskCategory: data.category,
      focusScore: data.focusScore,
      distractionCount: data.distractions,
      contextSwitches: Math.floor(data.distractions / 2),
      productivity: getProductivityLevel(data.focusScore)
    });
  });

  // Calculate metrics from demo sessions
  const totalFocusTime = sessions.reduce((sum, session) => sum + session.duration, 0);
  const averageFocusScore = sessions.reduce((sum, session) => sum + session.focusScore, 0) / sessions.length;
  const deepWorkSessions = sessions.filter(s => s.duration >= 45 && s.focusScore >= 70).length;
  const totalContextSwitches = sessions.reduce((sum, session) => sum + session.contextSwitches, 0);
  const averageSessionLength = totalFocusTime / sessions.length;

  // Generate app usage data
  const appUsageMap = new Map<string, AppUsage>();
  
  sessions.forEach(session => {
    const existing = appUsageMap.get(session.appName);
    if (existing) {
      existing.totalTime += session.duration;
      existing.sessionCount += 1;
      existing.focusTime += session.focusScore >= 70 ? session.duration : 0;
      existing.distractionTime += session.focusScore < 50 ? session.duration : 0;
    } else {
      appUsageMap.set(session.appName, {
        appName: session.appName,
        totalTime: session.duration,
        focusTime: session.focusScore >= 70 ? session.duration : 0,
        distractionTime: session.focusScore < 50 ? session.duration : 0,
        sessionCount: 1,
        averageSessionLength: session.duration,
        productivityScore: session.focusScore
      });
    }
  });

  // Update average session lengths and productivity scores
  appUsageMap.forEach(app => {
    app.averageSessionLength = app.totalTime / app.sessionCount;
  });

  const appUsage = Array.from(appUsageMap.values());
  const topProductiveApps = appUsage
    .filter(app => app.productivityScore >= 70)
    .sort((a, b) => b.focusTime - a.focusTime)
    .slice(0, 5);
  
  const topDistractingApps = appUsage
    .filter(app => app.productivityScore < 50)
    .sort((a, b) => b.distractionTime - a.distractionTime)
    .slice(0, 5);

  // Find most and least productive hours
  const hourlyProductivity = new Map<number, { total: number; count: number }>();
  sessions.forEach(session => {
    const hour = session.startTime.getHours();
    const existing = hourlyProductivity.get(hour);
    if (existing) {
      existing.total += session.focusScore;
      existing.count += 1;
    } else {
      hourlyProductivity.set(hour, { total: session.focusScore, count: 1 });
    }
  });

  let mostProductiveHour = 9;
  let leastProductiveHour = 15;
  let maxProductivity = 0;
  let minProductivity = 100;

  hourlyProductivity.forEach((data, hour) => {
    const avgProductivity = data.total / data.count;
    if (avgProductivity > maxProductivity) {
      maxProductivity = avgProductivity;
      mostProductiveHour = hour;
    }
    if (avgProductivity < minProductivity) {
      minProductivity = avgProductivity;
      leastProductiveHour = hour;
    }
  });

  const metrics: ProductivityMetrics = {
    date: now,
    totalFocusTime,
    totalDistractionTime: sessions
      .filter(s => s.focusScore < 50)
      .reduce((sum, s) => sum + s.duration, 0),
    focusScore: averageFocusScore,
    contextSwitches: totalContextSwitches,
    deepWorkSessions,
    averageSessionLength,
    mostProductiveHour,
    leastProductiveHour,
    topProductiveApps,
    topDistractingApps
  };

  return { focusSessions: sessions, productivityMetrics: metrics };
}

function getProductivityLevel(focusScore: number): ProductivityLevel {
  if (focusScore >= 90) return 'very-high';
  if (focusScore >= 75) return 'high';
  if (focusScore >= 50) return 'medium';
  if (focusScore >= 25) return 'low';
  return 'very-low';
}

/**
 * Generate demo coaching notifications
 */
export function generateDemoNotifications() {
  return [
    {
      id: uuidv4(),
      type: 'focus-reminder' as const,
      title: 'Great Focus Session!',
      message: 'You\'ve been focused for 45 minutes. Keep up the excellent work!',
      timestamp: subMinutes(new Date(), 5),
      priority: 'low' as const,
      actionable: false
    },
    {
      id: uuidv4(),
      type: 'break-suggestion' as const,
      title: 'Time for a Break',
      message: 'You\'ve been working for 2 hours. Consider taking a 10-minute break.',
      timestamp: subMinutes(new Date(), 15),
      priority: 'medium' as const,
      actionable: true,
      action: {
        label: 'Take Break',
        callback: () => console.log('Taking break...')
      }
    },
    {
      id: uuidv4(),
      type: 'distraction-alert' as const,
      title: 'Distraction Detected',
      message: 'You switched to social media. Try to stay focused on your current task.',
      timestamp: subMinutes(new Date(), 30),
      priority: 'high' as const,
      actionable: true,
      action: {
        label: 'Return to Work',
        callback: () => console.log('Returning to work...')
      }
    }
  ];
}

/**
 * Generate demo productivity goals
 */
export function generateDemoGoals() {
  return [
    {
      id: uuidv4(),
      name: 'Daily Focus Time',
      description: 'Achieve 6 hours of focused work per day',
      type: 'daily-focus-time' as const,
      target: 360, // 6 hours in minutes
      current: 265, // Current progress
      unit: 'minutes',
      deadline: new Date(new Date().setHours(23, 59, 59)),
      isActive: true,
      createdAt: subHours(new Date(), 24)
    },
    {
      id: uuidv4(),
      name: 'Deep Work Sessions',
      description: 'Complete 3 deep work sessions (45+ minutes) daily',
      type: 'increase-deep-work' as const,
      target: 3,
      current: 2,
      unit: 'sessions',
      deadline: new Date(new Date().setHours(23, 59, 59)),
      isActive: true,
      createdAt: subHours(new Date(), 24)
    },
    {
      id: uuidv4(),
      name: 'Reduce Distractions',
      description: 'Keep context switches under 15 per day',
      type: 'reduce-distractions' as const,
      target: 15,
      current: 12,
      unit: 'switches',
      deadline: new Date(new Date().setHours(23, 59, 59)),
      isActive: true,
      createdAt: subHours(new Date(), 24)
    }
  ];
}
