Based on the provided commits, here is the changelog for the new Screenpipe update:

### **New Features:**
- **Added thinking component for reasoning models:** Introduced a thinking component in both the rewind pipe and search pipe, enhancing model reasoning capabilities.
- **Improved onboarding experience:** Enhanced the onboarding process by streaming screenshots in the UI for better user guidance.

### **Improvements:**
- **Optimized plugin update check:** Improved the efficiency of the plugin update check process.

### **Fixes:**
- **Fixed duplicate bouncing icon:** Resolved the issue with the duplicate bouncing icon to improve visual feedback.

#### **Full Changelog:** [a13bf..8fa47](https://github.com/mediar-ai/screenpipe/compare/a13bf..8fa47)

