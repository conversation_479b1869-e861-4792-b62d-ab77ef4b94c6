import { pipe } from "@screenpipe/browser";
import { 
  CoachingNotification, 
  NotificationType, 
  FocusSession, 
  ProductivityGoal, 
  BreakRecommendation,
  AnalyticsConfig
} from "./types";
import { differenceInMinutes, format } from "date-fns";
import { v4 as uuidv4 } from "uuid";

export class RealTimeCoachingSystem {
  private config: AnalyticsConfig;
  private notifications: CoachingNotification[] = [];
  private goals: ProductivityGoal[] = [];
  private isMonitoring = false;
  private currentSession: FocusSession | null = null;
  private lastNotification: Date | null = null;
  private notificationCallbacks: ((notification: CoachingNotification) => void)[] = [];

  constructor(config: AnalyticsConfig) {
    this.config = config;
  }

  /**
   * Start real-time monitoring and coaching
   */
  async startMonitoring(): Promise<void> {
    if (this.isMonitoring) return;
    
    this.isMonitoring = true;
    console.log("Starting real-time productivity coaching...");

    // Start monitoring activity streams
    if (this.config.enableRealTimeCoaching) {
      this.startActivityMonitoring();
    }

    if (this.config.enableBreakReminders) {
      this.startBreakReminders();
    }

    if (this.config.enableGoalTracking) {
      this.startGoalTracking();
    }
  }

  /**
   * Stop monitoring
   */
  stopMonitoring(): void {
    this.isMonitoring = false;
    console.log("Stopped productivity coaching");
  }

  /**
   * Subscribe to coaching notifications
   */
  onNotification(callback: (notification: CoachingNotification) => void): void {
    this.notificationCallbacks.push(callback);
  }

  /**
   * Send a coaching notification
   */
  private sendNotification(notification: CoachingNotification): void {
    this.notifications.push(notification);
    this.lastNotification = new Date();
    
    // Notify all subscribers
    this.notificationCallbacks.forEach(callback => {
      try {
        callback(notification);
      } catch (error) {
        console.error("Error in notification callback:", error);
      }
    });

    // Send desktop notification if supported
    if (typeof window !== 'undefined' && 'Notification' in window) {
      this.sendDesktopNotification(notification);
    }
  }

  /**
   * Send desktop notification
   */
  private async sendDesktopNotification(notification: CoachingNotification): Promise<void> {
    try {
      if (Notification.permission === 'granted') {
        new Notification(notification.title, {
          body: notification.message,
          icon: '/128x128.png',
          tag: notification.type
        });
      } else if (Notification.permission !== 'denied') {
        const permission = await Notification.requestPermission();
        if (permission === 'granted') {
          new Notification(notification.title, {
            body: notification.message,
            icon: '/128x128.png',
            tag: notification.type
          });
        }
      }
    } catch (error) {
      console.error("Error sending desktop notification:", error);
    }
  }

  /**
   * Start monitoring activity for real-time coaching
   */
  private async startActivityMonitoring(): Promise<void> {
    try {
      // Use Screenpipe's streaming capabilities for real-time monitoring
      const visionStream = pipe.streamVision(false);
      
      for await (const visionData of visionStream) {
        if (!this.isMonitoring) break;
        
        await this.processVisionData(visionData);
      }
    } catch (error) {
      console.error("Error in activity monitoring:", error);
      // Fallback to polling if streaming fails
      this.startPollingMonitoring();
    }
  }

  /**
   * Fallback polling-based monitoring
   */
  private startPollingMonitoring(): void {
    const pollInterval = setInterval(async () => {
      if (!this.isMonitoring) {
        clearInterval(pollInterval);
        return;
      }

      try {
        await this.checkCurrentActivity();
      } catch (error) {
        console.error("Error in polling monitoring:", error);
      }
    }, 30000); // Check every 30 seconds
  }

  /**
   * Process real-time vision data
   */
  private async processVisionData(visionData: any): Promise<void> {
    try {
      const appName = visionData.appName || "";
      const windowName = visionData.windowName || "";
      const timestamp = new Date();

      // Detect focus mode changes
      await this.detectFocusModeChanges(appName, windowName, timestamp);
      
      // Check for distractions
      await this.checkForDistractions(appName, windowName, timestamp);
      
      // Update current session
      this.updateCurrentSession(appName, windowName, timestamp);
      
    } catch (error) {
      console.error("Error processing vision data:", error);
    }
  }

  /**
   * Check current activity (fallback method)
   */
  private async checkCurrentActivity(): Promise<void> {
    try {
      const now = new Date();
      const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000);

      const results = await pipe.queryScreenpipe({
        startTime: fiveMinutesAgo.toISOString(),
        endTime: now.toISOString(),
        contentType: "ocr",
        limit: 10
      });

      if (results?.data && results.data.length > 0) {
        const latestActivity = results.data[results.data.length - 1];
        if (latestActivity.type === "OCR") {
          const appName = latestActivity.content.appName || "";
          const windowName = latestActivity.content.windowName || "";
          const timestamp = new Date(latestActivity.content.timestamp);

          await this.detectFocusModeChanges(appName, windowName, timestamp);
          await this.checkForDistractions(appName, windowName, timestamp);
          this.updateCurrentSession(appName, windowName, timestamp);
        }
      }
    } catch (error) {
      console.error("Error checking current activity:", error);
    }
  }

  /**
   * Detect focus mode changes and provide coaching
   */
  private async detectFocusModeChanges(
    appName: string, 
    windowName: string, 
    timestamp: Date
  ): Promise<void> {
    const isProductiveApp = this.isProductiveApp(appName);
    const isDistractingApp = this.isDistractingApp(appName);

    // Entering focus mode
    if (isProductiveApp && (!this.currentSession || !this.isProductiveApp(this.currentSession.appName))) {
      this.sendNotification({
        id: uuidv4(),
        type: 'focus-reminder',
        title: 'Focus Mode Detected',
        message: `Great! You're starting focused work in ${appName}. Stay concentrated!`,
        timestamp,
        priority: 'low',
        actionable: false
      });
    }

    // Leaving focus mode
    if (this.currentSession && this.isProductiveApp(this.currentSession.appName) && isDistractingApp) {
      const sessionDuration = differenceInMinutes(timestamp, this.currentSession.startTime);
      
      if (sessionDuration >= this.config.focusThreshold) {
        this.sendNotification({
          id: uuidv4(),
          type: 'session-complete',
          title: 'Focus Session Complete',
          message: `Great job! You focused for ${sessionDuration} minutes. Consider taking a break.`,
          timestamp,
          priority: 'medium',
          actionable: true,
          action: {
            label: 'Take Break',
            callback: () => this.suggestBreak()
          }
        });
      } else {
        this.sendNotification({
          id: uuidv4(),
          type: 'distraction-alert',
          title: 'Distraction Detected',
          message: `You switched to ${appName} after only ${sessionDuration} minutes. Try to maintain focus!`,
          timestamp,
          priority: 'medium',
          actionable: true,
          action: {
            label: 'Return to Work',
            callback: () => this.suggestReturnToWork()
          }
        });
      }
    }
  }

  /**
   * Check for distracting patterns
   */
  private async checkForDistractions(
    appName: string, 
    windowName: string, 
    timestamp: Date
  ): Promise<void> {
    if (!this.isDistractingApp(appName)) return;

    // Check if user has been on distracting apps for too long
    const distractionThreshold = 15; // minutes
    const recentActivity = await this.getRecentActivity(distractionThreshold);
    
    const distractingTime = recentActivity
      .filter(activity => this.isDistractingApp(activity.appName))
      .reduce((total, activity) => total + activity.duration, 0);

    if (distractingTime >= distractionThreshold) {
      this.sendNotification({
        id: uuidv4(),
        type: 'distraction-alert',
        title: 'Extended Distraction Detected',
        message: `You've been on distracting apps for ${Math.round(distractingTime)} minutes. Time to refocus!`,
        timestamp,
        priority: 'high',
        actionable: true,
        action: {
          label: 'Start Focus Session',
          callback: () => this.suggestFocusSession()
        }
      });
    }
  }

  /**
   * Update current session tracking
   */
  private updateCurrentSession(appName: string, windowName: string, timestamp: Date): void {
    if (!this.currentSession || this.currentSession.appName !== appName) {
      // Start new session
      this.currentSession = {
        id: uuidv4(),
        startTime: timestamp,
        endTime: timestamp,
        duration: 0,
        appName,
        windowName,
        taskCategory: this.categorizeApp(appName),
        focusScore: 0,
        distractionCount: 0,
        contextSwitches: 0,
        productivity: 'medium'
      };
    } else {
      // Update existing session
      this.currentSession.endTime = timestamp;
      this.currentSession.duration = differenceInMinutes(timestamp, this.currentSession.startTime);
    }
  }

  /**
   * Start break reminder system
   */
  private startBreakReminders(): void {
    const reminderInterval = setInterval(() => {
      if (!this.isMonitoring) {
        clearInterval(reminderInterval);
        return;
      }

      this.checkBreakNeeds();
    }, this.config.breakReminderInterval * 60 * 1000); // Convert minutes to milliseconds
  }

  /**
   * Check if user needs a break
   */
  private checkBreakNeeds(): void {
    if (!this.currentSession) return;

    const sessionDuration = differenceInMinutes(new Date(), this.currentSession.startTime);
    
    // Suggest breaks based on session length
    if (sessionDuration >= 120 && sessionDuration % 30 === 0) { // Every 30 min after 2 hours
      this.sendNotification({
        id: uuidv4(),
        type: 'break-suggestion',
        title: 'Long Session Break',
        message: `You've been working for ${sessionDuration} minutes. Take a 15-minute break!`,
        timestamp: new Date(),
        priority: 'medium',
        actionable: true,
        action: {
          label: 'Take Break',
          callback: () => this.suggestBreak()
        }
      });
    } else if (sessionDuration >= 60 && sessionDuration % 20 === 0) { // Every 20 min after 1 hour
      this.sendNotification({
        id: uuidv4(),
        type: 'break-suggestion',
        title: 'Focus Break',
        message: `Great focus! Take a 5-minute break to recharge.`,
        timestamp: new Date(),
        priority: 'low',
        actionable: true,
        action: {
          label: 'Take Break',
          callback: () => this.suggestBreak()
        }
      });
    }
  }

  /**
   * Start goal tracking system
   */
  private startGoalTracking(): void {
    const trackingInterval = setInterval(() => {
      if (!this.isMonitoring) {
        clearInterval(trackingInterval);
        return;
      }

      this.updateGoalProgress();
    }, 10 * 60 * 1000); // Check every 10 minutes
  }

  /**
   * Update progress on active goals
   */
  private async updateGoalProgress(): Promise<void> {
    for (const goal of this.goals.filter(g => g.isActive)) {
      const progress = await this.calculateGoalProgress(goal);
      
      if (progress !== goal.current) {
        goal.current = progress;
        
        const progressPercentage = (progress / goal.target) * 100;
        
        if (progressPercentage >= 100) {
          this.sendNotification({
            id: uuidv4(),
            type: 'goal-progress',
            title: 'Goal Achieved!',
            message: `Congratulations! You've achieved your goal: ${goal.name}`,
            timestamp: new Date(),
            priority: 'high',
            actionable: false
          });
        } else if (progressPercentage >= 75 && progressPercentage < 100) {
          this.sendNotification({
            id: uuidv4(),
            type: 'goal-progress',
            title: 'Almost There!',
            message: `You're ${Math.round(progressPercentage)}% towards your goal: ${goal.name}`,
            timestamp: new Date(),
            priority: 'medium',
            actionable: false
          });
        }
      }
    }
  }

  /**
   * Calculate current progress for a goal
   */
  private async calculateGoalProgress(goal: ProductivityGoal): Promise<number> {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    
    try {
      const results = await pipe.queryScreenpipe({
        startTime: today.toISOString(),
        endTime: now.toISOString(),
        contentType: "all",
        limit: 1000
      });

      if (!results?.data) return goal.current;

      // Calculate based on goal type
      switch (goal.type) {
        case 'daily-focus-time':
          return this.calculateFocusTime(results.data);
        case 'reduce-distractions':
          return this.calculateDistractionReduction(results.data);
        case 'increase-deep-work':
          return this.calculateDeepWorkTime(results.data);
        default:
          return goal.current;
      }
    } catch (error) {
      console.error("Error calculating goal progress:", error);
      return goal.current;
    }
  }

  // Helper methods
  private isProductiveApp(appName: string): boolean {
    const productiveApps = ['vscode', 'intellij', 'xcode', 'figma', 'notion', 'excel', 'word'];
    return productiveApps.some(app => appName.toLowerCase().includes(app));
  }

  private isDistractingApp(appName: string): boolean {
    const distractingApps = ['facebook', 'twitter', 'instagram', 'youtube', 'netflix', 'reddit'];
    return distractingApps.some(app => appName.toLowerCase().includes(app));
  }

  private categorizeApp(appName: string): any {
    // Simplified categorization
    if (this.isProductiveApp(appName)) return 'deep-work';
    if (this.isDistractingApp(appName)) return 'distraction';
    return 'unknown';
  }

  private async getRecentActivity(minutes: number): Promise<Array<{appName: string; duration: number}>> {
    // Simplified implementation
    return [];
  }

  private calculateFocusTime(data: any[]): number {
    // Simplified calculation
    return 0;
  }

  private calculateDistractionReduction(data: any[]): number {
    // Simplified calculation
    return 0;
  }

  private calculateDeepWorkTime(data: any[]): number {
    // Simplified calculation
    return 0;
  }

  private suggestBreak(): void {
    console.log("Suggesting break activities...");
  }

  private suggestReturnToWork(): void {
    console.log("Suggesting return to productive work...");
  }

  private suggestFocusSession(): void {
    console.log("Suggesting start of focus session...");
  }

  // Public methods for managing goals
  addGoal(goal: Omit<ProductivityGoal, 'id' | 'createdAt'>): void {
    this.goals.push({
      ...goal,
      id: uuidv4(),
      createdAt: new Date()
    });
  }

  getGoals(): ProductivityGoal[] {
    return [...this.goals];
  }

  updateGoal(goalId: string, updates: Partial<ProductivityGoal>): void {
    const goalIndex = this.goals.findIndex(g => g.id === goalId);
    if (goalIndex !== -1) {
      this.goals[goalIndex] = { ...this.goals[goalIndex], ...updates };
    }
  }

  deleteGoal(goalId: string): void {
    this.goals = this.goals.filter(g => g.id !== goalId);
  }

  getNotifications(): CoachingNotification[] {
    return [...this.notifications];
  }

  clearNotifications(): void {
    this.notifications = [];
  }
}
