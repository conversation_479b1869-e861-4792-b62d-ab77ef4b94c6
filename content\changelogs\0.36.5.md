Based on your commits, here is the changelog for the new Screenpipe update:

### **New Features:**
- **Show local timezone on logs:** Added functionality to display the user's local timezone in the logs for better context.
- **Add endpoint to show window on demand:** Implemented a new API endpoint that allows users to request the current application window on demand.
- **Sort pipes by downloads count and creation date:** Enhanced user experience by allowing users to sort their pipes based on download counts and creation dates.
- **Implement deduplication SDK:** Introduced a deduplication SDK along with an embeddings endpoint to improve data processing and storage.
- **Add deduplication with Obsidian:** Integrated deduplication capabilities specifically for interactions with Obsidian.

### **Improvements:**
- *(No improvements explicitly mentioned in the provided commits)*

### **Fixes:**
- **Fix potential paths for Linux:** Addressed potential file path issues to enhance compatibility and functionality on Linux systems.
- **Fix layout shifting in app:** Resolved UI layout shifting problems to provide a more stable user interface.
- **Disable send recording when backend is down:** Improved system reliability by ensuring recording sends are disabled when the backend is unavailable.
- **Fix spinner not working in CLI:** Fixed an issue where the loading spinner did not function correctly in the command-line interface.

#### **Full Changelog:** [cceaf..a4154](https://github.com/mediar-ai/screenpipe/compare/cceaf..a4154)

