Based on your provided commits, here is the updated changelog for Screenpipe:

### **New Features:**
- **Added o3-mini model to the list:** Introduced support for the new o3-mini model.

### **Improvements:**
- **Updated documentation:** Enhanced the documentation for better clarity and usage instructions.

### **Fixes:**
- **Fixed rewind date change controls:** Resolved issues with the rewind date change controls for better user experience.
- **Fixed scroll not working:** Addressed the scrolling issue that was affecting usability.
- **Fixed pipe commands in documentation:** Corrected the pipe command examples in the documentation for accuracy. 

This changelog includes contributions that provide clear value to the end-user.

#### **Full Changelog:** [b7495..d4e9f](https://github.com/mediar-ai/screenpipe/compare/b7495..d4e9f)

