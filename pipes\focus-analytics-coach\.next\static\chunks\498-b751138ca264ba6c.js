"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[498],{54416:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},98305:(e,t,r)=>{r.d(t,{rc:()=>em,bm:()=>ew,VY:()=>ev,Kq:()=>ed,bL:()=>ef,hE:()=>ep,LM:()=>ec});var n,o=r(12115),i=r(47650),l=r(85185),a=r(6101),s=r(46081),u=r(95155),d=o.forwardRef((e,t)=>{let{children:r,...n}=e,i=o.Children.toArray(r),l=i.find(p);if(l){let e=l.props.children,r=i.map(t=>t!==l?t:o.Children.count(e)>1?o.Children.only(null):o.isValidElement(e)?e.props.children:null);return(0,u.jsx)(c,{...n,ref:t,children:o.isValidElement(e)?o.cloneElement(e,void 0,r):null})}return(0,u.jsx)(c,{...n,ref:t,children:r})});d.displayName="Slot";var c=o.forwardRef((e,t)=>{let{children:r,...n}=e;if(o.isValidElement(r)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(r);return o.cloneElement(r,{...function(e,t){let r={...t};for(let n in t){let o=e[n],i=t[n];/^on[A-Z]/.test(n)?o&&i?r[n]=(...e)=>{i(...e),o(...e)}:o&&(r[n]=o):"style"===n?r[n]={...o,...i}:"className"===n&&(r[n]=[o,i].filter(Boolean).join(" "))}return{...e,...r}}(n,r.props),ref:t?(0,a.t)(t,e):e})}return o.Children.count(r)>1?o.Children.only(null):null});c.displayName="SlotClone";var f=({children:e})=>(0,u.jsx)(u.Fragment,{children:e});function p(e){return o.isValidElement(e)&&e.type===f}var v=o.forwardRef((e,t)=>{let{children:r,...n}=e,i=o.Children.toArray(r),l=i.find(y);if(l){let e=l.props.children,r=i.map(t=>t!==l?t:o.Children.count(e)>1?o.Children.only(null):o.isValidElement(e)?e.props.children:null);return(0,u.jsx)(m,{...n,ref:t,children:o.isValidElement(e)?o.cloneElement(e,void 0,r):null})}return(0,u.jsx)(m,{...n,ref:t,children:r})});v.displayName="Slot";var m=o.forwardRef((e,t)=>{let{children:r,...n}=e;if(o.isValidElement(r)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(r);return o.cloneElement(r,{...function(e,t){let r={...t};for(let n in t){let o=e[n],i=t[n];/^on[A-Z]/.test(n)?o&&i?r[n]=(...e)=>{i(...e),o(...e)}:o&&(r[n]=o):"style"===n?r[n]={...o,...i}:"className"===n&&(r[n]=[o,i].filter(Boolean).join(" "))}return{...e,...r}}(n,r.props),ref:t?(0,a.t)(t,e):e})}return o.Children.count(r)>1?o.Children.only(null):null});m.displayName="SlotClone";var w=({children:e})=>(0,u.jsx)(u.Fragment,{children:e});function y(e){return o.isValidElement(e)&&e.type===w}var E=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let r=o.forwardRef((e,r)=>{let{asChild:n,...o}=e,i=n?v:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,u.jsx)(i,{...o,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function h(e,t){e&&i.flushSync(()=>e.dispatchEvent(t))}var x=r(39033),g="dismissableLayer.update",b=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),C=o.forwardRef((e,t)=>{var r,i;let{disableOutsidePointerEvents:s=!1,onEscapeKeyDown:d,onPointerDownOutside:c,onFocusOutside:f,onInteractOutside:p,onDismiss:v,...m}=e,w=o.useContext(b),[y,h]=o.useState(null),C=null!==(i=null==y?void 0:y.ownerDocument)&&void 0!==i?i:null===(r=globalThis)||void 0===r?void 0:r.document,[,R]=o.useState({}),j=(0,a.s)(t,e=>h(e)),L=Array.from(w.layers),[D]=[...w.layersWithOutsidePointerEventsDisabled].slice(-1),N=L.indexOf(D),S=y?L.indexOf(y):-1,k=w.layersWithOutsidePointerEventsDisabled.size>0,F=S>=N,A=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,n=(0,x.c)(e),i=o.useRef(!1),l=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!i.current){let t=function(){P("dismissableLayer.pointerDownOutside",n,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(r.removeEventListener("click",l.current),l.current=t,r.addEventListener("click",l.current,{once:!0})):t()}else r.removeEventListener("click",l.current);i.current=!1},t=window.setTimeout(()=>{r.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),r.removeEventListener("pointerdown",e),r.removeEventListener("click",l.current)}},[r,n]),{onPointerDownCapture:()=>i.current=!0}}(e=>{let t=e.target,r=[...w.branches].some(e=>e.contains(t));!F||r||(null==c||c(e),null==p||p(e),e.defaultPrevented||null==v||v())},C),O=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,n=(0,x.c)(e),i=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!i.current&&P("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return r.addEventListener("focusin",e),()=>r.removeEventListener("focusin",e)},[r,n]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(e=>{let t=e.target;[...w.branches].some(e=>e.contains(t))||(null==f||f(e),null==p||p(e),e.defaultPrevented||null==v||v())},C);return!function(e,t=globalThis?.document){let r=(0,x.c)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&r(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[r,t])}(e=>{S===w.layers.size-1&&(null==d||d(e),!e.defaultPrevented&&v&&(e.preventDefault(),v()))},C),o.useEffect(()=>{if(y)return s&&(0===w.layersWithOutsidePointerEventsDisabled.size&&(n=C.body.style.pointerEvents,C.body.style.pointerEvents="none"),w.layersWithOutsidePointerEventsDisabled.add(y)),w.layers.add(y),T(),()=>{s&&1===w.layersWithOutsidePointerEventsDisabled.size&&(C.body.style.pointerEvents=n)}},[y,C,s,w]),o.useEffect(()=>()=>{y&&(w.layers.delete(y),w.layersWithOutsidePointerEventsDisabled.delete(y),T())},[y,w]),o.useEffect(()=>{let e=()=>R({});return document.addEventListener(g,e),()=>document.removeEventListener(g,e)},[]),(0,u.jsx)(E.div,{...m,ref:j,style:{pointerEvents:k?F?"auto":"none":void 0,...e.style},onFocusCapture:(0,l.m)(e.onFocusCapture,O.onFocusCapture),onBlurCapture:(0,l.m)(e.onBlurCapture,O.onBlurCapture),onPointerDownCapture:(0,l.m)(e.onPointerDownCapture,A.onPointerDownCapture)})});C.displayName="DismissableLayer";var R=o.forwardRef((e,t)=>{let r=o.useContext(b),n=o.useRef(null),i=(0,a.s)(t,n);return o.useEffect(()=>{let e=n.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,u.jsx)(E.div,{...e,ref:i})});function T(){let e=new CustomEvent(g);document.dispatchEvent(e)}function P(e,t,r,n){let{discrete:o}=n,i=r.originalEvent.target,l=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&i.addEventListener(e,t,{once:!0}),o?h(i,l):i.dispatchEvent(l)}R.displayName="DismissableLayerBranch";var j=r(52712),L=o.forwardRef((e,t)=>{var r,n;let{container:l,...a}=e,[s,d]=o.useState(!1);(0,j.N)(()=>d(!0),[]);let c=l||s&&(null===(n=globalThis)||void 0===n?void 0:null===(r=n.document)||void 0===r?void 0:r.body);return c?i.createPortal((0,u.jsx)(E.div,{...a,ref:t}),c):null});L.displayName="Portal";var D=r(28905),N=r(5845),S=o.forwardRef((e,t)=>(0,u.jsx)(E.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));S.displayName="VisuallyHidden";var k="ToastProvider",[F,A,O]=function(e){let t=e+"CollectionProvider",[r,n]=(0,s.A)(t),[i,l]=r(t,{collectionRef:{current:null},itemMap:new Map}),c=e=>{let{scope:t,children:r}=e,n=o.useRef(null),l=o.useRef(new Map).current;return(0,u.jsx)(i,{scope:t,itemMap:l,collectionRef:n,children:r})};c.displayName=t;let f=e+"CollectionSlot",p=o.forwardRef((e,t)=>{let{scope:r,children:n}=e,o=l(f,r),i=(0,a.s)(t,o.collectionRef);return(0,u.jsx)(d,{ref:i,children:n})});p.displayName=f;let v=e+"CollectionItemSlot",m="data-radix-collection-item",w=o.forwardRef((e,t)=>{let{scope:r,children:n,...i}=e,s=o.useRef(null),c=(0,a.s)(t,s),f=l(v,r);return o.useEffect(()=>(f.itemMap.set(s,{ref:s,...i}),()=>void f.itemMap.delete(s))),(0,u.jsx)(d,{[m]:"",ref:c,children:n})});return w.displayName=v,[{Provider:c,Slot:p,ItemSlot:w},function(t){let r=l(e+"CollectionConsumer",t);return o.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(m,"]")));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},n]}("Toast"),[M,I]=(0,s.A)("Toast",[O]),[W,V]=M(k),K=e=>{let{__scopeToast:t,label:r="Notification",duration:n=5e3,swipeDirection:i="right",swipeThreshold:l=50,children:a}=e,[s,d]=o.useState(null),[c,f]=o.useState(0),p=o.useRef(!1),v=o.useRef(!1);return r.trim()||console.error("Invalid prop `label` supplied to `".concat(k,"`. Expected non-empty `string`.")),(0,u.jsx)(F.Provider,{scope:t,children:(0,u.jsx)(W,{scope:t,label:r,duration:n,swipeDirection:i,swipeThreshold:l,toastCount:c,viewport:s,onViewportChange:d,onToastAdd:o.useCallback(()=>f(e=>e+1),[]),onToastRemove:o.useCallback(()=>f(e=>e-1),[]),isFocusedToastEscapeKeyDownRef:p,isClosePausedRef:v,children:a})})};K.displayName=k;var _="ToastViewport",B=["F8"],q="toast.viewportPause",z="toast.viewportResume",X=o.forwardRef((e,t)=>{let{__scopeToast:r,hotkey:n=B,label:i="Notifications ({hotkey})",...l}=e,s=V(_,r),d=A(r),c=o.useRef(null),f=o.useRef(null),p=o.useRef(null),v=o.useRef(null),m=(0,a.s)(t,v,s.onViewportChange),w=n.join("+").replace(/Key/g,"").replace(/Digit/g,""),y=s.toastCount>0;o.useEffect(()=>{let e=e=>{var t;0!==n.length&&n.every(t=>e[t]||e.code===t)&&(null===(t=v.current)||void 0===t||t.focus())};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[n]),o.useEffect(()=>{let e=c.current,t=v.current;if(y&&e&&t){let r=()=>{if(!s.isClosePausedRef.current){let e=new CustomEvent(q);t.dispatchEvent(e),s.isClosePausedRef.current=!0}},n=()=>{if(s.isClosePausedRef.current){let e=new CustomEvent(z);t.dispatchEvent(e),s.isClosePausedRef.current=!1}},o=t=>{e.contains(t.relatedTarget)||n()},i=()=>{e.contains(document.activeElement)||n()};return e.addEventListener("focusin",r),e.addEventListener("focusout",o),e.addEventListener("pointermove",r),e.addEventListener("pointerleave",i),window.addEventListener("blur",r),window.addEventListener("focus",n),()=>{e.removeEventListener("focusin",r),e.removeEventListener("focusout",o),e.removeEventListener("pointermove",r),e.removeEventListener("pointerleave",i),window.removeEventListener("blur",r),window.removeEventListener("focus",n)}}},[y,s.isClosePausedRef]);let h=o.useCallback(e=>{let{tabbingDirection:t}=e,r=d().map(e=>{let r=e.ref.current,n=[r,...function(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}(r)];return"forwards"===t?n:n.reverse()});return("forwards"===t?r.reverse():r).flat()},[d]);return o.useEffect(()=>{let e=v.current;if(e){let t=t=>{let r=t.altKey||t.ctrlKey||t.metaKey;if("Tab"===t.key&&!r){var n,o,i;let r=document.activeElement,l=t.shiftKey;if(t.target===e&&l){null===(n=f.current)||void 0===n||n.focus();return}let a=h({tabbingDirection:l?"backwards":"forwards"}),s=a.findIndex(e=>e===r);eu(a.slice(s+1))?t.preventDefault():l?null===(o=f.current)||void 0===o||o.focus():null===(i=p.current)||void 0===i||i.focus()}};return e.addEventListener("keydown",t),()=>e.removeEventListener("keydown",t)}},[d,h]),(0,u.jsxs)(R,{ref:c,role:"region","aria-label":i.replace("{hotkey}",w),tabIndex:-1,style:{pointerEvents:y?void 0:"none"},children:[y&&(0,u.jsx)(U,{ref:f,onFocusFromOutsideViewport:()=>{eu(h({tabbingDirection:"forwards"}))}}),(0,u.jsx)(F.Slot,{scope:r,children:(0,u.jsx)(E.ol,{tabIndex:-1,...l,ref:m})}),y&&(0,u.jsx)(U,{ref:p,onFocusFromOutsideViewport:()=>{eu(h({tabbingDirection:"backwards"}))}})]})});X.displayName=_;var H="ToastFocusProxy",U=o.forwardRef((e,t)=>{let{__scopeToast:r,onFocusFromOutsideViewport:n,...o}=e,i=V(H,r);return(0,u.jsx)(S,{"aria-hidden":!0,tabIndex:0,...o,ref:t,style:{position:"fixed"},onFocus:e=>{var t;let r=e.relatedTarget;(null===(t=i.viewport)||void 0===t?void 0:t.contains(r))||n()}})});U.displayName=H;var Y="Toast",Z=o.forwardRef((e,t)=>{let{forceMount:r,open:n,defaultOpen:o,onOpenChange:i,...a}=e,[s=!0,d]=(0,N.i)({prop:n,defaultProp:o,onChange:i});return(0,u.jsx)(D.C,{present:r||s,children:(0,u.jsx)(J,{open:s,...a,ref:t,onClose:()=>d(!1),onPause:(0,x.c)(e.onPause),onResume:(0,x.c)(e.onResume),onSwipeStart:(0,l.m)(e.onSwipeStart,e=>{e.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:(0,l.m)(e.onSwipeMove,e=>{let{x:t,y:r}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","move"),e.currentTarget.style.setProperty("--radix-toast-swipe-move-x","".concat(t,"px")),e.currentTarget.style.setProperty("--radix-toast-swipe-move-y","".concat(r,"px"))}),onSwipeCancel:(0,l.m)(e.onSwipeCancel,e=>{e.currentTarget.setAttribute("data-swipe","cancel"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:(0,l.m)(e.onSwipeEnd,e=>{let{x:t,y:r}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","end"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.setProperty("--radix-toast-swipe-end-x","".concat(t,"px")),e.currentTarget.style.setProperty("--radix-toast-swipe-end-y","".concat(r,"px")),d(!1)})})})});Z.displayName=Y;var[$,G]=M(Y,{onClose(){}}),J=o.forwardRef((e,t)=>{let{__scopeToast:r,type:n="foreground",duration:s,open:d,onClose:c,onEscapeKeyDown:f,onPause:p,onResume:v,onSwipeStart:m,onSwipeMove:w,onSwipeCancel:y,onSwipeEnd:h,...g}=e,b=V(Y,r),[R,T]=o.useState(null),P=(0,a.s)(t,e=>T(e)),j=o.useRef(null),L=o.useRef(null),D=s||b.duration,N=o.useRef(0),S=o.useRef(D),k=o.useRef(0),{onToastAdd:A,onToastRemove:O}=b,M=(0,x.c)(()=>{var e;(null==R?void 0:R.contains(document.activeElement))&&(null===(e=b.viewport)||void 0===e||e.focus()),c()}),I=o.useCallback(e=>{e&&e!==1/0&&(window.clearTimeout(k.current),N.current=new Date().getTime(),k.current=window.setTimeout(M,e))},[M]);o.useEffect(()=>{let e=b.viewport;if(e){let t=()=>{I(S.current),null==v||v()},r=()=>{let e=new Date().getTime()-N.current;S.current=S.current-e,window.clearTimeout(k.current),null==p||p()};return e.addEventListener(q,r),e.addEventListener(z,t),()=>{e.removeEventListener(q,r),e.removeEventListener(z,t)}}},[b.viewport,D,p,v,I]),o.useEffect(()=>{d&&!b.isClosePausedRef.current&&I(D)},[d,D,b.isClosePausedRef,I]),o.useEffect(()=>(A(),()=>O()),[A,O]);let W=o.useMemo(()=>R?function e(t){let r=[];return Array.from(t.childNodes).forEach(t=>{var n;if(t.nodeType===t.TEXT_NODE&&t.textContent&&r.push(t.textContent),(n=t).nodeType===n.ELEMENT_NODE){let n=t.ariaHidden||t.hidden||"none"===t.style.display,o=""===t.dataset.radixToastAnnounceExclude;if(!n){if(o){let e=t.dataset.radixToastAnnounceAlt;e&&r.push(e)}else r.push(...e(t))}}}),r}(R):null,[R]);return b.viewport?(0,u.jsxs)(u.Fragment,{children:[W&&(0,u.jsx)(Q,{__scopeToast:r,role:"status","aria-live":"foreground"===n?"assertive":"polite","aria-atomic":!0,children:W}),(0,u.jsx)($,{scope:r,onClose:M,children:i.createPortal((0,u.jsx)(F.ItemSlot,{scope:r,children:(0,u.jsx)(C,{asChild:!0,onEscapeKeyDown:(0,l.m)(f,()=>{b.isFocusedToastEscapeKeyDownRef.current||M(),b.isFocusedToastEscapeKeyDownRef.current=!1}),children:(0,u.jsx)(E.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":d?"open":"closed","data-swipe-direction":b.swipeDirection,...g,ref:P,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:(0,l.m)(e.onKeyDown,e=>{"Escape"!==e.key||(null==f||f(e.nativeEvent),e.nativeEvent.defaultPrevented||(b.isFocusedToastEscapeKeyDownRef.current=!0,M()))}),onPointerDown:(0,l.m)(e.onPointerDown,e=>{0===e.button&&(j.current={x:e.clientX,y:e.clientY})}),onPointerMove:(0,l.m)(e.onPointerMove,e=>{if(!j.current)return;let t=e.clientX-j.current.x,r=e.clientY-j.current.y,n=!!L.current,o=["left","right"].includes(b.swipeDirection),i=["left","up"].includes(b.swipeDirection)?Math.min:Math.max,l=o?i(0,t):0,a=o?0:i(0,r),s="touch"===e.pointerType?10:2,u={x:l,y:a},d={originalEvent:e,delta:u};n?(L.current=u,ea("toast.swipeMove",w,d,{discrete:!1})):es(u,b.swipeDirection,s)?(L.current=u,ea("toast.swipeStart",m,d,{discrete:!1}),e.target.setPointerCapture(e.pointerId)):(Math.abs(t)>s||Math.abs(r)>s)&&(j.current=null)}),onPointerUp:(0,l.m)(e.onPointerUp,e=>{let t=L.current,r=e.target;if(r.hasPointerCapture(e.pointerId)&&r.releasePointerCapture(e.pointerId),L.current=null,j.current=null,t){let r=e.currentTarget,n={originalEvent:e,delta:t};es(t,b.swipeDirection,b.swipeThreshold)?ea("toast.swipeEnd",h,n,{discrete:!0}):ea("toast.swipeCancel",y,n,{discrete:!0}),r.addEventListener("click",e=>e.preventDefault(),{once:!0})}})})})}),b.viewport)})]}):null}),Q=e=>{let{__scopeToast:t,children:r,...n}=e,i=V(Y,t),[l,a]=o.useState(!1),[s,d]=o.useState(!1);return function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:()=>{},t=(0,x.c)(e);(0,j.N)(()=>{let e=0,r=0;return e=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(e),window.cancelAnimationFrame(r)}},[t])}(()=>a(!0)),o.useEffect(()=>{let e=window.setTimeout(()=>d(!0),1e3);return()=>window.clearTimeout(e)},[]),s?null:(0,u.jsx)(L,{asChild:!0,children:(0,u.jsx)(S,{...n,children:l&&(0,u.jsxs)(u.Fragment,{children:[i.label," ",r]})})})},ee=o.forwardRef((e,t)=>{let{__scopeToast:r,...n}=e;return(0,u.jsx)(E.div,{...n,ref:t})});ee.displayName="ToastTitle";var et=o.forwardRef((e,t)=>{let{__scopeToast:r,...n}=e;return(0,u.jsx)(E.div,{...n,ref:t})});et.displayName="ToastDescription";var er="ToastAction",en=o.forwardRef((e,t)=>{let{altText:r,...n}=e;return r.trim()?(0,u.jsx)(el,{altText:r,asChild:!0,children:(0,u.jsx)(ei,{...n,ref:t})}):(console.error("Invalid prop `altText` supplied to `".concat(er,"`. Expected non-empty `string`.")),null)});en.displayName=er;var eo="ToastClose",ei=o.forwardRef((e,t)=>{let{__scopeToast:r,...n}=e,o=G(eo,r);return(0,u.jsx)(el,{asChild:!0,children:(0,u.jsx)(E.button,{type:"button",...n,ref:t,onClick:(0,l.m)(e.onClick,o.onClose)})})});ei.displayName=eo;var el=o.forwardRef((e,t)=>{let{__scopeToast:r,altText:n,...o}=e;return(0,u.jsx)(E.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":n||void 0,...o,ref:t})});function ea(e,t,r,n){let{discrete:o}=n,i=r.originalEvent.currentTarget,l=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:r});t&&i.addEventListener(e,t,{once:!0}),o?h(i,l):i.dispatchEvent(l)}var es=function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,n=Math.abs(e.x),o=Math.abs(e.y),i=n>o;return"left"===t||"right"===t?i&&n>r:!i&&o>r};function eu(e){let t=document.activeElement;return e.some(e=>e===t||(e.focus(),document.activeElement!==t))}var ed=K,ec=X,ef=Z,ep=ee,ev=et,em=en,ew=ei}}]);