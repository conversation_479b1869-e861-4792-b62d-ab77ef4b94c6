### **New Features:**
- **Added support for Deepgram live transcription:** Enabled live transcription in Screenpipe Cloud to enhance accessibility and usability for users.

### **Improvements:**
- **Improved user experience for logged-in indication:** Enhanced UX to clearly display when a user is logged in.
- **Refactored OBSIDIAN prompt linking:** Improved the prompt to link individuals using OBSIDIAN syntax for better clarity.

### **Fixes:**
- **Fixed memory leak:** Resolved a memory leak issue (#1269) to improve performance.
- **Fixed known bug with CLI publish:** Added retry mechanism to CLI publish to handle known Bun error with fetch.
- **Fixed issues related to CI:** Addressed problems affecting Continuous Integration processes for better reliability.

#### **Full Changelog:** [4214e..b341f](https://github.com/mediar-ai/screenpipe/compare/4214e..b341f)

