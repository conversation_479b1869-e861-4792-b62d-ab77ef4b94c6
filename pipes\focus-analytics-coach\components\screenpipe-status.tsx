"use client";

import { useSettings } from "@/lib/settings-provider";
import { Badge } from "@/components/ui/badge";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { CheckCircle, XCircle, Loader2, AlertCircle } from "lucide-react";

export function ScreenpipeStatus() {
  const { screenpipeStatus, screenpipeError } = useSettings();

  const getStatusConfig = () => {
    switch (screenpipeStatus) {
      case 'checking':
        return {
          icon: <Loader2 className="h-3 w-3 animate-spin" />,
          label: 'Checking...',
          variant: 'secondary' as const,
          description: 'Checking Screenpipe connection...'
        };
      case 'connected':
        return {
          icon: <CheckCircle className="h-3 w-3" />,
          label: 'Connected',
          variant: 'default' as const,
          description: 'Connected to Screenpipe - real data available'
        };
      case 'disconnected':
        return {
          icon: <XCircle className="h-3 w-3" />,
          label: 'Demo Mode',
          variant: 'destructive' as const,
          description: screenpipeError || 'Screenpipe not available - showing demo data'
        };
      default:
        return {
          icon: <AlertCircle className="h-3 w-3" />,
          label: 'Unknown',
          variant: 'secondary' as const,
          description: 'Unknown connection status'
        };
    }
  };

  const config = getStatusConfig();

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Badge variant={config.variant} className="flex items-center gap-1 cursor-help">
            {config.icon}
            {config.label}
          </Badge>
        </TooltipTrigger>
        <TooltipContent>
          <p className="max-w-xs">{config.description}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

export function ScreenpipeStatusBanner() {
  const { screenpipeStatus, screenpipeError } = useSettings();

  if (screenpipeStatus === 'connected') {
    return null; // Don't show banner when connected
  }

  if (screenpipeStatus === 'checking') {
    return (
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
        <div className="flex items-center gap-3">
          <Loader2 className="h-5 w-5 text-blue-600 animate-spin" />
          <div>
            <h3 className="font-medium text-blue-900">Checking Screenpipe Connection</h3>
            <p className="text-sm text-blue-700">Verifying if Screenpipe is running...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-6">
      <div className="flex items-start gap-3">
        <AlertCircle className="h-5 w-5 text-orange-600 mt-0.5" />
        <div className="flex-1">
          <h3 className="font-medium text-orange-900">Demo Mode Active</h3>
          <p className="text-sm text-orange-700 mt-1">
            Screenpipe is not running. The plugin is displaying sample data for demonstration.
          </p>
          {screenpipeError && (
            <p className="text-xs text-orange-600 mt-2 font-mono bg-orange-100 p-2 rounded">
              {screenpipeError}
            </p>
          )}
          <div className="mt-3 text-sm text-orange-700">
            <p className="font-medium">To connect to real data:</p>
            <ol className="list-decimal list-inside mt-1 space-y-1">
              <li>Install and start Screenpipe</li>
              <li>Ensure it&apos;s running on port 3030</li>
              <li>Refresh this page</li>
            </ol>
          </div>
        </div>
      </div>
    </div>
  );
}
