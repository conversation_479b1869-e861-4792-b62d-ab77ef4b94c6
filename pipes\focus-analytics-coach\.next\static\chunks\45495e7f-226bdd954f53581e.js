"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[674],{68795:(e,t,a)=>{a.d(t,{Q6:()=>D}),a(42236);var r=a(97102),s=a(47700),i=a(9706),n=a(83881),o=a(21081),l=a(86632),h=a(64786),u=a(97953),d=a(34726),p=a(71553),g=a(32159),m=a(6843),c=a(44373),f=a(60217),I=a(10874),x=a(77308),E=a(65211),T=a(61560),L=a(26981);let _=r.kpo.YO,P={},y=(0,r._K2)().getNumber("CPU_HANDOFF_SIZE_THRESHOLD");class D extends r.uI_{nextDataId(){return D.nextDataId++}constructor(e){let t;if(super(),this.pendingRead=new WeakMap,this.pendingDisposal=new WeakSet,this.dataRefCount=new WeakMap,this.numBytesInGPU=0,this.uploadWaitMs=0,this.downloadWaitMs=0,this.lastGlFlushTime=0,this.warnedAboutMemory=!1,this.pendingDeletes=0,this.disposed=!1,!(0,r._K2)().getBool("HAS_WEBGL"))throw Error("WebGL is not supported on this device");if(null!=e){if(e instanceof d.n)t=e;else{let a=(0,s.bU)((0,r._K2)().getNumber("WEBGL_VERSION"),e);t=new d.n(a)}this.binaryCache={},this.gpgpuCreatedLocally=!1}else{let e=(0,s.bU)((0,r._K2)().getNumber("WEBGL_VERSION"));t=new d.n(e),this.binaryCache=function(e){return e in P||(P[e]={}),P[e]}((0,r._K2)().getNumber("WEBGL_VERSION")),this.gpgpuCreatedLocally=!0}this.gpgpu=t,this.canvas=this.gpgpu.gl.canvas,this.textureManager=new I.p(this.gpgpu),this.numMBBeforeWarning=null==(0,r._K2)().global.screen?1024:(0,r._K2)().global.screen.height*(0,r._K2)().global.screen.width*window.devicePixelRatio*600/1024/1024,this.texData=new r.GJx(this,(0,r.Hi9)())}numDataIds(){return this.texData.numDataIds()-this.pendingDeletes}writeTexture(e,t,a,r,s,i){let n=this.makeTensorInfo(t,a),o=this.texData.get(n.dataId);o.isPacked=!1,o.texture={texture:e,texShape:[r,s]},o.texShape=[r,s];let l=L.FP(t),u=new h.C(l,!1,i),d=this.runWebGLProgram(u,[n],a,[[r,s]]);return d.shape=t,o.texture=null,this.disposeIntermediateTensorInfo(n),d.dataId}write(e,t,a){if(((0,r._K2)().getBool("WEBGL_CHECK_NUMERICAL_PROBLEMS")||(0,r._K2)().getBool("DEBUG"))&&this.checkNumericalProblems(e),"complex64"===a&&null!=e)throw Error("Cannot write to a complex64 dtype. Please use tf.complex(real, imag).");let s={id:this.nextDataId()};return this.texData.set(s,{shape:t,dtype:a,values:e,usage:f.tT.UPLOAD,refCount:1}),s}refCount(e){return this.texData.has(e)?this.texData.get(e).refCount:0}incRef(e){let t=this.texData.get(e);t.refCount++}decRef(e){if(this.texData.has(e)){let t=this.texData.get(e);t.refCount--}}move(e,t,a,s,i){if((0,r._K2)().getBool("DEBUG")&&this.checkNumericalProblems(t),"complex64"===s)throw Error("Cannot write to a complex64 dtype. Please use tf.complex(real, imag).");this.texData.set(e,{shape:a,dtype:s,values:t,usage:f.tT.UPLOAD,refCount:i})}disposeIntermediateTensorInfo(e){this.disposeData(e.dataId)}readSync(e){let t,a;let{values:s,dtype:i,complexTensorInfos:n,slice:o,shape:l,isPacked:h}=this.texData.get(e);if(null!=o){let t;t=h?new E.rf(l,x.UC):new x.hE(l,x.UC);let a=this.runWebGLProgram(t,[{dataId:e,shape:l,dtype:i}],i),r=this.readSync(a.dataId);return this.disposeIntermediateTensorInfo(a),r}if(null!=s)return this.convertAndCacheOnCPU(e);if("string"===i)return s;let u=null!=this.activeTimers;if(u&&(t=r.ZSL.now()),"complex64"===i){let e=this.readSync(n.real.dataId),t=this.readSync(n.imag.dataId);a=r.C0T.mergeRealAndImagArrays(e,t)}else a=this.getValuesFromTexture(e);return u&&(this.downloadWaitMs+=r.ZSL.now()-t),this.convertAndCacheOnCPU(e,a)}async read(e){let t,a;if(this.pendingRead.has(e)){let t=this.pendingRead.get(e);return new Promise(e=>t.push(e))}let{values:s,shape:i,slice:n,dtype:o,complexTensorInfos:l,isPacked:h}=this.texData.get(e);if(null!=n){let t;t=h?new E.rf(i,x.UC):new x.hE(i,x.UC);let a=this.runWebGLProgram(t,[{dataId:e,shape:i,dtype:o}],o),r=this.read(a.dataId);return this.disposeIntermediateTensorInfo(a),r}if(null!=s)return this.convertAndCacheOnCPU(e);if((0,r._K2)().getBool("DEBUG")&&!(0,r._K2)().getBool("WEBGL_DOWNLOAD_FLOAT_ENABLED")&&2===(0,r._K2)().getNumber("WEBGL_VERSION"))throw Error("tensor.data() with WEBGL_DOWNLOAD_FLOAT_ENABLED=false and WEBGL_VERSION=2 not yet supported.");let u=null;if("complex64"!==o&&(0,r._K2)().get("WEBGL_BUFFER_SUPPORTED")){t=this.decode(e);let a=this.texData.get(t.dataId);u=this.gpgpu.createBufferFromTexture(a.texture.texture,...f.GM(i))}if(this.pendingRead.set(e,[]),"complex64"!==o&&await this.gpgpu.createAndWaitForFence(),"complex64"===o){let e=await Promise.all([this.read(l.real.dataId),this.read(l.imag.dataId)]),t=e[0],s=e[1];a=r.C0T.mergeRealAndImagArrays(t,s)}else if(null==u)a=this.getValuesFromTexture(e);else{let e=r.ZSL.sizeFromShape(i);a=this.gpgpu.downloadFloat32MatrixFromBuffer(u,e)}if(null!=t&&this.disposeIntermediateTensorInfo(t),null!=u){let e=this.gpgpu.gl;L.ul(e,()=>e.deleteBuffer(u))}let d=this.convertAndCacheOnCPU(e,a),p=this.pendingRead.get(e);return this.pendingRead.delete(e),p.forEach(e=>e(d)),this.pendingDisposal.has(e)&&(this.pendingDisposal.delete(e),this.disposeData(e)&&(0,r.Hi9)().removeDataId(e,this),this.pendingDeletes--),d}readToGPU(e,t={}){let{values:a,shape:s,slice:i,dtype:n,isPacked:o,texture:l}=this.texData.get(e);if("complex64"===n)throw Error("Does not support reading texture for complex64 dtype.");if(null!=i){let a;a=o?new E.rf(s,x.UC):new x.hE(s,x.UC);let r=this.runWebGLProgram(a,[{dataId:e,shape:s,dtype:n}],n),i=this.readToGPU(r,t);return this.disposeIntermediateTensorInfo(r),i}if(null==l){if(null!=a)throw Error("Data is not on GPU but on CPU.");throw Error("There is no data on GPU or CPU.")}let h=this.decode(e,t.customTexShape);return Object.assign({tensorRef:(0,r.Hi9)().makeTensorFromTensorInfo(h)},this.texData.get(h.dataId).texture)}bufferSync(e){let t=this.readSync(e.dataId);if("string"===e.dtype)try{let a=t.map(e=>r.ZSL.decodeString(e));return(0,r.ra8)(e.shape,e.dtype,a)}catch(e){throw Error("Failed to decode encoded string bytes into utf-8")}return(0,r.ra8)(e.shape,e.dtype,t)}checkNumericalProblems(e){if(null!=e)for(let t=0;t<e.length;t++){let a=e[t];if(!L.dm(a)){if((0,r._K2)().getBool("WEBGL_RENDER_FLOAT32_CAPABLE"))throw Error(`The value ${a} cannot be represented with your current settings. Consider enabling float32 rendering: 'tf.env().set('WEBGL_RENDER_FLOAT32_ENABLED', true);'`);throw Error(`The value ${a} cannot be represented on this device.`)}}}getValuesFromTexture(e){let{shape:t,dtype:a,isPacked:s}=this.texData.get(e),i=r.ZSL.sizeFromShape(t);if((0,r._K2)().getBool("WEBGL_DOWNLOAD_FLOAT_ENABLED")){let a=this.decode(e),r=this.texData.get(a.dataId),s=this.gpgpu.downloadMatrixFromPackedTexture(r.texture.texture,...f.GM(t)).subarray(0,i);return this.disposeIntermediateTensorInfo(a),s}let n=(0,r._K2)().getBool("WEBGL_PACK")&&!0===s,h=n?L.FP(t):t,u=n?new l.N(h):new o.$(h),d=this.runWebGLProgram(u,[{shape:h,dtype:a,dataId:e}],"float32"),p=this.texData.get(d.dataId),g=this.gpgpu.downloadByteEncodedFloatMatrixFromOutputTexture(p.texture.texture,p.texShape[0],p.texShape[1]).subarray(0,i);return this.disposeIntermediateTensorInfo(d),g}timerAvailable(){return(0,r._K2)().getNumber("WEBGL_DISJOINT_QUERY_TIMER_EXTENSION_RELIABLE")>0}time(e){let t=this.activeTimers,a=[],s=!1;null==this.programTimersStack?(this.programTimersStack=a,s=!0):this.activeTimers.push(a),this.activeTimers=a,e();let i=r.ZSL.flatten(this.activeTimers.map(e=>e.query)).filter(e=>null!=e),n=r.ZSL.flatten(this.activeTimers.map(e=>e.name)).filter(e=>null!=e);this.activeTimers=t,s&&(this.programTimersStack=null);let o={uploadWaitMs:this.uploadWaitMs,downloadWaitMs:this.downloadWaitMs,kernelMs:null,wallMs:null};return(async()=>{if((0,r._K2)().getNumber("WEBGL_DISJOINT_QUERY_TIMER_EXTENSION_RELIABLE")>0){let e=await Promise.all(i);o.kernelMs=r.ZSL.sum(e),o.getExtraProfileInfo=()=>e.map((e,t)=>({name:n[t],ms:e})).map(e=>`${e.name}: ${e.ms}`).join(", ")}else o.kernelMs={error:"WebGL query timers are not supported in this environment."};return this.uploadWaitMs=0,this.downloadWaitMs=0,o})()}memory(){return{unreliable:!1,numBytesInGPU:this.numBytesInGPU,numBytesInGPUAllocated:this.textureManager.numBytesAllocated,numBytesInGPUFree:this.textureManager.numBytesFree}}startTimer(){return(0,r._K2)().getNumber("WEBGL_DISJOINT_QUERY_TIMER_EXTENSION_RELIABLE")>0?this.gpgpu.beginQuery():{startMs:r.ZSL.now(),endMs:null}}endTimer(e){return(0,r._K2)().getNumber("WEBGL_DISJOINT_QUERY_TIMER_EXTENSION_RELIABLE")>0?this.gpgpu.endQuery():e.endMs=r.ZSL.now(),e}async getQueryTime(e){return(0,r._K2)().getNumber("WEBGL_DISJOINT_QUERY_TIMER_EXTENSION_RELIABLE")>0?this.gpgpu.waitForQueryAndGetTime(e):e.endMs-e.startMs}disposeData(e,t=!1){if(this.pendingDisposal.has(e))return!1;if(!this.texData.has(e))return!0;if(t?this.texData.get(e).refCount=0:this.texData.get(e).refCount--,!t&&this.texData.get(e).refCount>0)return!1;if(this.pendingRead.has(e))return this.pendingDisposal.add(e),this.pendingDeletes++,!1;this.releaseGPUData(e);let{complexTensorInfos:a}=this.texData.get(e);return null!=a&&(this.disposeData(a.real.dataId,t),this.disposeData(a.imag.dataId,t)),this.texData.delete(e),!0}releaseGPUData(e){let{texture:t,dtype:a,texShape:r,usage:s,isPacked:i,slice:n}=this.texData.get(e),o=n&&n.origDataId||e,l=this.dataRefCount.get(o);l>1?this.dataRefCount.set(o,l-1):(this.dataRefCount.delete(o),null!=t&&(this.numBytesInGPU-=this.computeBytes(r,a),this.textureManager.releaseTexture(t,r,s,i)));let h=this.texData.get(e);h.texture=null,h.texShape=null,h.isPacked=!1,h.slice=null}getTexture(e){return this.uploadToGPU(e),this.texData.get(e).texture.texture}getDataInfo(e){return this.texData.get(e)}shouldExecuteOnCPU(e,t=y){return(0,r._K2)().getBool("WEBGL_CPU_FORWARD")&&e.every(e=>null==this.texData.get(e.dataId).texture&&r.ZSL.sizeFromShape(e.shape)<t)}getGPGPUContext(){return this.gpgpu}where(e){r.C0T.warn("tf.where() in webgl locks the UI thread. Call tf.whereAsync() instead");let t=e.dataSync();return _(e.shape,t)}packedUnaryOp(e,t,a){let s=new E.rf(e.shape,t),i=this.compileAndRun(s,[e],a);return(0,r.Hi9)().makeTensorFromTensorInfo(i)}abs(e){if(this.shouldExecuteOnCPU([e])&&"complex64"!==e.dtype){let t=(0,g.f8)(this.texData.get(e.dataId).values);return this.makeOutput(e.shape,e.dtype,t)}if((0,r._K2)().getBool("WEBGL_PACK_UNARY_OPERATIONS"))return this.packedUnaryOp(e,x.pd,e.dtype);let t=new x.hE(e.shape,x.pd),a=this.compileAndRun(t,[e]);return(0,r.Hi9)().makeTensorFromTensorInfo(a)}makeTensorInfo(e,t,a){let s;if("string"===t&&null!=a&&a.length>0&&r.ZSL.isString(a[0])){let i=a.map(e=>r.ZSL.encodeString(e));s=this.write(i,e,t)}else s=this.write(a,e,t);return this.texData.get(s).usage=null,{dataId:s,shape:e,dtype:t}}makeOutput(e,t,a){return(0,r.Hi9)().makeTensorFromTensorInfo(this.makeTensorInfo(e,t,a),this)}unpackTensor(e){let t=new T.z(e.shape);return this.runWebGLProgram(t,[e],e.dtype)}packTensor(e){let t=new m.m(e.shape);return this.runWebGLProgram(t,[e],e.dtype,null,!0)}packedReshape(e,t){let a=[L.N0(e.shape),...L.Ph(e.shape)],r={dtype:e.dtype,shape:a,dataId:e.dataId},s=[L.N0(t),...L.Ph(t)],i=new c.R(s,a),n=this.runWebGLProgram(i,[r],e.dtype,[a],!0);return{dataId:n.dataId,shape:t,dtype:n.dtype}}decode(e,t){let a;let{isPacked:s,shape:o,dtype:l}=this.texData.get(e);if(null!=t){let e=r.ZSL.sizeFromShape(o),a=t[0]*t[1]*4;r.ZSL.assert(e<=a,()=>"customTexShape is too small. Row * Column * 4 should be equal or larger than the size of the tensor data.")}let h=L.FP(o);a=s?new n.S(h):new i.K(h);let u=[null!=t?t:f.GM(h)],d=this.runWebGLProgram(a,[{shape:h,dtype:l,dataId:e}],l,u,!0,t);return{dtype:l,shape:o,dataId:d.dataId}}runWebGLProgram(e,t,a,s,i=!1,n){let o;let l=this.makeTensorInfo(e.outputShape,a),h=this.texData.get(l.dataId);if(e.packedOutput&&(h.isPacked=!0),e.outPackingScheme===f.BB.DENSE&&(h.texShape=(null!=n?n:f.GM(e.outputShape)).map(e=>2*e)),null!=e.outTexUsage&&(h.usage=e.outTexUsage),0===r.ZSL.sizeFromShape(l.shape))return h.values=r.ZSL.getTypedArrayFromDType(l.dtype,0),l;let u=[],d=t.map(t=>{if("complex64"===t.dtype)throw Error("GPGPUProgram does not support complex64 input. For complex64 dtypes, please separate the program into real and imaginary parts.");let a=this.texData.get(t.dataId);if(null==a.texture){if(!e.packedInputs&&r.ZSL.sizeFromShape(t.shape)<=(0,r._K2)().getNumber("WEBGL_SIZE_UPLOAD_UNIFORM"))return{shape:t.shape,texData:null,isUniform:!0,uniformValues:a.values};e.packedInputs&&(a.isPacked=!0,a.shape=t.shape)}if(this.uploadToGPU(t.dataId),!!a.isPacked!=!!e.packedInputs)t=a.isPacked?this.unpackTensor(t):this.packTensor(t),u.push(t),a=this.texData.get(t.dataId);else if(a.isPacked&&!L.P0(a.shape,t.shape)){let e=t,r=t.shape;t.shape=a.shape,t=this.packedReshape(t,r),u.push(t),a=this.texData.get(t.dataId),e.shape=r}return{shape:t.shape,texData:a,isUniform:!1}});this.uploadToGPU(l.dataId);let g={shape:l.shape,texData:h,isUniform:!1},m=p.Tb(e,d,g),c=this.getAndSaveBinary(m,()=>p.Kz(this.gpgpu,e,d,g)),I=null!=this.activeTimers;I&&(o=this.startTimer()),(0,r._K2)().get("ENGINE_COMPILE_ONLY")||p.mB(this.gpgpu,c,d,g,s),u.forEach(e=>this.disposeIntermediateTensorInfo(e)),I&&(o=this.endTimer(o),this.activeTimers.push({name:e.constructor.name,query:this.getQueryTime(o)}));let x=(0,r._K2)().getNumber("WEBGL_FLUSH_THRESHOLD");if(x>0){let e=r.ZSL.now();e-this.lastGlFlushTime>x&&(this.gpgpu.gl.flush(),this.lastGlFlushTime=e)}if(!(0,r._K2)().getBool("WEBGL_LAZILY_UNPACK")&&h.isPacked&&!1===i){let e=this.unpackTensor(l);return this.disposeIntermediateTensorInfo(l),e}return l}compileAndRun(e,t,a,r,s=!1){return a=a||t[0].dtype,this.runWebGLProgram(e,t,a,r,s)}getAndSaveBinary(e,t){return e in this.binaryCache||(this.binaryCache[e]=t()),this.binaryCache[e]}getTextureManager(){return this.textureManager}dispose(){!this.disposed&&((0,r._K2)().getBool("IS_TEST")||Object.keys(this.binaryCache).forEach(e=>{this.gpgpu.deleteProgram(this.binaryCache[e].webGLProgram),delete this.binaryCache[e]}),this.textureManager.dispose(),null!=this.canvas&&"undefined"!=typeof HTMLCanvasElement&&this.canvas instanceof HTMLCanvasElement?this.canvas.remove():this.canvas=null,this.gpgpuCreatedLocally&&(this.gpgpu.program=null,this.gpgpu.dispose()),this.disposed=!0)}floatPrecision(){return null==this.floatPrecisionValue&&(this.floatPrecisionValue=(0,r.DZQ)(()=>{if(!(0,r._K2)().get("WEBGL_RENDER_FLOAT32_ENABLED")){let e=(0,r._K2)().getBool("DEBUG");(0,r._K2)().set("DEBUG",!1);let t=this.abs((0,r.d_2)(1e-8)).dataSync()[0];if((0,r._K2)().set("DEBUG",e),t>0)return 32}return 16})),this.floatPrecisionValue}epsilon(){return 32===this.floatPrecision()?1e-7:1e-4}uploadToGPU(e){let t;let a=this.texData.get(e),{shape:s,dtype:i,values:n,texture:o,usage:l,isPacked:d}=a;if(null!=o)return;let p=null!=this.activeTimers;p&&(t=r.ZSL.now());let g=a.texShape;if(null==g&&(a.texShape=g=L.fA(s,d)),null!=n){let e;let o=L.FP(s),l=g[1],m=g[0],c=n instanceof Uint8Array||n instanceof Uint8ClampedArray;(d||!c)&&([l,m]=f.NO(g[0],g[1])),e=d?new u.A(o,c):new h.C(o,c);let I=c?[m,l]:g,x=this.makeTensorInfo(I,i),E=this.texData.get(x.dataId);c?E.usage=f.tT.PIXELS:E.usage=f.tT.UPLOAD,E.texShape=I,this.gpgpu.uploadDenseMatrixToTexture(this.getTexture(x.dataId),l,m,n);let T=[[m,l]],_=this.runWebGLProgram(e,[x],i,T,!0),P=this.texData.get(_.dataId);a.texShape=P.texShape,a.isPacked=P.isPacked,a.usage=P.usage,(0,r._K2)().get("ENGINE_COMPILE_ONLY")?this.disposeData(_.dataId):(a.texture=P.texture,a.values=null,this.texData.delete(_.dataId)),this.disposeIntermediateTensorInfo(x),p&&(this.uploadWaitMs+=r.ZSL.now()-t)}else a.texture=this.acquireTexture(g,l,i,d)}convertAndCacheOnCPU(e,t){let a=this.texData.get(e),{dtype:r}=a;return null!=t&&(a.values=function(e,t){if("float32"===t||"complex64"===t)return e;if("int32"===t||"bool"===t){let a="int32"===t?new Int32Array(e.length):new Uint8Array(e.length);for(let t=0;t<a.length;++t)a[t]=Math.round(e[t]);return a}throw Error(`Unknown dtype ${t}`)}(t,r)),a.values}acquireTexture(e,t,a,r){if(this.numBytesInGPU+=this.computeBytes(e,a),!this.warnedAboutMemory&&this.numBytesInGPU>1048576*this.numMBBeforeWarning){let e=(this.numBytesInGPU/1024/1024).toFixed(2);this.warnedAboutMemory=!0,console.warn(`High memory usage in GPU: ${e} MB, most likely due to a memory leak`)}return this.textureManager.acquireTexture(e,t,r)}computeBytes(e,t){return e[0]*e[1]*r.ZSL.bytesPerElement(t)}checkCompileCompletion(){for(let[,e]of Object.entries(this.binaryCache))this.checkCompletion_(e)}async checkCompileCompletionAsync(){let e=[];if(this.gpgpu.parallelCompilationExtension){for(let[,t]of Object.entries(this.binaryCache))e.push(this.checkCompletionAsync_(t));return Promise.all(e)}for(let[,t]of Object.entries(this.binaryCache)){let a=new Promise(e=>{try{this.checkCompletion_(t),e(!0)}catch(e){throw e}});e.push(a)}return Promise.all(e)}async checkCompletionAsync_(e){return this.gpgpu.gl.getProgramParameter(e.webGLProgram,this.gpgpu.parallelCompilationExtension.COMPLETION_STATUS_KHR)?this.checkCompletion_(e):(await (0,r.dA1)(),this.checkCompletionAsync_(e))}checkCompletion_(e){if(!1===this.gpgpu.gl.getProgramParameter(e.webGLProgram,this.gpgpu.gl.LINK_STATUS)){if(console.log(this.gpgpu.gl.getProgramInfoLog(e.webGLProgram)),!1===this.gpgpu.gl.getShaderParameter(e.fragmentShader,this.gpgpu.gl.COMPILE_STATUS))throw L.cr(e.source,this.gpgpu.gl.getShaderInfoLog(e.fragmentShader)),Error("Failed to compile fragment shader.");throw Error("Failed to link vertex and fragment shaders.")}return!0}getUniformLocations(){for(let e of Object.values(this.binaryCache)){this.gpgpu.buildVao(e.webGLProgram);let{variablesLocations:t,customUniformLocations:a,infLoc:r,nanLoc:s,outShapeLocation:i,outShapeStridesLocation:n,outTexShapeLocation:o}=(0,p.Tw)(this.gpgpu,e.program,e.webGLProgram);e.variablesLocations=t,e.customUniformLocations=a,e.infLoc=r,e.nanLoc=s,e.outShapeLocation=i,e.outShapeStridesLocation=n,e.outTexShapeLocation=o}}createTensorFromGPUData(e,t,a){e.channels=e.channels||"RGBA";let{texture:s,height:i,width:n,channels:o}=e,l=(0,r.Hi9)().backend;if(!l.gpgpu.gl.isTexture(s))throw Error("The texture is invalid. Also, please make sure the texture and the TFJS WebGL backend are using the same canvas. If you want to use your own custom canvas, you have to create and use the custom TFJS WebGL backend created from the canvas through 'new tf.MathBackendWebGL(customCanvas)'.");let h=l.writeTexture(s,t,a,i,n,o);return(0,r.Hi9)().makeTensorFromDataId(h,t,a,l)}}D.nextDataId=0}}]);