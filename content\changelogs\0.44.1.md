Based on the provided commits, here is the changelog for the new Screenpipe update:

### **New Features:**
- **Support for active Browser URL detection in Linux:** Implemented functionality to detect the active browser URL on Linux systems.
- **Windows UI automation:** Added support for Windows UI automation to enhance user interaction.

### **Improvements:**
- **Enhanced Linux package installation process:** Improved the package installation process for Linux systems in the pre_build.js.
- **Refactor to use core-graphics-helmer-fork:** Updated the code to utilize the core-graphics-helmer-fork for better performance and reliability.

### **Fixes:**
- **Fixed Screenpipe not working due to IPv6 port and design flow:** Resolved issues that caused Screenpipe to malfunction related to IPv6 configurations.
- **Fixed compilation errors due to CMake version:** Addressed compilation errors that occurred due to an incompatible CMake version.
- **Fixed Linux integration test failures:** Resolved issues in the integration tests on Linux systems.
- **Fixed backend termination issue on Linux:** Corrected the behavior of backend processes to prevent unintended application termination.
- **Fixed few functions in Windows operator:** Addressed various function issues within the Windows operator implementation. 

This version of the changelog focuses on enhancements and fixes that provide clear value to users of Screenpipe.

#### **Full Changelog:** [4f160..d5130](https://github.com/mediar-ai/screenpipe/compare/4f160..d5130)

